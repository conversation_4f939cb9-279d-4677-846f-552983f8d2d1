jobs:
  deploy:
    if: github.ref == 'refs/heads/main'
    needs:
    - test
    - security-scan
    - frontend-test
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Deploy to staging
      run: '

        echo "Deploying to staging environment..."

        # 这里添加实际的部署脚本

        '
  frontend-test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
    - name: Install frontend dependencies
      run: '

        cd apps/frontend

        npm ci

        '
    - name: Run frontend tests
      run: '

        cd apps/frontend

        npm test -- --coverage --watchAll=false

        '
    - name: Build frontend
      run: '

        cd apps/frontend

        npm run build

        '
  security-scan:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Run security scan
      run: '

        pip install bandit safety

        bandit -r apps/backend/

        safety check

        '
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    - name: Install dependencies
      run: '

        pip install --upgrade pip

        pip install -r requirements.txt

        pip install pytest pytest-cov

        '
    - name: Run unit tests
      run: '

        python -m pytest tests/ -v --cov=apps/backend --cov-report=xml --cov-report=term-missing

        '
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
    strategy:
      matrix:
        python-version:
        - '3.9'
        - '3.10'
        - '3.11'
name: CI/CD Pipeline
'on':
  pull_request:
    branches:
    - main
  push:
    branches:
    - main
    - develop
