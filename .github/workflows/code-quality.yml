jobs:
  code-quality:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    - name: Install quality tools
      run: '

        pip install flake8 black isort mypy

        '
    - name: Run code formatting check
      run: '

        black --check apps/backend/

        isort --check-only apps/backend/

        '
    - name: Run linting
      run: '

        flake8 apps/backend/

        '
    - name: Run type checking
      run: '

        mypy apps/backend/ --ignore-missing-imports

        '
name: Code Quality
'on':
  pull_request:
    branches:
    - main
  push:
    branches:
    - main
    - develop
