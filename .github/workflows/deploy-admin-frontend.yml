name: Deploy Admin Frontend

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'apps/admin-frontend/**'
      - '.github/workflows/deploy-admin-frontend.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'apps/admin-frontend/**'

env:
  NODE_VERSION: '18'
  PNPM_VERSION: '8'

jobs:
  # 代码质量检查
  quality-check:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v3
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install --frozen-lockfile
        working-directory: apps/admin-frontend

      - name: TypeScript check
        run: pnpm run type-check
        working-directory: apps/admin-frontend

      - name: ESLint check
        run: pnpm run lint
        working-directory: apps/admin-frontend

      - name: Run tests
        run: pnpm run test
        working-directory: apps/admin-frontend

  # 构建应用
  build:
    needs: quality-check
    runs-on: ubuntu-latest
    strategy:
      matrix:
        environment: [development, production]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Install dependencies
        run: pnpm install --frozen-lockfile
        working-directory: apps/admin-frontend

      - name: Load environment variables
        run: |
          if [ "${{ matrix.environment }}" = "production" ]; then
            cp configs/environments/.env.production apps/admin-frontend/.env.production
          else
            cp configs/environments/.env.development apps/admin-frontend/.env.development
          fi

      - name: Build application
        run: |
          if [ "${{ matrix.environment }}" = "production" ]; then
            pnpm run build
          else
            pnpm run build:dev
          fi
        working-directory: apps/admin-frontend
        env:
          NODE_ENV: ${{ matrix.environment }}

      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: admin-frontend-${{ matrix.environment }}
          path: apps/admin-frontend/dist
          retention-days: 7

  # 部署到开发环境
  deploy-development:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    environment: development
    steps:
      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: admin-frontend-development
          path: ./dist

      - name: Deploy to development server
        run: |
          echo "Deploying to development environment..."
          # 这里添加实际的部署脚本
          # 例如：rsync, scp, 或者调用部署API

  # 部署到生产环境
  deploy-production:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production
    steps:
      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: admin-frontend-production
          path: ./dist

      - name: Deploy to production server
        run: |
          echo "Deploying to production environment..."
          # 这里添加实际的部署脚本
          # 例如：rsync, scp, 或者调用部署API

  # 性能测试
  performance-test:
    needs: deploy-development
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install Lighthouse CI
        run: npm install -g @lhci/cli@0.12.x

      - name: Run Lighthouse CI
        run: |
          lhci autorun
        env:
          LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}

  # 通知
  notify:
    needs: [deploy-development, deploy-production]
    runs-on: ubuntu-latest
    if: always()
    steps:
      - name: Notify deployment status
        run: |
          if [ "${{ needs.deploy-development.result }}" = "success" ] || [ "${{ needs.deploy-production.result }}" = "success" ]; then
            echo "Deployment successful!"
          else
            echo "Deployment failed!"
          fi
