jobs:
  auto-fix-docs:
    if: github.event_name == 'schedule'
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    - name: Auto-fix documentation issues
      run: '

        python scripts/fix_remaining_broken_links.py

        python scripts/update_code_examples.py

        '
    - name: Commit changes
      run: '

        git config --local user.email "<EMAIL>"

        git config --local user.name "GitHub Action"

        git add docs/

        git diff --staged --quiet || git commit -m "docs: auto-fix documentation issues"

        git push

        '
  validate-docs:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    - name: Install dependencies
      run: '

        pip install --upgrade pip

        pip install requests beautifulsoup4 lxml

        '
    - name: Validate documentation structure
      run: python scripts/analyze_documentation_status.py
    - name: Check broken links
      run: python scripts/fix_remaining_broken_links.py
    - name: Validate external links
      run: python scripts/validate_external_links.py
    - name: Generate quality report
      run: python scripts/validate_documentation_quality.py
    - name: Upload reports
      uses: actions/upload-artifact@v3
      with:
        name: documentation-reports
        path: output/*documentation*.json
name: Documentation CI
'on':
  pull_request:
    paths:
    - docs/**
    - scripts/*documentation*
  push:
    paths:
    - docs/**
    - scripts/*documentation*
  schedule:
  - cron: 0 2 * * 1
