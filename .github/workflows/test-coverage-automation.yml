name: 测试覆盖率自动化流水线

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # 每天凌晨2点自动运行
    - cron: '0 2 * * *'

jobs:
  backend-tests:
    name: 后端测试覆盖率
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.9, 3.10, 3.11]
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
        
    - name: 缓存依赖
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
          
    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov pytest-xdist coverage
        
    - name: 运行后端单元测试
      run: |
        python -m pytest tests/ -v \
          --cov=apps/backend \
          --cov-report=xml \
          --cov-report=term-missing \
          --cov-report=html \
          --cov-fail-under=75 \
          --maxfail=5 \
          -n auto
          
    - name: 运行新增高级测试
      run: |
        python -m pytest tests/backend/ -v \
          --cov=apps/backend \
          --cov-append \
          --cov-report=xml \
          --cov-report=term-missing
          
    - name: 上传覆盖率报告到Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: backend-tests
        name: backend-coverage-${{ matrix.python-version }}
        fail_ci_if_error: false
        
    - name: 保存覆盖率报告
      uses: actions/upload-artifact@v3
      with:
        name: backend-coverage-report-${{ matrix.python-version }}
        path: htmlcov/
        
  frontend-tests:
    name: 前端测试覆盖率
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18, 20]
        
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        cache-dependency-path: apps/frontend/package-lock.json
        
    - name: 安装前端依赖
      working-directory: apps/frontend
      run: |
        npm ci
        npm install --save-dev @testing-library/jest-dom
        
    - name: 运行前端测试
      working-directory: apps/frontend
      run: |
        npm run test -- --coverage --watchAll=false --ci
        
    - name: 检查覆盖率阈值
      working-directory: apps/frontend
      run: |
        npm run test -- --coverage --watchAll=false --ci --coverageThreshold='{"global":{"branches":70,"functions":70,"lines":75,"statements":75}}'
        
    - name: 上传前端覆盖率报告
      uses: codecov/codecov-action@v3
      with:
        file: apps/frontend/coverage/lcov.info
        flags: frontend-tests
        name: frontend-coverage-${{ matrix.node-version }}
        fail_ci_if_error: false
        
    - name: 保存前端覆盖率报告
      uses: actions/upload-artifact@v3
      with:
        name: frontend-coverage-report-${{ matrix.node-version }}
        path: apps/frontend/coverage/
        
  integration-tests:
    name: 集成测试
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests]
    
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_DB: test_recruitment
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
          
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置Python 3.10
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
        
    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov
        
    - name: 运行集成测试
      env:
        DATABASE_URL: postgresql://postgres:test_password@localhost:5432/test_recruitment
        REDIS_URL: redis://localhost:6379/0
        TEST_MODE: true
      run: |
        python -m pytest tests/integration/ -v \
          --cov=apps/backend \
          --cov-report=xml \
          --cov-report=term-missing
          
    - name: 运行API端到端测试
      env:
        DATABASE_URL: postgresql://postgres:test_password@localhost:5432/test_recruitment
        REDIS_URL: redis://localhost:6379/0
        TEST_MODE: true
      run: |
        python -m pytest tests/api/ -v \
          --cov=apps/backend \
          --cov-append \
          --cov-report=xml
          
  security-tests:
    name: 安全测试
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置Python 3.10
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
        
    - name: 安装安全测试工具
      run: |
        python -m pip install --upgrade pip
        pip install bandit safety semgrep
        pip install -r requirements.txt
        
    - name: 运行安全漏洞扫描
      run: |
        bandit -r apps/backend/ -f json -o security-report.json || true
        safety check --json --output safety-report.json || true
        
    - name: 运行高级安全测试
      run: |
        python -m pytest tests/backend/test_security_comprehensive.py -v
        
    - name: 保存安全报告
      uses: actions/upload-artifact@v3
      with:
        name: security-reports
        path: |
          security-report.json
          safety-report.json
          
  coverage-report:
    name: 生成综合覆盖率报告
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests, integration-tests]
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 下载所有覆盖率报告
      uses: actions/download-artifact@v3
      
    - name: 设置Python 3.10
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
        
    - name: 生成综合覆盖率报告
      run: |
        python scripts/generate_test_coverage_report.py
        
    - name: 创建覆盖率徽章
      run: |
        pip install coverage-badge
        coverage-badge -o coverage.svg
        
    - name: 上传综合报告
      uses: actions/upload-artifact@v3
      with:
        name: comprehensive-coverage-report
        path: |
          output/test_coverage_report_*.json
          coverage.svg
          
    - name: 评论PR覆盖率结果
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          const path = require('path');
          
          // 读取覆盖率报告
          const reportFiles = fs.readdirSync('output/').filter(f => f.startsWith('test_coverage_report_'));
          if (reportFiles.length > 0) {
            const reportPath = path.join('output/', reportFiles[0]);
            const report = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
            
            const comment = `## 📊 测试覆盖率报告
            
**整体覆盖率**: ${report.overall_coverage || 'N/A'}%
**测试通过率**: ${report.test_success_rate || 'N/A'}%
**新增测试**: ${report.new_tests_count || 0}个

### 模块覆盖率
${Object.entries(report.module_coverage || {}).map(([module, coverage]) => 
  `- **${module}**: ${coverage}%`
).join('\n')}

### 建议
${(report.recommendations || []).map(rec => 
  `- ${rec.action} (优先级: ${rec.priority})`
).join('\n')}

---
*自动生成于 ${new Date().toISOString()}*`;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });
          }
