"""
DeepSeek API 客户端
"""
import os
import json
import logging
import time
import requests
from typing import Dict, List, Any, Optional, Union, Tuple

from dotenv import load_dotenv

load_dotenv()

class DeepSeekClient:
    """DeepSeek API 客户端"""
    
    def __init__(self, api_key: Optional[str] = None, model: str = "deepseek-chat", logger: Optional[logging.Logger] = None):
        """
        初始化 DeepSeek API 客户端
        
        Args:
            api_key: DeepSeek API 密钥，如果为 None，则从环境变量 DEEPSEEK_API_KEY 中获取
            model: 使用的模型名称
            logger: 日志记录器
        """
        self.api_key = api_key or os.getenv("DEEPSEEK_API_KEY")
        if not self.api_key:
            raise ValueError("DeepSeek API key is required")
        
        self.model = model
        self.base_url = "https://api.deepseek.com/v1"
        self.logger = logger or logging.getLogger(__name__)
        
        # 设置请求头
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
    
    def is_available(self) -> bool:
        """
        检查 DeepSeek API 是否可用
        
        Returns:
            bool: 如果 API 可用，则返回 True，否则返回 False
        """
        try:
            response = requests.get(f"{self.base_url}/models", headers=self.headers, timeout=5)
            return response.status_code == 200
        except Exception as e:
            self.logger.error(f"Failed to check DeepSeek API availability: {e}")
            return False
    
    def chat_completion(self, 
                        messages: List[Dict[str, str]], 
                        temperature: float = 0.7, 
                        max_tokens: int = 2000,
                        stream: bool = False) -> Dict[str, Any]:
        """
        调用 DeepSeek API 进行聊天补全
        
        Args:
            messages: 消息列表，每个消息是一个字典，包含 role 和 content 字段
            temperature: 温度参数，控制输出的随机性
            max_tokens: 生成的最大 token 数
            stream: 是否使用流式响应
            
        Returns:
            Dict[str, Any]: API 响应
        """
        url = f"{self.base_url}/chat/completions"
        
        payload = {
            "model": self.model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens,
            "stream": stream
        }
        
        try:
            self.logger.debug(f"Calling DeepSeek API: {url}")
            response = requests.post(url, headers=self.headers, json=payload, timeout=60)
            
            if response.status_code != 200:
                self.logger.error(f"DeepSeek API error: {response.status_code} {response.text}")
                return {"error": f"API error: {response.status_code}", "text": response.text}
            
            return response.json()
        except Exception as e:
            self.logger.error(f"Failed to call DeepSeek API: {e}")
            return {"error": str(e)}
    
    def extract_content(self, response: Dict[str, Any]) -> str:
        """
        从 API 响应中提取内容
        
        Args:
            response: API 响应
            
        Returns:
            str: 提取的内容
        """
        try:
            if "error" in response:
                return f"Error: {response['error']}"
            
            if "choices" in response and len(response["choices"]) > 0:
                return response["choices"][0]["message"]["content"]
            
            return "No content found in response"
        except Exception as e:
            self.logger.error(f"Failed to extract content from response: {e}")
            return f"Error extracting content: {e}"
