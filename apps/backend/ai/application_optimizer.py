#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
申请材料优化建议器
为用户提供针对事业编制岗位的申请材料优化建议，生成针对性的优化建议
"""

import os
import re
import json
import logging
from typing import Dict, List, Optional, Any, Tuple

from ..utils.cache_manager import CacheManager
from apps.backend.ai import DeepSeekClient


class ApplicationOptimizer:
    """申请材料优化建议器"""

    def __init__(self, logger=None, use_local_components=True, cache_dir=None):
        """
        初始化申请材料优化建议器

        Args:
            logger: 日志记录器
            use_local_components: 是否使用本地组件，默认为True
            cache_dir: 缓存目录，默认为None
        """
        self.logger = logger or logging.getLogger(__name__)
        self.use_local_components = use_local_components

        # 初始化缓存管理器
        self.cache_manager = CacheManager(
            cache_dir=cache_dir or os.path.join(os.path.dirname(__file__), "cache"),
            cache_name="application_optimizer_cache"
        )

        # 初始化DeepSeek客户端
        self.deepseek_client = DeepSeekClient(logger=self.logger)

        # 初始化关键词和模式
        self.resume_sections = [
            '个人信息', '教育背景', '工作经验', '项目经验', '技能特长',
            '获奖情况', '自我评价', '求职意向'
        ]

        # 初始化优化建议模板
        self.optimization_templates = {
            "general": "针对{job_title}岗位，建议您在申请材料中突出以下几点：{points}",
            "education": "您的教育背景{education_match}该岗位要求，建议{education_advice}",
            "experience": "您的工作经验{experience_match}该岗位要求，建议{experience_advice}",
            "skills": "针对该岗位所需技能，建议您{skills_advice}",
            "personal_statement": "在个人陈述中，建议您{statement_advice}"
        }

    def optimize_application(self, job_info: Dict[str, Any], user_profile: Dict[str, Any]) -> Dict[str, Any]:
        """
        优化申请材料

        Args:
            job_info: 岗位信息，包含title, description, requirements等字段
            user_profile: 用户资料，包含education, experience, skills等字段

        Returns:
            优化建议
        """
        # 检查缓存
        cache_key = f"application_{hash(json.dumps(job_info, sort_keys=True))}_{hash(json.dumps(user_profile, sort_keys=True))}"
        cached_result = self.cache_manager.get(cache_key)
        if cached_result:
            self.logger.info("Using cached application optimization result")
            return cached_result

        # 使用本地组件优化
        if self.use_local_components:
            result = self._optimize_application_locally(job_info, user_profile)

            # 如果本地优化结果不完整，尝试使用DeepSeek API
            if not self._is_result_complete(result) and self.deepseek_client.is_available():
                self.logger.info("Local optimization incomplete, trying DeepSeek API")
                api_result = self._optimize_application_with_api(job_info, user_profile)
                # 合并结果，API结果优先
                for key, value in api_result.items():
                    if value and (key not in result or not result[key]):
                        result[key] = value
        else:
            # 直接使用DeepSeek API
            result = self._optimize_application_with_api(job_info, user_profile)

        # 缓存结果
        self.cache_manager.set(cache_key, result)

        return result

    def _optimize_application_locally(self, job_info: Dict[str, Any], user_profile: Dict[str, Any]) -> Dict[str, Any]:
        """
        使用本地组件优化申请材料

        Args:
            job_info: 岗位信息
            user_profile: 用户资料

        Returns:
            优化建议
        """
        # 提取岗位信息
        job_title = job_info.get("title", "")
        job_description = job_info.get("description", "") or job_info.get("job_description", "")
        job_requirements = job_info.get("requirements", "") or job_info.get("job_requirements", "")

        # 提取用户资料
        user_education = user_profile.get("education", "")
        user_major = user_profile.get("major", "")
        user_experience = user_profile.get("experience", "")
        user_skills = user_profile.get("skills", [])

        # 分析教育背景匹配度
        education_analysis = self._analyze_education_match(job_requirements, user_education, user_major)

        # 分析工作经验匹配度
        experience_analysis = self._analyze_experience_match(job_requirements, user_experience)

        # 分析技能匹配度
        skills_analysis = self._analyze_skills_match(job_description + "\n" + job_requirements, user_skills)

        # 生成通用优化建议
        general_suggestions = self._generate_general_suggestions(job_info, user_profile)

        # 生成个人陈述建议
        personal_statement_suggestions = self._generate_personal_statement_suggestions(job_info, user_profile)

        # 生成简历优化建议
        resume_suggestions = self._generate_resume_suggestions(job_info, user_profile)

        # 生成面试准备建议
        interview_suggestions = self._generate_interview_suggestions(job_info, user_profile)

        return {
            "general_suggestions": general_suggestions,
            "education_analysis": education_analysis,
            "experience_analysis": experience_analysis,
            "skills_analysis": skills_analysis,
            "personal_statement_suggestions": personal_statement_suggestions,
            "resume_suggestions": resume_suggestions,
            "interview_suggestions": interview_suggestions
        }

    def _optimize_application_with_api(self, job_info: Dict[str, Any], user_profile: Dict[str, Any]) -> Dict[str, Any]:
        """
        使用DeepSeek API优化申请材料

        Args:
            job_info: 岗位信息
            user_profile: 用户资料

        Returns:
            优化建议
        """
        # 提取岗位信息
        job_title = job_info.get("title", "")
        job_description = job_info.get("description", "") or job_info.get("job_description", "")
        job_requirements = job_info.get("requirements", "") or job_info.get("job_requirements", "")

        # 提取用户资料
        user_education = user_profile.get("education", "")
        user_major = user_profile.get("major", "")
        user_experience = user_profile.get("experience", "")
        user_skills = user_profile.get("skills", [])

        # 构建提示
        prompt = f"""
        请为以下用户提供针对事业编制岗位的申请材料优化建议。

        岗位信息：
        - 岗位名称：{job_title}
        - 岗位描述：{job_description}
        - 岗位要求：{job_requirements}

        用户资料：
        - 教育背景：{user_education}
        - 专业：{user_major}
        - 工作经验：{user_experience}
        - 技能：{', '.join(user_skills) if user_skills else '无'}

        请提供以下方面的优化建议：
        1. 通用建议：针对该岗位的整体申请策略
        2. 教育背景分析：分析用户教育背景与岗位要求的匹配度，并提供优化建议
        3. 工作经验分析：分析用户工作经验与岗位要求的匹配度，并提供优化建议
        4. 技能分析：分析用户技能与岗位要求的匹配度，并提供优化建议
        5. 个人陈述建议：如何撰写有针对性的个人陈述
        6. 简历优化建议：如何优化简历以突出与岗位相关的经历和能力
        7. 面试准备建议：如何准备面试，包括可能的问题和回答策略

        请以JSON格式返回结果，键名分别为：general_suggestions, education_analysis, experience_analysis, skills_analysis, personal_statement_suggestions, resume_suggestions, interview_suggestions。
        """

        try:
            response = self.deepseek_client.chat_completion(prompt)
            # 尝试从响应中提取JSON
            result = self._extract_json_from_response(response)
            return result
        except Exception as e:
            self.logger.error(f"Error using DeepSeek API for application optimization: {e}")
            # 如果API调用失败，返回空结果
            return {
                "general_suggestions": "",
                "education_analysis": {},
                "experience_analysis": {},
                "skills_analysis": {},
                "personal_statement_suggestions": "",
                "resume_suggestions": "",
                "interview_suggestions": ""
            }

    def _extract_json_from_response(self, response) -> Dict[str, Any]:
        """
        从API响应中提取JSON

        Args:
            response: API响应

        Returns:
            解析后的JSON对象
        """
        try:
            # 使用 DeepSeekClient 的 extract_json 方法
            return self.deepseek_client.extract_json(response)
        except Exception as e:
            self.logger.error(f"Error extracting JSON from response: {e}")
            return {
                "general_suggestions": "",
                "education_analysis": {},
                "experience_analysis": {},
                "skills_analysis": {},
                "personal_statement_suggestions": "",
                "resume_suggestions": "",
                "interview_suggestions": ""
            }

    def _is_result_complete(self, result: Dict[str, Any]) -> bool:
        """
        检查优化结果是否完整

        Args:
            result: 优化结果

        Returns:
            结果是否完整
        """
        # 检查关键字段是否有值
        required_fields = ["general_suggestions", "education_analysis", "experience_analysis", "skills_analysis"]
        for field in required_fields:
            if field not in result or not result[field]:
                return False

        return True

    def _analyze_education_match(self, job_requirements: str, user_education: str, user_major: str) -> Dict[str, Any]:
        """
        分析教育背景匹配度

        Args:
            job_requirements: 岗位要求
            user_education: 用户教育背景
            user_major: 用户专业

        Returns:
            教育背景匹配分析
        """
        result = {
            "match_level": "未知",
            "match_description": "",
            "suggestions": []
        }

        # 提取岗位要求的学历
        education_levels = {
            "博士": 5,
            "硕士": 4,
            "研究生": 4,
            "本科": 3,
            "学士": 3,
            "大专": 2,
            "专科": 2,
            "高中": 1,
            "中专": 1
        }

        required_education = None
        required_level = 0

        for level, value in education_levels.items():
            if level in job_requirements:
                if value > required_level:
                    required_education = level
                    required_level = value

        # 提取用户学历
        user_level = 0
        for level, value in education_levels.items():
            if level in user_education:
                if value > user_level:
                    user_level = value

        # 分析匹配度
        if required_level == 0:
            result["match_level"] = "未知"
            result["match_description"] = "岗位未明确要求学历"
            result["suggestions"].append("在申请材料中突出您的教育背景和专业知识")
        elif user_level >= required_level:
            result["match_level"] = "良好"
            result["match_description"] = f"您的学历（{user_education}）满足岗位要求（{required_education}）"
            result["suggestions"].append("在申请材料中突出您的学术成就和专业课程")
        elif user_level == required_level - 1:
            result["match_level"] = "一般"
            result["match_description"] = f"您的学历（{user_education}）略低于岗位要求（{required_education}）"
            result["suggestions"].append("强调您的实际工作能力和相关经验，弥补学历差距")
            result["suggestions"].append("突出您的持续学习和专业发展")
        else:
            result["match_level"] = "较差"
            result["match_description"] = f"您的学历（{user_education}）低于岗位要求（{required_education}）"
            result["suggestions"].append("考虑是否有特殊政策允许较低学历申请（如基层工作经验优惠政策）")
            result["suggestions"].append("突出您的实际工作成果和专业能力")

        # 分析专业匹配度
        if user_major and "专业" in job_requirements:
            # 提取岗位要求的专业
            major_pattern = r'([\u4e00-\u9fa5]+学|[\u4e00-\u9fa5]+工程|[\u4e00-\u9fa5]+专业)'
            major_matches = re.findall(major_pattern, job_requirements)

            if major_matches:
                required_majors = [m.replace("专业", "") for m in major_matches]

                # 检查用户专业是否匹配
                major_match = False
                for required_major in required_majors:
                    if required_major in user_major or user_major in required_major:
                        major_match = True
                        break

                if major_match:
                    result["suggestions"].append(f"强调您的{user_major}专业背景与岗位的直接相关性")
                else:
                    result["suggestions"].append("解释您的专业知识如何迁移到该岗位所需的技能")
                    result["suggestions"].append("突出您在相关领域的额外学习和培训经历")

        return result

    def _analyze_experience_match(self, job_requirements: str, user_experience: str) -> Dict[str, Any]:
        """
        分析工作经验匹配度

        Args:
            job_requirements: 岗位要求
            user_experience: 用户工作经验

        Returns:
            工作经验匹配分析
        """
        result = {
            "match_level": "未知",
            "match_description": "",
            "suggestions": []
        }

        # 提取岗位要求的工作经验
        experience_pattern = r'(\d+)[年-]以上工作经验|工作经验(\d+)[年-]以上|(\d+)-(\d+)年工作经验|工作经验(\d+)-(\d+)年'
        experience_matches = re.findall(experience_pattern, job_requirements)

        required_years = 0
        if experience_matches:
            # 提取数字
            for match in experience_matches:
                for group in match:
                    if group.isdigit():
                        years = int(group)
                        if years > required_years:
                            required_years = years
                        break

        # 提取用户工作经验
        user_years = 0
        if user_experience:
            years_pattern = r'(\d+)[年-]'
            years_matches = re.findall(years_pattern, user_experience)
            if years_matches:
                user_years = int(years_matches[0])

        # 分析匹配度
        if required_years == 0:
            result["match_level"] = "未知"
            result["match_description"] = "岗位未明确要求工作经验"
            result["suggestions"].append("在申请材料中突出您的相关工作经历和成就")
        elif user_years >= required_years:
            result["match_level"] = "良好"
            result["match_description"] = f"您的工作经验（{user_years}年）满足岗位要求（{required_years}年）"
            result["suggestions"].append("详细描述您的工作成就和解决问题的能力")
            result["suggestions"].append("强调您的领导经验和团队合作能力")
        elif user_years >= required_years * 0.7:
            result["match_level"] = "一般"
            result["match_description"] = f"您的工作经验（{user_years}年）接近岗位要求（{required_years}年）"
            result["suggestions"].append("强调您的快速学习能力和适应能力")
            result["suggestions"].append("突出您在短时间内取得的成就和进步")
        else:
            result["match_level"] = "较差"
            result["match_description"] = f"您的工作经验（{user_years}年）低于岗位要求（{required_years}年）"
            result["suggestions"].append("强调您的实习经历、项目经验和志愿服务")
            result["suggestions"].append("突出您的学习能力和成长潜力")

        return result

    def _analyze_skills_match(self, job_text: str, user_skills: List[str]) -> Dict[str, Any]:
        """
        分析技能匹配度

        Args:
            job_text: 岗位文本
            user_skills: 用户技能列表

        Returns:
            技能匹配分析
        """
        result = {
            "match_level": "未知",
            "matched_skills": [],
            "missing_skills": [],
            "suggestions": []
        }

        if not user_skills:
            result["match_level"] = "未知"
            result["suggestions"].append("在申请材料中详细列出您的技能和专长")
            return result

        # 提取岗位要求的技能
        common_skills = [
            "沟通", "团队合作", "领导", "解决问题", "分析", "创新", "组织", "规划",
            "管理", "协调", "谈判", "演讲", "写作", "研究", "教学", "培训",
            "计算机", "办公软件", "Word", "Excel", "PowerPoint", "数据分析",
            "项目管理", "预算", "财务", "人力资源", "市场营销", "公共关系",
            "法律", "政策", "行政", "服务", "咨询", "策划", "设计", "开发"
        ]

        required_skills = []
        for skill in common_skills:
            if skill in job_text:
                required_skills.append(skill)

        # 分析匹配度
        matched_skills = []
        for skill in user_skills:
            if skill in required_skills or any(skill in req_skill or req_skill in skill for req_skill in required_skills):
                matched_skills.append(skill)

        missing_skills = [skill for skill in required_skills if skill not in matched_skills and not any(skill in user_skill for user_skill in user_skills)]

        # 计算匹配度
        if not required_skills:
            result["match_level"] = "未知"
        elif len(matched_skills) >= len(required_skills) * 0.8:
            result["match_level"] = "良好"
        elif len(matched_skills) >= len(required_skills) * 0.5:
            result["match_level"] = "一般"
        else:
            result["match_level"] = "较差"

        # 记录匹配的技能和缺失的技能
        result["matched_skills"] = matched_skills
        result["missing_skills"] = missing_skills

        # 生成建议
        if matched_skills:
            result["suggestions"].append(f"突出您掌握的关键技能：{', '.join(matched_skills)}")

        if missing_skills:
            result["suggestions"].append(f"考虑学习或强调与这些技能相关的经验：{', '.join(missing_skills)}")

        result["suggestions"].append("使用具体例子和成就来证明您的技能水平")

        return result

    def _generate_general_suggestions(self, job_info: Dict[str, Any], user_profile: Dict[str, Any]) -> str:
        """
        生成通用优化建议

        Args:
            job_info: 岗位信息
            user_profile: 用户资料

        Returns:
            通用优化建议
        """
        job_title = job_info.get("title", "")

        suggestions = [
            f"针对{job_title}岗位，建议您在申请材料中突出以下几点：",
            "1. 将您的经历和能力与岗位要求一一对应，突出匹配点",
            "2. 使用具体的数据和成就来支持您的能力陈述",
            "3. 调整简历结构，将最相关的经历和能力放在前面",
            "4. 使用事业单位招聘常用的术语和表达方式",
            "5. 确保申请材料格式规范，无拼写和语法错误"
        ]

        return "\n".join(suggestions)

    def _generate_personal_statement_suggestions(self, job_info: Dict[str, Any], user_profile: Dict[str, Any]) -> str:
        """
        生成个人陈述建议

        Args:
            job_info: 岗位信息
            user_profile: 用户资料

        Returns:
            个人陈述建议
        """
        job_title = job_info.get("title", "")

        suggestions = [
            f"在撰写针对{job_title}岗位的个人陈述时，建议您：",
            "1. 开门见山地表达您对该岗位的兴趣和热情",
            "2. 简要介绍您的教育背景和专业经历，突出与岗位相关的部分",
            "3. 详细说明您的能力和成就如何与岗位要求匹配",
            "4. 解释您为什么选择事业单位工作，体现您的公共服务精神",
            "5. 表达您对未来工作的期望和承诺",
            "6. 保持真实、诚恳的语气，避免过度自夸或使用空洞的形容词"
        ]

        return "\n".join(suggestions)

    def _generate_resume_suggestions(self, job_info: Dict[str, Any], user_profile: Dict[str, Any]) -> str:
        """
        生成简历优化建议

        Args:
            job_info: 岗位信息
            user_profile: 用户资料

        Returns:
            简历优化建议
        """
        job_title = job_info.get("title", "")

        suggestions = [
            f"针对{job_title}岗位优化您的简历：",
            "1. 在简历顶部添加一个简短的个人概述，突出您的核心优势",
            "2. 调整教育经历部分，强调与岗位相关的课程和学术成就",
            "3. 在工作经历部分使用STAR法则（情境、任务、行动、结果）描述您的成就",
            "4. 添加专门的技能部分，列出与岗位相关的硬技能和软技能",
            "5. 如有相关证书或资格，创建单独的部分列出",
            "6. 如果有志愿服务或社区工作经历，突出您的公共服务精神",
            "7. 确保简历格式一致，使用清晰的标题和项目符号，便于阅读"
        ]

        return "\n".join(suggestions)

    def _generate_interview_suggestions(self, job_info: Dict[str, Any], user_profile: Dict[str, Any]) -> str:
        """
        生成面试准备建议

        Args:
            job_info: 岗位信息
            user_profile: 用户资料

        Returns:
            面试准备建议
        """
        job_title = job_info.get("title", "")

        suggestions = [
            f"准备{job_title}岗位的面试：",
            "1. 深入研究招聘单位的背景、使命和价值观",
            "2. 准备具体例子来说明您如何满足岗位的每项要求",
            "3. 练习常见的事业单位面试问题，如：",
            "   - 为什么选择事业单位工作？",
            "   - 您如何理解公共服务精神？",
            "   - 您的专业背景如何帮助您胜任这个岗位？",
            "   - 请描述您解决过的一个复杂问题",
            "4. 准备关于您简历中每段经历的详细说明",
            "5. 思考您可能面临的挑战问题，准备诚恳的回答",
            "6. 准备几个有见地的问题来问面试官",
            "7. 面试当天着装专业，提前到达，带齐所有必要文件"
        ]

        return "\n".join(suggestions)
