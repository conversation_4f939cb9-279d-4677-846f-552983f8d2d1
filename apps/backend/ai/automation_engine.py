#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
自动化流程引擎
"""

import time
import json
import asyncio
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from enum import Enum
from abc import ABC, abstractmethod

from apps.backend.utils.logger import setup_logger
from apps.backend.ai.intelligent_customer_service import intelligent_customer_service
from apps.backend.ai.ticket_management import ticket_manager, TicketStatus, TicketPriority

logger = setup_logger(__name__)


class TriggerType(Enum):
    """触发器类型"""
    TIME_BASED = "time_based"
    EVENT_BASED = "event_based"
    CONDITION_BASED = "condition_based"
    MANUAL = "manual"


class ActionType(Enum):
    """动作类型"""
    SEND_MESSAGE = "send_message"
    CREATE_TICKET = "create_ticket"
    UPDATE_TICKET = "update_ticket"
    ASSIGN_TICKET = "assign_ticket"
    ESCALATE_TICKET = "escalate_ticket"
    SEND_EMAIL = "send_email"
    SEND_SMS = "send_sms"
    CALL_API = "call_api"
    EXECUTE_SCRIPT = "execute_script"


class WorkflowStatus(Enum):
    """工作流状态"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    PAUSED = "paused"
    ERROR = "error"


@dataclass
class TriggerCondition:
    """触发条件"""
    trigger_type: TriggerType
    conditions: Dict[str, Any]
    schedule: Optional[str] = None  # cron表达式
    
    def check_condition(self, context: Dict[str, Any]) -> bool:
        """检查触发条件是否满足"""
        try:
            if self.trigger_type == TriggerType.TIME_BASED:
                return self._check_time_condition(context)
            elif self.trigger_type == TriggerType.EVENT_BASED:
                return self._check_event_condition(context)
            elif self.trigger_type == TriggerType.CONDITION_BASED:
                return self._check_condition_based(context)
            else:
                return False
        except Exception as e:
            logger.error(f"Error checking trigger condition: {e}")
            return False
    
    def _check_time_condition(self, context: Dict[str, Any]) -> bool:
        """检查时间条件"""
        # 简化的时间检查逻辑
        if "current_time" in context:
            current_time = context["current_time"]
            target_time = self.conditions.get("target_time")
            if target_time:
                return current_time >= target_time
        return False
    
    def _check_event_condition(self, context: Dict[str, Any]) -> bool:
        """检查事件条件"""
        event_type = self.conditions.get("event_type")
        if event_type and "event" in context:
            return context["event"].get("type") == event_type
        return False
    
    def _check_condition_based(self, context: Dict[str, Any]) -> bool:
        """检查基于条件的触发"""
        for key, expected_value in self.conditions.items():
            if key not in context or context[key] != expected_value:
                return False
        return True


@dataclass
class WorkflowAction:
    """工作流动作"""
    action_type: ActionType
    parameters: Dict[str, Any]
    delay_seconds: int = 0
    retry_count: int = 0
    
    async def execute(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """执行动作"""
        try:
            if self.delay_seconds > 0:
                await asyncio.sleep(self.delay_seconds)
            
            if self.action_type == ActionType.SEND_MESSAGE:
                return await self._send_message(context)
            elif self.action_type == ActionType.CREATE_TICKET:
                return await self._create_ticket(context)
            elif self.action_type == ActionType.UPDATE_TICKET:
                return await self._update_ticket(context)
            elif self.action_type == ActionType.ASSIGN_TICKET:
                return await self._assign_ticket(context)
            elif self.action_type == ActionType.ESCALATE_TICKET:
                return await self._escalate_ticket(context)
            elif self.action_type == ActionType.SEND_EMAIL:
                return await self._send_email(context)
            elif self.action_type == ActionType.CALL_API:
                return await self._call_api(context)
            else:
                return {"success": False, "error": f"Unsupported action type: {self.action_type}"}
                
        except Exception as e:
            logger.error(f"Error executing action {self.action_type}: {e}")
            return {"success": False, "error": str(e)}
    
    async def _send_message(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """发送消息"""
        user_id = self.parameters.get("user_id") or context.get("user_id")
        message = self.parameters.get("message", "")
        
        if user_id and message:
            # 这里应该调用实际的消息发送服务
            logger.info(f"Sending message to user {user_id}: {message}")
            return {"success": True, "message_sent": True}
        
        return {"success": False, "error": "Missing user_id or message"}
    
    async def _create_ticket(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """创建工单"""
        user_id = self.parameters.get("user_id") or context.get("user_id")
        title = self.parameters.get("title", "自动创建的工单")
        description = self.parameters.get("description", "")
        
        if user_id:
            ticket = ticket_manager.create_ticket(user_id, title, description)
            return {"success": True, "ticket_id": ticket.ticket_id}
        
        return {"success": False, "error": "Missing user_id"}
    
    async def _update_ticket(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """更新工单"""
        ticket_id = self.parameters.get("ticket_id") or context.get("ticket_id")
        status = self.parameters.get("status")
        comment = self.parameters.get("comment")
        
        if ticket_id and status:
            try:
                status_enum = TicketStatus(status)
                success = ticket_manager.update_ticket_status(ticket_id, status_enum, comment=comment)
                return {"success": success}
            except ValueError:
                return {"success": False, "error": f"Invalid status: {status}"}
        
        return {"success": False, "error": "Missing ticket_id or status"}
    
    async def _assign_ticket(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """分配工单"""
        ticket_id = self.parameters.get("ticket_id") or context.get("ticket_id")
        agent_id = self.parameters.get("agent_id")
        
        if ticket_id and agent_id:
            success = ticket_manager.assign_ticket(ticket_id, agent_id)
            return {"success": success}
        
        return {"success": False, "error": "Missing ticket_id or agent_id"}
    
    async def _escalate_ticket(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """升级工单"""
        ticket_id = self.parameters.get("ticket_id") or context.get("ticket_id")
        reason = self.parameters.get("reason", "自动升级")
        
        if ticket_id:
            success = ticket_manager.escalate_ticket(ticket_id, reason)
            return {"success": success}
        
        return {"success": False, "error": "Missing ticket_id"}
    
    async def _send_email(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """发送邮件"""
        to_email = self.parameters.get("to_email") or context.get("email")
        subject = self.parameters.get("subject", "")
        content = self.parameters.get("content", "")
        
        if to_email and subject and content:
            # 这里应该调用实际的邮件发送服务
            logger.info(f"Sending email to {to_email}: {subject}")
            return {"success": True, "email_sent": True}
        
        return {"success": False, "error": "Missing email parameters"}
    
    async def _call_api(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """调用API"""
        url = self.parameters.get("url")
        method = self.parameters.get("method", "GET")
        headers = self.parameters.get("headers", {})
        data = self.parameters.get("data", {})
        
        if url:
            # 这里应该调用实际的HTTP客户端
            logger.info(f"Calling API: {method} {url}")
            return {"success": True, "api_called": True}
        
        return {"success": False, "error": "Missing API URL"}


@dataclass
class Workflow:
    """工作流"""
    workflow_id: str
    name: str
    description: str
    trigger: TriggerCondition
    actions: List[WorkflowAction]
    status: WorkflowStatus = WorkflowStatus.ACTIVE
    created_at: datetime = None
    updated_at: datetime = None
    last_executed_at: Optional[datetime] = None
    execution_count: int = 0
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()


class AutomationEngine:
    """自动化引擎"""
    
    def __init__(self):
        self.logger = logger
        self.workflows: Dict[str, Workflow] = {}
        self.running = False
        self.check_interval = 60  # 检查间隔（秒）
        
        # 预定义工作流
        self._initialize_default_workflows()
    
    def _initialize_default_workflows(self):
        """初始化默认工作流"""
        # 1. 工单超时自动升级
        ticket_escalation_workflow = Workflow(
            workflow_id="ticket_auto_escalation",
            name="工单超时自动升级",
            description="当工单超过24小时未响应时自动升级",
            trigger=TriggerCondition(
                trigger_type=TriggerType.CONDITION_BASED,
                conditions={"ticket_overdue": True}
            ),
            actions=[
                WorkflowAction(
                    action_type=ActionType.ESCALATE_TICKET,
                    parameters={"reason": "超时自动升级"}
                ),
                WorkflowAction(
                    action_type=ActionType.SEND_MESSAGE,
                    parameters={"message": "您的工单已升级处理，我们会尽快为您解决问题。"}
                )
            ]
        )
        self.workflows[ticket_escalation_workflow.workflow_id] = ticket_escalation_workflow
        
        # 2. 新用户欢迎流程
        welcome_workflow = Workflow(
            workflow_id="new_user_welcome",
            name="新用户欢迎流程",
            description="新用户注册后发送欢迎消息和使用指南",
            trigger=TriggerCondition(
                trigger_type=TriggerType.EVENT_BASED,
                conditions={"event_type": "user_registered"}
            ),
            actions=[
                WorkflowAction(
                    action_type=ActionType.SEND_MESSAGE,
                    parameters={"message": "欢迎使用招聘平台！我是您的专属客服助手。"},
                    delay_seconds=5
                ),
                WorkflowAction(
                    action_type=ActionType.SEND_MESSAGE,
                    parameters={"message": "您可以通过我查找工作、了解申请状态、获取政策信息等。有任何问题随时联系我！"},
                    delay_seconds=10
                )
            ]
        )
        self.workflows[welcome_workflow.workflow_id] = welcome_workflow
        
        # 3. 工单满意度调查
        satisfaction_survey_workflow = Workflow(
            workflow_id="satisfaction_survey",
            name="工单满意度调查",
            description="工单解决后发送满意度调查",
            trigger=TriggerCondition(
                trigger_type=TriggerType.EVENT_BASED,
                conditions={"event_type": "ticket_resolved"}
            ),
            actions=[
                WorkflowAction(
                    action_type=ActionType.SEND_MESSAGE,
                    parameters={"message": "您的问题已解决，请为我们的服务打分（1-5分）："},
                    delay_seconds=300  # 5分钟后发送
                )
            ]
        )
        self.workflows[satisfaction_survey_workflow.workflow_id] = satisfaction_survey_workflow
        
        # 4. 高优先级工单通知
        high_priority_notification_workflow = Workflow(
            workflow_id="high_priority_notification",
            name="高优先级工单通知",
            description="创建高优先级工单时立即通知相关人员",
            trigger=TriggerCondition(
                trigger_type=TriggerType.EVENT_BASED,
                conditions={"event_type": "high_priority_ticket_created"}
            ),
            actions=[
                WorkflowAction(
                    action_type=ActionType.SEND_EMAIL,
                    parameters={
                        "to_email": "<EMAIL>",
                        "subject": "高优先级工单创建通知",
                        "content": "有新的高优先级工单需要处理"
                    }
                ),
                WorkflowAction(
                    action_type=ActionType.ASSIGN_TICKET,
                    parameters={"agent_id": "senior_agent_001"}
                )
            ]
        )
        self.workflows[high_priority_notification_workflow.workflow_id] = high_priority_notification_workflow
    
    def add_workflow(self, workflow: Workflow) -> bool:
        """添加工作流"""
        try:
            self.workflows[workflow.workflow_id] = workflow
            self.logger.info(f"Workflow added: {workflow.workflow_id}")
            return True
        except Exception as e:
            self.logger.error(f"Error adding workflow: {e}")
            return False
    
    def remove_workflow(self, workflow_id: str) -> bool:
        """移除工作流"""
        try:
            if workflow_id in self.workflows:
                del self.workflows[workflow_id]
                self.logger.info(f"Workflow removed: {workflow_id}")
                return True
            return False
        except Exception as e:
            self.logger.error(f"Error removing workflow: {e}")
            return False
    
    def update_workflow_status(self, workflow_id: str, status: WorkflowStatus) -> bool:
        """更新工作流状态"""
        try:
            if workflow_id in self.workflows:
                self.workflows[workflow_id].status = status
                self.workflows[workflow_id].updated_at = datetime.now()
                self.logger.info(f"Workflow {workflow_id} status updated to {status.value}")
                return True
            return False
        except Exception as e:
            self.logger.error(f"Error updating workflow status: {e}")
            return False
    
    async def trigger_workflow(self, workflow_id: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """手动触发工作流"""
        try:
            if workflow_id not in self.workflows:
                return {"success": False, "error": "Workflow not found"}
            
            workflow = self.workflows[workflow_id]
            if workflow.status != WorkflowStatus.ACTIVE:
                return {"success": False, "error": "Workflow is not active"}
            
            return await self._execute_workflow(workflow, context)
            
        except Exception as e:
            self.logger.error(f"Error triggering workflow: {e}")
            return {"success": False, "error": str(e)}
    
    async def process_event(self, event: Dict[str, Any]) -> List[Dict[str, Any]]:
        """处理事件，触发相关工作流"""
        results = []
        
        try:
            context = {"event": event, "timestamp": datetime.now()}
            
            for workflow in self.workflows.values():
                if workflow.status == WorkflowStatus.ACTIVE:
                    if workflow.trigger.check_condition(context):
                        result = await self._execute_workflow(workflow, context)
                        results.append({
                            "workflow_id": workflow.workflow_id,
                            "result": result
                        })
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error processing event: {e}")
            return []
    
    async def _execute_workflow(self, workflow: Workflow, context: Dict[str, Any]) -> Dict[str, Any]:
        """执行工作流"""
        try:
            self.logger.info(f"Executing workflow: {workflow.workflow_id}")
            
            workflow.execution_count += 1
            workflow.last_executed_at = datetime.now()
            
            action_results = []
            
            for action in workflow.actions:
                result = await action.execute(context)
                action_results.append(result)
                
                # 如果动作失败且没有重试，停止执行
                if not result.get("success", False) and action.retry_count == 0:
                    break
                
                # 更新上下文
                context.update(result)
            
            success = all(result.get("success", False) for result in action_results)
            
            return {
                "success": success,
                "workflow_id": workflow.workflow_id,
                "execution_count": workflow.execution_count,
                "action_results": action_results
            }
            
        except Exception as e:
            self.logger.error(f"Error executing workflow {workflow.workflow_id}: {e}")
            workflow.status = WorkflowStatus.ERROR
            return {"success": False, "error": str(e)}
    
    async def start_engine(self):
        """启动自动化引擎"""
        self.running = True
        self.logger.info("Automation engine started")
        
        while self.running:
            try:
                await self._check_time_based_workflows()
                await self._check_condition_based_workflows()
                await asyncio.sleep(self.check_interval)
            except Exception as e:
                self.logger.error(f"Error in automation engine loop: {e}")
                await asyncio.sleep(self.check_interval)
    
    def stop_engine(self):
        """停止自动化引擎"""
        self.running = False
        self.logger.info("Automation engine stopped")
    
    async def _check_time_based_workflows(self):
        """检查基于时间的工作流"""
        current_time = datetime.now()
        context = {"current_time": current_time}
        
        for workflow in self.workflows.values():
            if (workflow.status == WorkflowStatus.ACTIVE and 
                workflow.trigger.trigger_type == TriggerType.TIME_BASED):
                
                if workflow.trigger.check_condition(context):
                    await self._execute_workflow(workflow, context)
    
    async def _check_condition_based_workflows(self):
        """检查基于条件的工作流"""
        # 检查工单超时
        await self._check_overdue_tickets()
    
    async def _check_overdue_tickets(self):
        """检查超时工单"""
        current_time = datetime.now()
        
        for ticket in ticket_manager.tickets.values():
            if ticket.status in [TicketStatus.OPEN, TicketStatus.IN_PROGRESS]:
                # 检查是否超时
                hours_since_created = (current_time - ticket.created_at).total_seconds() / 3600
                
                if hours_since_created > 24:  # 24小时超时
                    context = {
                        "ticket_overdue": True,
                        "ticket_id": ticket.ticket_id,
                        "user_id": ticket.user_id
                    }
                    
                    # 触发超时工作流
                    for workflow in self.workflows.values():
                        if (workflow.workflow_id == "ticket_auto_escalation" and 
                            workflow.status == WorkflowStatus.ACTIVE):
                            await self._execute_workflow(workflow, context)
    
    def get_workflow_statistics(self) -> Dict[str, Any]:
        """获取工作流统计"""
        try:
            total_workflows = len(self.workflows)
            active_workflows = len([w for w in self.workflows.values() if w.status == WorkflowStatus.ACTIVE])
            
            status_counts = {}
            for status in WorkflowStatus:
                status_counts[status.value] = len([w for w in self.workflows.values() if w.status == status])
            
            total_executions = sum(w.execution_count for w in self.workflows.values())
            
            return {
                "total_workflows": total_workflows,
                "active_workflows": active_workflows,
                "status_distribution": status_counts,
                "total_executions": total_executions,
                "engine_running": self.running,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error getting workflow statistics: {e}")
            return {}


# 全局自动化引擎实例
automation_engine = AutomationEngine()
