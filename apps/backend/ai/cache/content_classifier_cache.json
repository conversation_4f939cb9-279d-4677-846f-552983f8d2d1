{"33bba2b8c4f651252970b3c05c6370c2": {"timestamp": 1747626695.94207, "value": {"industry": "IT互联网", "function": "技术研发", "level": "中级", "job_type": "社招", "education": "本科", "tags": ["Python", "SQL", "Linux", "工程师"]}}, "3b2d0238d53fb418a938ebb8c94d0cc2": {"timestamp": 1747626695.946337, "value": {"industry": "教育", "function": "教学科研", "level": "其他级别", "job_type": "社招", "education": "硕士", "tags": ["教师资格", "资格证"]}}, "22c0c711c4cb6fe7da4a91c29661c32a": {"timestamp": 1747626695.947982, "value": {"industry": "医疗卫生", "function": "医疗护理", "level": "其他级别", "job_type": "社招", "education": "本科", "tags": ["沟通", "医师", "执业证"]}}, "5c3780b1b3bc77bdcdc0fe281e555738": {"timestamp": 1747626695.954426, "value": {"industry": "金融", "function": "市场销售", "level": "管理层", "job_type": "社招", "education": "本科", "tags": ["数据分析", "分析能力"]}}, "c4c6ed18ce6db6819cfca8562d9c0645": {"timestamp": 1747626695.969461, "value": {"industry": "政府机构", "function": "行政后勤", "level": "管理层", "job_type": "社招", "education": "大专", "tags": ["沟通", "团队合作"]}}, "218aff85f88c90759adb43abd5d5cbc1": {"timestamp": 1747626695.971621, "value": {"industry": "咨询服务", "function": "产品运营", "level": "初级/实习", "job_type": "实习", "education": "本科", "tags": ["沟通", "团队合作", "学习能力"]}}, "ae2408a7224476f97df011df9e82f348": {"timestamp": 1747626719.838441, "value": {"industry": "IT互联网", "function": "技术研发", "level": "中级", "job_type": "社招", "education": "本科", "tags": ["Python", "SQL", "Linux", "工程师"]}}, "eabea38248df54df959e566eecb6daa4": {"timestamp": 1747626719.8438, "value": {"industry": "教育", "function": "教学科研", "level": "其他级别", "job_type": "社招", "education": "硕士", "tags": ["教师资格", "资格证"]}}, "7affdd721825893e582069678d5e74ec": {"timestamp": 1747626719.8476539, "value": {"industry": "医疗卫生", "function": "医疗护理", "level": "其他级别", "job_type": "社招", "education": "本科", "tags": ["沟通", "医师", "执业证"]}}, "74bc73582f4b4cb5de075aaf296296cd": {"timestamp": 1747626720.2091851, "value": {"industry": "金融", "function": "市场销售", "level": "管理层", "job_type": "社招", "education": "本科", "tags": ["数据分析", "分析能力"]}}, "a69c7b6055c7984c994783bc284028fa": {"timestamp": 1747626720.217099, "value": {"industry": "政府机构", "function": "行政后勤", "level": "管理层", "job_type": "社招", "education": "大专", "tags": ["沟通", "团队合作"]}}, "ad8fb15ee1f7d57adcc866d814de07f3": {"timestamp": 1747626720.220834, "value": {"industry": "咨询服务", "function": "产品运营", "level": "初级/实习", "job_type": "实习", "education": "本科", "tags": ["沟通", "团队合作", "学习能力"]}}, "3939428dff245f20ee539e77bf3f550f": {"timestamp": 1747626735.0854568, "value": {"industry": "IT互联网", "function": "技术研发", "level": "中级", "job_type": "社招", "education": "本科", "tags": ["Python", "SQL", "Linux", "工程师"], "source": "local_classifier"}}, "3a556c732e41911969167a64f10fea82": {"timestamp": 1747626735.087797, "value": {"industry": "教育", "function": "教学科研", "level": "其他级别", "job_type": "社招", "education": "硕士", "tags": ["教师资格", "资格证"], "source": "local_classifier"}}, "ed0ae4b606efca6d75e15c90d4af0744": {"timestamp": 1747626735.0994492, "value": {"industry": "医疗卫生", "function": "医疗护理", "level": "其他级别", "job_type": "社招", "education": "本科", "tags": ["沟通", "医师", "执业证"]}}, "eae2e93c01b3a30f25f27a33b2fe81f8": {"timestamp": 1747626776.355305, "value": {"industry": "IT互联网", "function": "技术研发", "level": "中级", "job_type": "社招", "education": "本科", "tags": ["Python", "SQL", "Linux", "工程师"], "source": "local_classifier"}}, "51ca4ceb0c015d4f05ced12fd0e375a9": {"timestamp": 1747626776.356716, "value": {"industry": "教育", "function": "教学科研", "level": "其他级别", "job_type": "社招", "education": "硕士", "tags": ["教师资格", "资格证"], "source": "local_classifier"}}, "95c7c50498673b26f8604f522480a514": {"timestamp": 1747626776.3667479, "value": {"industry": "医疗卫生", "function": "医疗护理", "level": "其他级别", "job_type": "社招", "education": "本科", "tags": ["沟通", "医师", "执业证"]}}, "866c4e1f967d078ddbf57c3dbd921f2a": {"timestamp": 1747626802.72307, "value": {"industry": "IT互联网", "function": "技术研发", "level": "中级", "job_type": "社招", "education": "本科", "tags": ["Python", "SQL", "Linux", "工程师"]}}, "a64f836378cc1a150f8bd5b3dba27ab9": {"timestamp": 1747626802.7252321, "value": {"industry": "政府机构", "function": "行政后勤", "level": "管理层", "job_type": "社招", "education": "大专", "tags": ["沟通", "团队合作"]}}, "87d7edf252405c56068916949c6af8de": {"timestamp": 1747626802.728628, "value": {"industry": "咨询服务", "function": "产品运营", "level": "初级/实习", "job_type": "实习", "education": "本科", "tags": ["沟通", "团队合作", "学习能力"]}}, "f3be6d36f16e5e3aaea4781e45dff3ca": {"timestamp": 1747626897.984046, "value": {"industry": "IT互联网", "function": "技术研发", "level": "中级", "job_type": "社招", "education": "本科", "tags": ["Python", "SQL", "Linux", "工程师"]}}, "a6d74d77b9f52a79139e7d935078ddb7": {"timestamp": 1747626897.986092, "value": {"industry": "政府机构", "function": "行政后勤", "level": "管理层", "job_type": "社招", "education": "大专", "tags": ["沟通", "团队合作"]}}, "3985dad4b15cd527fbd067ca962057f2": {"timestamp": 1747626897.990892, "value": {"industry": "咨询服务", "function": "产品运营", "level": "初级/实习", "job_type": "实习", "education": "本科", "tags": ["沟通", "团队合作", "学习能力"]}}, "943e86a00ff2bd5ad6b9c0d30a5b7261": {"timestamp": 1747626925.869396, "value": {"industry": "IT互联网", "function": "技术研发", "level": "中级", "job_type": "社招", "education": "本科", "tags": ["Python", "SQL", "Linux", "工程师"]}}, "d9ea3fa4dda91ac653a15208a8042310": {"timestamp": 1747626925.871118, "value": {"industry": "政府机构", "function": "行政后勤", "level": "管理层", "job_type": "社招", "education": "大专", "tags": ["沟通", "团队合作"]}}, "60302b424a53832392d1fb1d64d8c164": {"timestamp": 1747626925.874563, "value": {"industry": "咨询服务", "function": "产品运营", "level": "初级/实习", "job_type": "实习", "education": "本科", "tags": ["沟通", "团队合作", "学习能力"]}}}