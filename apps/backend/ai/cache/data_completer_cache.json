{"d31f826476c54282aab715b692487516": {"timestamp": 1747629915.392124, "value": {"job_title": "高级Java开发工程师", "company_name": "北京科技有限公司", "job_description": "负责公司核心业务系统的设计、开发和维护。", "requirements": "1. 本科及以上学历，计算机相关专业；\n2. 5年以上Java开发经验；\n3. 熟悉Spring、MyBatis等框架；\n4. 具有良好的沟通能力和团队合作精神。", "location": "北京", "education_required": "本科及以上学历", "experience_required": "5年以上Java开发经验", "responsibilities": "1. 负责产品功能模块的设计和开发\n2. 编写高质量、可维护的代码\n3. 解决开发过程中的技术问题\n4. 参与产品需求分析和技术方案讨论\n5. 编写技术文档，参与代码审查\n", "salary": "21K-28K/月", "major_required": "1. 本科及以上学历，计算机相关专业；", "job_type": "全职", "department": "技术部", "num_of_positions": "若干", "posting_date": "2025-05-19", "deadline": "2025-06-18", "benefits": "五险一金、带薪年假、节日福利、定期体检、团队建设活动", "skills_required": "熟悉常用开发工具和框架，具有良好的编码习惯和问题解决能力", "contact_info": "<EMAIL> 或 010-12345678", "language_required": "普通话流利，英语四级及以上优先", "certification_required": "无特定证书要求，有Oracle Java认证者优先", "reporting_to": "技术部经理", "travel_requirement": "偶尔需要国内出差，频率低于10%", "working_hours": "09:00-18:00（午休12:00-13:30），双休", "probation_period": "3个月", "career_path": "技术专家路线：高级开发工程师→技术经理→技术总监；管理路线：项目经理→部门负责人", "company_culture": "创新、协作、开放、平等，鼓励技术分享和持续学习", "company_description": "北京科技有限公司成立于2010年，是一家专注于企业级软件解决方案的高新技术企业，为金融、电商等行业提供核心技术支持。", "application_process": "1. 官网投递 2. 简历筛选 3. 初试 4. 复试 5. Offer发放", "interview_process": "初试（技术笔试+HR面）→ 复试（技术深度面试+项目负责人面）→ 终面（CTO面）", "completion_source": "local_rules_and_api"}}, "9c328d9a4c03e941ec0e59328f5786e5": {"timestamp": 1747629915.3967319, "value": {"job_title": "软件工程师", "company_name": "北京科技有限公司", "job_description": "北京科技有限公司现招聘软件工程师岗位。", "requirements": "1. 学历要求：本科及以上学历\n2. 专业要求：计算机、软件工程、电子信息等相关专业\n3. 经验要求：1年以上相关工作经验\n5. 具有良好的沟通能力和团队合作精神\n6. 有责任心，工作认真细致，有较强的学习能力\n", "location": "北京", "education_required": "本科及以上学历", "experience_required": "1年以上相关工作经验", "job_type": "全职", "department": "技术部", "completion_source": "local_rules_and_api"}}, "fd896a10221e9889a9bcd83d952bab2c": {"timestamp": 1747629922.6298938, "value": {"job_title": "高级Java开发工程师", "company_name": "北京科技有限公司", "job_description": "负责公司核心业务系统的设计、开发和维护。", "location": "北京", "requirements": "1. 学历要求：本科及以上学历\n2. 专业要求：计算机、软件工程、电子信息等相关专业\n3. 经验要求：5年以上相关工作经验\n5. 具有良好的沟通能力和团队合作精神\n6. 有责任心，工作认真细致，有较强的学习能力\n", "responsibilities": "1. 负责产品功能模块的设计和开发\n2. 编写高质量、可维护的代码\n3. 解决开发过程中的技术问题\n4. 参与产品需求分析和技术方案讨论\n5. 编写技术文档，参与代码审查\n", "salary": "16K-23K/月", "education_required": "本科及以上学历", "experience_required": "5年以上相关工作经验", "job_type": "全职", "department": "技术部", "num_of_positions": "若干", "posting_date": "2025-05-19", "deadline": "2025-06-18", "benefits": "五险一金、带薪年假、节日福利、定期体检、团队建设活动", "contact_info": "<EMAIL>", "language_required": "英语四级及以上", "certification_required": "无", "completion_source": "local_rules_and_api"}}, "9d50a48c27344bb9281c8f95bf631d97": {"timestamp": 1747629922.6354828, "value": {"job_title": "高级Java开发工程师", "company_name": "北京科技有限公司", "job_description": "负责公司核心业务系统的设计、开发和维护。", "location": "北京", "responsibilities": "1. 负责产品功能模块的设计和开发\n2. 编写高质量、可维护的代码\n3. 解决开发过程中的技术问题\n4. 参与产品需求分析和技术方案讨论\n5. 编写技术文档，参与代码审查\n", "salary": "16K-23K/月", "job_type": "全职", "department": "技术部", "completion_source": "local_rules_and_api"}}, "946b27750f369f7e400368dab9f2d44e": {"timestamp": 1747629993.1645749, "value": {"job_title": "高级Java开发工程师", "company_name": "北京科技有限公司", "job_description": "负责公司核心业务系统的设计、开发和维护。", "requirements": "1. 本科及以上学历，计算机相关专业；\n2. 5年以上Java开发经验；\n3. 熟悉Spring、MyBatis等框架；\n4. 具有良好的沟通能力和团队合作精神。", "location": "北京", "education_required": "本科及以上学历", "experience_required": "5年以上Java开发经验", "responsibilities": "1. 负责产品功能模块的设计和开发\n2. 编写高质量、可维护的代码\n3. 解决开发过程中的技术问题\n4. 参与产品需求分析和技术方案讨论\n5. 编写技术文档，参与代码审查\n", "salary": "21K-28K/月", "major_required": "1. 本科及以上学历，计算机相关专业；", "job_type": "全职", "department": "技术部", "num_of_positions": "若干", "posting_date": "2025-05-19", "deadline": "2025-06-18", "benefits": "五险一金、带薪年假、节日福利、定期体检、团队建设活动", "skills_required": "熟悉常用开发工具和框架，具有良好的编码习惯和问题解决能力", "contact_info": "<EMAIL>", "language_required": "普通话", "certification_required": "无", "reporting_to": "技术总监", "travel_requirement": "无", "working_hours": "09:00-18:00（周一至周五）", "probation_period": "3个月", "career_path": "技术专家/技术经理", "company_culture": "创新、协作、开放、进取", "company_description": "北京科技有限公司是一家专注于企业级软件解决方案的高科技公司，致力于为客户提供优质的技术服务和产品。", "application_process": "投递简历->简历筛选->技术面试->HR面试->发放offer", "interview_process": "初试（技术面试）->复试（HR面试）->终面（技术总监面试）", "completion_source": "local_rules_and_api"}}, "889d11929dc3039a4d8142b3acf285df": {"timestamp": 1747629993.169003, "value": {"job_title": "软件工程师", "company_name": "北京科技有限公司", "job_description": "北京科技有限公司现招聘软件工程师岗位。", "requirements": "1. 学历要求：本科及以上学历\n2. 专业要求：计算机、软件工程、电子信息等相关专业\n3. 经验要求：1年以上相关工作经验\n5. 具有良好的沟通能力和团队合作精神\n6. 有责任心，工作认真细致，有较强的学习能力\n", "location": "北京", "education_required": "本科及以上学历", "experience_required": "1年以上相关工作经验", "job_type": "全职", "department": "技术部", "completion_source": "local_rules_and_api"}}, "3ef05e8e321299fe7fd96be0b18b18c6": {"timestamp": 1747630001.6611688, "value": {"job_title": "高级Java开发工程师", "company_name": "北京科技有限公司", "job_description": "负责公司核心业务系统的设计、开发和维护。", "location": "北京", "requirements": "1. 学历要求：本科及以上学历\n2. 专业要求：计算机、软件工程、电子信息等相关专业\n3. 经验要求：5年以上相关工作经验\n5. 具有良好的沟通能力和团队合作精神\n6. 有责任心，工作认真细致，有较强的学习能力\n", "responsibilities": "1. 负责产品功能模块的设计和开发\n2. 编写高质量、可维护的代码\n3. 解决开发过程中的技术问题\n4. 参与产品需求分析和技术方案讨论\n5. 编写技术文档，参与代码审查\n", "salary": "16K-23K/月", "education_required": "本科及以上学历", "experience_required": "5年以上相关工作经验", "job_type": "全职", "department": "技术部", "num_of_positions": "若干", "posting_date": "2025-05-19", "deadline": "2025-06-18", "benefits": "五险一金、带薪年假、节日福利、定期体检、团队建设活动", "contact_info": "<EMAIL>", "language_required": "英语四级及以上", "certification_required": "无", "completion_source": "local_rules_and_api"}}, "f2a477742a4abb27fd48d420f2de4fd2": {"timestamp": 1747630001.665664, "value": {"job_title": "高级Java开发工程师", "company_name": "北京科技有限公司", "job_description": "负责公司核心业务系统的设计、开发和维护。", "location": "北京", "responsibilities": "1. 负责产品功能模块的设计和开发\n2. 编写高质量、可维护的代码\n3. 解决开发过程中的技术问题\n4. 参与产品需求分析和技术方案讨论\n5. 编写技术文档，参与代码审查\n", "salary": "16K-23K/月", "job_type": "全职", "department": "技术部", "completion_source": "local_rules_and_api"}}}