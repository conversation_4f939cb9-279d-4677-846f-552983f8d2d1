{"362e8a208f798c2c93d6af76b2b8db16": {"timestamp": 1747628963.622648, "value": {"ORG": [{"text": "北京大学", "start": 9, "end": 13, "type": "ORG"}, {"text": "北京大学信息科学技术学院", "start": 45, "end": 57, "type": "ORG"}, {"text": "负责学院", "start": 118, "end": 122, "type": "ORG"}, {"text": "参与学院", "start": 147, "end": 151, "type": "ORG"}, {"text": "具有良好的沟通能力和团", "start": 374, "end": 385, "type": "ORG"}, {"text": "有高校", "start": 403, "end": 406, "type": "ORG"}, {"text": "提供专业培训和职业发展机会", "start": 515, "end": 528, "type": "ORG"}, {"text": "招聘单位", "start": 40, "end": 44, "type": "ORG"}], "POSITION": [{"text": "北京大学招聘软件工程师", "start": 9, "end": 20, "type": "POSITION"}, {"text": "应聘软件工程师", "start": 591, "end": 598, "type": "POSITION"}], "LOC": [{"text": "北京市海淀区", "start": 71, "end": 77, "type": "LOC"}, {"text": "北京市海淀区颐和园路5号", "start": 71, "end": 83, "type": "LOC"}, {"text": "北京市", "start": 71, "end": 74, "type": "LOC"}], "DATE": [{"text": "2023年6月30日", "start": 628, "end": 638, "type": "DATE"}, {"text": "2023年7月10日", "start": 652, "end": 662, "type": "DATE"}, {"text": "2023年6月", "start": 628, "end": 635, "type": "DATE"}, {"text": "2023年7月", "start": 652, "end": 659, "type": "DATE"}, {"text": "6月30日", "start": 633, "end": 638, "type": "DATE"}, {"text": "7月10日", "start": 657, "end": 662, "type": "DATE"}, {"text": "7月20日", "start": 663, "end": 668, "type": "DATE"}, {"text": "2023年7月10日-7月20日", "start": 652, "end": 668, "type": "DATE"}], "TIME": [], "EDUCATION": [{"text": "本科及以上学历", "start": 230, "end": 237, "type": "EDUCATION"}], "MAJOR": [{"text": "计算机相关专业", "start": 222, "end": 229, "type": "MAJOR"}, {"text": "提供专业", "start": 515, "end": 519, "type": "MAJOR"}, {"text": "软件", "start": 15, "end": 17, "type": "MAJOR"}, {"text": "信息", "start": 49, "end": 51, "type": "MAJOR"}, {"text": "管理", "start": 124, "end": 126, "type": "MAJOR"}], "EXPERIENCE": [], "SKILL": [{"text": "熟悉Django", "start": 265, "end": 273, "type": "SKILL"}, {"text": "熟悉MySQL", "start": 295, "end": 302, "type": "SKILL"}, {"text": "熟悉Linux操作系统", "start": 337, "end": 348, "type": "SKILL"}, {"text": "了解网络编程和多线程编程", "start": 349, "end": 361, "type": "SKILL"}, {"text": "具有良好的沟通能力", "start": 374, "end": 383, "type": "SKILL"}, {"text": "有高校信息化系统开发经验", "start": 403, "end": 415, "type": "SKILL"}, {"text": "Python", "start": 254, "end": 260, "type": "SKILL"}, {"text": "SQL", "start": 299, "end": 302, "type": "SKILL"}, {"text": "Linux", "start": 339, "end": 344, "type": "SKILL"}, {"text": "沟通能力", "start": 379, "end": 383, "type": "SKILL"}, {"text": "团队合作", "start": 384, "end": 388, "type": "SKILL"}, {"text": "工程师", "start": 17, "end": 20, "type": "SKILL"}], "SALARY": [{"text": "3年", "start": 250, "end": 252, "type": "SALARY"}, {"text": "2023年", "start": 628, "end": 633, "type": "SALARY"}, {"text": "6月", "start": 633, "end": 635, "type": "SALARY"}, {"text": "7月", "start": 657, "end": 659, "type": "SALARY"}], "CONTACT": [{"text": "电话：010-12345678", "start": 700, "end": 715, "type": "CONTACT"}, {"text": "010-12345678", "start": 703, "end": 715, "type": "CONTACT"}, {"text": "<EMAIL>", "start": 570, "end": 583, "type": "CONTACT"}], "PERSON": [], "NUMBER": [], "QUALIFICATION": [], "BENEFIT": [{"text": "五险一金", "start": 488, "end": 492, "type": "BENEFIT"}, {"text": "节日福利", "start": 498, "end": 502, "type": "BENEFIT"}]}}, "66e15964bcb905ba1b364e7df03b8b8d": {"timestamp": 1747629058.216778, "value": {"ORG": [{"text": "北京大学", "start": 9, "end": 13, "type": "ORG"}, {"text": "北京大学信息科学技术学院", "start": 45, "end": 57, "type": "ORG"}, {"text": "负责学院", "start": 118, "end": 122, "type": "ORG"}, {"text": "参与学院", "start": 147, "end": 151, "type": "ORG"}, {"text": "具有良好的沟通能力和团", "start": 374, "end": 385, "type": "ORG"}, {"text": "有高校", "start": 403, "end": 406, "type": "ORG"}, {"text": "提供专业培训和职业发展机会", "start": 515, "end": 528, "type": "ORG"}, {"text": "招聘单位", "start": 40, "end": 44, "type": "ORG"}], "POSITION": [{"text": "北京大学招聘软件工程师", "start": 9, "end": 20, "type": "POSITION"}, {"text": "应聘软件工程师", "start": 591, "end": 598, "type": "POSITION"}], "LOC": [{"text": "北京市海淀区", "start": 71, "end": 77, "type": "LOC"}, {"text": "北京市海淀区颐和园路5号", "start": 71, "end": 83, "type": "LOC"}, {"text": "北京市", "start": 71, "end": 74, "type": "LOC"}], "DATE": [{"text": "2023年6月30日", "start": 628, "end": 638, "type": "DATE"}, {"text": "2023年7月10日", "start": 652, "end": 662, "type": "DATE"}, {"text": "2023年6月", "start": 628, "end": 635, "type": "DATE"}, {"text": "2023年7月", "start": 652, "end": 659, "type": "DATE"}, {"text": "6月30日", "start": 633, "end": 638, "type": "DATE"}, {"text": "7月10日", "start": 657, "end": 662, "type": "DATE"}, {"text": "7月20日", "start": 663, "end": 668, "type": "DATE"}, {"text": "2023年7月10日-7月20日", "start": 652, "end": 668, "type": "DATE"}], "TIME": [], "EDUCATION": [{"text": "本科及以上学历", "start": 230, "end": 237, "type": "EDUCATION"}], "MAJOR": [{"text": "计算机相关专业", "start": 222, "end": 229, "type": "MAJOR"}, {"text": "提供专业", "start": 515, "end": 519, "type": "MAJOR"}, {"text": "软件", "start": 15, "end": 17, "type": "MAJOR"}, {"text": "信息", "start": 49, "end": 51, "type": "MAJOR"}, {"text": "管理", "start": 124, "end": 126, "type": "MAJOR"}], "EXPERIENCE": [{"text": "3年以上Python", "start": 250, "end": 260, "type": "EXPERIENCE"}, {"text": "3年以上Python开发经验", "start": 250, "end": 264, "type": "EXPERIENCE"}], "SKILL": [{"text": "熟悉Django", "start": 265, "end": 273, "type": "SKILL"}, {"text": "熟悉MySQL", "start": 295, "end": 302, "type": "SKILL"}, {"text": "熟悉Linux操作系统", "start": 337, "end": 348, "type": "SKILL"}, {"text": "了解网络编程和多线程编程", "start": 349, "end": 361, "type": "SKILL"}, {"text": "具有良好的沟通能力", "start": 374, "end": 383, "type": "SKILL"}, {"text": "有高校信息化系统开发经验", "start": 403, "end": 415, "type": "SKILL"}, {"text": "Python", "start": 254, "end": 260, "type": "SKILL"}, {"text": "SQL", "start": 299, "end": 302, "type": "SKILL"}, {"text": "Linux", "start": 339, "end": 344, "type": "SKILL"}, {"text": "沟通能力", "start": 379, "end": 383, "type": "SKILL"}, {"text": "团队合作", "start": 384, "end": 388, "type": "SKILL"}, {"text": "工程师", "start": 17, "end": 20, "type": "SKILL"}], "SALARY": [{"text": "3年", "start": 250, "end": 252, "type": "SALARY"}, {"text": "2023年", "start": 628, "end": 633, "type": "SALARY"}, {"text": "6月", "start": 633, "end": 635, "type": "SALARY"}, {"text": "7月", "start": 657, "end": 659, "type": "SALARY"}, {"text": "月薪15000-25000元", "start": 454, "end": 468, "type": "SALARY"}], "CONTACT": [{"text": "电话：010-12345678", "start": 700, "end": 715, "type": "CONTACT"}, {"text": "010-12345678", "start": 703, "end": 715, "type": "CONTACT"}, {"text": "<EMAIL>", "start": 570, "end": 583, "type": "CONTACT"}], "PERSON": [], "NUMBER": [], "QUALIFICATION": [], "BENEFIT": [{"text": "五险一金", "start": 488, "end": 492, "type": "BENEFIT"}, {"text": "节日福利", "start": 498, "end": 502, "type": "BENEFIT"}]}}}