#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
智能内容分类器
用于自动分类岗位信息，提高数据组织和检索效率
"""

import os
import re
import logging
import json
from typing import Dict, List, Any, Optional, Set, Tuple

from .llm.enhanced_deepseek_client import EnhancedDeepSeekClient as DeepSeekLLMClient
from ..utils.cache_manager import CacheManager


class ContentClassifier:
    """智能内容分类器"""

    # 行业分类关键词
    INDUSTRY_KEYWORDS = {
        "教育": ["教育", "学校", "大学", "高校", "教师", "教学", "学院", "培训", "教研", "教务", "教育局", "教委"],
        "医疗卫生": ["医院", "医疗", "卫生", "健康", "医生", "护士", "药剂", "临床", "医学", "卫生院", "保健", "疾控"],
        "金融": ["银行", "金融", "证券", "保险", "投资", "基金", "理财", "信托", "资产", "财务", "会计", "审计"],
        "IT互联网": ["互联网", "软件", "IT", "计算机", "网络", "程序", "开发", "算法", "数据", "系统", "信息", "技术"],
        "政府机构": ["政府", "机关", "公务员", "行政", "事业单位", "国家", "省", "市", "县", "局", "委员会", "办公室"],
        "制造业": ["制造", "生产", "工厂", "工业", "加工", "装配", "质量", "设备", "工程", "机械", "电子", "材料"],
        "建筑地产": ["建筑", "房地产", "工程", "设计", "规划", "建设", "房产", "物业", "园区", "开发", "城建", "土木"],
        "文化传媒": ["文化", "传媒", "出版", "广告", "媒体", "设计", "艺术", "创意", "编辑", "记者", "影视", "娱乐"],
        "法律": ["法律", "律师", "法务", "合规", "诉讼", "仲裁", "法院", "检察院", "司法", "公证", "法规", "法制"],
        "农林牧渔": ["农业", "林业", "牧业", "渔业", "种植", "养殖", "农产品", "畜牧", "园艺", "农场", "林场", "水产"],
        "能源环保": ["能源", "环保", "电力", "石油", "天然气", "煤炭", "新能源", "环境", "节能", "污染", "生态", "资源"],
        "交通运输": ["交通", "运输", "物流", "航空", "铁路", "公路", "海运", "快递", "仓储", "货运", "客运", "配送"],
        "旅游酒店": ["旅游", "酒店", "餐饮", "旅行", "景区", "度假", "住宿", "服务", "接待", "导游", "民宿", "娱乐"],
        "咨询服务": ["咨询", "顾问", "服务", "外包", "人力资源", "猎头", "培训", "管理", "策划", "市场", "调研", "分析"],
        "其他行业": []
    }

    # 职能分类关键词
    FUNCTION_KEYWORDS = {
        "技术研发": ["研发", "开发", "工程师", "技术", "架构", "设计", "测试", "算法", "程序", "编程", "代码", "系统"],
        "产品运营": ["产品", "运营", "策划", "需求", "用户", "体验", "交互", "设计", "推广", "营销", "活动", "内容"],
        "市场销售": ["市场", "销售", "营销", "推广", "客户", "商务", "业务", "拓展", "渠道", "客服", "公关", "广告"],
        "人力资源": ["人力资源", "HR", "招聘", "培训", "绩效", "薪酬", "福利", "员工", "人事", "劳资", "组织", "文化"],
        "财务审计": ["财务", "会计", "审计", "税务", "成本", "预算", "核算", "出纳", "报表", "资金", "财税", "金融"],
        "行政后勤": ["行政", "后勤", "办公", "文秘", "前台", "秘书", "总务", "采购", "资产", "档案", "司机", "保安"],
        "教学科研": ["教学", "科研", "教师", "教授", "讲师", "研究", "实验", "学术", "课程", "教育", "培训", "辅导"],
        "医疗护理": ["医生", "护士", "医师", "护理", "临床", "医技", "药剂", "检验", "康复", "保健", "诊断", "治疗"],
        "法律合规": ["法律", "合规", "法务", "律师", "诉讼", "合同", "知识产权", "专利", "商标", "版权", "法规", "政策"],
        "设计创意": ["设计", "创意", "美工", "UI", "UX", "平面", "视觉", "交互", "动画", "插画", "广告", "艺术"],
        "生产制造": ["生产", "制造", "工艺", "质量", "安全", "设备", "维修", "车间", "班组", "工程", "技术", "操作"],
        "物流采购": ["物流", "采购", "供应链", "仓储", "运输", "配送", "供应商", "库存", "进出口", "报关", "货运", "快递"],
        "高级管理": ["总监", "经理", "主管", "总裁", "副总", "CEO", "COO", "CFO", "CTO", "负责人", "管理", "决策"],
        "其他职能": []
    }

    # 级别分类关键词
    LEVEL_KEYWORDS = {
        "初级/实习": ["实习", "初级", "助理", "学徒", "见习", "应届", "毕业生", "储备", "培训生", "实习生", "初出茅庐", "入门"],
        "中级": ["中级", "主管", "组长", "工程师", "专员", "分析师", "顾问", "讲师", "3年", "三年", "有经验", "熟练"],
        "高级": ["高级", "资深", "专家", "经理", "主任", "总监", "总工", "5年", "五年", "丰富经验", "精通", "带团队"],
        "管理层": ["总监", "经理", "主管", "总裁", "副总", "CEO", "COO", "CFO", "CTO", "负责人", "管理", "决策"],
        "其他级别": []
    }

    # 工作类型分类关键词
    JOB_TYPE_KEYWORDS = {
        "全职": ["全职", "正式", "长期", "固定", "专职", "全日制", "全天", "朝九晚五", "五险一金", "合同工"],
        "兼职": ["兼职", "临时", "业余", "非全日制", "非全职", "课余", "双休日", "周末", "晚上", "寒暑假"],
        "实习": ["实习", "见习", "培训生", "实习生", "学徒", "毕业实习", "带薪实习", "暑期实习", "寒假实习", "校企合作"],
        "校招": ["校招", "校园招聘", "应届生", "毕业生", "应届毕业生", "校园宣讲", "校园招聘会", "高校招聘", "校园", "高校"],
        "社招": ["社招", "社会招聘", "经验", "有工作经验", "在职", "跳槽", "职场", "工作经验", "社会人才", "专业人才"],
        "其他类型": []
    }

    # 学历要求分类关键词
    EDUCATION_KEYWORDS = {
        "不限": ["不限", "无要求", "无学历要求", "学历不限", "不要求学历", "无需学历", "无学历", "不看学历"],
        "高中及以下": ["高中", "中专", "技校", "职高", "初中", "小学", "义务教育", "高中及以下", "高中以下", "中学"],
        "大专": ["大专", "专科", "高职", "职业学院", "高等专科", "专科学历", "大专学历", "专科及以上", "大专及以上"],
        "本科": ["本科", "学士", "全日制本科", "统招本科", "普通本科", "本科学历", "学士学位", "本科及以上", "大学本科"],
        "硕士": ["硕士", "研究生", "硕士研究生", "全日制硕士", "硕士学位", "硕士及以上", "研究生学历", "硕士学历"],
        "博士": ["博士", "博士研究生", "博士后", "博士学位", "博士及以上", "博士学历", "PhD"],
        "其他学历": []
    }

    def __init__(self, deepseek_client: Optional[DeepSeekLLMClient] = None,
                 use_local_components: bool = True,
                 logger: Optional[logging.Logger] = None):
        """
        初始化内容分类器

        Args:
            deepseek_client: DeepSeek LLM客户端，如果为None则创建新的客户端
            use_local_components: 是否使用本地组件进行分类
            logger: 日志记录器
        """
        self.logger = logger or logging.getLogger(__name__)
        self.deepseek_client = deepseek_client or DeepSeekLLMClient(logger=self.logger)
        self.use_local_components = use_local_components

        # 初始化缓存管理器
        cache_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "cache")
        os.makedirs(cache_dir, exist_ok=True)
        self.cache_manager = CacheManager(
            cache_dir=cache_dir,
            cache_name="content_classifier_cache.json",
            logger=self.logger
        )

    def classify_job(self, job_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        对岗位信息进行分类

        Args:
            job_info: 岗位信息字典，包含job_title, job_description等字段

        Returns:
            Dict[str, Any]: 分类结果，包含industry, function, level, job_type, education等字段
        """
        # 提取关键信息
        job_title = job_info.get("job_title", "") or job_info.get("title", "")
        job_description = job_info.get("job_description", "") or job_info.get("description", "")
        company_name = job_info.get("company_name", "") or job_info.get("institution_name", "")
        education_required = job_info.get("education_required", "") or job_info.get("education", "")

        # 合并文本用于分类
        text_for_classification = f"{job_title}\n{company_name}\n{job_description}"

        # 检查缓存
        cache_key = f"classify_{hash(text_for_classification)}"
        cached_result = self.cache_manager.get(cache_key)
        if cached_result:
            self.logger.info("Using cached classification result")
            return cached_result

        # 使用本地组件分类
        if self.use_local_components:
            result = self._classify_locally(job_info)

            # 如果本地分类结果不完整，尝试使用DeepSeek API
            if not self._is_classification_complete(result) and self.deepseek_client.is_available():
                self.logger.info("Local classification incomplete, trying DeepSeek API")
                api_result = self._classify_with_api(job_info)
                # 合并结果，API结果优先
                for key, value in api_result.items():
                    if key not in result or not result[key]:
                        result[key] = value
        else:
            # 直接使用DeepSeek API
            result = self._classify_with_api(job_info)

        # 缓存结果
        self.cache_manager.set(cache_key, result)

        return result

    def _classify_locally(self, job_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        使用本地组件对岗位信息进行分类

        Args:
            job_info: 岗位信息字典

        Returns:
            Dict[str, Any]: 分类结果
        """
        # 提取关键信息
        job_title = job_info.get("job_title", "") or job_info.get("title", "")
        job_description = job_info.get("job_description", "") or job_info.get("description", "")
        company_name = job_info.get("company_name", "") or job_info.get("institution_name", "")
        education_required = job_info.get("education_required", "") or job_info.get("education", "")

        # 合并文本用于分类
        text = f"{job_title}\n{company_name}\n{job_description}"

        # 分类结果
        result = {
            "industry": self._classify_industry(text),
            "function": self._classify_function(text),
            "level": self._classify_level(text),
            "job_type": self._classify_job_type(text),
            "education": self._classify_education(education_required or text),
            "tags": self._extract_tags(text)
        }

        return result

    def _classify_with_api(self, job_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        使用DeepSeek API对岗位信息进行分类

        Args:
            job_info: 岗位信息字典

        Returns:
            Dict[str, Any]: 分类结果
        """
        try:
            # 提取关键信息
            job_title = job_info.get("job_title", "") or job_info.get("title", "")
            job_description = job_info.get("job_description", "") or job_info.get("description", "")
            company_name = job_info.get("company_name", "") or job_info.get("institution_name", "")
            education_required = job_info.get("education_required", "") or job_info.get("education", "")

            # 构建提示
            prompt = f"""
            请对以下招聘岗位信息进行分类，返回JSON格式的分类结果。

            岗位标题: {job_title}
            公司/机构名称: {company_name}
            学历要求: {education_required}
            岗位描述:
            {job_description}

            请从以下维度进行分类:
            1. 行业(industry): 教育、医疗卫生、金融、IT互联网、政府机构、制造业、建筑地产、文化传媒、法律、农林牧渔、能源环保、交通运输、旅游酒店、咨询服务、其他行业
            2. 职能(function): 技术研发、产品运营、市场销售、人力资源、财务审计、行政后勤、教学科研、医疗护理、法律合规、设计创意、生产制造、物流采购、高级管理、其他职能
            3. 级别(level): 初级/实习、中级、高级、管理层、其他级别
            4. 工作类型(job_type): 全职、兼职、实习、校招、社招、其他类型
            5. 学历要求(education): 不限、高中及以下、大专、本科、硕士、博士、其他学历
            6. 标签(tags): 提取3-5个能够描述该岗位特点的关键词标签

            返回JSON格式，包含industry, function, level, job_type, education, tags字段。
            """

            # 调用DeepSeek API
            messages = [
                {"role": "system", "content": "你是一个专业的招聘信息分类助手，擅长对招聘岗位信息进行多维度分类。"},
                {"role": "user", "content": prompt}
            ]

            response = self.deepseek_client.chat_completion(messages, temperature=0.2)
            result = self.deepseek_client.extract_json(response)

            # 确保结果包含所有必要字段
            required_fields = ["industry", "function", "level", "job_type", "education", "tags"]
            for field in required_fields:
                if field not in result:
                    result[field] = ""

            return result
        except Exception as e:
            self.logger.error(f"Error classifying with API: {e}")
            return {
                "industry": "",
                "function": "",
                "level": "",
                "job_type": "",
                "education": "",
                "tags": []
            }

    def _classify_industry(self, text: str) -> str:
        """
        分类行业

        Args:
            text: 用于分类的文本

        Returns:
            str: 行业分类结果
        """
        return self._classify_by_keywords(text, self.INDUSTRY_KEYWORDS)

    def _classify_function(self, text: str) -> str:
        """
        分类职能

        Args:
            text: 用于分类的文本

        Returns:
            str: 职能分类结果
        """
        return self._classify_by_keywords(text, self.FUNCTION_KEYWORDS)

    def _classify_level(self, text: str) -> str:
        """
        分类级别

        Args:
            text: 用于分类的文本

        Returns:
            str: 级别分类结果
        """
        return self._classify_by_keywords(text, self.LEVEL_KEYWORDS)

    def _classify_job_type(self, text: str) -> str:
        """
        分类工作类型

        Args:
            text: 用于分类的文本

        Returns:
            str: 工作类型分类结果
        """
        return self._classify_by_keywords(text, self.JOB_TYPE_KEYWORDS)

    def _classify_education(self, text: str) -> str:
        """
        分类学历要求

        Args:
            text: 用于分类的文本

        Returns:
            str: 学历要求分类结果
        """
        return self._classify_by_keywords(text, self.EDUCATION_KEYWORDS)

    def _classify_by_keywords(self, text: str, category_keywords: Dict[str, List[str]]) -> str:
        """
        根据关键词对文本进行分类

        Args:
            text: 用于分类的文本
            category_keywords: 分类关键词字典，key为分类名称，value为关键词列表

        Returns:
            str: 分类结果
        """
        # 计算各分类的匹配分数
        category_scores = {}
        for category, keywords in category_keywords.items():
            score = 0
            for keyword in keywords:
                if keyword in text:
                    score += 1
            category_scores[category] = score

        # 返回得分最高的分类
        if not category_scores:
            return ""

        max_score = max(category_scores.values())
        if max_score == 0:
            return list(category_keywords.keys())[-1]  # 返回"其他"分类

        # 如果有多个最高分，返回第一个
        max_categories = [cat for cat, score in category_scores.items() if score == max_score]
        return max_categories[0]

    def _extract_tags(self, text: str) -> List[str]:
        """
        从文本中提取标签

        Args:
            text: 用于提取标签的文本

        Returns:
            List[str]: 标签列表
        """
        # 提取常见技能和关键词
        tags = []

        # 技术技能
        tech_skills = ["Python", "Java", "C++", "JavaScript", "HTML", "CSS", "SQL", "Linux", "Windows",
                      "Office", "Excel", "Word", "PowerPoint", "AutoCAD", "Photoshop", "AI", "机器学习",
                      "深度学习", "数据分析", "大数据", "云计算", "区块链", "物联网", "5G", "人工智能"]

        # 软技能
        soft_skills = ["沟通", "团队合作", "领导力", "创新", "解决问题", "时间管理", "项目管理", "自我驱动",
                      "抗压", "适应性", "学习能力", "分析能力", "创造力", "决策力", "执行力"]

        # 证书和资格
        certifications = ["CPA", "注册会计师", "律师", "教师资格", "医师", "护士", "建筑师", "工程师", "注册",
                         "资格证", "执业证", "职称", "认证"]

        # 检查技术技能
        for skill in tech_skills:
            if skill in text:
                tags.append(skill)

        # 检查软技能
        for skill in soft_skills:
            if skill in text:
                tags.append(skill)

        # 检查证书和资格
        for cert in certifications:
            if cert in text:
                tags.append(cert)

        # 限制标签数量
        return tags[:5]

    def _is_classification_complete(self, result: Dict[str, Any]) -> bool:
        """
        检查分类结果是否完整

        Args:
            result: 分类结果

        Returns:
            bool: 是否完整
        """
        required_fields = ["industry", "function", "level", "job_type", "education"]
        return all(result.get(field) for field in required_fields)

    def batch_classify_jobs(self, job_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        批量对岗位信息进行分类

        Args:
            job_list: 岗位信息列表

        Returns:
            List[Dict[str, Any]]: 带有分类结果的岗位信息列表
        """
        classified_jobs = []

        for job in job_list:
            # 对岗位进行分类
            classification = self.classify_job(job)

            # 将分类结果添加到岗位信息中
            job_with_classification = job.copy()
            job_with_classification.update({
                "industry_category": classification.get("industry", ""),
                "function_category": classification.get("function", ""),
                "level_category": classification.get("level", ""),
                "job_type_category": classification.get("job_type", ""),
                "education_category": classification.get("education", ""),
                "tags": classification.get("tags", [])
            })

            classified_jobs.append(job_with_classification)

        return classified_jobs
