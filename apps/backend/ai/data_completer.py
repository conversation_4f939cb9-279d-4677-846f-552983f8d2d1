#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
智能数据补全器
用于自动补全缺失的岗位信息字段
"""

import os
import logging
import json
from typing import Dict, Any, List, Optional, Set, Union

from .llm.enhanced_deepseek_client import EnhancedDeepSeekClient as DeepSeekLLMClient
from ..utils.cache_manager import CacheManager


class DataCompleter:
    """智能数据补全器"""

    # 可补全的字段列表
    COMPLETABLE_FIELDS = {
        # 基本信息
        "job_title": "岗位标题",
        "company_name": "公司/机构名称",
        "job_description": "岗位描述",
        "requirements": "岗位要求",
        "responsibilities": "工作职责",
        "location": "工作地点",
        "salary": "薪资待遇",
        "education_required": "学历要求",
        "major_required": "专业要求",
        "experience_required": "经验要求",
        "job_type": "工作类型",
        "job_category": "岗位类别",
        "department": "所属部门",
        "reporting_to": "汇报对象",
        "num_of_positions": "招聘人数",
        "posting_date": "发布日期",
        "deadline": "截止日期",
        "contact_info": "联系方式",

        # 扩展信息
        "benefits": "福利待遇",
        "skills_required": "技能要求",
        "language_required": "语言要求",
        "certification_required": "证书要求",
        "travel_requirement": "出差要求",
        "working_hours": "工作时间",
        "probation_period": "试用期",
        "career_path": "职业发展路径",
        "company_culture": "公司文化",
        "company_description": "公司简介",
        "application_process": "申请流程",
        "interview_process": "面试流程"
    }

    # 字段依赖关系
    FIELD_DEPENDENCIES = {
        "job_description": ["job_title", "company_name", "responsibilities"],
        "requirements": ["job_title", "education_required", "experience_required", "skills_required"],
        "responsibilities": ["job_title", "job_description", "department"],
        "education_required": ["job_title", "requirements"],
        "major_required": ["job_title", "education_required", "requirements"],
        "experience_required": ["job_title", "requirements"],
        "skills_required": ["job_title", "requirements", "responsibilities"],
        "salary": ["job_title", "location", "experience_required", "education_required"],
        "benefits": ["company_name", "company_description"],
        "job_category": ["job_title", "responsibilities", "department"],
        "department": ["job_title", "company_name", "responsibilities"],
        "company_description": ["company_name", "company_culture"]
    }

    # 字段优先级（1-5，数字越小优先级越高）
    FIELD_PRIORITIES = {
        "job_title": 1,
        "company_name": 1,
        "job_description": 2,
        "requirements": 2,
        "responsibilities": 2,
        "location": 2,
        "education_required": 3,
        "major_required": 3,
        "experience_required": 3,
        "job_type": 3,
        "salary": 3,
        "department": 3,
        "num_of_positions": 4,
        "posting_date": 4,
        "deadline": 4,
        "contact_info": 4,
        "benefits": 4,
        "skills_required": 4,
        "language_required": 4,
        "certification_required": 4,
        "travel_requirement": 5,
        "working_hours": 5,
        "probation_period": 5,
        "career_path": 5,
        "company_culture": 5,
        "company_description": 5,
        "application_process": 5,
        "interview_process": 5
    }

    def __init__(self, deepseek_client: Optional[DeepSeekLLMClient] = None,
                 use_local_components: bool = True,
                 logger: Optional[logging.Logger] = None):
        """
        初始化智能数据补全器

        Args:
            deepseek_client: DeepSeek LLM客户端，如果为None则创建新的客户端
            use_local_components: 是否使用本地组件进行补全
            logger: 日志记录器
        """
        self.logger = logger or logging.getLogger(__name__)
        self.deepseek_client = deepseek_client or DeepSeekLLMClient(logger=self.logger)
        self.use_local_components = use_local_components

        # 初始化缓存管理器
        cache_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "cache")
        os.makedirs(cache_dir, exist_ok=True)
        self.cache_manager = CacheManager(
            cache_dir=cache_dir,
            cache_name="data_completer_cache.json",
            logger=self.logger
        )

    def complete_job_info(self, job_info: Dict[str, Any], fields_to_complete: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        补全岗位信息

        Args:
            job_info: 原始岗位信息
            fields_to_complete: 需要补全的字段列表，如果为None则自动检测缺失字段

        Returns:
            Dict[str, Any]: 补全后的岗位信息
        """
        # 检查缓存
        cache_key = f"complete_{hash(json.dumps(job_info, sort_keys=True))}"
        if fields_to_complete:
            cache_key += f"_{hash(json.dumps(fields_to_complete, sort_keys=True))}"

        cached_result = self.cache_manager.get(cache_key)
        if cached_result:
            self.logger.info("Using cached job info completion result")
            return cached_result

        # 如果未指定需要补全的字段，则自动检测缺失字段
        if not fields_to_complete:
            fields_to_complete = self._detect_missing_fields(job_info)

        # 如果没有需要补全的字段，直接返回原始信息
        if not fields_to_complete:
            return job_info

        # 按优先级排序字段
        fields_to_complete = self._sort_fields_by_priority(fields_to_complete)

        # 补全岗位信息
        completed_job_info = job_info.copy()

        # 使用本地规则补全
        if self.use_local_components:
            try:
                completed_job_info = self._complete_with_local_rules(completed_job_info, fields_to_complete)

                # 检查是否还有未补全的字段
                remaining_fields = [field for field in fields_to_complete if not completed_job_info.get(field)]

                # 如果还有未补全的字段且DeepSeek API可用，使用API补全
                if remaining_fields and self.deepseek_client.is_available():
                    self.logger.info(f"Local completion incomplete, trying DeepSeek API for {len(remaining_fields)} fields")
                    api_completed = self._complete_with_api(completed_job_info, remaining_fields)
                    completed_job_info.update(api_completed)
            except Exception as e:
                self.logger.error(f"Error in local completion: {e}")
                # 如果本地补全失败，尝试使用API补全
                if self.deepseek_client.is_available():
                    self.logger.info("Falling back to DeepSeek API for completion")
                    api_completed = self._complete_with_api(job_info, fields_to_complete)
                    completed_job_info.update(api_completed)
        else:
            # 直接使用DeepSeek API补全
            api_completed = self._complete_with_api(job_info, fields_to_complete)
            completed_job_info.update(api_completed)

        # 添加补全来源标记
        completed_job_info["completion_source"] = "local_rules_and_api" if self.use_local_components else "api_only"

        # 缓存结果
        self.cache_manager.set(cache_key, completed_job_info)

        return completed_job_info

    def _detect_missing_fields(self, job_info: Dict[str, Any]) -> List[str]:
        """
        检测缺失的字段

        Args:
            job_info: 岗位信息

        Returns:
            List[str]: 缺失的字段列表
        """
        missing_fields = []

        # 检查基本必填字段
        essential_fields = ["job_title", "company_name", "job_description", "requirements"]
        for field in essential_fields:
            if not job_info.get(field):
                missing_fields.append(field)

        # 检查其他可补全字段
        for field in self.COMPLETABLE_FIELDS:
            if field not in essential_fields and not job_info.get(field):
                # 根据已有信息的完整度决定是否补全该字段
                if self._should_complete_field(field, job_info):
                    missing_fields.append(field)

        return missing_fields

    def _should_complete_field(self, field: str, job_info: Dict[str, Any]) -> bool:
        """
        判断是否应该补全该字段

        Args:
            field: 字段名
            job_info: 岗位信息

        Returns:
            bool: 是否应该补全
        """
        # 检查依赖字段是否存在
        if field in self.FIELD_DEPENDENCIES:
            dependencies = self.FIELD_DEPENDENCIES[field]
            available_dependencies = [dep for dep in dependencies if job_info.get(dep)]

            # 如果依赖字段不足，则不补全
            if len(available_dependencies) < len(dependencies) * 0.5:
                return False

        # 根据字段优先级决定是否补全
        priority = self.FIELD_PRIORITIES.get(field, 5)

        # 优先级1-3的字段始终尝试补全
        if priority <= 3:
            return True

        # 优先级4-5的字段，只有在基本信息较完整时才补全
        essential_fields = ["job_title", "company_name", "job_description", "requirements"]
        essential_fields_count = sum(1 for f in essential_fields if job_info.get(f))

        if priority == 4 and essential_fields_count >= 3:
            return True

        if priority == 5 and essential_fields_count == 4:
            return True

        return False

    def _sort_fields_by_priority(self, fields: List[str]) -> List[str]:
        """
        按优先级排序字段

        Args:
            fields: 字段列表

        Returns:
            List[str]: 排序后的字段列表
        """
        return sorted(fields, key=lambda x: self.FIELD_PRIORITIES.get(x, 5))

    def _complete_with_local_rules(self, job_info: Dict[str, Any], fields_to_complete: List[str]) -> Dict[str, Any]:
        """
        使用本地规则补全岗位信息

        Args:
            job_info: 原始岗位信息
            fields_to_complete: 需要补全的字段列表

        Returns:
            Dict[str, Any]: 补全后的岗位信息
        """
        completed_info = job_info.copy()

        for field in fields_to_complete:
            # 跳过已有值的字段
            if completed_info.get(field):
                continue

            # 根据字段类型调用相应的补全方法
            if field == "job_title":
                completed_info[field] = self._complete_job_title(completed_info)
            elif field == "company_name":
                completed_info[field] = self._complete_company_name(completed_info)
            elif field == "job_description":
                completed_info[field] = self._complete_job_description(completed_info)
            elif field == "requirements":
                completed_info[field] = self._complete_requirements(completed_info)
            elif field == "responsibilities":
                completed_info[field] = self._complete_responsibilities(completed_info)
            elif field == "location":
                completed_info[field] = self._complete_location(completed_info)
            elif field == "education_required":
                completed_info[field] = self._complete_education_required(completed_info)
            elif field == "major_required":
                completed_info[field] = self._complete_major_required(completed_info)
            elif field == "experience_required":
                completed_info[field] = self._complete_experience_required(completed_info)
            elif field == "salary":
                completed_info[field] = self._complete_salary(completed_info)
            # 其他字段使用通用方法补全
            else:
                completed_value = self._complete_generic_field(field, completed_info)
                if completed_value:
                    completed_info[field] = completed_value

        return completed_info

    def _complete_with_api(self, job_info: Dict[str, Any], fields_to_complete: List[str]) -> Dict[str, Any]:
        """
        使用DeepSeek API补全岗位信息

        Args:
            job_info: 原始岗位信息
            fields_to_complete: 需要补全的字段列表

        Returns:
            Dict[str, Any]: 补全后的岗位信息
        """
        try:
            # 构建提示
            prompt = self._build_completion_prompt(job_info, fields_to_complete)

            # 调用DeepSeek API
            messages = [
                {"role": "system", "content": "你是一个专业的招聘信息补全助手，擅长根据已有信息推断和补全缺失的岗位信息字段。"},
                {"role": "user", "content": prompt}
            ]

            response = self.deepseek_client.chat_completion(messages, temperature=0.3)
            completed_fields = self.deepseek_client.extract_json(response)

            # 验证返回的字段
            validated_fields = {}
            for field in fields_to_complete:
                if field in completed_fields and completed_fields[field]:
                    validated_fields[field] = completed_fields[field]

            return validated_fields
        except Exception as e:
            self.logger.error(f"Error completing job info with API: {e}")
            return {}

    def _build_completion_prompt(self, job_info: Dict[str, Any], fields_to_complete: List[str]) -> str:
        """
        构建补全提示

        Args:
            job_info: 原始岗位信息
            fields_to_complete: 需要补全的字段列表

        Returns:
            str: 补全提示
        """
        # 构建已有信息部分
        existing_info = "已有岗位信息：\n"
        for field, value in job_info.items():
            if value and field in self.COMPLETABLE_FIELDS:
                field_name = self.COMPLETABLE_FIELDS.get(field, field)
                existing_info += f"{field_name}：{value}\n"

        # 构建需要补全的字段部分
        fields_to_complete_str = "需要补全的字段：\n"
        for field in fields_to_complete:
            if field in self.COMPLETABLE_FIELDS:
                field_name = self.COMPLETABLE_FIELDS.get(field, field)
                fields_to_complete_str += f"- {field}（{field_name}）\n"

        # 构建完整提示
        prompt = f"""
        请根据已有的岗位信息，补全缺失的字段。请确保补全的信息与已有信息保持一致，并且符合实际情况。

        {existing_info}

        {fields_to_complete_str}

        请以JSON格式返回补全的字段，只包含需要补全的字段，不要包含已有字段。例如：
        {{
            "field1": "补全的值1",
            "field2": "补全的值2"
        }}
        """

        return prompt

    def _complete_job_title(self, job_info: Dict[str, Any]) -> str:
        """
        补全岗位标题

        Args:
            job_info: 岗位信息

        Returns:
            str: 补全的岗位标题
        """
        # 从岗位描述或职责中提取
        description = job_info.get("job_description", "")
        responsibilities = job_info.get("responsibilities", "")

        if description:
            # 尝试从描述的前几句话中提取
            sentences = description.split("。")
            for sentence in sentences[:3]:
                if "招聘" in sentence and len(sentence) < 30:
                    parts = sentence.split("招聘")
                    if len(parts) > 1 and parts[1]:
                        return parts[1].strip()

                if "职位" in sentence or "岗位" in sentence:
                    for keyword in ["职位是", "职位：", "岗位是", "岗位："]:
                        if keyword in sentence:
                            parts = sentence.split(keyword)
                            if len(parts) > 1 and parts[1]:
                                return parts[1].strip()

        # 从职责中提取关键词组合
        if responsibilities:
            keywords = ["开发", "设计", "管理", "销售", "市场", "运营", "客服", "技术", "研究", "教学", "医疗", "行政", "人事", "财务", "法务"]
            for keyword in keywords:
                if keyword in responsibilities:
                    return f"{keyword}岗位"

        # 默认返回通用岗位名称
        return "招聘岗位"

    def _complete_company_name(self, job_info: Dict[str, Any]) -> str:
        """
        补全公司/机构名称

        Args:
            job_info: 岗位信息

        Returns:
            str: 补全的公司/机构名称
        """
        # 从岗位描述或联系方式中提取
        description = job_info.get("job_description", "")
        contact_info = job_info.get("contact_info", "")

        if description:
            # 尝试从描述的前几句话中提取
            sentences = description.split("。")
            for sentence in sentences[:3]:
                if "公司" in sentence or "单位" in sentence or "机构" in sentence or "学校" in sentence or "医院" in sentence:
                    for entity in ["有限公司", "股份有限公司", "集团", "大学", "学院", "研究所", "医院", "中心", "局", "厅", "部", "处"]:
                        if entity in sentence:
                            start = max(0, sentence.find(entity) - 20)
                            end = sentence.find(entity) + len(entity)
                            return sentence[start:end].strip()

        # 从联系方式中提取
        if contact_info:
            if "@" in contact_info:
                # 从邮箱域名提取
                parts = contact_info.split("@")
                if len(parts) > 1 and parts[1]:
                    domain = parts[1].split(".")[0]
                    return f"{domain}公司"

        # 默认返回通用名称
        return "招聘单位"

    def _complete_job_description(self, job_info: Dict[str, Any]) -> str:
        """
        补全岗位描述

        Args:
            job_info: 岗位信息

        Returns:
            str: 补全的岗位描述
        """
        # 从职责和要求中组合
        job_title = job_info.get("job_title", "")
        company_name = job_info.get("company_name", "")
        responsibilities = job_info.get("responsibilities", "")
        requirements = job_info.get("requirements", "")

        description = ""

        if job_title and company_name:
            description += f"{company_name}现招聘{job_title}岗位。"
        elif job_title:
            description += f"招聘{job_title}岗位。"
        elif company_name:
            description += f"{company_name}招聘岗位。"

        if responsibilities:
            description += f"\n\n岗位职责：\n{responsibilities}"

        if requirements:
            description += f"\n\n岗位要求：\n{requirements}"

        return description

    def _complete_requirements(self, job_info: Dict[str, Any]) -> str:
        """
        补全岗位要求

        Args:
            job_info: 岗位信息

        Returns:
            str: 补全的岗位要求
        """
        job_title = job_info.get("job_title", "")
        education = job_info.get("education_required", "")
        experience = job_info.get("experience_required", "")
        major = job_info.get("major_required", "")
        skills = job_info.get("skills_required", "")

        requirements = ""

        if education:
            requirements += f"1. 学历要求：{education}\n"
        elif "工程师" in job_title or "开发" in job_title or "技术" in job_title:
            requirements += "1. 学历要求：本科及以上学历\n"
        elif "经理" in job_title or "主管" in job_title:
            requirements += "1. 学历要求：本科及以上学历\n"
        else:
            requirements += "1. 学历要求：大专及以上学历\n"

        if major:
            requirements += f"2. 专业要求：{major}\n"
        elif "工程师" in job_title:
            requirements += "2. 专业要求：计算机、软件工程、电子信息等相关专业\n"
        elif "财务" in job_title:
            requirements += "2. 专业要求：会计、财务管理等相关专业\n"

        if experience:
            requirements += f"3. 经验要求：{experience}\n"
        elif "高级" in job_title or "资深" in job_title:
            requirements += "3. 经验要求：5年以上相关工作经验\n"
        elif "经理" in job_title or "主管" in job_title:
            requirements += "3. 经验要求：3年以上相关工作经验\n"
        else:
            requirements += "3. 经验要求：1年以上相关工作经验\n"

        if skills:
            requirements += f"4. 技能要求：{skills}\n"

        requirements += "5. 具有良好的沟通能力和团队合作精神\n"
        requirements += "6. 有责任心，工作认真细致，有较强的学习能力\n"

        return requirements

    def _complete_responsibilities(self, job_info: Dict[str, Any]) -> str:
        """
        补全工作职责

        Args:
            job_info: 岗位信息

        Returns:
            str: 补全的工作职责
        """
        job_title = job_info.get("job_title", "")
        description = job_info.get("job_description", "")

        responsibilities = ""

        if "开发" in job_title or "工程师" in job_title:
            responsibilities += "1. 负责产品功能模块的设计和开发\n"
            responsibilities += "2. 编写高质量、可维护的代码\n"
            responsibilities += "3. 解决开发过程中的技术问题\n"
            responsibilities += "4. 参与产品需求分析和技术方案讨论\n"
            responsibilities += "5. 编写技术文档，参与代码审查\n"
        elif "销售" in job_title:
            responsibilities += "1. 负责产品销售和客户开发\n"
            responsibilities += "2. 完成销售目标和业绩指标\n"
            responsibilities += "3. 维护客户关系，提供售后服务\n"
            responsibilities += "4. 收集市场信息和客户反馈\n"
            responsibilities += "5. 参与销售策略制定和市场推广活动\n"
        elif "人事" in job_title or "HR" in job_title:
            responsibilities += "1. 负责招聘、培训和员工关系管理\n"
            responsibilities += "2. 制定和执行人力资源政策和流程\n"
            responsibilities += "3. 组织员工活动和团队建设\n"
            responsibilities += "4. 处理员工福利和薪酬事务\n"
            responsibilities += "5. 维护人力资源系统和档案\n"
        elif "财务" in job_title:
            responsibilities += "1. 负责公司财务核算和报表编制\n"
            responsibilities += "2. 管理公司资金和预算\n"
            responsibilities += "3. 处理税务申报和财务审计\n"
            responsibilities += "4. 提供财务分析和决策支持\n"
            responsibilities += "5. 优化财务流程和控制风险\n"
        elif "经理" in job_title or "主管" in job_title:
            responsibilities += "1. 负责团队管理和业务发展\n"
            responsibilities += "2. 制定和执行部门计划和目标\n"
            responsibilities += "3. 协调内外部资源，解决问题\n"
            responsibilities += "4. 培养和发展团队成员\n"
            responsibilities += "5. 向上级汇报工作进展和结果\n"
        else:
            # 从描述中提取关键信息
            if description:
                sentences = description.split("。")
                for sentence in sentences:
                    if "负责" in sentence or "从事" in sentence or "承担" in sentence:
                        responsibilities += f"1. {sentence.strip()}\n"
                        break

            responsibilities += "2. 完成领导交办的其他工作任务\n"
            responsibilities += "3. 与团队成员协作，共同完成项目目标\n"
            responsibilities += "4. 持续学习和提升专业技能\n"
            responsibilities += "5. 参与部门日常工作和活动\n"

        return responsibilities

    def _complete_location(self, job_info: Dict[str, Any]) -> str:
        """
        补全工作地点

        Args:
            job_info: 岗位信息

        Returns:
            str: 补全的工作地点
        """
        company_name = job_info.get("company_name", "")
        description = job_info.get("job_description", "")

        # 常见城市列表
        cities = ["北京", "上海", "广州", "深圳", "杭州", "南京", "成都", "重庆", "武汉", "西安", "天津", "苏州", "长沙", "郑州", "青岛", "宁波", "东莞", "无锡", "厦门", "福州", "大连", "合肥", "济南", "昆明", "哈尔滨", "长春", "沈阳", "石家庄", "南宁", "贵阳", "南昌", "太原", "乌鲁木齐", "兰州", "呼和浩特", "海口", "银川", "西宁", "拉萨"]

        # 从公司名称中提取
        if company_name:
            for city in cities:
                if city in company_name:
                    return city

        # 从描述中提取
        if description:
            for city in cities:
                if city in description:
                    return city

        # 默认返回北京
        return "北京"

    def _complete_education_required(self, job_info: Dict[str, Any]) -> str:
        """
        补全学历要求

        Args:
            job_info: 岗位信息

        Returns:
            str: 补全的学历要求
        """
        job_title = job_info.get("job_title", "")
        requirements = job_info.get("requirements", "")

        # 从要求中提取
        if requirements:
            education_keywords = ["博士", "硕士", "研究生", "本科", "大学本科", "大专", "专科", "高中", "中专", "初中"]
            for keyword in education_keywords:
                if keyword in requirements:
                    if "以上" in requirements[requirements.find(keyword)-5:requirements.find(keyword)+10]:
                        return f"{keyword}及以上学历"
                    else:
                        return f"{keyword}学历"

        # 根据职位推断
        if "高级" in job_title or "资深" in job_title or "总监" in job_title or "经理" in job_title:
            return "本科及以上学历"
        elif "工程师" in job_title or "开发" in job_title or "设计师" in job_title:
            return "本科及以上学历"
        elif "专员" in job_title or "助理" in job_title:
            return "大专及以上学历"

        # 默认返回
        return "大专及以上学历"

    def _complete_major_required(self, job_info: Dict[str, Any]) -> str:
        """
        补全专业要求

        Args:
            job_info: 岗位信息

        Returns:
            str: 补全的专业要求
        """
        job_title = job_info.get("job_title", "")
        requirements = job_info.get("requirements", "")

        # 从要求中提取
        if requirements:
            if "专业" in requirements:
                sentences = requirements.split("\n")
                for sentence in sentences:
                    if "专业" in sentence:
                        return sentence.strip()

        # 根据职位推断
        if "软件" in job_title or "开发" in job_title or "程序" in job_title:
            return "计算机科学、软件工程等相关专业"
        elif "硬件" in job_title or "电子" in job_title:
            return "电子工程、通信工程等相关专业"
        elif "财务" in job_title or "会计" in job_title:
            return "会计学、财务管理等相关专业"
        elif "人力资源" in job_title or "HR" in job_title:
            return "人力资源管理、工商管理等相关专业"
        elif "市场" in job_title or "营销" in job_title:
            return "市场营销、广告学等相关专业"
        elif "设计" in job_title:
            return "艺术设计、工业设计等相关专业"

        # 默认返回
        return "相关专业"

    def _complete_experience_required(self, job_info: Dict[str, Any]) -> str:
        """
        补全经验要求

        Args:
            job_info: 岗位信息

        Returns:
            str: 补全的经验要求
        """
        job_title = job_info.get("job_title", "")
        requirements = job_info.get("requirements", "")

        # 从要求中提取
        if requirements:
            experience_patterns = [r"\d+年以上(?:相关)?(?:工作)?经验", r"应届生", r"经验不限"]
            for pattern in experience_patterns:
                import re
                matches = re.findall(pattern, requirements)
                if matches:
                    return matches[0]

        # 根据职位推断
        if "高级" in job_title or "资深" in job_title:
            return "5年以上相关工作经验"
        elif "经理" in job_title or "主管" in job_title:
            return "3年以上相关工作经验"
        elif "专员" in job_title or "助理" in job_title:
            return "1年以上相关工作经验"
        elif "实习" in job_title:
            return "经验不限，应届生优先"

        # 默认返回
        return "2年以上相关工作经验"

    def _complete_salary(self, job_info: Dict[str, Any]) -> str:
        """
        补全薪资待遇

        Args:
            job_info: 岗位信息

        Returns:
            str: 补全的薪资待遇
        """
        job_title = job_info.get("job_title", "")
        location = job_info.get("location", "")
        experience = job_info.get("experience_required", "")

        # 根据职位、地点和经验推断
        salary_range = ""

        # 基础薪资
        base_salary = 8000

        # 职位加成
        if "高级" in job_title or "资深" in job_title:
            base_salary += 7000
        elif "经理" in job_title or "主管" in job_title:
            base_salary += 10000
        elif "总监" in job_title or "总经理" in job_title:
            base_salary += 15000
        elif "专员" in job_title or "助理" in job_title:
            base_salary -= 2000

        # 地点加成
        if location in ["北京", "上海", "深圳", "杭州"]:
            base_salary += 3000
        elif location in ["广州", "南京", "成都", "武汉", "西安", "苏州"]:
            base_salary += 1000

        # 经验加成
        if "5年以上" in experience:
            base_salary += 5000
        elif "3年以上" in experience:
            base_salary += 3000
        elif "1年以上" in experience:
            base_salary += 1000

        # 计算薪资范围
        min_salary = max(base_salary - 2000, 4000)
        max_salary = base_salary + 5000

        # 格式化薪资范围
        if max_salary >= 20000:
            salary_range = f"{min_salary // 1000}K-{max_salary // 1000}K/月"
        else:
            salary_range = f"{min_salary}-{max_salary}元/月"

        return salary_range

    def _complete_generic_field(self, field: str, job_info: Dict[str, Any]) -> str:
        """
        通用字段补全方法

        Args:
            field: 字段名
            job_info: 岗位信息

        Returns:
            str: 补全的字段值
        """
        # 根据字段类型返回默认值
        if field == "job_type":
            return "全职"
        elif field == "job_category":
            job_title = job_info.get("job_title", "")
            if "开发" in job_title or "工程师" in job_title:
                return "技术/研发"
            elif "销售" in job_title:
                return "销售/客服"
            elif "市场" in job_title or "营销" in job_title:
                return "市场/营销"
            elif "财务" in job_title or "会计" in job_title:
                return "财务/审计"
            elif "人事" in job_title or "HR" in job_title:
                return "人力资源"
            else:
                return "其他"
        elif field == "department":
            job_title = job_info.get("job_title", "")
            if "开发" in job_title or "工程师" in job_title:
                return "技术部"
            elif "销售" in job_title:
                return "销售部"
            elif "市场" in job_title or "营销" in job_title:
                return "市场部"
            elif "财务" in job_title or "会计" in job_title:
                return "财务部"
            elif "人事" in job_title or "HR" in job_title:
                return "人力资源部"
            else:
                return "相关部门"
        elif field == "num_of_positions":
            return "若干"
        elif field == "posting_date":
            from datetime import datetime
            return datetime.now().strftime("%Y-%m-%d")
        elif field == "deadline":
            from datetime import datetime, timedelta
            return (datetime.now() + timedelta(days=30)).strftime("%Y-%m-%d")
        elif field == "benefits":
            return "五险一金、带薪年假、节日福利、定期体检、团队建设活动"
        elif field == "skills_required":
            job_title = job_info.get("job_title", "")
            if "开发" in job_title or "工程师" in job_title:
                return "熟悉常用开发工具和框架，具有良好的编码习惯和问题解决能力"
            elif "销售" in job_title:
                return "良好的沟通能力和谈判技巧，具有客户开发和维护经验"
            elif "市场" in job_title:
                return "熟悉市场营销策略和方法，具有良好的创意思维和执行力"
            else:
                return "具备相关专业知识和技能，有良好的沟通能力和团队合作精神"

        # 其他字段返回空字符串
        return ""
