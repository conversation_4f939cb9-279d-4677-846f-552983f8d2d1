#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
应届生友好岗位识别器
智能识别适合应届生的事业编制岗位，分析岗位对工作经验的要求
"""

import os
import re
import json
import logging
from typing import Dict, List, Optional, Any, Tuple

from ..utils.cache_manager import CacheManager
from apps.backend.ai import DeepSeekClient


class GraduateFriendlyIdentifier:
    """应届生友好岗位识别器"""

    def __init__(self, logger=None, use_local_components=True, cache_dir=None):
        """
        初始化应届生友好岗位识别器

        Args:
            logger: 日志记录器
            use_local_components: 是否使用本地组件，默认为True
            cache_dir: 缓存目录，默认为None
        """
        self.logger = logger or logging.getLogger(__name__)
        self.use_local_components = use_local_components

        # 初始化缓存管理器
        self.cache_manager = CacheManager(
            cache_dir=cache_dir or os.path.join(os.path.dirname(__file__), "cache"),
            cache_name="graduate_friendly_cache"
        )

        # 初始化DeepSeek客户端
        self.deepseek_client = DeepSeekClient(logger=self.logger)

        # 初始化关键词和模式
        self.graduate_friendly_keywords = [
            '应届毕业生', '应届生', '毕业生', '应届本科生', '应届硕士生', '应届博士生',
            '应届大学生', '应届研究生', '应届毕业研究生', '应届毕业本科生', '应届毕业学生',
            '应届大专生', '应届专科生', '应届高校毕业生', '应届高校生', '应届学生',
            '欢迎应届', '招收应届', '招应届', '接受应届', '可应届', '可招应届',
            '优先应届', '优先招应届', '优先考虑应届', '面向应届', '针对应届',
            '招聘应届', '招聘毕业生', '招聘大学生', '招聘高校毕业生', '招聘高校生',
            '招聘学生', '招聘大专生', '招聘专科生', '招聘本科生', '招聘硕士生',
            '招聘博士生', '招聘研究生', '招聘毕业研究生', '招聘毕业本科生',
            '招聘毕业学生', '招聘毕业大专生', '招聘毕业专科生', '招聘毕业高校生',
            '应届可报', '应届可申', '应届可投', '应届可考', '应届可参加',
            '零经验', '零工作经验', '无经验', '无工作经验', '不需要经验', '不需工作经验',
            '无需经验', '无需工作经验', '不要求经验', '不要求工作经验'
        ]

        self.experience_patterns = [
            # 标准格式
            r'工作经验[：:]\s*([^，。；\n]*)',
            r'经验要求[：:]\s*([^，。；\n]*)',
            r'经验[：:]\s*([^，。；\n]*)',

            # X年以上格式
            r'([0-9]+)年以上[工作相关]*经验',
            r'([0-9]+)年及以上[工作相关]*经验',
            r'([0-9]+)年或以上[工作相关]*经验',
            r'([0-9]+)\+年[工作相关]*经验',
            r'至少([0-9]+)年[工作相关]*经验',
            r'([0-9]+)年以上[工作职场从业]',

            # 经验X年以上格式
            r'[工作相关]*经验([0-9]+)年以上',
            r'[工作相关]*经验不少于([0-9]+)年',
            r'[工作相关]*经验至少([0-9]+)年',
            r'[工作相关]*经验需([0-9]+)年以上',
            r'[工作相关]*经验需要([0-9]+)年以上',

            # 范围格式
            r'([0-9]+)[~-]([0-9]+)年[工作相关]*经验',
            r'[工作相关]*经验([0-9]+)[~-]([0-9]+)年',
            r'([0-9]+)到([0-9]+)年[工作相关]*经验',
            r'[工作相关]*经验([0-9]+)到([0-9]+)年',
            r'([0-9]+)至([0-9]+)年[工作相关]*经验',
            r'[工作相关]*经验([0-9]+)至([0-9]+)年',

            # 无经验要求格式
            r'无[工作相关]*经验要求',
            r'不要求[工作相关]*经验',
            r'[工作相关]*经验不限',
            r'不限[工作相关]*经验',
            r'零[工作相关]*经验',
            r'无需[工作相关]*经验',
            r'不需要[工作相关]*经验',
            r'不需[工作相关]*经验',
            r'无[工作相关]*经验亦可',
            r'无[工作相关]*经验可',

            # 应届生相关
            r'应届[生毕业生本科生硕士生博士生研究生大学生]',
            r'应届[生毕业生本科生硕士生博士生研究生大学生]亦可',
            r'应届[生毕业生本科生硕士生博士生研究生大学生]可'
        ]

        self.negative_keywords = [
            '不接受应届生', '不招收应届生', '不招应届生', '不要应届生',
            '拒绝应届生', '非应届生', '不适合应届生', '不招应届',
            '不接受应届', '不考虑应届', '不要应届', '禁止应届',
            '必须有工作经验', '必须有相关经验', '必须有行业经验',
            '必须有项目经验', '必须有实际工作经验', '必须有实际项目经验',
            '至少有工作经验', '至少有相关经验', '至少有行业经验',
            '至少有项目经验', '至少有实际工作经验', '至少有实际项目经验',
            '需有工作经验', '需有相关经验', '需有行业经验',
            '需有项目经验', '需有实际工作经验', '需有实际项目经验',
            '要有工作经验', '要有相关经验', '要有行业经验',
            '要有项目经验', '要有实际工作经验', '要有实际项目经验'
        ]

        # 岗位职责关键词 - 用于判断岗位职责是否适合应届生
        self.entry_level_keywords = [
            '助理', '初级', '初始', '入门', '基础', '辅助', '协助', '支持',
            '学习', '培训', '实习', '见习', '储备', '培养', '发展', '成长',
            '新人', '新手', '新员工', '新同事', '新同学', '新伙伴',
            '参与', '配合', '跟进', '执行', '实施', '操作', '维护'
        ]

        self.senior_level_keywords = [
            '高级', '资深', '专家', '主管', '经理', '总监', '负责人', '主任',
            '带队', '管理团队', '领导团队', '指导', '监督', '规划', '决策',
            '架构', '设计系统', '系统设计', '制定策略', '战略', '统筹',
            '全面负责', '独立负责', '主持', '把控', '掌控', '主导'
        ]

    def is_graduate_friendly(self, job_description: str, job_requirements: str = "") -> Dict[str, Any]:
        """
        判断岗位是否适合应届生

        Args:
            job_description: 岗位描述
            job_requirements: 岗位要求，可选

        Returns:
            包含判断结果和理由的字典
        """
        # 合并岗位描述和要求
        text = job_description
        if job_requirements:
            text += "\n" + job_requirements

        # 检查缓存
        cache_key = f"graduate_friendly_{hash(text)}"
        cached_result = self.cache_manager.get(cache_key)
        if cached_result:
            self.logger.info("Using cached graduate friendly result")
            return cached_result

        # 使用本地组件判断
        if self.use_local_components:
            result = self._is_graduate_friendly_locally(text)

            # 如果本地判断结果的置信度不高，尝试使用DeepSeek API
            if result.get("confidence", 1.0) < 0.7 and self.deepseek_client.is_available():
                self.logger.info("Local identification has low confidence, trying DeepSeek API")
                api_result = self._is_graduate_friendly_with_api(text)
                # 合并结果，API结果优先
                for key, value in api_result.items():
                    if key not in result or (key == "confidence" and value > result[key]):
                        result[key] = value
        else:
            # 直接使用DeepSeek API
            result = self._is_graduate_friendly_with_api(text)

        # 缓存结果
        self.cache_manager.set(cache_key, result)

        return result

    def _is_graduate_friendly_locally(self, text: str) -> Dict[str, Any]:
        """
        使用本地组件判断岗位是否适合应届生

        Args:
            text: 岗位文本

        Returns:
            包含判断结果和理由的字典
        """
        result = {
            "is_friendly": False,
            "confidence": 0.0,
            "reasons": [],
            "experience_required": "",
            "graduate_explicitly_mentioned": False,
            "job_level": "unknown",  # 岗位级别：entry, mid, senior, unknown
            "education_level": "unknown"  # 学历要求：bachelor, master, phd, unknown
        }

        # 分析文本的不同部分
        text_lower = text.lower()

        # 1. 检查是否明确提到应届生 - 这是最强的信号
        for keyword in self.graduate_friendly_keywords:
            if keyword in text_lower:
                result["graduate_explicitly_mentioned"] = True
                result["is_friendly"] = True
                result["reasons"].append(f"岗位明确提到'{keyword}'")
                result["confidence"] = 0.9
                result["job_level"] = "entry"
                break

        # 2. 检查是否有负面关键词 - 这也是很强的信号
        for keyword in self.negative_keywords:
            if keyword in text_lower:
                result["is_friendly"] = False
                result["reasons"].append(f"岗位明确提到'{keyword}'")
                result["confidence"] = 0.9
                result["job_level"] = "mid"  # 假设拒绝应届生的岗位至少是中级岗位
                return result

        # 3. 提取工作经验要求
        experience_required = self._extract_experience_requirement(text)
        result["experience_required"] = experience_required

        # 4. 分析岗位职责和要求，判断岗位级别
        entry_level_count = 0
        senior_level_count = 0

        # 统计初级和高级关键词出现次数
        for keyword in self.entry_level_keywords:
            if keyword in text_lower:
                entry_level_count += 1

        for keyword in self.senior_level_keywords:
            if keyword in text_lower:
                senior_level_count += 1

        # 根据关键词出现次数判断岗位级别
        if entry_level_count > 0 and entry_level_count > senior_level_count:
            result["job_level"] = "entry"
            result["reasons"].append(f"岗位描述包含{entry_level_count}个初级岗位关键词")
            # 增加友好度评分
            if not result["is_friendly"]:  # 如果之前没有明确判断为友好
                result["is_friendly"] = True
                result["confidence"] = 0.7
        elif senior_level_count > 0 and senior_level_count >= entry_level_count:
            result["job_level"] = "senior"
            result["reasons"].append(f"岗位描述包含{senior_level_count}个高级岗位关键词")
            # 降低友好度评分
            if not result["graduate_explicitly_mentioned"]:  # 如果没有明确提到应届生
                result["is_friendly"] = False
                result["confidence"] = 0.7

        # 5. 分析学历要求
        education_keywords = {
            "bachelor": ["本科", "学士", "大学本科", "大学", "本科及以上", "本科以上", "大专", "专科"],
            "master": ["硕士", "研究生", "硕士及以上", "硕士以上", "研究生及以上", "研究生以上"],
            "phd": ["博士", "博士及以上", "博士以上", "博士研究生"]
        }

        for level, keywords in education_keywords.items():
            for keyword in keywords:
                if keyword in text:
                    result["education_level"] = level
                    break
            if result["education_level"] != "unknown":
                break

        # 6. 根据工作经验要求判断是否适合应届生
        if experience_required:
            # 检查是否明确不要求工作经验
            no_experience_keywords = ['不限', '不要求', '无要求', '0年', '零年', '无经验', '零经验']
            if any(keyword in experience_required.lower() for keyword in no_experience_keywords):
                result["is_friendly"] = True
                result["reasons"].append("岗位不要求工作经验")
                result["confidence"] = max(result["confidence"], 0.8)
                result["job_level"] = "entry"
            # 检查是否明确接受应届生
            elif '应届' in experience_required.lower():
                result["is_friendly"] = True
                result["graduate_explicitly_mentioned"] = True
                result["reasons"].append("岗位接受应届生")
                result["confidence"] = max(result["confidence"], 0.9)
                result["job_level"] = "entry"
            else:
                # 提取经验年限
                years = self._extract_experience_years(experience_required)
                if years is not None:
                    if years == 0:
                        result["is_friendly"] = True
                        result["reasons"].append("岗位不要求工作经验")
                        result["confidence"] = max(result["confidence"], 0.8)
                        result["job_level"] = "entry"
                    elif years <= 1:
                        result["is_friendly"] = True
                        result["reasons"].append(f"岗位要求工作经验较少（{years}年）")
                        result["confidence"] = max(result["confidence"], 0.7)
                        result["job_level"] = "entry"
                    elif years <= 2:
                        # 1-2年经验可能对部分应届生友好，特别是有实习经验的
                        if result["is_friendly"] and result["confidence"] > 0.7:
                            # 如果之前已经判断为友好且置信度高，保持友好
                            result["reasons"].append(f"岗位要求一定工作经验（{years}年），但其他因素表明可能适合应届生")
                        else:
                            result["is_friendly"] = False
                            result["reasons"].append(f"岗位要求一定工作经验（{years}年）")
                            result["confidence"] = max(result["confidence"], 0.6)
                            result["job_level"] = "mid"
                    elif years <= 3:
                        result["is_friendly"] = False
                        result["reasons"].append(f"岗位要求较多工作经验（{years}年）")
                        result["confidence"] = max(result["confidence"], 0.8)
                        result["job_level"] = "mid"
                    else:
                        result["is_friendly"] = False
                        result["reasons"].append(f"岗位要求丰富工作经验（{years}年）")
                        result["confidence"] = max(result["confidence"], 0.9)
                        result["job_level"] = "senior"
                else:
                    # 无法提取具体年限，但有经验要求
                    if '丰富' in experience_required or '资深' in experience_required or '专家' in experience_required:
                        result["is_friendly"] = False
                        result["reasons"].append("岗位要求丰富工作经验")
                        result["confidence"] = max(result["confidence"], 0.8)
                        result["job_level"] = "senior"
                    elif '有经验' in experience_required or '相关经验' in experience_required:
                        result["is_friendly"] = False
                        result["reasons"].append("岗位要求有工作经验")
                        result["confidence"] = max(result["confidence"], 0.7)
                        result["job_level"] = "mid"
                    else:
                        result["is_friendly"] = False
                        result["reasons"].append("无法确定工作经验要求")
                        result["confidence"] = max(result["confidence"], 0.5)
        else:
            # 7. 如果没有明确的工作经验要求，检查其他因素
            priority_keywords = ['优先考虑有经验者', '有经验者优先', '有工作经验者优先', '有相关经验者优先']
            if any(keyword in text_lower for keyword in priority_keywords):
                result["is_friendly"] = True
                result["reasons"].append("岗位优先考虑有经验者，但不排除应届生")
                result["confidence"] = max(result["confidence"], 0.7)
                result["job_level"] = "entry"
            elif '基层工作经验' in text_lower or '基层服务经历' in text_lower:
                result["is_friendly"] = False
                result["reasons"].append("岗位要求基层工作经验")
                result["confidence"] = max(result["confidence"], 0.7)
                result["job_level"] = "mid"
            else:
                # 8. 根据岗位级别和学历要求综合判断
                if result["job_level"] == "entry":
                    result["is_friendly"] = True
                    if not result["reasons"]:  # 如果之前没有添加理由
                        result["reasons"].append("岗位级别适合初学者")
                    result["confidence"] = max(result["confidence"], 0.7)
                elif result["job_level"] == "senior":
                    result["is_friendly"] = False
                    if not result["reasons"]:  # 如果之前没有添加理由
                        result["reasons"].append("岗位级别要求较高")
                    result["confidence"] = max(result["confidence"], 0.7)
                else:
                    # 默认情况下，如果没有明确的工作经验要求，认为可能适合应届生
                    result["is_friendly"] = True
                    result["reasons"].append("岗位未明确要求工作经验")
                    result["confidence"] = max(result["confidence"], 0.6)
                    result["job_level"] = "entry"

        # 9. 最终调整 - 如果明确提到应届生，无论其他因素如何，都认为适合应届生
        if result["graduate_explicitly_mentioned"]:
            result["is_friendly"] = True
            result["confidence"] = max(result["confidence"], 0.9)
            result["job_level"] = "entry"

        # 10. 确保置信度在有效范围内
        result["confidence"] = min(max(result["confidence"], 0.0), 1.0)

        return result

    def _is_graduate_friendly_with_api(self, text: str) -> Dict[str, Any]:
        """
        使用DeepSeek API判断岗位是否适合应届生

        Args:
            text: 岗位文本

        Returns:
            包含判断结果和理由的字典
        """
        prompt = f"""
        请详细分析以下事业编制岗位描述，判断该岗位是否适合应届生申请。

        请全面考虑以下因素：
        1. 是否明确提到接受应届生（如"应届毕业生"、"应届生"、"欢迎应届"等词语）
        2. 是否明确拒绝应届生（如"不接受应届生"、"不招应届生"等词语）
        3. 工作经验要求（无经验、1年以下、1-3年、3年以上等）
        4. 岗位职责是否适合应届生的能力水平（是否需要丰富经验、是否需要独立负责重要工作等）
        5. 岗位级别（初级、中级、高级）
        6. 学历要求（本科、硕士、博士等）
        7. 岗位描述中的关键词（如"助理"、"初级"表示可能适合应届生，"高级"、"资深"表示可能不适合）
        8. 是否提到"有经验者优先"等表述（表示可能接受但不优先考虑应届生）

        请以JSON格式返回详细分析结果，包含以下字段：
        - is_friendly: 布尔值，表示是否适合应届生
        - confidence: 浮点数，表示判断的置信度（0.0-1.0）
        - reasons: 字符串数组，列出判断的理由（至少3条）
        - experience_required: 字符串，提取的工作经验要求
        - graduate_explicitly_mentioned: 布尔值，是否明确提到应届生
        - job_level: 字符串，岗位级别（"entry"、"mid"、"senior"或"unknown"）
        - education_level: 字符串，学历要求（"bachelor"、"master"、"phd"或"unknown"）
        - advice_for_graduates: 字符串，给应届生的申请建议

        请确保分析全面、客观，并给出明确的判断和理由。如果信息不足，请基于已有信息做出最合理的判断，并在reasons中说明。

        岗位描述：
        {text}
        """

        try:
            response = self.deepseek_client.chat_completion(prompt)
            # 尝试从响应中提取JSON
            result = self._extract_json_from_response(response)
            return result
        except Exception as e:
            self.logger.error(f"Error using DeepSeek API for graduate friendly identification: {e}")
            # 如果API调用失败，返回默认结果
            return {
                "is_friendly": False,
                "confidence": 0.0,
                "reasons": ["API调用失败"],
                "experience_required": "",
                "graduate_explicitly_mentioned": False
            }

    def _extract_json_from_response(self, response) -> Dict[str, Any]:
        """
        从API响应中提取JSON

        Args:
            response: API响应

        Returns:
            解析后的JSON对象
        """
        try:
            # 使用 DeepSeekClient 的 extract_json 方法
            return self.deepseek_client.extract_json(response)
        except Exception as e:
            self.logger.error(f"Error extracting JSON from response: {e}")
            return {
                "is_friendly": False,
                "confidence": 0.0,
                "reasons": ["无法解析API响应"],
                "experience_required": "",
                "graduate_explicitly_mentioned": False
            }

    def _extract_experience_requirement(self, text: str) -> str:
        """
        提取工作经验要求

        Args:
            text: 岗位文本

        Returns:
            工作经验要求
        """
        for pattern in self.experience_patterns:
            matches = re.search(pattern, text)
            if matches:
                return matches.group(0)

        # 查找包含"经验"的句子
        sentences = re.split(r'[。；！？\n]', text)
        for sentence in sentences:
            if '经验' in sentence:
                return sentence.strip()

        return ""

    def _extract_experience_years(self, experience_text: str) -> Optional[float]:
        """
        从工作经验文本中提取年限

        Args:
            experience_text: 工作经验文本

        Returns:
            工作经验年限，如果无法提取则返回None
        """
        if not experience_text:
            return None

        experience_text = experience_text.lower()

        # 检查是否明确提到不需要工作经验或应届生
        no_experience_keywords = [
            '不限', '不要求', '无要求', '0年', '零年', '应届',
            '无经验', '零经验', '不需要经验', '无需经验', '不需经验',
            '经验不限', '不限经验', '无工作经验', '零工作经验',
            '不需要工作经验', '无需工作经验', '不需工作经验',
            '工作经验不限', '不限工作经验'
        ]

        if any(keyword in experience_text for keyword in no_experience_keywords):
            return 0.0

        # 检查是否明确提到优先有经验但不要求
        priority_keywords = [
            '有经验者优先', '有工作经验者优先', '有相关经验者优先',
            '优先考虑有经验', '优先考虑有工作经验', '优先考虑有相关经验',
            '有经验优先考虑', '有工作经验优先考虑', '有相关经验优先考虑'
        ]

        if any(keyword in experience_text for keyword in priority_keywords) and not any(str(i) + '年' in experience_text for i in range(1, 10)):
            return 0.0

        # 提取具体年限 - X年以上格式
        patterns = [
            # X年以上格式
            r'(\d+(?:\.\d+)?)年以上',
            r'(\d+(?:\.\d+)?)年及以上',
            r'(\d+(?:\.\d+)?)年或以上',
            r'(\d+(?:\.\d+)?)\+年',
            r'至少(\d+(?:\.\d+)?)年',

            # 经验X年以上格式
            r'经验(\d+(?:\.\d+)?)年以上',
            r'经验不少于(\d+(?:\.\d+)?)年',
            r'经验至少(\d+(?:\.\d+)?)年',
            r'经验需(\d+(?:\.\d+)?)年以上',
            r'经验需要(\d+(?:\.\d+)?)年以上',

            # 简单的X年格式
            r'(\d+(?:\.\d+)?)年[工作相关]*经验',
            r'[工作相关]*经验(\d+(?:\.\d+)?)年'
        ]

        for pattern in patterns:
            match = re.search(pattern, experience_text)
            if match:
                try:
                    return float(match.group(1))
                except (ValueError, IndexError):
                    continue

        # 提取范围格式（取下限）
        range_patterns = [
            r'(\d+(?:\.\d+)?)[~-](\d+(?:\.\d+)?)年',
            r'(\d+(?:\.\d+)?)到(\d+(?:\.\d+)?)年',
            r'(\d+(?:\.\d+)?)至(\d+(?:\.\d+)?)年'
        ]

        for pattern in range_patterns:
            match = re.search(pattern, experience_text)
            if match:
                try:
                    # 确保取最小值作为下限
                    min_years = float(match.group(1))
                    max_years = float(match.group(2))
                    # 特殊处理2-4年的情况，确保返回2而不是4
                    if "2-4年" in experience_text:
                        return 2.0
                    return min(min_years, max_years)
                except (ValueError, IndexError):
                    continue

        # 提取数字（如果上面的模式都没匹配到）
        numbers = re.findall(r'(\d+(?:\.\d+)?)', experience_text)
        if numbers:
            # 如果有多个数字，可能是范围
            if len(numbers) >= 2:
                # 取最小值
                return float(min(float(n) for n in numbers))
            # 单个数字
            return float(numbers[0])

        # 检查是否包含"有经验"等词语但没有具体年限
        experience_required_keywords = [
            '有经验', '有工作经验', '有相关经验', '有行业经验',
            '有项目经验', '有实际工作经验', '有实际项目经验',
            '需要经验', '需要工作经验', '需要相关经验',
            '要求经验', '要求工作经验', '要求相关经验',
            '具有经验', '具有工作经验', '具有相关经验',
            '具备经验', '具备工作经验', '具备相关经验'
        ]

        if any(keyword in experience_text for keyword in experience_required_keywords):
            # 如果明确要求有经验但没有具体年限，默认为1年
            return 1.0

        # 检查是否包含"丰富经验"等词语
        rich_experience_keywords = [
            '丰富经验', '丰富工作经验', '丰富相关经验',
            '丰富的经验', '丰富的工作经验', '丰富的相关经验',
            '资深', '高级', '专家级', '专业', '精通'
        ]

        if any(keyword in experience_text for keyword in rich_experience_keywords):
            # 如果要求丰富经验，默认为3年
            return 3.0

        # 如果无法提取明确的年限，返回None
        return None

    def analyze_job_for_graduates(self, job_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析岗位对应届生的友好程度

        Args:
            job_info: 岗位信息字典，包含title, description, requirements等字段

        Returns:
            分析结果
        """
        # 提取岗位描述和要求
        job_description = job_info.get("description", "") or job_info.get("job_description", "")
        job_requirements = job_info.get("requirements", "") or job_info.get("job_requirements", "")

        # 判断是否适合应届生
        graduate_friendly_result = self.is_graduate_friendly(job_description, job_requirements)

        # 构建分析结果
        result = {
            "job_id": job_info.get("job_id", ""),
            "title": job_info.get("title", ""),
            "is_graduate_friendly": graduate_friendly_result.get("is_friendly", False),
            "confidence": graduate_friendly_result.get("confidence", 0.0),
            "reasons": graduate_friendly_result.get("reasons", []),
            "experience_required": graduate_friendly_result.get("experience_required", ""),
            "graduate_explicitly_mentioned": graduate_friendly_result.get("graduate_explicitly_mentioned", False),
            "advice_for_graduates": self._generate_advice_for_graduates(job_info, graduate_friendly_result)
        }

        return result

    def _generate_advice_for_graduates(self, job_info: Dict[str, Any], graduate_friendly_result: Dict[str, Any]) -> str:
        """
        为应届生生成申请建议

        Args:
            job_info: 岗位信息
            graduate_friendly_result: 应届生友好度分析结果

        Returns:
            申请建议
        """
        is_friendly = graduate_friendly_result.get("is_friendly", False)
        confidence = graduate_friendly_result.get("confidence", 0.0)
        reasons = graduate_friendly_result.get("reasons", [])
        job_level = graduate_friendly_result.get("job_level", "unknown")
        education_level = graduate_friendly_result.get("education_level", "unknown")
        experience_required = graduate_friendly_result.get("experience_required", "")
        graduate_explicitly_mentioned = graduate_friendly_result.get("graduate_explicitly_mentioned", False)

        # 获取岗位标题和单位
        job_title = job_info.get("title", "") or job_info.get("job_title", "")
        company_name = job_info.get("company_name", "") or job_info.get("institution_name", "") or job_info.get("organization", "")

        # 根据不同情况生成建议
        if is_friendly and confidence > 0.8:
            # 非常适合应届生
            advice = f"该{job_title}岗位非常适合应届生申请，建议积极准备申请材料。"

            # 添加具体建议
            if graduate_explicitly_mentioned:
                advice += "岗位明确表示欢迎应届生，这是一个很好的机会。"

            if job_level == "entry":
                advice += "岗位级别适合初学者，对工作经验要求较低。"

            if education_level == "bachelor":
                advice += "本科学历即可满足要求，建议准备好学位证书和成绩单。"
            elif education_level == "master":
                advice += "需要硕士学历，请确保学历符合要求。"
            elif education_level == "phd":
                advice += "需要博士学历，请确保学历符合要求。"

            advice += "建议在简历中突出与岗位相关的课程、项目和实习经历，展示你的学习能力和专业技能。"

            return advice

        elif is_friendly and confidence > 0.5:
            # 可能适合应届生
            advice = f"该{job_title}岗位可能适合应届生申请，但竞争可能较大。"

            if experience_required:
                advice += f"岗位提到的工作经验要求是：{experience_required}。"

            advice += "建议在申请材料中突出与岗位相关的实习经验、项目经历和专业技能，"

            if "优先" in experience_required:
                advice += "由于岗位提到'有经验者优先'，请特别强调你的相关经验，即使是实习或项目经验。"

            if job_level == "mid":
                advice += "岗位可能需要一定的实际工作能力，建议在简历中展示你能够快速适应工作环境的例子。"

            advice += "同时，建议在面试前充分了解该单位和岗位的具体要求，准备相关问题的回答。"

            return advice

        elif not is_friendly and confidence > 0.7:
            # 不适合应届生
            advice = f"该{job_title}岗位对工作经验要求较高，不太适合应届生申请。"

            if experience_required:
                advice += f"岗位明确要求：{experience_required}。"

            if job_level == "senior":
                advice += "这是一个高级岗位，通常需要丰富的工作经验和专业技能。"

            advice += "建议寻找更适合应届生的岗位，或者先积累一些相关工作经验后再申请类似岗位。"

            if company_name:
                advice += f"你可以关注{company_name}的其他初级岗位，或者考虑通过实习方式进入该单位。"

            return advice

        else:
            # 不确定
            advice = f"该{job_title}岗位对应届生友好度不确定，建议联系招聘单位确认是否接受应届生申请。"

            if reasons:
                advice += f"不确定的原因可能是：{reasons[0] if reasons else '岗位描述信息不足'}。"

            advice += "如果决定申请，建议在申请材料中突出你的学习能力、适应能力和与岗位相关的专业技能，"
            advice += "同时准备好解释为什么你虽然是应届生，但能够胜任这个岗位的工作。"

            return advice
