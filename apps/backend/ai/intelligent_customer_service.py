#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
智能客服系统
"""

import time
import json
import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from enum import Enum
from sqlalchemy.orm import Session

from apps.backend.utils.logger import setup_logger
from apps.backend.ai.llm import LLMRouter
from apps.backend.config import LLM_CONFIG

logger = setup_logger(__name__)


class MessageType(Enum):
    """消息类型"""
    TEXT = "text"
    IMAGE = "image"
    FILE = "file"
    QUICK_REPLY = "quick_reply"
    CARD = "card"


class ConversationStatus(Enum):
    """对话状态"""
    ACTIVE = "active"
    WAITING = "waiting"
    RESOLVED = "resolved"
    ESCALATED = "escalated"
    CLOSED = "closed"


class IntentType(Enum):
    """意图类型"""
    JOB_SEARCH = "job_search"
    APPLICATION_STATUS = "application_status"
    ACCOUNT_ISSUE = "account_issue"
    TECHNICAL_SUPPORT = "technical_support"
    POLICY_INQUIRY = "policy_inquiry"
    COMPLAINT = "complaint"
    GENERAL_INQUIRY = "general_inquiry"
    GREETING = "greeting"
    GOODBYE = "goodbye"


@dataclass
class UserMessage:
    """用户消息"""
    message_id: str
    user_id: str
    content: str
    message_type: MessageType
    timestamp: datetime
    attachments: List[str] = None
    
    def __post_init__(self):
        if self.attachments is None:
            self.attachments = []


@dataclass
class BotResponse:
    """机器人响应"""
    response_id: str
    content: str
    message_type: MessageType
    intent: IntentType
    confidence: float
    quick_replies: List[str] = None
    cards: List[Dict[str, Any]] = None
    suggested_actions: List[str] = None
    
    def __post_init__(self):
        if self.quick_replies is None:
            self.quick_replies = []
        if self.cards is None:
            self.cards = []
        if self.suggested_actions is None:
            self.suggested_actions = []


@dataclass
class ConversationContext:
    """对话上下文"""
    conversation_id: str
    user_id: str
    status: ConversationStatus
    current_intent: Optional[IntentType]
    entities: Dict[str, Any]
    history: List[Dict[str, Any]]
    created_at: datetime
    updated_at: datetime
    
    # 用户信息
    user_profile: Dict[str, Any] = None
    
    # 对话状态
    waiting_for_input: bool = False
    escalation_reason: str = ""
    satisfaction_rating: Optional[int] = None
    
    def __post_init__(self):
        if self.user_profile is None:
            self.user_profile = {}
        if self.entities is None:
            self.entities = {}
        if self.history is None:
            self.history = []


class IntentClassifier:
    """意图识别器"""
    
    def __init__(self):
        self.logger = logger
        self.llm_router = LLMRouter(config=LLM_CONFIG)
        
        # 意图关键词映射
        self.intent_keywords = {
            IntentType.JOB_SEARCH: [
                "找工作", "招聘", "岗位", "职位", "求职", "工作机会", 
                "招聘信息", "职业", "就业", "面试"
            ],
            IntentType.APPLICATION_STATUS: [
                "申请状态", "申请进度", "投递", "简历", "面试结果", 
                "录用", "通知", "结果", "进展"
            ],
            IntentType.ACCOUNT_ISSUE: [
                "账号", "密码", "登录", "注册", "个人信息", "修改", 
                "忘记密码", "账户", "绑定", "解绑"
            ],
            IntentType.TECHNICAL_SUPPORT: [
                "技术问题", "系统", "bug", "错误", "故障", "无法", 
                "打不开", "卡顿", "崩溃", "异常"
            ],
            IntentType.POLICY_INQUIRY: [
                "政策", "规定", "要求", "条件", "资格", "标准", 
                "流程", "程序", "办法", "规则"
            ],
            IntentType.COMPLAINT: [
                "投诉", "举报", "不满", "问题", "建议", "意见", 
                "反馈", "抱怨", "差评"
            ],
            IntentType.GREETING: [
                "你好", "您好", "hi", "hello", "早上好", "下午好", 
                "晚上好", "在吗", "客服"
            ],
            IntentType.GOODBYE: [
                "再见", "拜拜", "谢谢", "结束", "没事了", "解决了", 
                "好的", "明白了"
            ]
        }
    
    def classify_intent(self, message: str, context: ConversationContext = None) -> Tuple[IntentType, float]:
        """
        分类用户意图
        
        Args:
            message: 用户消息
            context: 对话上下文
            
        Returns:
            Tuple[IntentType, float]: 意图类型和置信度
        """
        try:
            # 1. 基于关键词的快速分类
            keyword_intent, keyword_confidence = self._classify_by_keywords(message)
            
            # 2. 如果关键词置信度较高，直接返回
            if keyword_confidence > 0.8:
                return keyword_intent, keyword_confidence
            
            # 3. 使用LLM进行更精确的意图识别
            llm_intent, llm_confidence = self._classify_by_llm(message, context)
            
            # 4. 综合判断
            if llm_confidence > keyword_confidence:
                return llm_intent, llm_confidence
            else:
                return keyword_intent, keyword_confidence
                
        except Exception as e:
            self.logger.error(f"Error classifying intent: {e}")
            return IntentType.GENERAL_INQUIRY, 0.5
    
    def _classify_by_keywords(self, message: str) -> Tuple[IntentType, float]:
        """基于关键词分类意图"""
        message_lower = message.lower()
        intent_scores = {}
        
        for intent, keywords in self.intent_keywords.items():
            score = 0
            for keyword in keywords:
                if keyword in message_lower:
                    score += 1
            
            if score > 0:
                # 计算置信度
                confidence = min(score / len(keywords), 1.0)
                intent_scores[intent] = confidence
        
        if intent_scores:
            best_intent = max(intent_scores, key=intent_scores.get)
            return best_intent, intent_scores[best_intent]
        
        return IntentType.GENERAL_INQUIRY, 0.3
    
    def _classify_by_llm(self, message: str, context: ConversationContext = None) -> Tuple[IntentType, float]:
        """使用LLM分类意图"""
        try:
            # 构建上下文信息
            context_info = ""
            if context and context.history:
                recent_history = context.history[-3:]  # 最近3轮对话
                context_info = f"对话历史：{json.dumps(recent_history, ensure_ascii=False)}"
            
            prompt = f"""
            请分析用户消息的意图，从以下类别中选择最合适的一个：
            
            1. job_search - 求职相关（找工作、查看岗位等）
            2. application_status - 申请状态查询
            3. account_issue - 账号问题
            4. technical_support - 技术支持
            5. policy_inquiry - 政策咨询
            6. complaint - 投诉建议
            7. general_inquiry - 一般咨询
            8. greeting - 问候
            9. goodbye - 告别
            
            {context_info}
            
            用户消息：{message}
            
            请返回JSON格式：{{"intent": "意图类型", "confidence": 置信度(0-1)}}
            """
            
            response = self.llm_router.chat_completion(prompt, temperature=0.1)
            result = self.llm_router.extract_json(response)
            
            intent_str = result.get("intent", "general_inquiry")
            confidence = float(result.get("confidence", 0.5))
            
            # 转换为枚举类型
            try:
                intent = IntentType(intent_str)
            except ValueError:
                intent = IntentType.GENERAL_INQUIRY
                confidence = 0.5
            
            return intent, confidence
            
        except Exception as e:
            self.logger.error(f"Error in LLM intent classification: {e}")
            return IntentType.GENERAL_INQUIRY, 0.5


class EntityExtractor:
    """实体提取器"""
    
    def __init__(self):
        self.logger = logger
        self.llm_router = LLMRouter(config=LLM_CONFIG)
    
    def extract_entities(self, message: str, intent: IntentType) -> Dict[str, Any]:
        """
        提取消息中的实体
        
        Args:
            message: 用户消息
            intent: 意图类型
            
        Returns:
            Dict[str, Any]: 提取的实体
        """
        try:
            # 根据意图类型提取不同的实体
            if intent == IntentType.JOB_SEARCH:
                return self._extract_job_search_entities(message)
            elif intent == IntentType.APPLICATION_STATUS:
                return self._extract_application_entities(message)
            elif intent == IntentType.ACCOUNT_ISSUE:
                return self._extract_account_entities(message)
            else:
                return self._extract_general_entities(message)
                
        except Exception as e:
            self.logger.error(f"Error extracting entities: {e}")
            return {}
    
    def _extract_job_search_entities(self, message: str) -> Dict[str, Any]:
        """提取求职相关实体"""
        entities = {}
        
        # 地点提取
        location_patterns = [
            r'(北京|上海|广州|深圳|杭州|南京|武汉|成都|西安|重庆|天津|青岛|大连|厦门|苏州|无锡|宁波|长沙|郑州|济南|福州|合肥|昆明|南昌|太原|石家庄|哈尔滨|长春|沈阳|乌鲁木齐|兰州|银川|西宁|拉萨|呼和浩特|南宁|海口|贵阳)',
            r'(\w+市|\w+区|\w+县)'
        ]
        
        for pattern in location_patterns:
            matches = re.findall(pattern, message)
            if matches:
                entities['location'] = matches[0] if isinstance(matches[0], str) else matches[0][0]
                break
        
        # 职位类型提取
        job_patterns = [
            r'(软件工程师|产品经理|数据分析师|UI设计师|运营专员|市场专员|人事专员|财务专员|行政助理)',
            r'(\w+工程师|\w+经理|\w+专员|\w+助理|\w+主管|\w+总监)'
        ]
        
        for pattern in job_patterns:
            matches = re.findall(pattern, message)
            if matches:
                entities['job_title'] = matches[0] if isinstance(matches[0], str) else matches[0][0]
                break
        
        # 薪资提取
        salary_pattern = r'(\d+)k?[-~到](\d+)k?'
        salary_match = re.search(salary_pattern, message)
        if salary_match:
            entities['salary_range'] = {
                'min': int(salary_match.group(1)),
                'max': int(salary_match.group(2))
            }
        
        return entities
    
    def _extract_application_entities(self, message: str) -> Dict[str, Any]:
        """提取申请状态相关实体"""
        entities = {}
        
        # 申请ID提取
        id_pattern = r'(申请|投递|简历).*?(\d{6,})'
        id_match = re.search(id_pattern, message)
        if id_match:
            entities['application_id'] = id_match.group(2)
        
        # 公司名称提取
        company_pattern = r'(公司|企业|单位).*?([A-Za-z\u4e00-\u9fa5]+)'
        company_match = re.search(company_pattern, message)
        if company_match:
            entities['company'] = company_match.group(2)
        
        return entities
    
    def _extract_account_entities(self, message: str) -> Dict[str, Any]:
        """提取账号相关实体"""
        entities = {}
        
        # 手机号提取
        phone_pattern = r'1[3-9]\d{9}'
        phone_match = re.search(phone_pattern, message)
        if phone_match:
            entities['phone'] = phone_match.group()
        
        # 邮箱提取
        email_pattern = r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}'
        email_match = re.search(email_pattern, message)
        if email_match:
            entities['email'] = email_match.group()
        
        return entities
    
    def _extract_general_entities(self, message: str) -> Dict[str, Any]:
        """提取通用实体"""
        try:
            prompt = f"""
            请从以下用户消息中提取关键实体信息：
            
            用户消息：{message}
            
            请提取以下类型的实体（如果存在）：
            - 时间：日期、时间段等
            - 地点：城市、地区等
            - 人名：姓名等
            - 组织：公司、机构等
            - 数字：金额、数量等
            
            请返回JSON格式：{{"时间": [], "地点": [], "人名": [], "组织": [], "数字": []}}
            """
            
            response = self.llm_router.chat_completion(prompt, temperature=0.1)
            result = self.llm_router.extract_json(response)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error in general entity extraction: {e}")
            return {}


class ResponseGenerator:
    """响应生成器"""
    
    def __init__(self):
        self.logger = logger
        self.llm_router = LLMRouter(config=LLM_CONFIG)
        
        # 预定义响应模板
        self.response_templates = {
            IntentType.GREETING: [
                "您好！我是智能客服助手，很高兴为您服务。请问有什么可以帮助您的吗？",
                "您好！欢迎使用招聘平台，我可以帮您解答各种问题。",
                "您好！我是您的专属客服助手，有什么需要帮助的吗？"
            ],
            IntentType.GOODBYE: [
                "感谢您的咨询，祝您生活愉快！如有其他问题，随时联系我。",
                "再见！希望我的服务对您有帮助，期待下次为您服务。",
                "谢谢您的使用，祝您工作顺利！"
            ],
            IntentType.JOB_SEARCH: [
                "我来帮您查找合适的工作机会。请告诉我您期望的职位、地点和薪资要求。",
                "为了给您推荐最合适的岗位，请提供您的专业背景和工作经验。"
            ],
            IntentType.APPLICATION_STATUS: [
                "我来帮您查询申请状态。请提供您的申请编号或投递的岗位信息。",
                "请稍等，我为您查询申请进度..."
            ]
        }
    
    def generate_response(self, intent: IntentType, entities: Dict[str, Any], 
                         context: ConversationContext) -> BotResponse:
        """
        生成响应
        
        Args:
            intent: 用户意图
            entities: 提取的实体
            context: 对话上下文
            
        Returns:
            BotResponse: 机器人响应
        """
        try:
            if intent in self.response_templates:
                # 使用模板响应
                return self._generate_template_response(intent, entities, context)
            else:
                # 使用LLM生成响应
                return self._generate_llm_response(intent, entities, context)
                
        except Exception as e:
            self.logger.error(f"Error generating response: {e}")
            return BotResponse(
                response_id=f"resp_{int(time.time())}",
                content="抱歉，我遇到了一些技术问题。请稍后再试或联系人工客服。",
                message_type=MessageType.TEXT,
                intent=intent,
                confidence=0.5
            )
    
    def _generate_template_response(self, intent: IntentType, entities: Dict[str, Any], 
                                  context: ConversationContext) -> BotResponse:
        """生成模板响应"""
        import random
        
        templates = self.response_templates[intent]
        content = random.choice(templates)
        
        # 根据意图添加快速回复选项
        quick_replies = []
        if intent == IntentType.GREETING:
            quick_replies = ["找工作", "查询申请状态", "账号问题", "技术支持"]
        elif intent == IntentType.JOB_SEARCH:
            quick_replies = ["推荐岗位", "搜索条件", "薪资查询", "面试技巧"]
        
        return BotResponse(
            response_id=f"resp_{int(time.time())}",
            content=content,
            message_type=MessageType.TEXT,
            intent=intent,
            confidence=0.9,
            quick_replies=quick_replies
        )
    
    def _generate_llm_response(self, intent: IntentType, entities: Dict[str, Any], 
                             context: ConversationContext) -> BotResponse:
        """使用LLM生成响应"""
        try:
            # 构建上下文
            context_info = ""
            if context.history:
                recent_history = context.history[-3:]
                context_info = f"对话历史：{json.dumps(recent_history, ensure_ascii=False)}"
            
            user_info = ""
            if context.user_profile:
                user_info = f"用户信息：{json.dumps(context.user_profile, ensure_ascii=False)}"
            
            prompt = f"""
            你是一个专业的招聘平台客服助手，请根据用户意图和提取的实体信息生成合适的响应。
            
            用户意图：{intent.value}
            提取的实体：{json.dumps(entities, ensure_ascii=False)}
            {context_info}
            {user_info}
            
            请生成一个友好、专业、有帮助的响应，并提供相关的快速回复选项。
            
            返回JSON格式：
            {{
                "content": "响应内容",
                "quick_replies": ["快速回复选项1", "快速回复选项2"],
                "suggested_actions": ["建议操作1", "建议操作2"]
            }}
            """
            
            response = self.llm_router.chat_completion(prompt, temperature=0.7)
            result = self.llm_router.extract_json(response)
            
            return BotResponse(
                response_id=f"resp_{int(time.time())}",
                content=result.get("content", "我会尽力帮助您解决问题。"),
                message_type=MessageType.TEXT,
                intent=intent,
                confidence=0.8,
                quick_replies=result.get("quick_replies", []),
                suggested_actions=result.get("suggested_actions", [])
            )
            
        except Exception as e:
            self.logger.error(f"Error in LLM response generation: {e}")
            return BotResponse(
                response_id=f"resp_{int(time.time())}",
                content="我理解您的问题，让我为您查找相关信息...",
                message_type=MessageType.TEXT,
                intent=intent,
                confidence=0.5
            )


class IntelligentCustomerService:
    """智能客服系统主类"""
    
    def __init__(self):
        self.logger = logger
        self.intent_classifier = IntentClassifier()
        self.entity_extractor = EntityExtractor()
        self.response_generator = ResponseGenerator()
        
        # 对话管理
        self.active_conversations: Dict[str, ConversationContext] = {}
        
        # 配置
        self.config = {
            "max_conversation_duration": 3600,  # 1小时
            "escalation_threshold": 3,  # 连续3次无法解决问题时转人工
            "confidence_threshold": 0.6,  # 置信度阈值
        }
    
    def process_message(self, user_message: UserMessage, db: Session = None) -> BotResponse:
        """
        处理用户消息
        
        Args:
            user_message: 用户消息
            db: 数据库会话
            
        Returns:
            BotResponse: 机器人响应
        """
        try:
            # 1. 获取或创建对话上下文
            context = self._get_or_create_context(user_message.user_id)
            
            # 2. 更新对话历史
            self._update_conversation_history(context, user_message)
            
            # 3. 意图识别
            intent, confidence = self.intent_classifier.classify_intent(
                user_message.content, context
            )
            
            # 4. 实体提取
            entities = self.entity_extractor.extract_entities(
                user_message.content, intent
            )
            
            # 5. 更新上下文
            context.current_intent = intent
            context.entities.update(entities)
            context.updated_at = datetime.now()
            
            # 6. 生成响应
            response = self.response_generator.generate_response(
                intent, entities, context
            )
            
            # 7. 更新对话历史
            self._update_conversation_history(context, None, response)
            
            # 8. 检查是否需要转人工
            if confidence < self.config["confidence_threshold"]:
                response = self._handle_low_confidence(response, context)
            
            return response
            
        except Exception as e:
            self.logger.error(f"Error processing message: {e}")
            return BotResponse(
                response_id=f"resp_{int(time.time())}",
                content="抱歉，系统出现了问题。请稍后再试或联系人工客服。",
                message_type=MessageType.TEXT,
                intent=IntentType.GENERAL_INQUIRY,
                confidence=0.5
            )
    
    def _get_or_create_context(self, user_id: str) -> ConversationContext:
        """获取或创建对话上下文"""
        if user_id not in self.active_conversations:
            self.active_conversations[user_id] = ConversationContext(
                conversation_id=f"conv_{user_id}_{int(time.time())}",
                user_id=user_id,
                status=ConversationStatus.ACTIVE,
                current_intent=None,
                entities={},
                history=[],
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
        
        return self.active_conversations[user_id]
    
    def _update_conversation_history(self, context: ConversationContext, 
                                   user_message: UserMessage = None, 
                                   bot_response: BotResponse = None):
        """更新对话历史"""
        if user_message:
            context.history.append({
                "type": "user",
                "content": user_message.content,
                "timestamp": user_message.timestamp.isoformat()
            })
        
        if bot_response:
            context.history.append({
                "type": "bot",
                "content": bot_response.content,
                "intent": bot_response.intent.value,
                "confidence": bot_response.confidence,
                "timestamp": datetime.now().isoformat()
            })
        
        # 限制历史记录长度
        if len(context.history) > 20:
            context.history = context.history[-20:]
    
    def _handle_low_confidence(self, response: BotResponse, 
                             context: ConversationContext) -> BotResponse:
        """处理低置信度响应"""
        # 增加转人工的建议
        response.suggested_actions.append("转人工客服")
        response.content += "\n\n如果我的回答没有解决您的问题，您可以选择转接人工客服。"
        
        return response
    
    def escalate_to_human(self, user_id: str, reason: str = "") -> bool:
        """转接人工客服"""
        try:
            if user_id in self.active_conversations:
                context = self.active_conversations[user_id]
                context.status = ConversationStatus.ESCALATED
                context.escalation_reason = reason
                context.updated_at = datetime.now()
                
                self.logger.info(f"Conversation {context.conversation_id} escalated to human: {reason}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error escalating conversation: {e}")
            return False
    
    def end_conversation(self, user_id: str, satisfaction_rating: int = None) -> bool:
        """结束对话"""
        try:
            if user_id in self.active_conversations:
                context = self.active_conversations[user_id]
                context.status = ConversationStatus.CLOSED
                context.satisfaction_rating = satisfaction_rating
                context.updated_at = datetime.now()
                
                # 移除活跃对话
                del self.active_conversations[user_id]
                
                self.logger.info(f"Conversation {context.conversation_id} ended")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error ending conversation: {e}")
            return False
    
    def get_conversation_stats(self) -> Dict[str, Any]:
        """获取对话统计"""
        try:
            total_conversations = len(self.active_conversations)
            status_counts = {}
            
            for context in self.active_conversations.values():
                status = context.status.value
                status_counts[status] = status_counts.get(status, 0) + 1
            
            return {
                "total_active_conversations": total_conversations,
                "status_distribution": status_counts,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error getting conversation stats: {e}")
            return {}


# 全局智能客服实例
intelligent_customer_service = IntelligentCustomerService()
