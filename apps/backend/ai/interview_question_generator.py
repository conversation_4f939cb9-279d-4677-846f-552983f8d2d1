#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
面试问题生成器
提供本地面试问题生成功能，减少对外部API的依赖
"""

import os
import json
import logging
import re
import random
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import pickle


class InterviewQuestionGenerator:
    """面试问题生成器，提供本地面试问题生成功能"""

    # 通用面试问题模板
    GENERAL_QUESTION_TEMPLATES = [
        "请简单介绍一下你自己",
        "你为什么选择应聘这个岗位？",
        "你认为你的优势是什么？",
        "你的职业规划是什么？",
        "你如何看待加班？",
        "你对我们单位/公司了解多少？",
        "你期望的薪资是多少？",
        "你能接受出差吗？",
        "你有什么问题想问我们吗？",
        "你为什么离开上一家单位/公司？",
        "你如何处理工作中的压力？",
        "你如何看待团队合作？",
        "你认为自己的不足是什么？",
        "你如何看待这个行业的发展趋势？",
        "你为什么选择这个专业？"
    ]

    # 岗位类型关键词映射
    JOB_TYPE_KEYWORDS = {
        "技术类": ["工程师", "开发", "程序", "技术", "研发", "算法", "架构", "测试", "运维", "数据", "网络", "系统"],
        "管理类": ["经理", "主管", "总监", "负责人", "管理", "leader", "组长", "项目经理"],
        "市场营销类": ["市场", "营销", "销售", "推广", "客户", "商务", "公关", "策划"],
        "行政人事类": ["行政", "人事", "人力资源", "HR", "招聘", "培训", "绩效", "薪酬"],
        "财务类": ["财务", "会计", "审计", "税务", "金融", "投资", "理财", "预算"],
        "教育类": ["教师", "教授", "讲师", "辅导", "教育", "培训", "教学"],
        "医疗类": ["医生", "护士", "药剂师", "医疗", "临床", "护理", "康复"],
        "法律类": ["法律", "律师", "法务", "合规", "知识产权", "合同"],
        "设计类": ["设计", "UI", "UX", "美工", "创意", "平面", "交互"],
        "研究类": ["研究", "科研", "实验", "分析", "调研"]
    }

    # 专业类型关键词映射
    MAJOR_TYPE_KEYWORDS = {
        "计算机类": ["计算机", "软件", "网络", "信息", "数据", "人工智能", "网络安全"],
        "工程类": ["工程", "机械", "电子", "电气", "土木", "化工", "材料"],
        "理学类": ["数学", "物理", "化学", "生物", "地理", "天文", "统计"],
        "医学类": ["医学", "临床", "护理", "药学", "公共卫生", "中医", "口腔"],
        "经济管理类": ["经济", "管理", "金融", "会计", "市场", "贸易", "物流"],
        "文学类": ["文学", "汉语", "外语", "新闻", "传播", "编辑", "出版"],
        "法学类": ["法学", "政治", "行政", "社会", "公安", "国际关系"],
        "教育类": ["教育", "心理", "学前教育", "特殊教育", "教育技术"],
        "艺术类": ["艺术", "音乐", "美术", "设计", "表演", "戏剧", "舞蹈"],
        "农学类": ["农学", "林学", "园艺", "畜牧", "水产", "农业工程"]
    }

    # 专业问题模板
    PROFESSIONAL_QUESTION_TEMPLATES = {
        "计算机类": [
            "请介绍一下你熟悉的编程语言及其特点",
            "你如何理解面向对象编程？",
            "请描述一下你参与过的项目架构",
            "你如何处理高并发场景？",
            "你如何保证代码质量？",
            "你如何进行性能优化？",
            "你如何理解数据库索引？",
            "你如何处理网络安全问题？",
            "你如何看待前端和后端的关系？",
            "你如何进行代码版本控制？"
        ],
        "工程类": [
            "请介绍一下你参与过的工程项目",
            "你如何确保工程质量？",
            "你如何处理工程中的安全问题？",
            "你如何进行工程成本控制？",
            "你如何看待工程创新？",
            "你如何处理工程中的突发问题？",
            "你如何进行工程风险评估？",
            "你如何看待工程标准化？",
            "你如何进行工程团队管理？",
            "你如何看待工程环保问题？"
        ],
        # 其他专业类型的问题模板...
    }

    # 准备建议模板
    PREPARATION_TIP_TEMPLATES = [
        "提前了解单位/公司的背景、文化和业务",
        "准备具体的项目经验和成果案例",
        "准备针对岗位要求的专业知识点",
        "准备一些关于行业趋势的见解",
        "准备一些关于自己职业规划的思考",
        "准备一些有深度的问题来提问面试官",
        "提前模拟面试，练习回答问题",
        "准备一些应对压力面试的策略",
        "了解单位/公司的最新动态和新闻",
        "准备一些展示自己软技能的例子"
    ]

    def __init__(self, logger: Optional[logging.Logger] = None, cache_dir: Optional[str] = None):
        """
        初始化面试问题生成器

        Args:
            logger: 日志记录器
            cache_dir: 缓存目录，默认为.cache
        """
        self.logger = logger or logging.getLogger(self.__class__.__name__)

        # 设置缓存目录
        if cache_dir is None:
            project_root = os.path.abspath(os.path.join(
                os.path.dirname(__file__), '..', '..'
            ))
            self.cache_dir = os.path.join(project_root, ".cache")
        else:
            self.cache_dir = cache_dir

        # 确保缓存目录存在
        os.makedirs(self.cache_dir, exist_ok=True)

        # 缓存文件路径
        self.cache_path = os.path.join(self.cache_dir, "interview_questions_cache.pkl")

        # 初始化缓存
        self.cache: Dict[str, Dict[str, Any]] = self._load_cache()

        # 加载专业问题模板
        self._load_professional_templates()

    def _load_cache(self) -> Dict[str, Dict[str, Any]]:
        """
        加载缓存

        Returns:
            Dict[str, Dict[str, Any]]: 缓存数据
        """
        try:
            if os.path.exists(self.cache_path):
                with open(self.cache_path, 'rb') as f:
                    cache = pickle.load(f)
                self.logger.info(f"Loaded {len(cache)} cache entries from {self.cache_path}")
                return cache
            else:
                self.logger.info(f"Cache file not found, creating new cache: {self.cache_path}")
                return {}
        except Exception as e:
            self.logger.error(f"Error loading cache: {e}")
            return {}

    def _save_cache(self) -> bool:
        """
        保存缓存

        Returns:
            bool: 是否成功保存
        """
        try:
            with open(self.cache_path, 'wb') as f:
                pickle.dump(self.cache, f)
            self.logger.info(f"Saved {len(self.cache)} cache entries to {self.cache_path}")
            return True
        except Exception as e:
            self.logger.error(f"Error saving cache: {e}")
            return False

    def _load_professional_templates(self):
        """加载专业问题模板"""
        # 这里可以从文件加载更多专业问题模板
        # 目前使用内置模板
        pass

    def _detect_job_type(self, job_description: str) -> str:
        """
        检测岗位类型

        Args:
            job_description: 岗位描述

        Returns:
            str: 岗位类型
        """
        job_type_scores = {}
        
        # 计算各类型的关键词匹配分数
        for job_type, keywords in self.JOB_TYPE_KEYWORDS.items():
            score = 0
            for keyword in keywords:
                if keyword in job_description:
                    score += 1
            job_type_scores[job_type] = score
        
        # 返回得分最高的类型
        if not job_type_scores:
            return "通用类"
            
        max_score = max(job_type_scores.values())
        if max_score == 0:
            return "通用类"
            
        # 如果有多个最高分，随机选择一个
        max_types = [jt for jt, score in job_type_scores.items() if score == max_score]
        return random.choice(max_types)

    def _detect_major_type(self, job_description: str) -> str:
        """
        检测专业类型

        Args:
            job_description: 岗位描述

        Returns:
            str: 专业类型
        """
        major_type_scores = {}
        
        # 计算各类型的关键词匹配分数
        for major_type, keywords in self.MAJOR_TYPE_KEYWORDS.items():
            score = 0
            for keyword in keywords:
                if keyword in job_description:
                    score += 1
            major_type_scores[major_type] = score
        
        # 返回得分最高的类型
        if not major_type_scores:
            return "通用类"
            
        max_score = max(major_type_scores.values())
        if max_score == 0:
            return "通用类"
            
        # 如果有多个最高分，随机选择一个
        max_types = [mt for mt, score in major_type_scores.items() if score == max_score]
        return random.choice(max_types)

    def generate_interview_questions(self, job_description: str, user_background: str = "") -> Dict[str, Any]:
        """
        生成面试问题

        Args:
            job_description: 岗位描述
            user_background: 用户背景（可选）

        Returns:
            Dict[str, Any]: 面试问题和建议
        """
        # 检查缓存
        cache_key = f"{job_description[:100]}|{user_background[:50]}"
        if cache_key in self.cache:
            cache_entry = self.cache[cache_key]
            self.logger.debug(f"Cache hit for interview questions: {cache_key}")
            return cache_entry['result']

        try:
            # 检测岗位类型和专业类型
            job_type = self._detect_job_type(job_description)
            major_type = self._detect_major_type(job_description)
            
            self.logger.info(f"Detected job type: {job_type}, major type: {major_type}")
            
            # 生成通用问题
            general_questions = random.sample(
                self.GENERAL_QUESTION_TEMPLATES, 
                min(5, len(self.GENERAL_QUESTION_TEMPLATES))
            )
            
            # 生成专业问题
            professional_questions = []
            if major_type in self.PROFESSIONAL_QUESTION_TEMPLATES:
                templates = self.PROFESSIONAL_QUESTION_TEMPLATES[major_type]
                professional_questions = random.sample(
                    templates, 
                    min(5, len(templates))
                )
            else:
                # 如果没有匹配的专业类型，使用通用问题
                professional_questions = [
                    "请介绍一下你的专业背景",
                    "你的专业知识如何应用到这个岗位？",
                    "你在专业领域有哪些成果？",
                    "你如何保持专业知识的更新？",
                    "你认为你的专业优势是什么？"
                ]
            
            # 生成准备建议
            preparation_tips = random.sample(
                self.PREPARATION_TIP_TEMPLATES,
                min(5, len(self.PREPARATION_TIP_TEMPLATES))
            )
            
            # 构造结果
            result = {
                "general_questions": general_questions,
                "professional_questions": professional_questions,
                "preparation_tips": preparation_tips,
                "job_type": job_type,
                "major_type": major_type,
                "source": "local_generator"
            }
            
            # 更新缓存
            self.cache[cache_key] = {
                'result': result,
                'timestamp': datetime.now().isoformat()
            }
            self._save_cache()
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error generating interview questions: {e}")
            import traceback
            self.logger.debug(traceback.format_exc())
            
            # 返回基本问题
            return {
                "general_questions": [
                    "请简单介绍一下你自己",
                    "你为什么选择应聘这个岗位？",
                    "你认为你的优势是什么？"
                ],
                "professional_questions": [
                    "请介绍一下你的专业背景",
                    "你的专业知识如何应用到这个岗位？"
                ],
                "preparation_tips": [
                    "提前了解单位/公司的背景、文化和业务",
                    "准备具体的项目经验和成果案例"
                ],
                "source": "fallback"
            }
