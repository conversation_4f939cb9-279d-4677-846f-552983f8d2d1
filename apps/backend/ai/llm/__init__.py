#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
LLM客户端模块
提供与各种大型语言模型的交互功能
"""

from .base_client import BaseLLMClient, LLMMessage, LLMResponse, LLMClientException
from .enhanced_deepseek_client import EnhancedDeepSeekClient
from .client_factory import LLMClientFactory
from .llm_router import LLMRouter

# 为了向后兼容，提供别名
DeepSeekClient = EnhancedDeepSeekClient
DeepSeekLLMClient = EnhancedDeepSeekClient

__all__ = ['BaseLLMClient', 'LLMMessage', 'LLMResponse', 'LLMClientException',
           'EnhancedDeepSeekClient', 'DeepSeekClient', 'DeepSeekLLMClient',
           'LLMClientFactory', 'LLMRouter']
