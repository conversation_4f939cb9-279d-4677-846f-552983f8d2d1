#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
LLM客户端基类
定义LLM客户端的通用接口
"""

import abc
import logging
from typing import Dict, Any, Optional, List, Union, Sequence


class LLMClientException(Exception):
    """LLM客户端异常"""
    pass


class LLMMessage:
    """LLM消息类，表示一条消息"""

    def __init__(self, role: str, content: str):
        """
        初始化LLM消息

        Args:
            role: 消息角色，如"system", "user", "assistant"
            content: 消息内容
        """
        self.role = role
        self.content = content

    def to_dict(self) -> Dict[str, str]:
        """
        转换为字典

        Returns:
            Dict[str, str]: 消息字典
        """
        return {
            "role": self.role,
            "content": self.content
        }

    @classmethod
    def from_dict(cls, data: Dict[str, str]) -> 'LLMMessage':
        """
        从字典创建消息

        Args:
            data: 消息字典

        Returns:
            LLMMessage: 消息对象
        """
        return cls(data["role"], data["content"])


class LLMResponse:
    """LLM响应类，表示一个LLM响应"""

    def __init__(self, content: str, model: str, usage: Optional[Dict[str, int]] = None, raw_response: Any = None):
        """
        初始化LLM响应

        Args:
            content: 响应内容
            model: 模型名称
            usage: 使用情况，如token使用量
            raw_response: 原始响应
        """
        self.content = content
        self.model = model
        self.usage = usage or {}
        self.raw_response = raw_response

    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典

        Returns:
            Dict[str, Any]: 响应字典
        """
        return {
            "content": self.content,
            "model": self.model,
            "usage": self.usage
        }


class BaseLLMClient(abc.ABC):
    """LLM客户端基类"""

    def __init__(self, api_key: Optional[str] = None, logger: Optional[logging.Logger] = None):
        """
        初始化LLM客户端

        Args:
            api_key: API密钥
            logger: 日志记录器
        """
        self.api_key = api_key
        self.logger = logger or logging.getLogger(self.__class__.__name__)
        self._available = self._check_availability()

    def _check_availability(self) -> bool:
        """
        检查客户端是否可用

        Returns:
            bool: 客户端是否可用
        """
        return self.api_key is not None

    def is_available(self) -> bool:
        """
        检查客户端是否可用

        Returns:
            bool: 客户端是否可用
        """
        return self._available

    @abc.abstractmethod
    def chat_completion(self,
                       messages: Union[str, List[Dict[str, str]], List[LLMMessage]],
                       model: Optional[str] = None,
                       temperature: float = 0.7,
                       max_tokens: int = 1000,
                       **kwargs) -> LLMResponse:
        """
        聊天补全

        Args:
            messages: 消息列表或提示文本
            model: 模型名称
            temperature: 温度参数，控制输出的随机性
            max_tokens: 最大生成token数
            **kwargs: 其他参数

        Returns:
            LLMResponse: LLM响应
        """
        pass

    def extract_json(self, response: LLMResponse) -> Dict[str, Any]:
        """
        从响应中提取JSON

        Args:
            response: LLM响应

        Returns:
            Dict[str, Any]: 提取的JSON
        """
        import json
        import re

        content = response.content

        # 尝试直接解析JSON
        try:
            return json.loads(content)
        except json.JSONDecodeError:
            # 尝试提取JSON部分
            json_match = re.search(r'```json\s*(.*?)\s*```', content, re.DOTALL)
            if json_match:
                try:
                    return json.loads(json_match.group(1))
                except json.JSONDecodeError:
                    self.logger.error(f"Failed to parse JSON from markdown block: {json_match.group(1)}")

            # 尝试提取没有markdown格式的JSON对象
            json_match = re.search(r'(\{.*\})', content, re.DOTALL)
            if json_match:
                try:
                    return json.loads(json_match.group(1))
                except json.JSONDecodeError:
                    self.logger.error(f"Failed to parse JSON from content: {json_match.group(1)}")

            self.logger.error(f"Failed to parse JSON from AI response: {content}")
            return {"raw_response": content}

    def _normalize_messages(self, messages: Union[str, List[Dict[str, str]], List[LLMMessage]]) -> List[Dict[str, str]]:
        """
        标准化消息格式

        Args:
            messages: 消息列表或提示文本

        Returns:
            List[Dict[str, str]]: 标准化的消息列表
        """
        if isinstance(messages, str):
            return [{"role": "user", "content": messages}]
        elif isinstance(messages, list):
            if len(messages) > 0:
                if isinstance(messages[0], dict):
                    # 类型检查已经确认这是Dict[str, str]的列表
                    return messages  # type: ignore
                elif isinstance(messages[0], LLMMessage):
                    return [msg.to_dict() for msg in messages]

        raise ValueError(f"Unsupported message format: {type(messages)}")


class LLMClientFactory:
    """LLM客户端工厂类"""

    @staticmethod
    def create(client_type: str, api_key: Optional[str] = None, logger: Optional[logging.Logger] = None) -> BaseLLMClient:
        """
        创建LLM客户端

        Args:
            client_type: 客户端类型，如"deepseek"（未来支持："openai", "claude", "gemini"）
            api_key: API密钥
            logger: 日志记录器

        Returns:
            BaseLLMClient: LLM客户端
        """
        # 导入DeepSeek客户端
        from .enhanced_deepseek_client import EnhancedDeepSeekClient as DeepSeekLLMClient

        # 未来将支持的客户端（未来功能）
        # 注意：以下导入为未来计划实现的功能，当前版本仅支持DeepSeek客户端
        # 未来将支持OpenAI、Claude、Gemini等客户端
        # from .openai_client import OpenAIClient
        # from .claude_client import ClaudeClient
        # from .gemini_client import GeminiClient

        client_map = {
            # 当前仅支持DeepSeek
            "deepseek": DeepSeekLLMClient,

            # 未来将支持的客户端（未来功能）
            # 注意：以下客户端为未来计划实现的功能，当前版本仅支持DeepSeek客户端
            # "openai": OpenAIClient,
            # "claude": ClaudeClient,
            # "gemini": GeminiClient
        }

        if client_type not in client_map:
            raise ValueError(f"Unsupported client type: {client_type}")

        return client_map[client_type](api_key, logger)
