#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
LLM请求缓存管理器
提供LLM请求的缓存功能，减少重复请求，提高性能
"""

import os
import json
import time
import hashlib
import logging
from typing import Dict, Any, Optional, Union, Tuple
from datetime import datetime, timedelta


class LLMCacheManager:
    """LLM请求缓存管理器"""

    def __init__(self, 
                 cache_dir: Optional[str] = None,
                 cache_file: Optional[str] = None,
                 default_ttl: int = 86400,  # 默认缓存有效期为1天
                 max_cache_size: int = 1000,  # 最大缓存条目数
                 logger: Optional[logging.Logger] = None):
        """
        初始化LLM请求缓存管理器

        Args:
            cache_dir: 缓存目录，如果为None则使用默认目录
            cache_file: 缓存文件路径，如果为None则使用默认文件
            default_ttl: 默认缓存有效期（秒）
            max_cache_size: 最大缓存条目数
            logger: 日志记录器
        """
        self.logger = logger or logging.getLogger(__name__)
        self.default_ttl = default_ttl
        self.max_cache_size = max_cache_size
        
        # 设置缓存目录
        if cache_dir is None:
            # 默认缓存目录为当前文件所在目录的cache子目录
            cache_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "cache")
        
        # 确保缓存目录存在
        os.makedirs(cache_dir, exist_ok=True)
        
        # 设置缓存文件路径
        if cache_file is None:
            # 默认缓存文件为llm_cache.json
            self.cache_file = os.path.join(cache_dir, "llm_cache.json")
        else:
            self.cache_file = cache_file
        
        # 初始化缓存
        self.cache = self._load_cache()
        
        # 缓存统计信息
        self.stats = {
            "hits": 0,
            "misses": 0,
            "evictions": 0,
            "size": len(self.cache)
        }
        
        # 清理过期缓存
        self._clean_expired_cache()

    def _load_cache(self) -> Dict[str, Dict[str, Any]]:
        """
        加载缓存

        Returns:
            Dict[str, Dict[str, Any]]: 缓存数据
        """
        try:
            if os.path.exists(self.cache_file):
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)
                self.logger.debug(f"Loaded cache from {self.cache_file}")
                return cache_data
            else:
                self.logger.debug(f"Cache file {self.cache_file} does not exist, creating new cache")
                return {}
        except Exception as e:
            self.logger.error(f"Error loading cache: {e}")
            return {}

    def _save_cache(self) -> None:
        """保存缓存到文件"""
        try:
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(self.cache, f, ensure_ascii=False, indent=2)
            self.logger.debug(f"Saved cache to {self.cache_file}")
        except Exception as e:
            self.logger.error(f"Error saving cache: {e}")

    def _clean_expired_cache(self) -> None:
        """清理过期缓存"""
        now = time.time()
        expired_keys = []
        
        for key, cache_item in self.cache.items():
            # 检查是否过期
            if cache_item.get("expires_at", 0) < now:
                expired_keys.append(key)
        
        # 删除过期缓存
        for key in expired_keys:
            del self.cache[key]
            self.stats["evictions"] += 1
        
        if expired_keys:
            self.logger.info(f"Cleaned {len(expired_keys)} expired cache items")
            self._save_cache()
            self.stats["size"] = len(self.cache)

    def _evict_cache_if_needed(self) -> None:
        """如果缓存超过最大大小，则驱逐最旧的缓存"""
        if len(self.cache) <= self.max_cache_size:
            return
        
        # 按访问时间排序
        sorted_items = sorted(self.cache.items(), key=lambda x: x[1].get("last_accessed", 0))
        
        # 计算需要驱逐的数量
        evict_count = len(self.cache) - self.max_cache_size
        
        # 驱逐最旧的缓存
        for i in range(evict_count):
            key, _ = sorted_items[i]
            del self.cache[key]
            self.stats["evictions"] += 1
        
        self.logger.info(f"Evicted {evict_count} oldest cache items")
        self._save_cache()
        self.stats["size"] = len(self.cache)

    def _generate_cache_key(self, request_data: Dict[str, Any]) -> str:
        """
        生成缓存键

        Args:
            request_data: 请求数据

        Returns:
            str: 缓存键
        """
        # 将请求数据转换为JSON字符串
        request_str = json.dumps(request_data, sort_keys=True)
        
        # 计算哈希值作为缓存键
        return hashlib.md5(request_str.encode('utf-8')).hexdigest()

    def get(self, request_data: Dict[str, Any]) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """
        获取缓存

        Args:
            request_data: 请求数据

        Returns:
            Tuple[bool, Optional[Dict[str, Any]]]: (是否命中缓存, 缓存数据)
        """
        # 生成缓存键
        cache_key = self._generate_cache_key(request_data)
        
        # 检查缓存是否存在
        if cache_key in self.cache:
            cache_item = self.cache[cache_key]
            
            # 检查是否过期
            if cache_item.get("expires_at", 0) > time.time():
                # 更新最后访问时间
                self.cache[cache_key]["last_accessed"] = time.time()
                self.stats["hits"] += 1
                self.logger.debug(f"Cache hit for key {cache_key}")
                return True, cache_item.get("data")
            else:
                # 缓存已过期，删除
                del self.cache[cache_key]
                self.stats["evictions"] += 1
                self._save_cache()
                self.stats["size"] = len(self.cache)
        
        self.stats["misses"] += 1
        self.logger.debug(f"Cache miss for key {cache_key}")
        return False, None

    def set(self, request_data: Dict[str, Any], response_data: Dict[str, Any], ttl: Optional[int] = None) -> None:
        """
        设置缓存

        Args:
            request_data: 请求数据
            response_data: 响应数据
            ttl: 缓存有效期（秒），如果为None则使用默认有效期
        """
        # 生成缓存键
        cache_key = self._generate_cache_key(request_data)
        
        # 设置缓存
        self.cache[cache_key] = {
            "data": response_data,
            "created_at": time.time(),
            "last_accessed": time.time(),
            "expires_at": time.time() + (ttl or self.default_ttl)
        }
        
        self.logger.debug(f"Cache set for key {cache_key}")
        
        # 检查是否需要驱逐缓存
        self._evict_cache_if_needed()
        
        # 保存缓存
        self._save_cache()
        self.stats["size"] = len(self.cache)

    def invalidate(self, request_data: Dict[str, Any]) -> bool:
        """
        使缓存失效

        Args:
            request_data: 请求数据

        Returns:
            bool: 是否成功使缓存失效
        """
        # 生成缓存键
        cache_key = self._generate_cache_key(request_data)
        
        # 检查缓存是否存在
        if cache_key in self.cache:
            del self.cache[cache_key]
            self.logger.debug(f"Cache invalidated for key {cache_key}")
            self._save_cache()
            self.stats["size"] = len(self.cache)
            return True
        
        return False

    def clear(self) -> None:
        """清空缓存"""
        self.cache = {}
        self.logger.info("Cache cleared")
        self._save_cache()
        self.stats["size"] = 0
        self.stats["evictions"] += 1

    def get_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息

        Returns:
            Dict[str, Any]: 缓存统计信息
        """
        return {
            "hits": self.stats["hits"],
            "misses": self.stats["misses"],
            "hit_ratio": self.stats["hits"] / (self.stats["hits"] + self.stats["misses"]) if (self.stats["hits"] + self.stats["misses"]) > 0 else 0,
            "evictions": self.stats["evictions"],
            "size": self.stats["size"],
            "max_size": self.max_cache_size
        }

    def preload(self, request_data_list: list[Dict[str, Any]], response_data_list: list[Dict[str, Any]], ttl: Optional[int] = None) -> None:
        """
        预加载缓存

        Args:
            request_data_list: 请求数据列表
            response_data_list: 响应数据列表
            ttl: 缓存有效期（秒），如果为None则使用默认有效期
        """
        if len(request_data_list) != len(response_data_list):
            self.logger.error("Request data list and response data list must have the same length")
            return
        
        for i in range(len(request_data_list)):
            self.set(request_data_list[i], response_data_list[i], ttl)
        
        self.logger.info(f"Preloaded {len(request_data_list)} cache items")
