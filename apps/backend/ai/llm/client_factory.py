#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
LLM客户端工厂
提供创建各种LLM客户端的工厂方法
"""

import os
import logging
from typing import Dict, Any, Optional, List, Union, Literal

from .base_client import BaseLLMClient
from .enhanced_deepseek_client import EnhancedDeepSeekClient

# 为了向后兼容，将 EnhancedDeepSeekClient 别名为 DeepSeekLLMClient
DeepSeekLLMClient = EnhancedDeepSeekClient


class LLMClientFactory:
    """LLM客户端工厂类"""

    @staticmethod
    def create_client(
        client_type: Literal["deepseek", "enhanced_deepseek"] = "enhanced_deepseek",
        api_key: Optional[str] = None,
        logger: Optional[logging.Logger] = None,
        **kwargs
    ) -> BaseLLMClient:
        """
        创建LLM客户端

        Args:
            client_type: 客户端类型，支持"deepseek"和"enhanced_deepseek"
            api_key: API密钥，如果为None则从环境变量获取
            logger: 日志记录器
            **kwargs: 其他参数，传递给具体的客户端类

        Returns:
            BaseLLMClient: LLM客户端
        """
        # 获取API密钥
        api_key = api_key or os.getenv("DEEPSEEK_API_KEY")

        # 创建日志记录器
        if logger is None:
            logger = logging.getLogger("llm_client")

        # 现在 deepseek 和 enhanced_deepseek 都使用同一个增强版客户端
        if client_type in ["deepseek", "enhanced_deepseek"]:
            return EnhancedDeepSeekClient(api_key=api_key, logger=logger, **kwargs)
        else:
            raise ValueError(f"Unsupported client type: {client_type}")

    @staticmethod
    def create_deepseek_client(
        api_key: Optional[str] = None,
        logger: Optional[logging.Logger] = None
    ) -> DeepSeekLLMClient:
        """
        创建DeepSeek客户端

        Args:
            api_key: API密钥，如果为None则从环境变量获取
            logger: 日志记录器

        Returns:
            DeepSeekLLMClient: DeepSeek客户端
        """
        return LLMClientFactory.create_client("deepseek", api_key, logger)

    @staticmethod
    def create_enhanced_deepseek_client(
        api_key: Optional[str] = None,
        logger: Optional[logging.Logger] = None,
        cache_dir: Optional[str] = None,
        enable_cache: bool = True,
        cache_ttl: int = 86400,
        enable_circuit_breaker: bool = True,
        enable_performance_monitor: bool = True
    ) -> EnhancedDeepSeekClient:
        """
        创建增强版DeepSeek客户端

        Args:
            api_key: API密钥，如果为None则从环境变量获取
            logger: 日志记录器
            cache_dir: 缓存目录，如果为None则使用默认目录
            enable_cache: 是否启用缓存
            cache_ttl: 缓存有效期（秒）
            enable_circuit_breaker: 是否启用断路器
            enable_performance_monitor: 是否启用性能监控

        Returns:
            EnhancedDeepSeekClient: 增强版DeepSeek客户端
        """
        return LLMClientFactory.create_client(
            "enhanced_deepseek",
            api_key,
            logger,
            cache_dir=cache_dir,
            enable_cache=enable_cache,
            cache_ttl=cache_ttl,
            enable_circuit_breaker=enable_circuit_breaker,
            enable_performance_monitor=enable_performance_monitor
        )
