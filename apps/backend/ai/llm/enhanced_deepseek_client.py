#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
增强版DeepSeek API 客户端
提供与 DeepSeek AI 服务的交互功能，增强了缓存、错误处理和性能监控
"""

import os
import logging
import time
import json
import re
import uuid
from typing import Dict, Any, Optional, List, Union, Tuple
from datetime import datetime, timedelta
from functools import wraps

import requests
from requests.exceptions import RequestException, Timeout, ConnectionError

# 移除对已删除文件的依赖
from .base_client import BaseLLMClient, LLMMessage, LLMResponse
from .cache_manager import LLMCacheManager


class CircuitBreaker:
    """断路器，用于防止在API不可用时持续发送请求"""

    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 30, logger: Optional[logging.Logger] = None):
        """
        初始化断路器

        Args:
            failure_threshold: 触发断路的失败次数阈值
            recovery_timeout: 恢复尝试的超时时间（秒）
            logger: 日志记录器
        """
        self.logger = logger or logging.getLogger(__name__)
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failures = 0
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
        self.last_failure_time = 0

    def record_success(self) -> None:
        """记录成功请求"""
        if self.state == "HALF_OPEN":
            self.state = "CLOSED"
            self.failures = 0
            self.logger.info("Circuit breaker reset to CLOSED state after successful request")
        elif self.state == "CLOSED":
            self.failures = 0

    def record_failure(self) -> None:
        """记录失败请求"""
        self.failures += 1
        self.last_failure_time = time.time()

        if self.state == "CLOSED" and self.failures >= self.failure_threshold:
            self.state = "OPEN"
            self.logger.warning(f"Circuit breaker tripped to OPEN state after {self.failures} consecutive failures")

    def allow_request(self) -> bool:
        """
        检查是否允许请求

        Returns:
            bool: 是否允许请求
        """
        if self.state == "CLOSED":
            return True

        if self.state == "OPEN":
            # 检查是否达到恢复尝试的时间
            if time.time() - self.last_failure_time >= self.recovery_timeout:
                self.state = "HALF_OPEN"
                self.logger.info("Circuit breaker switched to HALF_OPEN state, allowing a test request")
                return True
            return False

        # HALF_OPEN状态允许一个请求尝试
        return True


class PerformanceMonitor:
    """性能监控器，用于监控API调用性能"""

    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化性能监控器

        Args:
            logger: 日志记录器
        """
        self.logger = logger or logging.getLogger(__name__)
        self.metrics = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "cached_requests": 0,
            "total_latency": 0,
            "min_latency": float('inf'),
            "max_latency": 0,
            "request_timestamps": [],
            "error_types": {}
        }

    def record_request(self, success: bool, latency: float, cached: bool = False, error_type: Optional[str] = None) -> None:
        """
        记录请求

        Args:
            success: 请求是否成功
            latency: 请求耗时（秒）
            cached: 是否命中缓存
            error_type: 错误类型
        """
        self.metrics["total_requests"] += 1
        self.metrics["request_timestamps"].append(time.time())

        if cached:
            self.metrics["cached_requests"] += 1
            return

        if success:
            self.metrics["successful_requests"] += 1
        else:
            self.metrics["failed_requests"] += 1
            if error_type:
                self.metrics["error_types"][error_type] = self.metrics["error_types"].get(error_type, 0) + 1

        self.metrics["total_latency"] += latency
        self.metrics["min_latency"] = min(self.metrics["min_latency"], latency) if self.metrics["min_latency"] != float('inf') else latency
        self.metrics["max_latency"] = max(self.metrics["max_latency"], latency)

    def get_metrics(self) -> Dict[str, Any]:
        """
        获取性能指标

        Returns:
            Dict[str, Any]: 性能指标
        """
        metrics = self.metrics.copy()

        # 计算平均延迟
        if metrics["successful_requests"] > 0:
            metrics["avg_latency"] = metrics["total_latency"] / metrics["successful_requests"]
        else:
            metrics["avg_latency"] = 0

        # 计算成功率
        if metrics["total_requests"] > 0:
            metrics["success_rate"] = metrics["successful_requests"] / metrics["total_requests"]
            metrics["cache_hit_rate"] = metrics["cached_requests"] / metrics["total_requests"]
        else:
            metrics["success_rate"] = 0
            metrics["cache_hit_rate"] = 0

        # 计算最近一分钟的请求数
        now = time.time()
        recent_requests = [ts for ts in metrics["request_timestamps"] if now - ts <= 60]
        metrics["requests_per_minute"] = len(recent_requests)

        # 移除原始时间戳列表
        del metrics["request_timestamps"]

        return metrics

    def reset(self) -> None:
        """重置性能指标"""
        self.metrics = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "cached_requests": 0,
            "total_latency": 0,
            "min_latency": float('inf'),
            "max_latency": 0,
            "request_timestamps": [],
            "error_types": {}
        }


def adaptive_retry(max_retries: int = 3,
                  initial_backoff: float = 1.0,
                  max_backoff: float = 30.0,
                  backoff_factor: float = 2.0,
                  retry_on_exceptions: Optional[Tuple[Exception, ...]] = None):
    """
    自适应重试装饰器，根据错误类型调整重试策略

    Args:
        max_retries: 最大重试次数
        initial_backoff: 初始退避时间（秒）
        max_backoff: 最大退避时间（秒）
        backoff_factor: 退避因子
        retry_on_exceptions: 需要重试的异常类型
    """
    if retry_on_exceptions is None:
        retry_on_exceptions = (RequestException, ConnectionError, Timeout)

    def decorator(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            retries = 0
            backoff = initial_backoff

            while retries < max_retries:
                try:
                    return func(self, *args, **kwargs)
                except retry_on_exceptions as e:
                    retries += 1
                    if retries == max_retries:
                        self.logger.error(f"Maximum retries reached: {e}")
                        raise

                    # 根据错误类型调整退避时间
                    if isinstance(e, Timeout):
                        # 超时错误，增加更多退避时间
                        sleep_time = min(backoff * 1.5, max_backoff)
                    elif isinstance(e, ConnectionError):
                        # 连接错误，增加更多退避时间
                        sleep_time = min(backoff * 2, max_backoff)
                    else:
                        # 其他错误，使用标准退避时间
                        sleep_time = min(backoff, max_backoff)

                    self.logger.warning(f"Request failed with {e.__class__.__name__}: {e}. Retrying in {sleep_time:.2f}s ({retries}/{max_retries})")
                    time.sleep(sleep_time)
                    backoff *= backoff_factor
                except Exception as e:
                    # 对于其他类型的异常，不进行重试
                    self.logger.error(f"Non-retriable error: {e}")
                    raise

            # 不应该到达这里
            raise Exception("Unexpected error in retry logic")
        return wrapper
    return decorator


class EnhancedDeepSeekClient(BaseLLMClient):
    """增强版DeepSeek API客户端，增加了缓存、错误处理和性能监控功能"""

    BASE_URL = "https://api.deepseek.com/v1"
    DEFAULT_MODEL = "deepseek-chat"

    def __init__(self,
                 api_key: Optional[str] = None,
                 logger: Optional[logging.Logger] = None,
                 cache_dir: Optional[str] = None,
                 enable_cache: bool = True,
                 cache_ttl: int = 86400,
                 enable_circuit_breaker: bool = True,
                 enable_performance_monitor: bool = True):
        """
        初始化增强版DeepSeek API客户端

        Args:
            api_key: DeepSeek API密钥，如果为None则从环境变量获取
            logger: 日志记录器
            cache_dir: 缓存目录，如果为None则使用默认目录
            enable_cache: 是否启用缓存
            cache_ttl: 缓存有效期（秒）
            enable_circuit_breaker: 是否启用断路器
            enable_performance_monitor: 是否启用性能监控
        """
        # 获取API密钥
        self.api_key = api_key or os.getenv("DEEPSEEK_API_KEY")
        super().__init__(self.api_key, logger)

        # 设置请求头
        if self._available:
            self.headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

        # 初始化缓存管理器
        self.enable_cache = enable_cache
        if enable_cache:
            self.cache_manager = LLMCacheManager(
                cache_dir=cache_dir,
                default_ttl=cache_ttl,
                logger=logger
            )
            self.logger.info("Initialized cache manager")

        # 初始化断路器
        self.enable_circuit_breaker = enable_circuit_breaker
        if enable_circuit_breaker:
            self.circuit_breaker = CircuitBreaker(logger=logger)
            self.logger.info("Initialized circuit breaker")

        # 初始化性能监控器
        self.enable_performance_monitor = enable_performance_monitor
        if enable_performance_monitor:
            self.performance_monitor = PerformanceMonitor(logger=logger)
            self.logger.info("Initialized performance monitor")

        # 请求ID生成器
        self.request_id_counter = 0

    def _generate_request_id(self) -> str:
        """
        生成请求ID

        Returns:
            str: 请求ID
        """
        self.request_id_counter += 1
        return f"{int(time.time())}-{self.request_id_counter}-{uuid.uuid4().hex[:8]}"

    @adaptive_retry(max_retries=3, initial_backoff=1.0, max_backoff=10.0, backoff_factor=2.0)
    def chat_completion(self,
                       messages: Union[str, List[Dict[str, str]], List[LLMMessage]],
                       model: Optional[str] = None,
                       temperature: float = 0.7,
                       max_tokens: int = 1000,
                       use_cache: Optional[bool] = None,
                       **kwargs) -> LLMResponse:
        """
        聊天补全

        Args:
            messages: 消息列表或提示文本
            model: 模型名称，默认为deepseek-chat
            temperature: 温度参数，控制输出的随机性
            max_tokens: 最大生成token数
            use_cache: 是否使用缓存，如果为None则使用全局设置
            **kwargs: 其他参数

        Returns:
            LLMResponse: LLM响应
        """
        if not self._available:
            raise Exception("DeepSeek API密钥未提供，API功能不可用")

        # 检查断路器状态
        if self.enable_circuit_breaker and not self.circuit_breaker.allow_request():
            self.logger.warning("Circuit breaker is open, request blocked")
            raise Exception("Circuit breaker is open, request blocked")

        # 生成请求ID
        request_id = self._generate_request_id()

        # 标准化消息
        normalized_messages = self._normalize_messages(messages)

        # 构建请求数据
        url = f"{self.BASE_URL}/chat/completions"
        model = model or self.DEFAULT_MODEL

        request_data = {
            "model": model,
            "messages": normalized_messages,
            "temperature": temperature,
            "max_tokens": max_tokens
        }

        # 添加其他参数
        for key, value in kwargs.items():
            request_data[key] = value

        # 检查是否使用缓存
        should_use_cache = self.enable_cache if use_cache is None else use_cache

        # 如果启用缓存，尝试从缓存获取
        if should_use_cache:
            # 创建缓存键（不包含request_id）
            cache_hit, cached_response = self.cache_manager.get(request_data)

            if cache_hit and cached_response:
                self.logger.debug(f"[{request_id}] Cache hit, returning cached response")

                # 记录性能指标
                if self.enable_performance_monitor:
                    self.performance_monitor.record_request(success=True, latency=0, cached=True)

                return LLMResponse(
                    content=cached_response.get("content", ""),
                    model=model,
                    usage=cached_response.get("usage", {}),
                    raw_response=cached_response
                )

        # 记录请求开始时间
        start_time = time.time()

        try:
            self.logger.debug(f"[{request_id}] Calling DeepSeek API: {url}")

            # 发送请求
            response = requests.post(url, headers=self.headers, json=request_data, timeout=30)
            response.raise_for_status()
            response_json = response.json()

            # 计算请求耗时
            latency = time.time() - start_time

            # 提取响应内容
            content = response_json["choices"][0]["message"]["content"]
            usage = response_json.get("usage", {})

            # 记录成功请求
            if self.enable_circuit_breaker:
                self.circuit_breaker.record_success()

            # 记录性能指标
            if self.enable_performance_monitor:
                self.performance_monitor.record_request(success=True, latency=latency)

            # 构建响应对象
            llm_response = LLMResponse(
                content=content,
                model=model,
                usage=usage,
                raw_response=response_json
            )

            # 如果启用缓存，缓存响应
            if should_use_cache:
                # 只缓存低温度的请求
                if temperature <= 0.3:
                    self.cache_manager.set(request_data, response_json)

            return llm_response

        except Exception as e:
            # 计算请求耗时
            latency = time.time() - start_time

            # 记录失败请求
            if self.enable_circuit_breaker:
                self.circuit_breaker.record_failure()

            # 记录性能指标
            if self.enable_performance_monitor:
                self.performance_monitor.record_request(
                    success=False,
                    latency=latency,
                    error_type=e.__class__.__name__
                )

            self.logger.error(f"[{request_id}] Error in chat completion: {e}")
            raise

    def get_cache_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息

        Returns:
            Dict[str, Any]: 缓存统计信息
        """
        if not self.enable_cache:
            return {"enabled": False}

        return {
            "enabled": True,
            **self.cache_manager.get_stats()
        }

    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        获取性能指标

        Returns:
            Dict[str, Any]: 性能指标
        """
        if not self.enable_performance_monitor:
            return {"enabled": False}

        return {
            "enabled": True,
            **self.performance_monitor.get_metrics()
        }

    def get_circuit_breaker_status(self) -> Dict[str, Any]:
        """
        获取断路器状态

        Returns:
            Dict[str, Any]: 断路器状态
        """
        if not self.enable_circuit_breaker:
            return {"enabled": False}

        return {
            "enabled": True,
            "state": self.circuit_breaker.state,
            "failures": self.circuit_breaker.failures,
            "last_failure_time": self.circuit_breaker.last_failure_time
        }

    def clear_cache(self) -> None:
        """清空缓存"""
        if self.enable_cache:
            self.cache_manager.clear()
            self.logger.info("Cache cleared")

    def reset_circuit_breaker(self) -> None:
        """重置断路器"""
        if self.enable_circuit_breaker:
            self.circuit_breaker.state = "CLOSED"
            self.circuit_breaker.failures = 0
            self.logger.info("Circuit breaker reset to CLOSED state")

    def reset_performance_monitor(self) -> None:
        """重置性能监控器"""
        if self.enable_performance_monitor:
            self.performance_monitor.reset()
            self.logger.info("Performance monitor reset")

    def get_client_status(self) -> Dict[str, Any]:
        """
        获取客户端状态

        Returns:
            Dict[str, Any]: 客户端状态
        """
        return {
            "available": self._available,
            "cache": self.get_cache_stats() if self.enable_cache else {"enabled": False},
            "circuit_breaker": self.get_circuit_breaker_status() if self.enable_circuit_breaker else {"enabled": False},
            "performance": self.get_performance_metrics() if self.enable_performance_monitor else {"enabled": False}
        }

    def extract_json(self, response: Union[LLMResponse, Dict[str, Any], str]) -> Dict[str, Any]:
        """
        从API响应中提取JSON，增强版本支持更复杂的JSON提取

        Args:
            response: API响应，可以是LLMResponse对象、字典或字符串

        Returns:
            Dict[str, Any]: 解析后的JSON对象
        """
        try:
            # 根据响应类型获取内容
            if isinstance(response, LLMResponse):
                content = response.content
            elif isinstance(response, dict):
                # 处理DeepSeek API的响应格式
                if "choices" in response and len(response["choices"]) > 0:
                    content = response["choices"][0]["message"]["content"]
                elif "content" in response:
                    content = response["content"]
                else:
                    # 如果响应是字典但没有预期的字段，尝试直接返回
                    return response
            elif isinstance(response, str):
                content = response
            else:
                self.logger.error(f"无法识别的响应格式: {type(response)}")
                return {}

            # 尝试直接解析JSON
            try:
                return json.loads(content)
            except json.JSONDecodeError:
                # 如果直接解析失败，尝试从文本中提取JSON部分
                try:
                    # 查找可能的JSON开始和结束位置
                    json_pattern = r'```(?:json)?\s*([\s\S]*?)\s*```'
                    match = re.search(json_pattern, content)
                    if match:
                        json_str = match.group(1)
                        return json.loads(json_str)

                    # 如果没有找到markdown格式的JSON，尝试直接查找JSON对象
                    json_pattern = r'(\{[\s\S]*\})'
                    match = re.search(json_pattern, content)
                    if match:
                        json_str = match.group(1)
                        return json.loads(json_str)

                    # 尝试查找JSON数组
                    json_pattern = r'(\[[\s\S]*\])'
                    match = re.search(json_pattern, content)
                    if match:
                        json_str = match.group(1)
                        return json.loads(json_str)

                    # 尝试修复常见的JSON格式错误
                    # 1. 单引号替换为双引号
                    fixed_content = content.replace("'", '"')
                    # 2. 修复没有引号的键
                    fixed_content = re.sub(r'(\s*)(\w+)(\s*):(\s*)', r'\1"\2"\3:\4', fixed_content)
                    # 3. 尝试提取并解析
                    json_pattern = r'(\{[\s\S]*\})'
                    match = re.search(json_pattern, fixed_content)
                    if match:
                        json_str = match.group(1)
                        return json.loads(json_str)

                except Exception as e:
                    self.logger.error(f"Error extracting JSON from content: {e}")

            # 如果仍然失败，返回空结果
            self.logger.error("Failed to extract JSON from API response")
            return {}
        except Exception as e:
            self.logger.error(f"Error extracting JSON from response: {e}")
            return {}
