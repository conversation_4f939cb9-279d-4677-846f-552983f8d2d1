#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
LLM路由器
根据配置选择合适的LLM客户端
"""

import os
import logging
from typing import Dict, Any, Optional, List, Union

from .base_client import BaseLLMClient, LLMMessage, LLMResponse, LLMClientFactory


class LLMRouter:
    """LLM路由器"""

    def __init__(self, config: Optional[Dict[str, Any]] = None, logger: Optional[logging.Logger] = None):
        """
        初始化LLM路由器

        Args:
            config: 配置字典，包含默认客户端类型、API密钥等
            logger: 日志记录器
        """
        self.logger = logger or logging.getLogger(self.__class__.__name__)
        self.config = config or {}

        # 默认客户端类型
        self.default_client_type = self.config.get("default_client_type", "deepseek")

        # 初始化客户端
        self.clients = {}
        self._init_clients()

    def _init_clients(self):
        """初始化客户端"""
        # 当前仅支持DeepSeek客户端
        client_type = "deepseek"
        api_key_env = f"{client_type.upper()}_API_KEY"
        api_key = self.config.get(f"{client_type}_api_key") or os.getenv(api_key_env)

        if api_key:
            try:
                self.clients[client_type] = LLMClientFactory.create(client_type, api_key, self.logger)
                self.logger.info(f"Initialized {client_type} client")
            except Exception as e:
                self.logger.error(f"Failed to initialize {client_type} client: {e}")

        # 检查是否有可用的客户端
        if not self.clients:
            self.logger.warning("No LLM clients available")

        # 未来将支持的客户端（未来功能）
        # 注意：以下代码为未来计划实现的功能，当前版本仅支持DeepSeek客户端
        # 未来将支持OpenAI、Claude、Gemini等客户端
        # client_types = ["openai", "claude", "gemini", "deepseek"]
        # for client_type in client_types:
        #     api_key_env = f"{client_type.upper()}_API_KEY"
        #     api_key = self.config.get(f"{client_type}_api_key") or os.getenv(api_key_env)
        #
        #     if api_key:
        #         try:
        #             self.clients[client_type] = LLMClientFactory.create(client_type, api_key, self.logger)
        #             self.logger.info(f"Initialized {client_type} client")
        #         except Exception as e:
        #             self.logger.error(f"Failed to initialize {client_type} client: {e}")

    def get_client(self, client_type: Optional[str] = None) -> Optional[BaseLLMClient]:
        """
        获取指定类型的客户端

        Args:
            client_type: 客户端类型，如果为None则返回默认客户端

        Returns:
            Optional[BaseLLMClient]: LLM客户端，如果不可用则返回None
        """
        client_type = client_type or self.default_client_type

        if client_type in self.clients and self.clients[client_type].is_available():
            return self.clients[client_type]

        # 如果指定的客户端不可用，尝试使用默认客户端
        if client_type != self.default_client_type and self.default_client_type in self.clients:
            self.logger.warning(f"{client_type} client not available, using {self.default_client_type} client")
            return self.clients[self.default_client_type]

        # 如果默认客户端也不可用，尝试使用任何可用的客户端
        for client_type, client in self.clients.items():
            if client.is_available():
                self.logger.warning(f"Using {client_type} client as fallback")
                return client

        # 如果没有可用的客户端，返回None
        self.logger.error("No LLM clients available")
        return None

    def chat_completion(self,
                       messages: Union[str, List[Dict[str, str]], List[LLMMessage]],
                       client_type: Optional[str] = None,
                       model: Optional[str] = None,
                       temperature: float = 0.7,
                       max_tokens: int = 1000,
                       **kwargs) -> Optional[LLMResponse]:
        """
        聊天补全

        Args:
            messages: 消息列表或提示文本
            client_type: 客户端类型，如果为None则使用默认客户端
            model: 模型名称
            temperature: 温度参数，控制输出的随机性
            max_tokens: 最大生成token数
            **kwargs: 其他参数

        Returns:
            Optional[LLMResponse]: LLM响应，如果没有可用的客户端则返回None
        """
        client = self.get_client(client_type)

        if not client:
            return None

        try:
            return client.chat_completion(
                messages=messages,
                model=model,
                temperature=temperature,
                max_tokens=max_tokens,
                **kwargs
            )
        except Exception as e:
            self.logger.error(f"Error in chat completion: {e}")

            # 如果指定了客户端类型但失败了，尝试使用其他客户端
            if client_type and client_type in self.clients:
                for fallback_type, fallback_client in self.clients.items():
                    if fallback_type != client_type and fallback_client.is_available():
                        self.logger.warning(f"Trying fallback client: {fallback_type}")
                        try:
                            return fallback_client.chat_completion(
                                messages=messages,
                                model=model,
                                temperature=temperature,
                                max_tokens=max_tokens,
                                **kwargs
                            )
                        except Exception as fallback_e:
                            self.logger.error(f"Error in fallback chat completion: {fallback_e}")

            return None

    def extract_json(self, response: Optional[LLMResponse]) -> Dict[str, Any]:
        """
        从响应中提取JSON

        Args:
            response: LLM响应

        Returns:
            Dict[str, Any]: 提取的JSON，如果响应为None则返回空字典
        """
        if not response:
            return {}

        client = self.get_client()

        if not client:
            return {}

        return client.extract_json(response)
