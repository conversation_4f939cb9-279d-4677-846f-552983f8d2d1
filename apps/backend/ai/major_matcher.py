#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
专业匹配器
提供专业标准化、匹配度计算和缓存功能
"""

import os
import json
import logging
import difflib
import re
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime
import pickle


class MajorMatcher:
    """专业匹配器，提供专业标准化、匹配度计算和缓存功能"""

    # 常见专业别名映射
    MAJOR_ALIASES = {
        "计算机科学与技术": ["计算机", "计算机科学", "计算机技术", "计算机应用", "计算机类", "计算机相关", "计算机及相关"],
        "软件工程": ["软件开发", "软件技术", "软件开发工程", "软件设计", "软件相关"],
        "电子信息工程": ["电子信息", "电子信息技术", "信息工程", "电子信息类", "电子类"],
        "通信工程": ["通信技术", "通信与信息系统", "通信类", "通信相关"],
        "人工智能": ["智能科学", "智能技术", "人工智能技术", "AI", "机器学习", "深度学习"],
        "数据科学与大数据技术": ["数据科学", "大数据", "数据分析", "数据工程", "数据挖掘", "数据处理"],
        "会计学": ["会计", "财务会计", "审计学", "财会", "会计与审计"],
        "金融学": ["金融", "金融管理", "金融工程", "金融类", "金融相关"],
        "经济学": ["经济", "政治经济学", "应用经济学", "经济类", "经济相关"],
        "工商管理": ["管理学", "管理", "行政管理", "企业管理", "工商类", "管理类"],
        "法学": ["法律", "民商法", "经济法", "法律专业", "法律相关", "法学类"],
        "临床医学": ["医学", "基础医学", "中医学", "西医学", "医学类", "医学相关"],
        "护理学": ["护理", "护理技术", "护理专业", "护理相关"],
        "教育学": ["教育", "教育技术学", "学前教育", "教育类", "教育相关"],
        "心理学": ["应用心理学", "心理健康", "心理咨询", "心理学类", "心理相关"],
        "英语": ["英语语言文学", "商务英语", "英语翻译", "英语专业", "英语相关"],
        "汉语言文学": ["中文", "汉语国际教育", "汉语言", "汉语", "中文系", "文学类"],
        "新闻学": ["新闻", "新闻传播学", "传媒", "新闻传播", "媒体", "传播学"],
        "建筑学": ["建筑", "建筑设计", "城市规划", "建筑类", "建筑相关"],
        "土木工程": ["土木", "建筑工程", "工民建", "土木类", "土木相关"],
        "网络工程": ["网络技术", "计算机网络", "网络管理", "网络安全", "网络相关"],
        "信息安全": ["网络安全", "信息安全技术", "网络与信息安全", "安全", "信息安全类"],
        "电气工程及其自动化": ["电气工程", "电气", "电气自动化", "电气类", "电气相关"],
        "自动化": ["自动控制", "智能控制", "自动化技术", "自动化类", "自动化相关"],
        "机械工程": ["机械", "机械设计", "机械制造", "机械类", "机械相关"],
        "生物工程": ["生物技术", "生物科学", "生物医学", "生物类", "生物相关"],
        "环境工程": ["环境", "环境科学", "环保", "环境类", "环境相关"],
        "材料科学与工程": ["材料", "材料科学", "材料工程", "材料类", "材料相关"],
        "化学工程与工艺": ["化工", "化学工程", "化学工艺", "化工类", "化学相关"],
        "数学与应用数学": ["数学", "应用数学", "统计学", "数学类", "数学相关"],
        "物理学": ["物理", "应用物理", "物理类", "物理相关"],
        "化学": ["应用化学", "化学类", "化学相关"]
    }

    # 专业要求关键词
    MAJOR_KEYWORDS = [
        "专业", "学科", "学历要求", "专业要求", "相关专业",
        "专业背景", "专业领域", "学科专业", "专业方向",
        "专业不限", "专业优先", "相关专业优先", "专业类别",
        "学科类别", "专业类型", "学科门类", "专业学科"
    ]

    # 专业分类关键词
    MAJOR_CATEGORIES = {
        "工学": ["工科", "理工科", "工程类", "工程技术", "工程学"],
        "理学": ["理科", "自然科学", "理科类"],
        "医学": ["医科", "医药类", "医疗类", "卫生类"],
        "经济学": ["经济类", "经济管理", "经管类"],
        "管理学": ["管理类", "管理科学", "行政管理"],
        "法学": ["法律类", "政法类", "法律相关"],
        "文学": ["文科", "人文类", "文科类", "语言类"],
        "教育学": ["教育类", "师范类", "教学类"],
        "农学": ["农业类", "农林类", "农牧类"],
        "艺术学": ["艺术类", "设计类", "美术类", "音乐类"]
    }

    # 专业级别关键词
    MAJOR_LEVELS = {
        "本科": ["学士", "本科及以上", "大学本科", "全日制本科"],
        "硕士": ["研究生", "硕士及以上", "硕士研究生", "全日制硕士"],
        "博士": ["博士研究生", "博士及以上", "博士后", "全日制博士"],
        "专科": ["大专", "专科及以上", "高职", "全日制专科"]
    }

    def __init__(self, logger: Optional[logging.Logger] = None,
                 major_dict_path: Optional[str] = None,
                 cache_dir: Optional[str] = None):
        """
        初始化专业匹配器

        Args:
            logger: 日志记录器
            major_dict_path: 专业词典路径，默认为data/major_dict.json
            cache_dir: 缓存目录，默认为.cache
        """
        self.logger = logger or logging.getLogger(self.__class__.__name__)

        # 设置专业词典路径
        if major_dict_path is None:
            # 默认路径为项目根目录下的data/major_dict.json
            project_root = os.path.abspath(os.path.join(
                os.path.dirname(__file__), '..', '..'
            ))
            self.major_dict_path = os.path.join(project_root, "data", "major_dict.json")
        else:
            self.major_dict_path = major_dict_path

        # 设置缓存目录
        if cache_dir is None:
            project_root = os.path.abspath(os.path.join(
                os.path.dirname(__file__), '..', '..'
            ))
            self.cache_dir = os.path.join(project_root, ".cache")
        else:
            self.cache_dir = cache_dir

        # 确保缓存目录存在
        os.makedirs(self.cache_dir, exist_ok=True)

        # 缓存文件路径
        self.standardize_cache_path = os.path.join(self.cache_dir, "major_standardize_cache.pkl")
        self.match_cache_path = os.path.join(self.cache_dir, "major_match_cache.pkl")

        # 加载专业词典
        self.major_dict = self._load_major_dict()

        # 初始化缓存
        self.standardize_cache: Dict[str, Dict[str, Any]] = self._load_cache(self.standardize_cache_path)
        self.match_cache: Dict[str, Dict[str, Any]] = self._load_cache(self.match_cache_path)

        # 专业分类映射
        self.category_map = self._build_category_map()

        # 构建专业别名映射
        self.alias_map = self._build_alias_map()

        # 专业相似度矩阵
        self.similarity_matrix = self._build_similarity_matrix()

    def _load_major_dict(self) -> Dict[str, Dict[str, Any]]:
        """
        加载专业词典

        Returns:
            Dict[str, Dict[str, Any]]: 专业词典
        """
        try:
            if os.path.exists(self.major_dict_path):
                with open(self.major_dict_path, 'r', encoding='utf-8') as f:
                    major_dict = json.load(f)
                self.logger.info(f"Loaded {len(major_dict)} majors from {self.major_dict_path}")
                return major_dict
            else:
                self.logger.warning(f"Major dictionary file not found: {self.major_dict_path}")
                return {}
        except Exception as e:
            self.logger.error(f"Error loading major dictionary: {e}")
            return {}

    def _load_cache(self, cache_path: str) -> Dict[str, Dict[str, Any]]:
        """
        加载缓存

        Args:
            cache_path: 缓存文件路径

        Returns:
            Dict[str, Dict[str, Any]]: 缓存数据
        """
        try:
            if os.path.exists(cache_path):
                with open(cache_path, 'rb') as f:
                    cache = pickle.load(f)
                self.logger.info(f"Loaded {len(cache)} cache entries from {cache_path}")
                return cache
            else:
                self.logger.info(f"Cache file not found, creating new cache: {cache_path}")
                return {}
        except Exception as e:
            self.logger.error(f"Error loading cache: {e}")
            return {}

    def _save_cache(self, cache: Dict[str, Dict[str, Any]], cache_path: str) -> bool:
        """
        保存缓存

        Args:
            cache: 缓存数据
            cache_path: 缓存文件路径

        Returns:
            bool: 是否成功保存
        """
        try:
            with open(cache_path, 'wb') as f:
                pickle.dump(cache, f)
            self.logger.info(f"Saved {len(cache)} cache entries to {cache_path}")
            return True
        except Exception as e:
            self.logger.error(f"Error saving cache: {e}")
            return False

    def _build_category_map(self) -> Dict[str, List[str]]:
        """
        构建专业分类映射

        Returns:
            Dict[str, List[str]]: 专业分类映射，key为分类，value为该分类下的专业列表
        """
        category_map: Dict[str, List[str]] = {}

        for major, info in self.major_dict.items():
            category = info.get('category', '其他')
            if category not in category_map:
                category_map[category] = []
            category_map[category].append(major)

        return category_map

    def _build_alias_map(self) -> Dict[str, str]:
        """
        构建专业别名映射

        Returns:
            Dict[str, str]: 专业别名映射，key为别名，value为标准名称
        """
        alias_map: Dict[str, str] = {}

        # 添加预定义的别名
        for standard, aliases in self.MAJOR_ALIASES.items():
            for alias in aliases:
                alias_map[alias] = standard

        # 添加词典中的别名（如果有）
        for major, info in self.major_dict.items():
            # 处理专业代码作为别名
            code = info.get('code', '')
            if code:
                alias_map[code] = major

            # 处理专业简称
            if '与' in major:
                simple_name = major.split('与')[0]
                alias_map[simple_name] = major

            # 处理专业类别作为别名
            if major.endswith('类'):
                base_name = major[:-1]
                alias_map[base_name] = major

            # 处理专业名称中的常见模式
            if '学' in major and not major.endswith('学'):
                simple_name = major.split('学')[0] + '学'
                if simple_name != major:
                    alias_map[simple_name] = major

            # 处理专业名称中的"工程"
            if '工程' in major and not major.endswith('工程'):
                simple_name = major.split('工程')[0] + '工程'
                if simple_name != major:
                    alias_map[simple_name] = major

        return alias_map

    def _build_similarity_matrix(self) -> Dict[str, Dict[str, float]]:
        """
        构建专业相似度矩阵

        Returns:
            Dict[str, Dict[str, float]]: 专业相似度矩阵
        """
        similarity_matrix: Dict[str, Dict[str, float]] = {}

        # 初始化矩阵
        for major in self.major_dict:
            similarity_matrix[major] = {}

        # 填充矩阵
        for major1 in self.major_dict:
            for major2 in self.major_dict:
                if major1 == major2:
                    similarity_matrix[major1][major2] = 1.0
                    continue

                # 如果已经计算过，直接使用
                if major2 in similarity_matrix[major1]:
                    continue

                # 计算相似度
                similarity = self._calculate_major_similarity(major1, major2)
                similarity_matrix[major1][major2] = similarity
                similarity_matrix[major2][major1] = similarity

        return similarity_matrix

    def _calculate_major_similarity(self, major1: str, major2: str) -> float:
        """
        计算两个专业的相似度 - 增强版

        Args:
            major1: 专业1
            major2: 专业2

        Returns:
            float: 相似度，范围[0, 1]
        """
        # 如果专业名称相同，相似度为1
        if major1 == major2:
            return 1.0

        # 检查别名映射
        std_major1 = self.alias_map.get(major1, major1)
        std_major2 = self.alias_map.get(major2, major2)

        # 如果标准化后的专业名称相同，相似度为0.95
        if std_major1 == std_major2:
            return 0.95

        # 获取专业信息
        info1 = self.major_dict.get(std_major1, {})
        info2 = self.major_dict.get(std_major2, {})

        # 如果专业不在词典中，使用增强的字符串相似度
        if not info1 or not info2:
            return self._enhanced_string_similarity(std_major1, std_major2)

        # 计算多维度相似度
        similarity_score = 0.0
        total_weight = 0.0

        # 1. 学科门类相似度（权重：0.35）
        category_sim = self._calculate_category_similarity(info1, info2)
        similarity_score += category_sim * 0.35
        total_weight += 0.35

        # 2. 专业名称语义相似度（权重：0.25）
        semantic_sim = self._calculate_semantic_similarity(std_major1, std_major2)
        similarity_score += semantic_sim * 0.25
        total_weight += 0.25

        # 3. 技能和知识领域相似度（权重：0.20）
        skill_sim = self._calculate_skill_similarity(info1, info2)
        similarity_score += skill_sim * 0.20
        total_weight += 0.20

        # 4. 就业方向相似度（权重：0.15）
        career_sim = self._calculate_career_similarity(info1, info2)
        similarity_score += career_sim * 0.15
        total_weight += 0.15

        # 5. 专业代码相似度（权重：0.05）
        code_sim = self._calculate_code_similarity(info1, info2)
        similarity_score += code_sim * 0.05
        total_weight += 0.05

        return similarity_score / total_weight if total_weight > 0 else 0.0

    def _enhanced_string_similarity(self, major1: str, major2: str) -> float:
        """增强的字符串相似度计算"""
        # 基础字符串相似度
        base_similarity = difflib.SequenceMatcher(None, major1, major2).ratio()

        # 检查包含关系
        if major1 in major2 or major2 in major1:
            contained_bonus = min(len(major1), len(major2)) / max(len(major1), len(major2))
            base_similarity = max(base_similarity, contained_bonus * 0.8)

        # 检查关键词匹配
        keywords1 = self._extract_keywords(major1)
        keywords2 = self._extract_keywords(major2)

        if keywords1 and keywords2:
            keyword_similarity = len(keywords1 & keywords2) / len(keywords1 | keywords2)
            base_similarity = max(base_similarity, keyword_similarity * 0.7)

        return base_similarity

    def _calculate_category_similarity(self, info1: Dict, info2: Dict) -> float:
        """计算学科门类相似度"""
        category1 = info1.get('category', '')
        category2 = info2.get('category', '')

        if not category1 or not category2:
            return 0.5

        if category1 == category2:
            return 1.0

        # 定义学科门类相似度矩阵
        category_similarity_matrix = {
            ('工学', '理学'): 0.7,
            ('理学', '工学'): 0.7,
            ('经济学', '管理学'): 0.8,
            ('管理学', '经济学'): 0.8,
            ('文学', '教育学'): 0.6,
            ('教育学', '文学'): 0.6,
            ('医学', '理学'): 0.4,
            ('理学', '医学'): 0.4,
            ('法学', '管理学'): 0.5,
            ('管理学', '法学'): 0.5,
            ('艺术学', '文学'): 0.6,
            ('文学', '艺术学'): 0.6,
        }

        return category_similarity_matrix.get((category1, category2), 0.2)

    def _calculate_semantic_similarity(self, major1: str, major2: str) -> float:
        """计算专业名称语义相似度"""
        # 提取关键词
        keywords1 = self._extract_keywords(major1)
        keywords2 = self._extract_keywords(major2)

        if not keywords1 or not keywords2:
            return difflib.SequenceMatcher(None, major1, major2).ratio()

        # 计算关键词相似度
        intersection = keywords1 & keywords2
        union = keywords1 | keywords2

        if not union:
            return 0.0

        keyword_similarity = len(intersection) / len(union)

        # 考虑关键词的重要性权重
        important_keywords = {'计算机', '软件', '网络', '信息', '数据', '人工智能',
                            '金融', '经济', '管理', '会计', '法学', '医学', '教育'}

        important_match = 0
        for keyword in intersection:
            if keyword in important_keywords:
                important_match += 1

        # 如果有重要关键词匹配，提升相似度
        if important_match > 0:
            keyword_similarity = min(1.0, keyword_similarity + important_match * 0.1)

        return keyword_similarity

    def _calculate_skill_similarity(self, info1: Dict, info2: Dict) -> float:
        """计算技能和知识领域相似度"""
        skills1 = set(info1.get('skills', []))
        skills2 = set(info2.get('skills', []))

        if not skills1 or not skills2:
            return 0.5

        intersection = skills1 & skills2
        union = skills1 | skills2

        return len(intersection) / len(union) if union else 0.0

    def _calculate_career_similarity(self, info1: Dict, info2: Dict) -> float:
        """计算就业方向相似度"""
        careers1 = set(info1.get('careers', []))
        careers2 = set(info2.get('careers', []))

        if not careers1 or not careers2:
            return 0.5

        intersection = careers1 & careers2
        union = careers1 | careers2

        return len(intersection) / len(union) if union else 0.0

    def _calculate_code_similarity(self, info1: Dict, info2: Dict) -> float:
        """计算专业代码相似度"""
        code1 = info1.get('code', '')
        code2 = info2.get('code', '')

        if not code1 or not code2 or len(code1) < 4 or len(code2) < 4:
            return 0.5

        # 比较专业代码的层次结构
        similarity = 0.0

        # 一级学科代码（前2位）
        if code1[:2] == code2[:2]:
            similarity += 0.6

        # 二级学科代码（前4位）
        if len(code1) >= 4 and len(code2) >= 4 and code1[:4] == code2[:4]:
            similarity += 0.4

        return min(similarity, 1.0)

    def _extract_keywords(self, major_name: str) -> set:
        """从专业名称中提取关键词"""
        # 定义关键词模式
        keyword_patterns = [
            r'计算机', r'软件', r'网络', r'信息', r'数据', r'人工智能', r'智能',
            r'金融', r'经济', r'管理', r'会计', r'财务', r'审计',
            r'法学', r'法律', r'医学', r'临床', r'护理',
            r'教育', r'心理', r'英语', r'汉语', r'新闻', r'传播',
            r'建筑', r'土木', r'电气', r'自动化', r'机械',
            r'生物', r'环境', r'材料', r'化学', r'物理', r'数学'
        ]

        keywords = set()
        for pattern in keyword_patterns:
            if re.search(pattern, major_name):
                keywords.add(pattern)

        # 添加专业名称中的核心词汇
        core_words = re.findall(r'[\u4e00-\u9fff]+', major_name)
        for word in core_words:
            if len(word) >= 2:
                keywords.add(word)

        return keywords

    def standardize_major(self, major_requirement: str) -> Tuple[str, List[str]]:
        """
        标准化专业要求

        Args:
            major_requirement: 专业要求文本

        Returns:
            Tuple[str, List[str]]: (标准化后的专业, 相似专业列表)
        """
        # 检查缓存
        cache_key = major_requirement.strip()
        if cache_key in self.standardize_cache:
            cache_entry = self.standardize_cache[cache_key]
            self.logger.debug(f"Cache hit for standardize_major: {cache_key}")
            return cache_entry['standardized_major'], cache_entry['similar_majors']

        # 提取专业名称
        extracted_major = self._extract_major_from_text(major_requirement)
        if not extracted_major:
            # 如果无法提取，返回原文本
            result = (major_requirement, [])
            self.standardize_cache[cache_key] = {
                'standardized_major': result[0],
                'similar_majors': result[1],
                'timestamp': datetime.now().isoformat()
            }
            return result

        # 直接检查是否是专业词典中的专业
        if extracted_major in self.major_dict:
            similar_majors = self._find_similar_majors(extracted_major)
            result = (extracted_major, similar_majors)

            # 更新缓存
            self.standardize_cache[cache_key] = {
                'standardized_major': result[0],
                'similar_majors': result[1],
                'timestamp': datetime.now().isoformat()
            }
            self._save_cache(self.standardize_cache, self.standardize_cache_path)
            return result

        # 查找别名映射
        if extracted_major in self.alias_map:
            standard_major = self.alias_map[extracted_major]
            similar_majors = self._find_similar_majors(standard_major)
            result = (standard_major, similar_majors)

            # 更新缓存
            self.standardize_cache[cache_key] = {
                'standardized_major': result[0],
                'similar_majors': result[1],
                'timestamp': datetime.now().isoformat()
            }
            self._save_cache(self.standardize_cache, self.standardize_cache_path)
            return result

        # 查找最匹配的专业
        best_match, similarity = self._find_best_match(extracted_major)
        if similarity > 0.7:  # 降低相似度阈值
            similar_majors = self._find_similar_majors(best_match)
            result = (best_match, similar_majors)
        else:
            # 如果没有高相似度匹配，但是是预定义别名中的关键词
            for standard, aliases in self.MAJOR_ALIASES.items():
                if any(alias in extracted_major for alias in aliases):
                    similar_majors = self._find_similar_majors(standard)
                    result = (standard, similar_majors)
                    break
            else:
                # 如果以上都不匹配，返回原文本
                result = (extracted_major, [])

        # 更新缓存
        self.standardize_cache[cache_key] = {
            'standardized_major': result[0],
            'similar_majors': result[1],
            'timestamp': datetime.now().isoformat()
        }
        self._save_cache(self.standardize_cache, self.standardize_cache_path)
        return result

    def _extract_major_from_text(self, text: str) -> str:
        """
        从文本中提取专业名称

        Args:
            text: 专业要求文本

        Returns:
            str: 提取的专业名称
        """
        if not text:
            return ""

        # 清理文本
        text = text.strip()

        # 检查"专业不限"情况
        if any(phrase in text for phrase in ["专业不限", "不限专业", "专业不限制", "不限制专业"]):
            return "专业不限"

        # 如果文本很短，可能就是专业名称
        if len(text) < 10 and '专业' not in text:
            return text

        # 处理包含多个专业的情况，优先选择计算机科学与技术
        if '计算机' in text and ('软件' in text or '网络' in text):
            return '计算机科学与技术'

        # 定义提取模式 - 按优先级排序
        patterns = [
            # 明确的专业要求格式
            r'专业要求[:：]?\s*([^，。；,;]+)',
            r'专业[:：]?\s*([^，。；,;]+)',
            r'学历要求[:：]?[^，。；,;]*专业[:：]?\s*([^，。；,;]+)',
            r'专业背景[:：]?\s*([^，。；,;]+)',
            r'学科专业[:：]?\s*([^，。；,;]+)',
            r'专业方向[:：]?\s*([^，。；,;]+)',
            # 常见的表述方式
            r'([\u4e00-\u9fa5]+学)专业',  # 例如：计算机科学专业
            r'([\u4e00-\u9fa5]+)相关专业',  # 例如：计算机相关专业
            r'([\u4e00-\u9fa5]+)及相关专业',  # 例如：计算机及相关专业
            r'([\u4e00-\u9fa5]+)或相关专业',  # 例如：计算机或相关专业
            r'([\u4e00-\u9fa5]+)专业优先',  # 例如：计算机专业优先
            # 复杂的表述方式
            r'如[：:]?([^，。；,;]+)等[相关]?专业',
            r'专业[为是]([^，。；,;]+)'
        ]

        # 尝试使用模式提取
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                extracted = match.group(1).strip()
                # 处理提取结果
                return self._process_extracted_major(extracted)

        # 如果没有匹配到模式，尝试提取多个专业
        # 常见的多专业分隔符
        multi_major_patterns = [
            r'([\w\s]+)[、，,/\\]([\w\s]+)(?:[、，,/\\]([\w\s]+))?(?:[、，,/\\]([\w\s]+))?等?相关?专业',
            r'专业[：:]?([\w\s]+)[、，,/\\]([\w\s]+)(?:[、，,/\\]([\w\s]+))?(?:[、，,/\\]([\w\s]+))?'
        ]

        for pattern in multi_major_patterns:
            match = re.search(pattern, text)
            if match:
                # 获取第一个非空的匹配组
                for group in match.groups():
                    if group and group.strip():
                        return self._process_extracted_major(group.strip())

        # 如果没有匹配到模式，尝试查找专业词典中的专业
        # 优先查找完整匹配
        for major in self.major_dict:
            if major in text:
                return major

        # 检查别名
        for alias, standard in self.alias_map.items():
            if alias in text and len(alias) > 1:  # 避免单字符匹配
                return standard

        # 检查专业分类
        for category, keywords in self.MAJOR_CATEGORIES.items():
            for keyword in keywords:
                if keyword in text:
                    # 返回该分类下的第一个专业作为代表
                    if category in self.category_map and self.category_map[category]:
                        return self.category_map[category][0]
                    return f"{category}相关专业"

        # 如果还是没找到，返回清理后的文本
        # 去除常见的修饰词
        cleaned_text = text
        for keyword in self.MAJOR_KEYWORDS:
            cleaned_text = cleaned_text.replace(keyword, '')

        # 特殊处理常见专业简称
        if '计算机' in cleaned_text:
            return '计算机科学与技术'
        if '软件' in cleaned_text:
            return '软件工程'
        if '电子' in cleaned_text:
            return '电子信息工程'
        if '金融' in cleaned_text:
            return '金融学'
        if '法律' in cleaned_text:
            return '法学'
        if '医学' in cleaned_text or '医科' in cleaned_text:
            return '临床医学'

        # 如果文本过长，可能不是专业名称
        if len(cleaned_text.strip()) > 20:
            # 尝试提取最可能的专业名称
            words = cleaned_text.split()
            for word in words:
                if len(word) >= 2 and word in self.major_dict:
                    return word
            # 如果没有找到，返回一个合理的默认值
            return "专业不限"

        return cleaned_text.strip()

    def _process_extracted_major(self, extracted: str) -> str:
        """
        处理提取出的专业名称

        Args:
            extracted: 提取出的专业名称

        Returns:
            str: 处理后的专业名称
        """
        # 清理文本
        extracted = extracted.strip()

        # 特殊处理常见专业简称
        if extracted == '计算机':
            return '计算机科学与技术'
        if extracted == '软件':
            return '软件工程'
        if extracted == '电子':
            return '电子信息工程'
        if extracted == '金融':
            return '金融学'
        if extracted == '法律':
            return '法学'
        if extracted == '医学' or extracted == '医科':
            return '临床医学'

        # 检查是否在专业词典中
        if extracted in self.major_dict:
            return extracted

        # 检查是否是别名
        if extracted in self.alias_map:
            return self.alias_map[extracted]

        # 检查是否包含"类"、"相关"等后缀
        for suffix in ["类", "相关", "等", "及"]:
            if extracted.endswith(suffix):
                base_major = extracted[:-len(suffix)].strip()
                # 检查基础专业
                if base_major in self.major_dict:
                    return base_major
                # 检查基础专业是否是别名
                if base_major in self.alias_map:
                    return self.alias_map[base_major]

        # 返回原始提取结果
        return extracted

    def _find_best_match(self, major: str) -> Tuple[str, float]:
        """
        查找最匹配的专业

        Args:
            major: 专业名称

        Returns:
            Tuple[str, float]: (最匹配的专业, 相似度)
        """
        best_match = ""
        best_similarity = 0.0

        # 遍历专业词典
        for dict_major in self.major_dict:
            similarity = difflib.SequenceMatcher(None, major, dict_major).ratio()
            if similarity > best_similarity:
                best_similarity = similarity
                best_match = dict_major

        return best_match, best_similarity

    def _find_similar_majors(self, major: str, threshold: float = 0.6, max_count: int = 5) -> List[str]:
        """
        查找相似专业

        Args:
            major: 专业名称
            threshold: 相似度阈值
            max_count: 最大返回数量

        Returns:
            List[str]: 相似专业列表
        """
        # 如果专业不在词典中，尝试标准化
        if major not in self.major_dict:
            # 检查是否是别名
            if major in self.alias_map:
                major = self.alias_map[major]
            else:
                # 使用字符串相似度查找最匹配的专业
                best_match = ""
                best_similarity = 0.0
                for dict_major in self.major_dict:
                    similarity = difflib.SequenceMatcher(None, major, dict_major).ratio()
                    if similarity > best_similarity:
                        best_similarity = similarity
                        best_match = dict_major

                if best_similarity >= 0.7:
                    major = best_match
                else:
                    # 如果没有找到匹配的专业，返回空列表
                    return []

        # 获取专业信息中的相关专业
        info = self.major_dict.get(major, {})

        # 检查similar字段
        similar = info.get('similar', [])
        if similar:
            return similar[:max_count]

        # 否则使用相似度矩阵查找相似专业
        if major in self.similarity_matrix:
            similarities = self.similarity_matrix[major]
            similar_majors = [(m, s) for m, s in similarities.items() if s >= threshold and m != major]
            similar_majors.sort(key=lambda x: x[1], reverse=True)
            return [m for m, _ in similar_majors[:max_count]]

        # 如果以上方法都没有找到相似专业，使用分类查找
        category = info.get('category', '')
        if category and category in self.category_map:
            # 获取同一分类下的其他专业
            same_category_majors = [m for m in self.category_map[category] if m != major]
            if same_category_majors:
                # 按照名称相似度排序
                same_category_majors.sort(
                    key=lambda m: difflib.SequenceMatcher(None, major, m).ratio(),
                    reverse=True
                )
                return same_category_majors[:max_count]

        return []

    def analyze_major_match(self, major_requirement: str, user_major: str) -> Dict[str, Any]:
        """
        分析专业匹配度

        Args:
            major_requirement: 岗位专业要求
            user_major: 用户专业

        Returns:
            Dict[str, Any]: 匹配分析结果
        """
        # 检查缓存
        cache_key = f"{major_requirement}|{user_major}"
        if cache_key in self.match_cache:
            cache_entry = self.match_cache[cache_key]
            self.logger.debug(f"Cache hit for analyze_major_match: {cache_key}")
            return cache_entry['result']

        # 标准化专业要求和用户专业
        std_requirement, req_similar = self.standardize_major(major_requirement)
        std_user_major, user_similar = self.standardize_major(user_major)

        # 计算匹配分数
        match_score = self._calculate_match_score(std_requirement, std_user_major)

        # 生成匹配理由
        match_reason = self._generate_match_reason(std_requirement, std_user_major, match_score, req_similar, user_similar)

        # 生成结果
        result = {
            'match_score': match_score,
            'reason': match_reason,
            'standardized_requirement': std_requirement,
            'standardized_user_major': std_user_major,
            'requirement_similar_majors': req_similar,
            'user_similar_majors': user_similar
        }

        # 更新缓存
        self.match_cache[cache_key] = {
            'result': result,
            'timestamp': datetime.now().isoformat()
        }
        self._save_cache(self.match_cache, self.match_cache_path)

        return result

    def _calculate_match_score(self, requirement: str, user_major: str) -> int:
        """
        计算专业匹配分数

        Args:
            requirement: 标准化后的专业要求
            user_major: 标准化后的用户专业

        Returns:
            int: 匹配分数，范围[0, 100]
        """
        # 处理特殊情况
        if requirement == "专业不限":
            return 100  # 专业不限，任何专业都完全匹配

        # 如果专业完全相同，分数为100
        if requirement == user_major:
            return 100

        # 标准化专业名称（处理别名）
        std_requirement = self.alias_map.get(requirement, requirement)
        std_user_major = self.alias_map.get(user_major, user_major)

        # 如果标准化后的专业相同，分数为95
        if std_requirement == std_user_major:
            return 95

        # 处理包含"相关专业"的情况
        if "相关专业" in std_requirement:
            # 提取基础专业名称
            base_major = std_requirement.replace("相关专业", "").strip()
            if base_major:
                # 计算用户专业与基础专业的相似度
                similarity = self._calculate_major_similarity(base_major, std_user_major)
                # 相似度高于0.7认为是相关专业
                if similarity >= 0.7:
                    return 90
                # 相似度在0.5-0.7之间认为是较相关专业
                elif similarity >= 0.5:
                    return 80
                # 相似度在0.3-0.5之间认为是弱相关专业
                elif similarity >= 0.3:
                    return 60

        # 检查是否在相关专业列表中
        req_similar = []
        user_similar = []

        # 获取专业要求的相关专业
        if std_requirement in self.major_dict:
            req_info = self.major_dict[std_requirement]
            req_similar = req_info.get('similar', [])

            # 检查专业要求是否包含"专业优先"
            if "优先" in requirement:
                # 如果是"优先"而非"必须"，增加基础匹配分数
                base_priority_score = 60
            else:
                base_priority_score = 0

        # 获取用户专业的相关专业
        if std_user_major in self.major_dict:
            user_info = self.major_dict[std_user_major]
            user_similar = user_info.get('similar', [])

        # 如果用户专业在专业要求的相关专业列表中，分数为90
        if std_user_major in req_similar:
            return 90

        # 如果专业要求在用户专业的相关专业列表中，分数为85
        if std_requirement in user_similar:
            return 85

        # 检查相关专业列表的交集
        if req_similar and user_similar:
            intersection = set(req_similar).intersection(set(user_similar))
            if intersection:
                # 有共同的相关专业，增加匹配分数
                return 80

        # 计算专业相似度
        similarity = self._calculate_major_similarity(std_requirement, std_user_major)

        # 基于相似度的分数映射
        if similarity >= 0.8:
            return 85  # 非常相似
        elif similarity >= 0.6:
            return 75  # 较相似
        elif similarity >= 0.4:
            return 60  # 中等相似
        elif similarity >= 0.2:
            return 40  # 弱相似

        # 如果两个专业都在专业词典中
        if std_requirement in self.major_dict and std_user_major in self.major_dict:
            # 检查专业分类
            req_info = self.major_dict[std_requirement]
            user_info = self.major_dict[std_user_major]

            # 基础分数 - 不同分类的专业基础分数更低
            if req_info.get('category') == user_info.get('category'):
                base_score = 50  # 同一分类的基础分数
            else:
                base_score = 30  # 不同分类的基础分数

                # 特殊情况：工学和医学的匹配度更低
                if (req_info.get('category') == '工学' and user_info.get('category') == '医学') or \
                   (req_info.get('category') == '医学' and user_info.get('category') == '工学'):
                    base_score = 20

                # 特殊情况：理工科和文科的匹配度较低
                elif (req_info.get('category') in ['工学', '理学'] and user_info.get('category') in ['文学', '法学']) or \
                     (req_info.get('category') in ['文学', '法学'] and user_info.get('category') in ['工学', '理学']):
                    base_score = 25

            # 如果学科门类相同，增加分数
            if req_info.get('discipline', '') == user_info.get('discipline', ''):
                base_score += 15

            # 如果专业代码前两位相同（同一大类），增加分数
            code1 = req_info.get('code', '')
            code2 = user_info.get('code', '')
            if code1 and code2 and len(code1) >= 4 and len(code2) >= 4 and code1[:2] == code2[:2]:
                base_score += 10

            # 计算名称相似度
            name_similarity = difflib.SequenceMatcher(None, std_requirement, std_user_major).ratio()
            similarity_score = int(name_similarity * 25)  # 最多25分

            # 计算总分
            score = base_score + similarity_score

            # 如果有"优先"加分
            if base_priority_score > 0:
                score = max(score, base_priority_score)

            return min(score, 100)
        else:
            # 如果专业不在词典中，使用字符串相似度
            text_similarity = difflib.SequenceMatcher(None, requirement, user_major).ratio()

            # 如果相似度很高，可能是同一专业的不同表述
            if text_similarity >= 0.8:
                return 90

            # 否则基于相似度计算分数
            base_score = 40  # 基础分数
            similarity_score = int(text_similarity * 50)  # 最多50分

            return base_score + similarity_score

    def _generate_match_reason(self, requirement: str, user_major: str,
                              match_score: int, req_similar: List[str],
                              user_similar: List[str]) -> str:
        """
        生成匹配理由

        Args:
            requirement: 标准化后的专业要求
            user_major: 标准化后的用户专业
            match_score: 匹配分数
            req_similar: 专业要求的相似专业
            user_similar: 用户专业的相似专业

        Returns:
            str: 匹配理由
        """
        if match_score >= 95:
            return f"您的专业({user_major})与岗位要求的专业({requirement})完全匹配。"

        if match_score >= 80:
            return f"您的专业({user_major})与岗位要求的专业({requirement})高度相关，匹配度很高。"

        if match_score >= 70:
            return f"您的专业({user_major})与岗位要求的专业({requirement})相关性较高，基本符合要求。"

        if match_score >= 60:
            # 检查用户专业是否在专业要求的相似专业列表中
            if user_major in req_similar:
                return f"您的专业({user_major})是岗位要求专业({requirement})的相关专业，可以考虑申请。"

            # 检查专业要求是否在用户专业的相似专业列表中
            if requirement in user_similar:
                return f"岗位要求的专业({requirement})是您专业({user_major})的相关专业，可以考虑申请。"

            return f"您的专业({user_major})与岗位要求的专业({requirement})有一定相关性，但匹配度不高。"

        if match_score >= 40:
            return f"您的专业({user_major})与岗位要求的专业({requirement})相关性较低，可能需要额外的专业背景或技能。"

        return f"您的专业({user_major})与岗位要求的专业({requirement})相关性很低，不太符合岗位要求。"

    def batch_analyze_majors(self, job_requirements: List[str], user_major: str) -> List[Dict[str, Any]]:
        """
        批量分析多个岗位的专业匹配度

        Args:
            job_requirements: 多个岗位的专业要求列表
            user_major: 用户专业

        Returns:
            List[Dict[str, Any]]: 匹配分析结果列表
        """
        results = []
        for requirement in job_requirements:
            result = self.analyze_major_match(requirement, user_major)
            results.append(result)

        # 按匹配分数排序
        results.sort(key=lambda x: x['match_score'], reverse=True)
        return results

    def recommend_jobs_by_major(self, job_list: List[Dict[str, Any]], user_major: str,
                               threshold: int = 60, max_count: int = 10) -> List[Dict[str, Any]]:
        """
        根据用户专业推荐岗位

        Args:
            job_list: 岗位列表，每个岗位是一个字典，必须包含'major_requirement'字段
            user_major: 用户专业
            threshold: 匹配分数阈值
            max_count: 最大推荐数量

        Returns:
            List[Dict[str, Any]]: 推荐岗位列表，每个岗位增加了'match_score'和'match_reason'字段
        """
        # 分析每个岗位的匹配度
        job_matches = []
        for job in job_list:
            # 确保岗位包含专业要求字段
            if 'major_requirement' not in job:
                continue

            # 分析匹配度
            match_result = self.analyze_major_match(job['major_requirement'], user_major)

            # 如果匹配分数超过阈值，添加到结果列表
            if match_result['match_score'] >= threshold:
                # 创建岗位副本，避免修改原始数据
                job_copy = job.copy()

                # 添加匹配信息
                job_copy['match_score'] = match_result['match_score']
                job_copy['match_reason'] = match_result['reason']

                job_matches.append(job_copy)

        # 按匹配分数排序
        job_matches.sort(key=lambda x: x['match_score'], reverse=True)

        # 返回前max_count个结果
        return job_matches[:max_count]

    def find_alternative_majors(self, major: str, min_similarity: float = 0.5, max_count: int = 10) -> List[Dict[str, Any]]:
        """
        查找替代专业，可用于专业提升建议

        Args:
            major: 专业名称
            min_similarity: 最小相似度
            max_count: 最大返回数量

        Returns:
            List[Dict[str, Any]]: 替代专业列表，每个专业包含名称、相似度和建议
        """
        # 标准化专业
        std_major, _ = self.standardize_major(major)

        # 获取专业信息
        major_info = self.major_dict.get(std_major, {})
        major_category = major_info.get('category', '')

        # 创建结果列表
        alternatives = []

        # 如果标准化后的专业不在词典中，使用字符串相似度查找相似专业
        if std_major not in self.major_dict:
            for dict_major in self.major_dict:
                similarity = difflib.SequenceMatcher(None, std_major, dict_major).ratio()
                if similarity >= min_similarity:
                    # 获取专业信息
                    alt_info = self.major_dict.get(dict_major, {})
                    category = alt_info.get('category', '未知')
                    discipline = alt_info.get('discipline', '未知')

                    # 生成更详细的建议
                    if similarity >= 0.8:
                        suggestion = f"这个专业与您的专业非常相似，可以直接应用您的专业知识。建议了解{dict_major}的特定课程和技能要求。"
                    elif similarity >= 0.6:
                        suggestion = f"这个专业与您的专业较为相似，建议学习{dict_major}的核心课程和专业技能，弥补知识差距。"
                    else:
                        suggestion = f"这个专业与您的专业相关性一般，需要系统学习{dict_major}的基础知识和专业技能。"

                    alternatives.append({
                        'major': dict_major,
                        'similarity': similarity,
                        'category': category,
                        'discipline': discipline,
                        'suggestion': suggestion
                    })

            # 按相似度排序
            alternatives.sort(key=lambda x: x['similarity'], reverse=True)
            return alternatives[:max_count]

        # 如果专业在词典中，使用相似度矩阵和专业信息查找相似专业
        if std_major in self.similarity_matrix:
            similarities = self.similarity_matrix[std_major]

            # 1. 首先添加相似度高的专业
            for other_major, similarity in similarities.items():
                if similarity >= min_similarity and other_major != std_major:
                    # 获取专业信息
                    alt_info = self.major_dict.get(other_major, {})
                    category = alt_info.get('category', '未知')
                    discipline = alt_info.get('discipline', '未知')
                    employment = alt_info.get('employment_rate', 0)
                    demand = alt_info.get('market_demand', '未知')

                    # 根据相似度和专业信息生成个性化建议
                    if similarity >= 0.8:
                        if employment > 0.8:
                            suggestion = f"这个专业与您的专业高度相似，且就业前景良好。建议学习{other_major}特有的专业技能，可以拓宽就业方向。"
                        else:
                            suggestion = f"这个专业与您的专业高度相似，可以利用已有知识快速掌握。建议了解{other_major}的行业应用场景和特定技能要求。"
                    elif similarity >= 0.6:
                        if major_category == category:
                            suggestion = f"这个专业与您的专业属于同一学科门类，有共同的知识基础。建议学习{other_major}的核心课程和专业技能，弥补知识差距。"
                        else:
                            suggestion = f"这个专业与您的专业有一定相关性，但学科门类不同。建议系统学习{other_major}的基础理论和专业知识，可以拓展跨学科能力。"
                    else:
                        suggestion = f"这个专业与您的专业相关性一般，需要投入较多时间学习{other_major}的基础知识和专业技能。建议从基础课程开始，逐步深入。"

                    # 添加市场需求信息（如果有）
                    if demand and demand != '未知':
                        suggestion += f" 该专业市场需求{demand}。"

                    alternatives.append({
                        'major': other_major,
                        'similarity': similarity,
                        'category': category,
                        'discipline': discipline,
                        'suggestion': suggestion
                    })

            # 2. 如果结果不足，添加同一学科门类但相似度较低的专业
            if len(alternatives) < max_count and major_category:
                same_category_majors = []
                for dict_major, info in self.major_dict.items():
                    if info.get('category') == major_category and dict_major != std_major and dict_major not in [a['major'] for a in alternatives]:
                        similarity = self._calculate_major_similarity(std_major, dict_major)
                        if similarity >= min_similarity * 0.8:  # 降低相似度要求
                            same_category_majors.append({
                                'major': dict_major,
                                'similarity': similarity,
                                'category': major_category,
                                'discipline': info.get('discipline', '未知'),
                                'suggestion': f"这个专业与您的专业属于同一学科门类({major_category})，可以作为拓展方向。建议了解{dict_major}的特定知识体系和应用领域。"
                            })

                # 按相似度排序并添加到结果中
                same_category_majors.sort(key=lambda x: x['similarity'], reverse=True)
                alternatives.extend(same_category_majors[:max_count - len(alternatives)])

            # 3. 如果结果仍不足，添加就业前景好的专业
            if len(alternatives) < max_count:
                employment_majors = []
                for dict_major, info in self.major_dict.items():
                    if dict_major != std_major and dict_major not in [a['major'] for a in alternatives]:
                        employment = info.get('employment_rate', 0)
                        demand = info.get('market_demand', '')
                        if employment > 0.8 or (demand and '高' in demand):
                            similarity = self._calculate_major_similarity(std_major, dict_major)
                            if similarity >= min_similarity * 0.7:  # 进一步降低相似度要求
                                employment_majors.append({
                                    'major': dict_major,
                                    'similarity': similarity,
                                    'category': info.get('category', '未知'),
                                    'discipline': info.get('discipline', '未知'),
                                    'suggestion': f"这个专业就业前景较好，虽然与您的专业相关性不高，但可以作为职业转型的选择。建议系统学习{dict_major}的专业知识。"
                                })

                # 按相似度排序并添加到结果中
                employment_majors.sort(key=lambda x: x['similarity'], reverse=True)
                alternatives.extend(employment_majors[:max_count - len(alternatives)])

            # 按相似度排序
            alternatives.sort(key=lambda x: x['similarity'], reverse=True)
            return alternatives[:max_count]

        return []
