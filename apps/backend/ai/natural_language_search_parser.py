#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
自然语言搜索解析器
提供本地自然语言搜索查询解析功能，减少对外部API的依赖
"""

import os
import json
import logging
import re
from typing import Dict, List, Any, Optional
from datetime import datetime
import pickle


class NaturalLanguageSearchParser:
    """自然语言搜索解析器，提供本地自然语言搜索查询解析功能"""

    # 地点关键词
    LOCATION_KEYWORDS = [
        # 直辖市
        "北京", "上海", "天津", "重庆",
        # 一线城市
        "广州", "深圳",
        # 新一线城市
        "杭州", "南京", "成都", "武汉", "西安", "苏州", "郑州", "长沙", "东莞", "青岛", "沈阳", "宁波", "昆明", "合肥",
        # 二线城市
        "福州", "厦门", "济南", "大连", "无锡", "佛山", "南通", "南宁", "长春", "哈尔滨", "石家庄", "常州", "泉州", "南昌", "贵阳", "太原", "烟台", "嘉兴", "南阳", "洛阳", "海口", "乌鲁木齐", "兰州", "呼和浩特", "银川", "西宁", "拉萨",
        # 行政区划
        "省", "市", "区", "县", "地区", "自治区", "特别行政区"
    ]

    # 学历关键词
    EDUCATION_KEYWORDS = {
        "博士": ["博士", "博士学位", "博士研究生", "博士及以上", "博士以上"],
        "硕士": ["硕士", "硕士学位", "硕士研究生", "研究生", "硕士及以上", "硕士以上"],
        "本科": ["本科", "学士", "大学本科", "全日制本科", "本科及以上", "本科以上", "学士学位"],
        "大专": ["大专", "专科", "高职", "高专", "大专及以上", "大专以上", "专科及以上"],
        "中专": ["中专", "职高", "职业高中", "技校", "中专及以上", "中专以上"],
        "高中": ["高中", "普高", "普通高中", "高中及以上", "高中以上"]
    }

    # 经验关键词
    EXPERIENCE_KEYWORDS = {
        "应届生": ["应届生", "应届毕业生", "应届", "毕业生", "无经验", "零经验", "刚毕业", "在校生", "学生"],
        "经验不限": ["经验不限", "不限经验", "不要求经验", "无需经验", "无经验要求"],
        "1年以下": ["1年以下", "一年以下", "1年内", "一年内", "一年左右", "1年左右", "不到一年", "不到1年"],
        "1-3年": ["1-3年", "1到3年", "一到三年", "1至3年", "一至三年", "1-2年", "一到两年", "2-3年", "两到三年"],
        "3-5年": ["3-5年", "3到5年", "三到五年", "3至5年", "三至五年", "3-4年", "三到四年", "4-5年", "四到五年"],
        "5-10年": ["5-10年", "5到10年", "五到十年", "5至10年", "五至十年", "5年以上", "五年以上", "5-8年", "五到八年", "8-10年", "八到十年"],
        "10年以上": ["10年以上", "十年以上", "高级", "资深", "专家", "十年经验", "10年经验", "十年工作经验", "10年工作经验"]
    }

    # 岗位类型关键词
    JOB_TYPE_KEYWORDS = {
        "全职": ["全职", "正式", "长期", "全日制", "固定工", "正式工"],
        "兼职": ["兼职", "非全日制", "临时工", "小时工", "钟点工", "业余", "非全职"],
        "实习": ["实习", "实习生", "见习", "实训", "学徒", "培训生"],
        "校招": ["校招", "校园招聘", "应届生招聘", "校园宣讲", "校园招募", "高校招聘"],
        "社招": ["社招", "社会招聘", "社会人员", "在职人员"],
        "事业编": ["事业编", "事业单位", "事业编制", "事业岗位", "事业身份", "事业性质"],
        "公务员": ["公务员", "公务员考试", "公考", "行政编制", "行政岗位", "政府部门"],
        "国企": ["国企", "国有企业", "央企", "国营", "国有控股"],
        "私企": ["私企", "民营企业", "私营企业", "民企"],
        "外企": ["外企", "外资企业", "合资企业", "跨国公司", "国际企业"],
        "创业": ["创业", "合伙人", "联合创始人", "创始团队", "初创企业"],
        "灵活就业": ["灵活就业", "自由职业", "自由工作者", "自由撰稿人", "自由接单"]
    }

    # 薪资关键词
    SALARY_KEYWORDS = {
        "面议": ["面议", "薪资面议", "待遇面议", "协商", "薪资协商", "待遇协商", "薪酬面议"],
        "3k以下": ["3k以下", "3000以下", "三千以下", "3000元以下", "3000元/月以下", "月薪3000以下"],
        "3k-5k": ["3k-5k", "3000-5000", "三千到五千", "3000到5000", "3000-5000元", "月薪3000-5000", "月薪3k-5k"],
        "5k-8k": ["5k-8k", "5000-8000", "五千到八千", "5000到8000", "5000-8000元", "月薪5000-8000", "月薪5k-8k"],
        "8k-10k": ["8k-10k", "8000-10000", "八千到一万", "8000到10000", "8000-10000元", "月薪8000-10000", "月薪8k-10k"],
        "10k-15k": ["10k-15k", "10000-15000", "一万到一万五", "10000到15000", "10000-15000元", "月薪10000-15000", "月薪10k-15k"],
        "15k-20k": ["15k-20k", "15000-20000", "一万五到两万", "15000到20000", "15000-20000元", "月薪15000-20000", "月薪15k-20k"],
        "20k-30k": ["20k-30k", "20000-30000", "两万到三万", "20000到30000", "20000-30000元", "月薪20000-30000", "月薪20k-30k"],
        "30k-50k": ["30k-50k", "30000-50000", "三万到五万", "30000到50000", "30000-50000元", "月薪30000-50000", "月薪30k-50k"],
        "50k以上": ["50k以上", "50000以上", "五万以上", "50000元以上", "月薪50000以上", "月薪50k以上", "高薪", "超高薪"],
        "年薪制": ["年薪", "年薪制", "年薪xx万", "年薪百万", "百万年薪", "年薪50万", "年薪百万以上"]
    }

    # 专业关键词
    MAJOR_KEYWORDS = {
        "计算机": ["计算机", "软件", "编程", "开发", "IT", "互联网", "信息技术", "程序员", "软件工程", "计算机科学", "人工智能", "大数据", "云计算", "网络安全", "前端", "后端", "全栈", "Java", "Python", "C++", "JavaScript", "数据库", "系统架构"],
        "金融": ["金融", "财务", "会计", "审计", "经济", "投资", "证券", "银行", "保险", "基金", "资产管理", "风控", "理财", "税务", "财会", "金融分析", "投资分析", "风险管理"],
        "管理": ["管理", "行政", "人力资源", "HR", "人事", "行政管理", "办公室", "总助", "秘书", "文员", "招聘", "培训", "绩效", "薪酬", "员工关系", "组织发展"],
        "市场": ["市场", "营销", "销售", "推广", "公关", "广告", "品牌", "市场调研", "市场分析", "产品经理", "产品运营", "内容运营", "用户运营", "活动策划", "商务拓展", "客户关系"],
        "教育": ["教育", "教师", "老师", "培训", "讲师", "教学", "教务", "教研", "教育咨询", "课程设计", "学科教学", "幼教", "小学", "中学", "高中", "高等教育", "职业教育"],
        "医疗": ["医疗", "医学", "护理", "药学", "临床", "医生", "护士", "药剂师", "医技", "康复", "公共卫生", "医疗器械", "生物医学", "中医", "西医", "口腔", "眼科", "内科", "外科"],
        "法律": ["法律", "法学", "律师", "法务", "合规", "知识产权", "合同管理", "诉讼", "仲裁", "法律顾问", "法律咨询", "民法", "商法", "经济法", "刑法", "行政法"],
        "工程": ["工程", "土木", "建筑", "机械", "电气", "结构", "给排水", "暖通", "电气工程", "机械设计", "自动化", "工业设计", "工程造价", "工程监理", "施工管理", "质量控制"],
        "设计": ["设计", "美工", "UI", "UX", "平面设计", "产品设计", "交互设计", "用户体验", "视觉设计", "工业设计", "室内设计", "景观设计", "服装设计", "动画设计", "游戏设计"],
        "文学": ["文学", "传媒", "新闻", "编辑", "写作", "记者", "出版", "媒体", "文案", "策划", "内容创作", "文学创作", "新闻采编", "广播电视", "影视制作", "数字媒体"],
        "理学": ["理学", "数学", "物理", "化学", "生物", "地理", "天文", "地质", "统计学", "应用数学", "理论物理", "分析化学", "有机化学", "分子生物学", "生态学"],
        "农学": ["农学", "园艺", "林学", "畜牧", "水产", "农业经济", "农业工程", "植物保护", "土壤学", "作物学", "动物科学", "农业资源利用"],
        "艺术": ["艺术", "音乐", "美术", "舞蹈", "戏剧", "影视", "摄影", "书法", "绘画", "雕塑", "艺术设计", "艺术教育", "艺术管理", "艺术史论"],
        "体育": ["体育", "运动", "健身", "教练", "体育教育", "运动训练", "体育管理", "体育经济", "运动康复", "体育赛事", "体育传媒"]
    }

    # 排除词
    EXCLUDE_WORDS = ["找", "寻找", "查找", "搜索", "希望", "想要", "需要", "帮我", "请", "麻烦", "谢谢", "我想", "我要", "我需要", "我希望", "我想找", "我要找", "帮我找", "请帮我", "请问", "有没有", "是否有", "能否", "可以", "可否"]

    # 福利待遇关键词
    BENEFITS_KEYWORDS = {
        "五险一金": ["五险一金", "五险", "社保", "保险", "公积金", "医保", "养老保险", "失业保险", "工伤保险", "生育保险"],
        "年终奖": ["年终奖", "年终分红", "年底双薪", "双薪", "年终双薪", "年终福利", "年终激励"],
        "带薪年假": ["带薪年假", "带薪休假", "年假", "法定假日", "法定节假日", "带薪病假", "调休"],
        "加班费": ["加班费", "加班补助", "加班补贴", "加班工资", "加班报酬"],
        "餐补": ["餐补", "餐饮补贴", "餐费补贴", "免费工作餐", "免费午餐", "工作餐"],
        "交通补贴": ["交通补贴", "交通补助", "车补", "油补", "通勤补贴"],
        "通讯补贴": ["通讯补贴", "话费补贴", "电话补贴", "手机补贴"],
        "住房补贴": ["住房补贴", "住房公积金", "住房津贴", "租房补贴", "购房补贴"],
        "医疗保险": ["医疗保险", "商业医疗保险", "补充医疗保险", "医疗福利", "健康体检"],
        "培训机会": ["培训机会", "在职培训", "专业培训", "技能培训", "学习机会", "进修机会", "继续教育"],
        "晋升空间": ["晋升空间", "晋升机会", "职业发展", "职业规划", "发展空间", "成长空间"],
        "弹性工作": ["弹性工作", "弹性工作制", "弹性上下班", "自由办公", "远程办公", "在家办公"]
    }

    # 工作城市等级
    CITY_TIERS = {
        "一线城市": ["北京", "上海", "广州", "深圳"],
        "新一线城市": ["成都", "杭州", "重庆", "西安", "苏州", "武汉", "南京", "天津", "郑州", "长沙", "东莞", "宁波", "佛山", "合肥", "青岛"],
        "二线城市": ["沈阳", "厦门", "福州", "济南", "大连", "无锡", "昆明", "南宁", "南昌", "贵阳", "太原", "哈尔滨", "长春", "石家庄", "常州", "泉州", "南通", "温州", "金华", "嘉兴", "珠海", "惠州", "中山", "台州", "烟台", "兰州", "海口", "乌鲁木齐", "呼和浩特", "银川", "西宁", "拉萨"]
    }

    def __init__(self, logger: Optional[logging.Logger] = None, cache_dir: Optional[str] = None):
        """
        初始化自然语言搜索解析器

        Args:
            logger: 日志记录器
            cache_dir: 缓存目录，默认为.cache
        """
        self.logger = logger or logging.getLogger(self.__class__.__name__)

        # 设置缓存目录
        if cache_dir is None:
            project_root = os.path.abspath(os.path.join(
                os.path.dirname(__file__), '..', '..'
            ))
            self.cache_dir = os.path.join(project_root, ".cache")
        else:
            self.cache_dir = cache_dir

        # 确保缓存目录存在
        os.makedirs(self.cache_dir, exist_ok=True)

        # 缓存文件路径
        self.cache_path = os.path.join(self.cache_dir, "nl_search_cache.pkl")

        # 初始化缓存
        self.cache: Dict[str, Dict[str, Any]] = self._load_cache()

    def _load_cache(self) -> Dict[str, Dict[str, Any]]:
        """
        加载缓存

        Returns:
            Dict[str, Dict[str, Any]]: 缓存数据
        """
        try:
            if os.path.exists(self.cache_path):
                with open(self.cache_path, 'rb') as f:
                    cache = pickle.load(f)
                self.logger.info(f"Loaded {len(cache)} cache entries from {self.cache_path}")
                return cache
            else:
                self.logger.info(f"Cache file not found, creating new cache: {self.cache_path}")
                return {}
        except Exception as e:
            self.logger.error(f"Error loading cache: {e}")
            return {}

    def _save_cache(self) -> bool:
        """
        保存缓存

        Returns:
            bool: 是否成功保存
        """
        try:
            with open(self.cache_path, 'wb') as f:
                pickle.dump(self.cache, f)
            self.logger.info(f"Saved {len(self.cache)} cache entries to {self.cache_path}")
            return True
        except Exception as e:
            self.logger.error(f"Error saving cache: {e}")
            return False

    def _extract_keywords(self, query: str) -> List[str]:
        """
        提取关键词

        Args:
            query: 查询字符串

        Returns:
            List[str]: 关键词列表
        """
        # 清理查询字符串
        cleaned_query = query.lower()

        # 移除排除词
        for word in self.EXCLUDE_WORDS:
            cleaned_query = cleaned_query.replace(word, " ")

        # 分词
        words = re.findall(r'[\w\u4e00-\u9fa5]+', cleaned_query)

        # 过滤短词
        keywords = [word for word in words if len(word) > 1]

        return keywords

    def _extract_location(self, query: str) -> Dict[str, Any]:
        """
        提取地点

        Args:
            query: 查询字符串

        Returns:
            Dict[str, Any]: 地点信息，包含地点名称和城市等级
        """
        result: Dict[str, Any] = {"location": None, "city_tier": None}

        # 先检查是否直接提到城市等级
        for tier, cities in self.CITY_TIERS.items():
            if tier in query:
                result["city_tier"] = tier
                break

        # 然后提取具体地点
        for location in self.LOCATION_KEYWORDS:
            if location in query:
                # 尝试提取完整地点（如"北京市海淀区"）
                pattern = f"[\\w\\u4e00-\\u9fa5]*{location}[\\w\\u4e00-\\u9fa5]*"
                match = re.search(pattern, query)
                if match:
                    result["location"] = match.group(0)
                    # 如果没有直接提到城市等级，尝试根据地点判断
                    if not result["city_tier"]:
                        for tier, cities in self.CITY_TIERS.items():
                            if any(city in str(result["location"]) for city in cities):
                                result["city_tier"] = tier
                                break
                    return result

                result["location"] = location
                # 如果没有直接提到城市等级，尝试根据地点判断
                if not result["city_tier"]:
                    for tier, cities in self.CITY_TIERS.items():
                        if location in cities:
                            result["city_tier"] = tier
                            break
                return result

        return result

    def _extract_education(self, query: str) -> Optional[str]:
        """
        提取学历要求

        Args:
            query: 查询字符串

        Returns:
            Optional[str]: 学历要求
        """
        for edu_level, keywords in self.EDUCATION_KEYWORDS.items():
            for keyword in keywords:
                if keyword in query:
                    return edu_level
        return None

    def _extract_experience(self, query: str) -> Optional[str]:
        """
        提取经验要求

        Args:
            query: 查询字符串

        Returns:
            Optional[str]: 经验要求
        """
        for exp_level, keywords in self.EXPERIENCE_KEYWORDS.items():
            for keyword in keywords:
                if keyword in query:
                    return exp_level
        return None

    def _extract_job_type(self, query: str) -> Optional[str]:
        """
        提取岗位类型

        Args:
            query: 查询字符串

        Returns:
            Optional[str]: 岗位类型
        """
        for job_type, keywords in self.JOB_TYPE_KEYWORDS.items():
            for keyword in keywords:
                if keyword in query:
                    return job_type
        return None

    def _extract_salary(self, query: str) -> Optional[str]:
        """
        提取薪资要求

        Args:
            query: 查询字符串

        Returns:
            Optional[str]: 薪资要求
        """
        for salary_range, keywords in self.SALARY_KEYWORDS.items():
            for keyword in keywords:
                if keyword in query:
                    return salary_range
        return None

    def _extract_major(self, query: str) -> Dict[str, Any]:
        """
        提取专业要求

        Args:
            query: 查询字符串

        Returns:
            Dict[str, Any]: 专业要求信息，包含主专业和相关专业
        """
        result: Dict[str, Any] = {"major": None, "related_majors": []}

        # 提取主专业
        for major, keywords in self.MAJOR_KEYWORDS.items():
            for keyword in keywords:
                if keyword in query:
                    result["major"] = major
                    break
            if result["major"]:
                break

        # 如果找到了主专业，尝试找相关专业
        if result["major"]:
            # 排除主专业，查找其他可能提到的专业
            for major, keywords in self.MAJOR_KEYWORDS.items():
                if major != result["major"]:
                    for keyword in keywords:
                        if keyword in query:
                            result["related_majors"].append(major)
                            break

        return result

    def _extract_benefits(self, query: str) -> List[str]:
        """
        提取福利待遇要求

        Args:
            query: 查询字符串

        Returns:
            List[str]: 福利待遇列表
        """
        benefits = []

        for benefit, keywords in self.BENEFITS_KEYWORDS.items():
            for keyword in keywords:
                if keyword in query and benefit not in benefits:
                    benefits.append(benefit)
                    break

        return benefits

    def parse_query(self, query: str) -> Dict[str, Any]:
        """
        解析自然语言搜索查询

        Args:
            query: 自然语言查询字符串

        Returns:
            Dict[str, Any]: 解析后的结构化查询条件
        """
        # 检查缓存
        cache_key = query.strip()
        if cache_key in self.cache:
            cache_entry = self.cache[cache_key]
            self.logger.debug(f"Cache hit for query: {cache_key}")
            return cache_entry['result']

        try:
            # 提取关键词
            keywords = self._extract_keywords(query)

            # 提取地点信息
            location_info = self._extract_location(query)

            # 提取学历要求
            education = self._extract_education(query)

            # 提取经验要求
            experience = self._extract_experience(query)

            # 提取岗位类型
            job_type = self._extract_job_type(query)

            # 提取薪资要求
            salary = self._extract_salary(query)

            # 提取专业要求
            major_info = self._extract_major(query)

            # 提取福利待遇
            benefits = self._extract_benefits(query)

            # 构造结果
            result = {
                "keywords": keywords,
                "location": location_info.get("location"),
                "city_tier": location_info.get("city_tier"),
                "education": education,
                "experience": experience,
                "job_type": job_type,
                "salary": salary,
                "major": major_info.get("major"),
                "related_majors": major_info.get("related_majors"),
                "benefits": benefits,
                "source": "local_parser",
                "query_time": datetime.now().isoformat()
            }

            # 过滤None值和空列表
            result = {k: v for k, v in result.items() if v is not None and (not isinstance(v, list) or len(v) > 0)}

            # 更新缓存
            self.cache[cache_key] = {
                'result': result,
                'timestamp': datetime.now().isoformat()
            }
            self._save_cache()

            # 记录日志
            self.logger.info(f"Parsed query: {query}")
            self.logger.debug(f"Parse result: {result}")

            return result

        except Exception as e:
            self.logger.error(f"Error parsing query: {e}")
            import traceback
            self.logger.debug(traceback.format_exc())

            # 返回基本的关键词搜索
            return {"keywords": query, "source": "fallback"}
