#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
招聘政策智能解读器
将复杂的事业编制招聘政策转化为通俗易懂的解读，突出重点和注意事项
"""

import os
import re
import json
import logging
import difflib
from typing import Dict, List, Optional, Any, Set

# 尝试导入jieba，如果不存在则使用简单的分词
try:
    import jieba
except ImportError:
    # 简单的分词函数，用于替代jieba
    class SimpleCut:
        @staticmethod
        def cut(text):
            # 简单按空格分词
            return text.split()

from ..utils.cache_manager import CacheManager
from apps.backend.ai import DeepSeekClient


class PolicyInterpreter:
    """招聘政策智能解读器"""

    def __init__(self, logger=None, use_local_components=True, cache_dir=None):
        """
        初始化招聘政策智能解读器

        Args:
            logger: 日志记录器
            use_local_components: 是否使用本地组件，默认为True
            cache_dir: 缓存目录，默认为None
        """
        self.logger = logger or logging.getLogger(__name__)
        self.use_local_components = use_local_components

        # 初始化缓存管理器
        self.cache_manager = CacheManager(
            cache_dir=cache_dir or os.path.join(os.path.dirname(__file__), "cache"),
            cache_name="policy_interpreter_cache"
        )

        # 初始化DeepSeek客户端
        self.deepseek_client = DeepSeekClient(logger=self.logger)

        # 初始化关键词和模式
        self.policy_keywords = [
            '政策', '规定', '办法', '条例', '规则', '要求', '流程', '程序',
            '注意事项', '重要提示', '特别说明', '温馨提示', '招聘简章', '招聘公告',
            '招聘启事', '招聘计划', '招聘方案', '招聘通知', '招聘章程', '招聘细则',
            '招聘安排', '招聘岗位', '招聘职位', '招聘条件', '招聘程序', '招聘流程',
            '招聘日程', '招聘时间', '招聘地点', '招聘单位', '招聘部门', '招聘人数',
            '事业单位', '机关单位', '公务员', '编制', '人事', '人才', '人力资源'
        ]
        self.important_keywords = [
            '必须', '务必', '一定', '严格', '重要', '关键', '特别', '注意',
            '不得', '禁止', '拒绝', '不允许', '不接受', '不可', '限制', '资格',
            '取消', '终止', '撤销', '作废', '无效', '责任', '后果', '严禁',
            '严肃', '严厉', '严正', '严格执行', '严格遵守', '严格按照', '严格要求',
            '务请', '请务必', '切记', '切勿', '谨记', '特此', '特别强调', '特别提醒',
            '重点', '重要', '重大', '重点关注', '重点提示', '重点说明', '重点注意',
            '凡是', '凡', '所有', '任何', '一切', '每个', '每位', '每名', '每一',
            '一律', '统一', '全部', '全员', '全面', '全程', '全时', '全天',
            '立即', '马上', '即刻', '当即', '立刻', '迅速', '尽快', '尽早',
            '逾期', '过期', '超时', '延误', '迟到', '缺席', '缺勤', '缺考',
            '作弊', '舞弊', '违规', '违纪', '违反', '违背', '违法', '违约',
            '取消资格', '取消应聘资格', '取消考试资格', '取消报名资格', '不予受理'
        ]

        # 政策类型关键词
        self.policy_type_keywords = {
            "报名政策": ["报名", "申请", "提交申请", "报名时间", "报名方式", "报名流程", "报名条件", "报名材料",
                     "网上报名", "现场报名", "报名表", "报名费", "报名系统", "报名入口", "报名平台", "报名网站"],
            "资格条件": ["资格条件", "应聘条件", "招聘条件", "报名条件", "学历要求", "专业要求", "工作经验", "年龄要求",
                     "资格审查", "资格复审", "资格初审", "资格预审", "资格核验", "资格认定", "资格确认", "条件限制"],
            "考试政策": ["考试", "笔试", "面试", "资格审查", "考试时间", "考试地点", "考试内容", "考试形式",
                     "考试科目", "考试大纲", "考试题型", "考试分值", "考试成绩", "成绩查询", "成绩复查", "合格分数线"],
            "录用政策": ["录用", "聘用", "录取", "公示", "体检", "考察", "签约", "入职", "试用期",
                     "政审", "背景调查", "档案审核", "资格复核", "公示期", "公示时间", "录用通知", "聘用合同"],
            "材料要求": ["材料", "证件", "证书", "复印件", "原件", "照片", "表格", "简历", "申请表",
                     "身份证", "学历证", "学位证", "资格证", "职称证", "工作证明", "推荐信", "个人陈述"],
            "薪酬福利": ["薪酬", "工资", "待遇", "福利", "保险", "公积金", "补贴", "奖金", "津贴",
                     "薪资标准", "工资待遇", "薪资待遇", "福利待遇", "五险一金", "住房补贴", "交通补贴", "绩效奖金"],
            "应届生政策": ["应届生", "应届毕业生", "应届硕士", "应届博士", "应届本科生", "应届研究生", "毕业生", "在校生",
                      "毕业证", "学位证", "就业推荐表", "实习经历", "校园招聘", "高校招聘", "校招", "毕业年度"],
            "其他政策": ["其他", "补充", "说明", "解释", "附则", "附件", "联系方式", "咨询方式",
                     "监督电话", "投诉电话", "咨询电话", "举报电话", "官方网站", "官方微信", "官方公众号", "官方平台"]
        }

        # 时间相关关键词
        self.time_keywords = [
            "时间", "日期", "期限", "截止", "开始", "结束", "年", "月", "日",
            "上午", "下午", "晚上", "点", "分", "小时", "天", "周", "星期",
            "报名时间", "考试时间", "面试时间", "公示时间", "体检时间", "考察时间",
            "资格审查时间", "领取准考证时间", "查询成绩时间", "复查时间", "申诉时间",
            "签约时间", "入职时间", "截止日期", "起始日期", "期间", "阶段", "过程",
            "之前", "之后", "以前", "以后", "届时", "当天", "当日", "次日", "工作日"
        ]

        # 地点相关关键词
        self.location_keywords = [
            "地点", "地址", "场所", "位置", "考场", "教室", "大厅", "中心",
            "楼", "室", "厅", "区", "市", "省", "县", "街道", "路",
            "报名地点", "考试地点", "面试地点", "体检地点", "考察地点", "签约地点",
            "办公室", "会议室", "礼堂", "报告厅", "招聘现场", "招聘会场", "人才市场",
            "人力资源市场", "人社局", "人力资源和社会保障局", "组织部", "单位", "部门",
            "学校", "医院", "机关", "事业单位", "行政中心", "政务中心", "服务中心"
        ]

        # 初始化政策解读模板
        self.interpretation_templates = {
            "summary": "本招聘政策主要涉及{aspects}等方面的内容。",
            "key_points": "需要特别注意的是：{points}",
            "special_notes": "特别提醒：{notes}",
            "time_sensitive": "时间敏感信息：{times}",
            "location_info": "地点信息：{locations}",
            "policy_type": "政策类型：{types}"
        }

        # 加载停用词
        self.stopwords = self._load_stopwords()

    def interpret_policy(self, policy_text: str) -> Dict[str, Any]:
        """
        解读招聘政策

        Args:
            policy_text: 招聘政策文本

        Returns:
            解读结果，包含摘要、要点和特别说明
        """
        # 检查缓存
        cache_key = f"policy_{hash(policy_text)}"
        cached_result = self.cache_manager.get(cache_key)
        if cached_result:
            self.logger.info("Using cached policy interpretation result")
            return cached_result

        # 使用本地组件解读
        if self.use_local_components:
            result = self._interpret_policy_locally(policy_text)

            # 如果本地解读结果不完整，尝试使用DeepSeek API
            if not self._is_result_complete(result) and self.deepseek_client.is_available():
                self.logger.info("Local interpretation incomplete, trying DeepSeek API")
                api_result = self._interpret_policy_with_api(policy_text)
                # 合并结果，API结果优先
                for key, value in api_result.items():
                    if value and (key not in result or not result[key]):
                        result[key] = value
        else:
            # 直接使用DeepSeek API
            result = self._interpret_policy_with_api(policy_text)

        # 缓存结果
        self.cache_manager.set(cache_key, result)

        return result

    def _load_stopwords(self) -> Set[str]:
        """
        加载停用词表

        Returns:
            停用词集合
        """
        # 常用停用词
        common_stopwords = {
            "的", "了", "和", "与", "或", "是", "在", "对", "等", "及", "之", "以",
            "为", "于", "上", "下", "由", "中", "从", "到", "将", "把", "被", "向",
            "且", "但", "而", "因", "如", "若", "则", "所", "以", "乃", "其", "该",
            "此", "这", "那", "各", "每", "某", "凡", "何", "任", "何", "哪", "什么",
            "怎么", "怎样", "如何", "几", "多少", "谁", "哪个", "哪些", "哪里", "何时",
            "何处", "为何", "为什么", "如何", "怎么办", "怎么样", "什么样", "什么时候"
        }

        # 尝试从文件加载更多停用词
        try:
            stopwords_path = os.path.join(os.path.dirname(__file__), "data", "stopwords.txt")
            if os.path.exists(stopwords_path):
                with open(stopwords_path, 'r', encoding='utf-8') as f:
                    file_stopwords = {line.strip() for line in f if line.strip()}
                common_stopwords.update(file_stopwords)
                self.logger.info(f"Loaded {len(file_stopwords)} stopwords from file")
        except Exception as e:
            self.logger.warning(f"Failed to load stopwords from file: {e}")

        return common_stopwords

    def _interpret_policy_locally(self, policy_text: str) -> Dict[str, Any]:
        """
        使用本地组件解读招聘政策

        Args:
            policy_text: 招聘政策文本

        Returns:
            解读结果，包含摘要、要点和特别说明
        """
        # 提取政策段落
        policy_paragraphs = self._extract_policy_paragraphs(policy_text)

        # 提取关键点
        key_points = self._extract_key_points(policy_text)

        # 提取特别说明
        special_notes = self._extract_special_notes(policy_text)

        # 提取时间敏感信息
        time_sensitive_info = self._extract_time_sensitive_info(policy_text)

        # 提取地点信息
        location_info = self._extract_location_info(policy_text)

        # 识别政策类型
        policy_types = self._identify_policy_types(policy_text)

        # 生成摘要
        summary = self._generate_summary(policy_paragraphs, policy_types)

        # 生成通俗解读
        plain_interpretation = self._generate_plain_interpretation(
            policy_paragraphs,
            key_points,
            time_sensitive_info,
            location_info
        )

        return {
            "summary": summary,
            "key_points": key_points,
            "special_notes": special_notes,
            "time_sensitive_info": time_sensitive_info,
            "location_info": location_info,
            "policy_types": policy_types,
            "plain_interpretation": plain_interpretation
        }

    def _extract_time_sensitive_info(self, text: str) -> List[Dict[str, Any]]:
        """
        提取时间敏感信息

        Args:
            text: 政策文本

        Returns:
            时间敏感信息列表，每项包含类型、时间和描述
        """
        time_info = []

        # 日期模式
        date_patterns = [
            r'(\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日号]?)',  # 2023-05-15, 2023年05月15日
            r'(\d{1,2}[-/月]\d{1,2}[日号]?)',  # 05-15, 05月15日
            r'(\d{4}年\d{1,2}月)',  # 2023年05月
            r'(\d{1,2}月\d{1,2}[日号])',  # 5月15日
            r'(\d{4}[-/]\d{1,2}[-/]\d{1,2})',  # 2023-05-15, 2023/05/15
            r'(\d{4}\.\d{1,2}\.\d{1,2})',  # 2023.05.15
            r'(\d{4}年)',  # 2023年
            r'(\d{1,2}月)',  # 5月
        ]

        # 时间模式
        time_patterns = [
            r'(\d{1,2}[:：]\d{1,2})',  # 10:30, 10：30
            r'(\d{1,2}时\d{1,2}分)',  # 10时30分
            r'(\d{1,2}[点时](?:\d{1,2}分)?)',  # 10点, 10点30分
            r'(上午|下午|晚上)(\d{1,2}[点时:：](?:\d{1,2}分)?)',  # 上午10点, 下午2:30
            r'(\d{1,2}[点时:：]\d{1,2}分?)-(上午|下午|晚上)?(\d{1,2}[点时:：](?:\d{1,2}分)?)',  # 9:00-11:30
            r'(\d{1,2}[点时:：]\d{1,2}分?)(?:至|到)(\d{1,2}[点时:：](?:\d{1,2}分)?)',  # 9:00至11:30
        ]

        # 时间段模式
        period_patterns = [
            r'(\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日号]?)\s*[-至~到]\s*(\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日号]?)',  # 2023-05-15 - 2023-05-20
            r'(\d{1,2}[-/月]\d{1,2}[日号]?)\s*[-至~到]\s*(\d{1,2}[-/月]\d{1,2}[日号]?)',  # 05-15 - 05-20
            r'(\d{1,2}月\d{1,2}[日号])\s*[-至~到]\s*(\d{1,2}月\d{1,2}[日号])',  # 5月15日 - 5月20日
            r'(\d{1,2}[:：]\d{1,2})\s*[-至~到]\s*(\d{1,2}[:：]\d{1,2})',  # 10:30 - 11:30
            r'(\d{4}年\d{1,2}月\d{1,2}日)(?:起|开始|从).*?(?:止|结束|到|至)(\d{4}年\d{1,2}月\d{1,2}日)',  # 2023年5月15日起至2023年5月20日止
            r'从(\d{4}年\d{1,2}月\d{1,2}日).*?(?:止|结束|到|至)(\d{4}年\d{1,2}月\d{1,2}日)',  # 从2023年5月15日至2023年5月20日
            r'自(\d{4}年\d{1,2}月\d{1,2}日).*?(?:止|结束|到|至)(\d{4}年\d{1,2}月\d{1,2}日)',  # 自2023年5月15日至2023年5月20日
        ]

        # 特定时间关键词模式
        specific_time_patterns = [
            (r'报名时间[:：]?\s*([^，。；！？\n]{5,50})', "报名时间"),
            (r'考试时间[:：]?\s*([^，。；！？\n]{5,50})', "考试时间"),
            (r'面试时间[:：]?\s*([^，。；！？\n]{5,50})', "面试时间"),
            (r'公示时间[:：]?\s*([^，。；！？\n]{5,50})', "公示时间"),
            (r'体检时间[:：]?\s*([^，。；！？\n]{5,50})', "体检时间"),
            (r'资格审查时间[:：]?\s*([^，。；！？\n]{5,50})', "资格审查时间"),
            (r'成绩公布时间[:：]?\s*([^，。；！？\n]{5,50})', "成绩公布时间"),
            (r'领取准考证时间[:：]?\s*([^，。；！？\n]{5,50})', "领取准考证时间"),
        ]

        # 查找时间相关的句子
        sentences = re.split(r'[。；！？\n]', text)
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue

            # 检查是否包含时间关键词
            if any(keyword in sentence for keyword in self.time_keywords):
                # 尝试提取日期
                for pattern in date_patterns:
                    dates = re.findall(pattern, sentence)
                    if dates:
                        for date in dates:
                            time_info.append({
                                "type": "date",
                                "time": date,
                                "description": sentence
                            })

                # 尝试提取时间
                for pattern in time_patterns:
                    times = re.findall(pattern, sentence)
                    if times:
                        for time_str in times:
                            time_info.append({
                                "type": "time",
                                "time": time_str,
                                "description": sentence
                            })

                # 尝试提取时间段
                for pattern in period_patterns:
                    periods = re.findall(pattern, sentence)
                    if periods:
                        for period in periods:
                            time_info.append({
                                "type": "period",
                                "time": f"{period[0]} 至 {period[1]}",
                                "description": sentence
                            })

                # 尝试提取特定时间关键词
                for pattern, time_type in specific_time_patterns:
                    matches = re.findall(pattern, sentence)
                    if matches:
                        for match in matches:
                            time_info.append({
                                "type": time_type,
                                "time": match,
                                "description": sentence
                            })

        # 去重
        unique_time_info = []
        seen = set()
        for info in time_info:
            key = f"{info['type']}:{info['time']}"
            if key not in seen:
                seen.add(key)
                unique_time_info.append(info)

        return unique_time_info

    def _extract_location_info(self, text: str) -> List[Dict[str, Any]]:
        """
        提取地点信息

        Args:
            text: 政策文本

        Returns:
            地点信息列表，每项包含类型和地点
        """
        location_info = []

        # 地点模式
        location_patterns = [
            r'地[点址][:：]?\s*([^，。；！？\n]{2,30})',  # 地点: xxx
            r'考[试场]地[点址][:：]?\s*([^，。；！？\n]{2,30})',  # 考试地点: xxx
            r'面试地[点址][:：]?\s*([^，。；！？\n]{2,30})',  # 面试地点: xxx
            r'报名地[点址][:：]?\s*([^，。；！？\n]{2,30})',  # 报名地点: xxx
            r'体检地[点址][:：]?\s*([^，。；！？\n]{2,30})',  # 体检地点: xxx
            r'资格审查地[点址][:：]?\s*([^，。；！？\n]{2,30})',  # 资格审查地点: xxx
            r'签约地[点址][:：]?\s*([^，。；！？\n]{2,30})',  # 签约地点: xxx
            r'在\s*([^，。；！？\n]{2,30}[路街道区县市省])',  # 在xxx路/街道/区/县/市/省
            r'([^，。；！？\n]{2,30}[路街道区县市省])\s*举[办行]',  # xxx路/街道/区/县/市/省举办/举行
            r'地点[为是在][:：]?\s*([^，。；！？\n]{2,30})',  # 地点为/是/在xxx
            r'([^，。；！？\n]{2,30}(?:大学|学院|中学|小学|学校|医院|中心|大厦|广场|大楼|会堂|礼堂|报告厅|会议室|办公室|大厅))',  # 包含特定场所名称的地点
            r'([^，。；！？\n]{2,30}(?:路|街|道|巷|号|楼|层|室|区|县|市|省)(?:[0-9A-Za-z一二三四五六七八九十]+[号栋层室座])?)',  # 包含地址特征的地点
        ]

        # 特定地点关键词模式
        specific_location_patterns = [
            (r'报名地点[:：]?\s*([^，。；！？\n]{2,50})', "报名地点"),
            (r'考试地点[:：]?\s*([^，。；！？\n]{2,50})', "考试地点"),
            (r'面试地点[:：]?\s*([^，。；！？\n]{2,50})', "面试地点"),
            (r'体检地点[:：]?\s*([^，。；！？\n]{2,50})', "体检地点"),
            (r'资格审查地点[:：]?\s*([^，。；！？\n]{2,50})', "资格审查地点"),
            (r'签约地点[:：]?\s*([^，。；！？\n]{2,50})', "签约地点"),
            (r'领取准考证地点[:：]?\s*([^，。；！？\n]{2,50})', "领取准考证地点"),
            (r'成绩查询地点[:：]?\s*([^，。；！？\n]{2,50})', "成绩查询地点"),
        ]

        # 查找地点相关的句子
        sentences = re.split(r'[。；！？\n]', text)
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue

            # 检查是否包含地点关键词
            if any(keyword in sentence for keyword in self.location_keywords):
                # 尝试提取地点
                for pattern in location_patterns:
                    locations = re.findall(pattern, sentence)
                    if locations:
                        for location in locations:
                            location_type = "其他"
                            if "考试" in sentence:
                                location_type = "考试地点"
                            elif "面试" in sentence:
                                location_type = "面试地点"
                            elif "报名" in sentence:
                                location_type = "报名地点"
                            elif "体检" in sentence:
                                location_type = "体检地点"

                            location_info.append({
                                "type": location_type,
                                "location": location,
                                "description": sentence
                            })

                # 尝试提取特定地点关键词
                for pattern, loc_type in specific_location_patterns:
                    matches = re.findall(pattern, sentence)
                    if matches:
                        for match in matches:
                            location_info.append({
                                "type": loc_type,
                                "location": match,
                                "description": sentence
                            })

        # 去重
        unique_location_info = []
        seen = set()
        for info in location_info:
            key = f"{info['type']}:{info['location']}"
            if key not in seen:
                seen.add(key)
                unique_location_info.append(info)

        return unique_location_info

    def _identify_policy_types(self, text: str) -> List[str]:
        """
        识别政策类型

        Args:
            text: 政策文本

        Returns:
            政策类型列表
        """
        policy_types = []

        # 计算每种政策类型的关键词出现次数
        type_scores = {}
        for policy_type, keywords in self.policy_type_keywords.items():
            score = 0
            for keyword in keywords:
                score += text.count(keyword) * 2  # 完全匹配权重高

                # 部分匹配
                try:
                    for word in jieba.cut(text):
                        if keyword in word and word != keyword:
                            score += 1  # 部分匹配权重低
                except NameError:
                    # 如果jieba不可用，使用简单的分词
                    for word in text.split():
                        if keyword in word and word != keyword:
                            score += 1  # 部分匹配权重低

            type_scores[policy_type] = score

        # 特殊处理：检查是否是应届生政策
        graduate_keywords = ["应届", "应届生", "应届毕业生", "毕业生", "毕业", "在校生", "高校", "院校", "学生"]
        if any(keyword in text for keyword in graduate_keywords):
            # 如果是应届生政策，确保"应届生政策"类型被添加
            if "应届生政策" in type_scores:
                type_scores["应届生政策"] += 10  # 增加权重
            else:
                type_scores["应届生政策"] = 10

        # 选择得分最高的几种类型
        sorted_types = sorted(type_scores.items(), key=lambda x: x[1], reverse=True)
        for policy_type, score in sorted_types:
            if score > 0:
                policy_types.append(policy_type)

            # 最多返回4种类型
            if len(policy_types) >= 4:
                break

        return policy_types

    def _interpret_policy_with_api(self, policy_text: str) -> Dict[str, Any]:
        """
        使用DeepSeek API解读招聘政策

        Args:
            policy_text: 招聘政策文本

        Returns:
            解读结果，包含摘要、要点和特别说明
        """
        prompt = f"""
        请将以下事业编制招聘政策转化为通俗易懂的解读，突出重点和注意事项。

        请包含以下内容：
        1. 政策摘要：简要概括政策的主要内容
        2. 关键要点：列出政策中的重要规定和要求
        3. 特别说明：提醒应聘者需要特别注意的事项
        4. 时间敏感信息：提取政策中的所有时间点和时间段，如报名时间、考试时间等
        5. 地点信息：提取政策中的所有地点信息，如报名地点、考试地点等
        6. 政策类型：识别政策的类型，如报名政策、考试政策、录用政策、应届生政策等
        7. 通俗解读：用简单易懂的语言解释政策内容

        特别注意：
        - 请特别关注政策是否针对应届生的招聘，如果是，请在政策类型中添加"应届生政策"
        - 请详细提取所有时间信息，包括报名时间、考试时间、面试时间、公示时间等
        - 请详细提取所有地点信息，包括报名地点、考试地点、面试地点等
        - 请特别关注政策中的限制条件，如学历要求、专业要求、工作经验要求、年龄要求等

        请以JSON格式返回结果，键名分别为：summary, key_points, special_notes, time_sensitive_info, location_info, policy_types, plain_interpretation。其中key_points, special_notes, policy_types应为字符串数组，time_sensitive_info和location_info应为对象数组。

        招聘政策内容：
        {policy_text}
        """

        try:
            response = self.deepseek_client.chat_completion(prompt)
            # 尝试从响应中提取JSON
            result = self._extract_json_from_response(response)
            return result
        except Exception as e:
            self.logger.error(f"Error using DeepSeek API for policy interpretation: {e}")
            # 如果API调用失败，返回空结果
            return {
                "summary": "",
                "key_points": [],
                "special_notes": [],
                "time_sensitive_info": [],
                "location_info": [],
                "policy_types": [],
                "plain_interpretation": ""
            }

    def _extract_json_from_response(self, response) -> Dict[str, Any]:
        """
        从API响应中提取JSON

        Args:
            response: API响应

        Returns:
            解析后的JSON对象
        """
        try:
            # 使用 DeepSeekClient 的 extract_json 方法
            return self.deepseek_client.extract_json(response)
        except Exception as e:
            self.logger.error(f"Error extracting JSON from response: {e}")
            return {
                "summary": "",
                "key_points": [],
                "special_notes": [],
                "plain_interpretation": ""
            }

    def _is_result_complete(self, result: Dict[str, Any]) -> bool:
        """
        检查解读结果是否完整

        Args:
            result: 解读结果

        Returns:
            结果是否完整
        """
        # 检查关键字段是否有值
        required_fields = ["summary", "key_points", "plain_interpretation"]
        for field in required_fields:
            if field not in result or not result[field]:
                return False

        # 检查key_points是否至少有一个要点
        if "key_points" in result and isinstance(result["key_points"], list) and len(result["key_points"]) == 0:
            return False

        return True

    def _extract_policy_paragraphs(self, text: str) -> List[str]:
        """
        提取政策段落

        Args:
            text: 政策文本

        Returns:
            政策段落列表
        """
        paragraphs = []

        # 按行分割文本
        lines = text.split('\n')
        current_paragraph = []

        for line in lines:
            line = line.strip()
            if not line:
                # 空行表示段落结束
                if current_paragraph:
                    paragraphs.append(' '.join(current_paragraph))
                    current_paragraph = []
            else:
                current_paragraph.append(line)

        # 添加最后一个段落
        if current_paragraph:
            paragraphs.append(' '.join(current_paragraph))

        # 过滤出包含政策关键词的段落
        policy_paragraphs = []
        for paragraph in paragraphs:
            if any(keyword in paragraph for keyword in self.policy_keywords):
                policy_paragraphs.append(paragraph)

        # 如果没有找到包含政策关键词的段落，返回所有段落
        if not policy_paragraphs:
            return paragraphs

        return policy_paragraphs

    def _extract_key_points(self, text: str) -> List[str]:
        """
        提取关键点

        Args:
            text: 政策文本

        Returns:
            关键点列表
        """
        key_points = []

        # 查找包含重要关键词的句子
        sentences = re.split(r'[。；！？\n]', text)
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue

            # 检查是否包含重要关键词
            if any(keyword in sentence for keyword in self.important_keywords):
                key_points.append(sentence)

            # 检查是否包含应届生相关关键词
            if any(keyword in sentence for keyword in ["应届", "应届生", "毕业生", "毕业", "在校生"]):
                key_points.append(sentence)

        # 查找编号的条目
        numbered_items = re.findall(r'[\d一二三四五六七八九十]+[\.、．]\s*([^。；！？\n]+)', text)
        for item in numbered_items:
            item = item.strip()
            if item and (any(keyword in item for keyword in self.important_keywords) or
                         any(keyword in item for keyword in ["应届", "应届生", "毕业生", "毕业", "在校生"])):
                key_points.append(item)

        return key_points

    def _extract_special_notes(self, text: str) -> List[str]:
        """
        提取特别说明

        Args:
            text: 政策文本

        Returns:
            特别说明列表
        """
        special_notes = []

        # 查找特别说明相关的文本
        note_indicators = ['注意事项', '重要提示', '特别说明', '温馨提示', '注意']

        for indicator in note_indicators:
            # 查找包含指示词的段落
            pattern = f'.*{indicator}.*(?:\n.*)*'
            matches = re.findall(pattern, text, re.MULTILINE)

            for match in matches:
                # 按行分割
                lines = match.split('\n')
                for line in lines:
                    line = line.strip()
                    if line and len(line) > 10:
                        # 检查是否是编号的说明
                        if re.match(r'^[\d一二三四五六七八九十]+[\.、．]', line):
                            special_notes.append(line)
                        # 检查是否包含关键词
                        elif any(indicator in line for indicator in note_indicators):
                            special_notes.append(line)

        return special_notes

    def _generate_summary(self, policy_paragraphs: List[str], policy_types: Optional[List[str]] = None) -> str:
        """
        生成政策摘要

        Args:
            policy_paragraphs: 政策段落列表
            policy_types: 政策类型列表，可选

        Returns:
            政策摘要
        """
        if not policy_paragraphs:
            return ""

        # 提取政策涉及的方面
        aspects = []

        # 如果提供了政策类型，直接使用
        if policy_types and len(policy_types) > 0:
            aspects = policy_types
        else:
            # 否则从段落中提取
            aspect_keywords = {
                "报名": ["报名", "申请", "提交申请"],
                "资格条件": ["资格条件", "应聘条件", "招聘条件", "报名条件"],
                "考试": ["考试", "笔试", "面试", "资格审查"],
                "录用": ["录用", "聘用", "录取", "公示"],
                "材料": ["材料", "证件", "证书", "复印件"]
            }

            for paragraph in policy_paragraphs:
                for aspect, keywords in aspect_keywords.items():
                    if any(keyword in paragraph for keyword in keywords) and aspect not in aspects:
                        aspects.append(aspect)

        # 生成摘要
        if aspects:
            aspects_str = "、".join(aspects)
            summary = self.interpretation_templates["summary"].format(aspects=aspects_str)
        else:
            # 如果没有提取到方面，使用第一段作为摘要
            summary = policy_paragraphs[0][:100] + "..."

        return summary

    def _generate_plain_interpretation(self, policy_paragraphs: List[str], key_points: List[str],
                                      time_sensitive_info: Optional[List[Dict[str, Any]]] = None,
                                      location_info: Optional[List[Dict[str, Any]]] = None) -> str:
        """
        生成通俗解读

        Args:
            policy_paragraphs: 政策段落列表
            key_points: 关键点列表
            time_sensitive_info: 时间敏感信息列表，可选
            location_info: 地点信息列表，可选

        Returns:
            通俗解读
        """
        if not policy_paragraphs:
            return ""

        # 简化政策语言
        simplified_paragraphs = []
        for paragraph in policy_paragraphs:
            # 替换复杂术语
            simplified = paragraph
            simplified = simplified.replace("根据相关规定", "按照规定")
            simplified = simplified.replace("依照有关法律法规", "按照法律规定")
            simplified = simplified.replace("须", "需要")
            simplified = simplified.replace("应当", "应该")
            simplified = simplified.replace("予以", "给予")
            simplified = simplified.replace("如实填报", "真实填写")
            simplified = simplified.replace("不得", "不能")
            simplified = simplified.replace("按照规定程序", "按照流程")
            simplified = simplified.replace("相关部门", "有关部门")
            simplified = simplified.replace("相关规定", "有关规定")
            simplified = simplified.replace("相关要求", "有关要求")
            simplified = simplified.replace("相关事宜", "有关事项")
            simplified = simplified.replace("相关信息", "有关信息")
            simplified = simplified.replace("相关资料", "有关资料")
            simplified = simplified.replace("相关证明", "有关证明")
            simplified = simplified.replace("相关证件", "有关证件")
            simplified = simplified.replace("相关材料", "有关材料")
            simplified = simplified.replace("相关手续", "有关手续")
            simplified = simplified.replace("相关流程", "有关流程")
            simplified = simplified.replace("相关程序", "有关程序")
            simplified = simplified.replace("相关政策", "有关政策")
            simplified = simplified.replace("相关法规", "有关法规")
            simplified = simplified.replace("相关法律", "有关法律")
            simplified = simplified.replace("相关条例", "有关条例")
            simplified = simplified.replace("相关办法", "有关办法")
            simplified = simplified.replace("相关规则", "有关规则")
            simplified = simplified.replace("相关标准", "有关标准")
            simplified = simplified.replace("相关要求", "有关要求")
            simplified = simplified.replace("相关规范", "有关规范")
            simplified = simplified.replace("相关指南", "有关指南")
            simplified = simplified.replace("相关指引", "有关指引")
            simplified = simplified.replace("相关说明", "有关说明")
            simplified = simplified.replace("相关通知", "有关通知")
            simplified = simplified.replace("相关公告", "有关公告")
            simplified = simplified.replace("相关文件", "有关文件")

            simplified_paragraphs.append(simplified)

        # 组合解读
        interpretation = "这份招聘政策的主要内容是：\n\n"
        interpretation += "\n\n".join(simplified_paragraphs)

        # 添加关键点
        if key_points:
            interpretation += "\n\n特别需要注意的是：\n"
            for i, point in enumerate(key_points):
                interpretation += f"{i+1}. {point}\n"

        # 添加时间信息
        if time_sensitive_info and len(time_sensitive_info) > 0:
            interpretation += "\n\n重要时间节点：\n"
            for i, info in enumerate(time_sensitive_info):
                time_type = ""
                if "报名" in info["description"]:
                    time_type = "报名"
                elif "考试" in info["description"]:
                    time_type = "考试"
                elif "面试" in info["description"]:
                    time_type = "面试"
                elif "公示" in info["description"]:
                    time_type = "公示"
                elif "体检" in info["description"]:
                    time_type = "体检"

                if time_type:
                    interpretation += f"{i+1}. {time_type}时间：{info['time']}\n"
                else:
                    interpretation += f"{i+1}. 时间节点：{info['time']}\n"

        # 添加地点信息
        if location_info and len(location_info) > 0:
            interpretation += "\n\n重要地点信息：\n"
            for i, info in enumerate(location_info):
                interpretation += f"{i+1}. {info['type']}：{info['location']}\n"

        return interpretation

    def interpret_policy_for_job(self, job_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        为特定岗位解读招聘政策

        Args:
            job_info: 岗位信息字典，包含description, requirements等字段

        Returns:
            政策解读结果
        """
        # 提取岗位描述和要求
        job_description = job_info.get("description", "") or job_info.get("job_description", "")
        job_requirements = job_info.get("requirements", "") or job_info.get("job_requirements", "")

        # 合并文本
        policy_text = job_description + "\n" + job_requirements

        # 解读政策
        interpretation = self.interpret_policy(policy_text)

        # 构建结果
        result = {
            "job_id": job_info.get("job_id", ""),
            "title": job_info.get("title", ""),
            "policy_summary": interpretation.get("summary", ""),
            "policy_key_points": interpretation.get("key_points", []),
            "policy_special_notes": interpretation.get("special_notes", []),
            "policy_time_sensitive_info": interpretation.get("time_sensitive_info", []),
            "policy_location_info": interpretation.get("location_info", []),
            "policy_types": interpretation.get("policy_types", []),
            "policy_plain_interpretation": interpretation.get("plain_interpretation", "")
        }

        return result

    def compare_policies(self, policy_text1: str, policy_text2: str, policy1_name: str = "政策1", policy2_name: str = "政策2") -> Dict[str, Any]:
        """
        比较两个政策的异同

        Args:
            policy_text1: 第一个政策文本
            policy_text2: 第二个政策文本
            policy1_name: 第一个政策的名称，默认为"政策1"
            policy2_name: 第二个政策的名称，默认为"政策2"

        Returns:
            比较结果，包含相同点和不同点
        """
        # 检查缓存
        cache_key = f"policy_compare_{hash(policy_text1)}_{hash(policy_text2)}"
        cached_result = self.cache_manager.get(cache_key)
        if cached_result:
            self.logger.info("Using cached policy comparison result")
            return cached_result

        # 解读两个政策
        interpretation1 = self.interpret_policy(policy_text1)
        interpretation2 = self.interpret_policy(policy_text2)

        # 如果本地比较可用，使用本地比较
        if self.use_local_components:
            result = self._compare_policies_locally(interpretation1, interpretation2, policy1_name, policy2_name)

            # 如果本地比较结果不完整，尝试使用DeepSeek API
            if not self._is_comparison_complete(result) and self.deepseek_client.is_available():
                self.logger.info("Local comparison incomplete, trying DeepSeek API")
                api_result = self._compare_policies_with_api(policy_text1, policy_text2, policy1_name, policy2_name)
                # 合并结果，API结果优先
                for key, value in api_result.items():
                    if value and (key not in result or not result[key]):
                        result[key] = value
        else:
            # 直接使用DeepSeek API
            result = self._compare_policies_with_api(policy_text1, policy_text2, policy1_name, policy2_name)

        # 缓存结果
        self.cache_manager.set(cache_key, result)

        return result

    def _compare_policies_locally(self, interpretation1: Dict[str, Any], interpretation2: Dict[str, Any],
                                 policy1_name: str, policy2_name: str) -> Dict[str, Any]:
        """
        本地比较两个政策的解读结果

        Args:
            interpretation1: 第一个政策的解读结果
            interpretation2: 第二个政策的解读结果
            policy1_name: 第一个政策的名称
            policy2_name: 第二个政策的名称

        Returns:
            比较结果，包含相同点和不同点
        """
        # 比较政策类型
        policy_types1 = interpretation1.get("policy_types", [])
        policy_types2 = interpretation2.get("policy_types", [])

        common_types = [t for t in policy_types1 if t in policy_types2]
        diff_types1 = [t for t in policy_types1 if t not in policy_types2]
        diff_types2 = [t for t in policy_types2 if t not in policy_types1]

        # 比较关键点
        key_points1 = interpretation1.get("key_points", [])
        key_points2 = interpretation2.get("key_points", [])

        # 使用文本相似度比较关键点
        similar_points = []
        unique_points1 = []
        unique_points2 = []

        for point1 in key_points1:
            best_match = None
            best_ratio = 0

            for point2 in key_points2:
                # 计算相似度
                ratio = difflib.SequenceMatcher(None, point1, point2).ratio()
                if ratio > 0.7 and ratio > best_ratio:  # 相似度阈值
                    best_match = point2
                    best_ratio = ratio

            if best_match:
                similar_points.append({
                    "point1": point1,
                    "point2": best_match,
                    "similarity": best_ratio
                })
            else:
                unique_points1.append(point1)

        # 找出第二个政策中独特的关键点
        for point2 in key_points2:
            if not any(item["point2"] == point2 for item in similar_points):
                unique_points2.append(point2)

        # 比较时间信息
        time_info1 = interpretation1.get("time_sensitive_info", [])
        time_info2 = interpretation2.get("time_sensitive_info", [])

        time_diff = []

        # 按类型分组时间信息
        time_by_type1 = {}
        time_by_type2 = {}

        for info in time_info1:
            desc = info.get("description", "")
            for keyword in ["报名", "考试", "面试", "公示", "体检", "资格审查", "成绩公布", "领取准考证"]:
                if keyword in desc:
                    if keyword not in time_by_type1:
                        time_by_type1[keyword] = []
                    time_by_type1[keyword].append(info)
                    break

        for info in time_info2:
            desc = info.get("description", "")
            for keyword in ["报名", "考试", "面试", "公示", "体检", "资格审查", "成绩公布", "领取准考证"]:
                if keyword in desc:
                    if keyword not in time_by_type2:
                        time_by_type2[keyword] = []
                    time_by_type2[keyword].append(info)
                    break

        # 比较相同类型的时间信息
        for keyword in set(list(time_by_type1.keys()) + list(time_by_type2.keys())):
            times1 = [info["time"] for info in time_by_type1.get(keyword, [])]
            times2 = [info["time"] for info in time_by_type2.get(keyword, [])]

            if times1 and times2 and times1[0] != times2[0]:
                time_diff.append({
                    "type": keyword,
                    "time1": times1[0],
                    "time2": times2[0],
                    "description": f"{policy1_name}的{keyword}时间为{times1[0]}，而{policy2_name}的为{times2[0]}"
                })
            elif times1 and not times2:
                time_diff.append({
                    "type": keyword,
                    "time1": times1[0],
                    "time2": "未指定",
                    "description": f"{policy1_name}指定了{keyword}时间为{times1[0]}，而{policy2_name}未指定"
                })
            elif not times1 and times2:
                time_diff.append({
                    "type": keyword,
                    "time1": "未指定",
                    "time2": times2[0],
                    "description": f"{policy1_name}未指定{keyword}时间，而{policy2_name}的为{times2[0]}"
                })

        # 比较地点信息
        location_info1 = interpretation1.get("location_info", [])
        location_info2 = interpretation2.get("location_info", [])

        location_diff = []

        # 按类型分组地点信息
        location_by_type1 = {}
        location_by_type2 = {}

        for info in location_info1:
            loc_type = info.get("type", "")
            if loc_type not in location_by_type1:
                location_by_type1[loc_type] = []
            location_by_type1[loc_type].append(info)

        for info in location_info2:
            loc_type = info.get("type", "")
            if loc_type not in location_by_type2:
                location_by_type2[loc_type] = []
            location_by_type2[loc_type].append(info)

        # 比较相同类型的地点信息
        for loc_type in set(list(location_by_type1.keys()) + list(location_by_type2.keys())):
            locs1 = [info["location"] for info in location_by_type1.get(loc_type, [])]
            locs2 = [info["location"] for info in location_by_type2.get(loc_type, [])]

            if locs1 and locs2 and locs1[0] != locs2[0]:
                location_diff.append({
                    "type": loc_type,
                    "location1": locs1[0],
                    "location2": locs2[0],
                    "description": f"{policy1_name}的{loc_type}为{locs1[0]}，而{policy2_name}的为{locs2[0]}"
                })
            elif locs1 and not locs2:
                location_diff.append({
                    "type": loc_type,
                    "location1": locs1[0],
                    "location2": "未指定",
                    "description": f"{policy1_name}指定了{loc_type}为{locs1[0]}，而{policy2_name}未指定"
                })
            elif not locs1 and locs2:
                location_diff.append({
                    "type": loc_type,
                    "location1": "未指定",
                    "location2": locs2[0],
                    "description": f"{policy1_name}未指定{loc_type}，而{policy2_name}的为{locs2[0]}"
                })

        # 检查是否有应届生政策差异
        is_graduate_policy1 = "应届生政策" in policy_types1
        is_graduate_policy2 = "应届生政策" in policy_types2
        graduate_policy_diff = None

        if is_graduate_policy1 and not is_graduate_policy2:
            graduate_policy_diff = f"{policy1_name}是针对应届生的招聘政策，而{policy2_name}不是。"
        elif not is_graduate_policy1 and is_graduate_policy2:
            graduate_policy_diff = f"{policy1_name}不是针对应届生的招聘政策，而{policy2_name}是。"

        # 生成比较结果
        result = {
            "policy1_name": policy1_name,
            "policy2_name": policy2_name,
            "common_types": common_types,
            "diff_types": {
                "policy1_only": diff_types1,
                "policy2_only": diff_types2
            },
            "similar_points": similar_points,
            "unique_points": {
                "policy1_only": unique_points1,
                "policy2_only": unique_points2
            },
            "time_differences": time_diff,
            "location_differences": location_diff,
            "graduate_policy_diff": graduate_policy_diff,
            "comparison_summary": self._generate_comparison_summary(
                common_types, diff_types1, diff_types2,
                similar_points, unique_points1, unique_points2,
                time_diff, location_diff, graduate_policy_diff,
                policy1_name, policy2_name
            )
        }

        return result

    def _is_comparison_complete(self, result: Dict[str, Any]) -> bool:
        """
        检查比较结果是否完整

        Args:
            result: 比较结果

        Returns:
            比较结果是否完整
        """
        # 检查关键字段是否有值
        required_fields = ["common_types", "diff_types", "similar_points", "unique_points", "comparison_summary"]
        for field in required_fields:
            if field not in result:
                return False

        return True

    def _generate_comparison_summary(self, common_types: List[str], diff_types1: List[str], diff_types2: List[str],
                                    similar_points: List[Dict[str, Any]], unique_points1: List[str], unique_points2: List[str],
                                    time_diff: List[Dict[str, Any]], location_diff: Optional[List[Dict[str, Any]]] = None,
                                    graduate_policy_diff: Optional[str] = None, policy1_name: str = "政策1", policy2_name: str = "政策2") -> str:
        """
        生成比较摘要

        Args:
            common_types: 共同的政策类型
            diff_types1: 第一个政策特有的类型
            diff_types2: 第二个政策特有的类型
            similar_points: 相似的关键点
            unique_points1: 第一个政策特有的关键点
            unique_points2: 第二个政策特有的关键点
            time_diff: 时间差异
            location_diff: 地点差异，可选
            graduate_policy_diff: 应届生政策差异，可选
            policy1_name: 第一个政策的名称
            policy2_name: 第二个政策的名称

        Returns:
            比较摘要
        """
        summary = f"{policy1_name}和{policy2_name}的比较分析：\n\n"

        # 添加政策类型比较
        if common_types:
            summary += f"两个政策都涉及以下类型：{', '.join(common_types)}\n\n"

        if diff_types1:
            summary += f"{policy1_name}特有的政策类型：{', '.join(diff_types1)}\n\n"

        if diff_types2:
            summary += f"{policy2_name}特有的政策类型：{', '.join(diff_types2)}\n\n"

        # 添加应届生政策差异
        if graduate_policy_diff:
            summary += f"{graduate_policy_diff}\n\n"

        # 添加关键点比较
        if similar_points:
            summary += f"两个政策有{len(similar_points)}个相似的关键点\n\n"

        if unique_points1:
            summary += f"{policy1_name}特有的关键点数量：{len(unique_points1)}\n\n"

        if unique_points2:
            summary += f"{policy2_name}特有的关键点数量：{len(unique_points2)}\n\n"

        # 添加时间差异
        if time_diff:
            summary += "时间差异：\n"
            for diff in time_diff:
                if 'description' in diff:
                    summary += f"- {diff['description']}\n"
                else:
                    summary += f"- {diff['type']}：{policy1_name}为{diff['time1']}，{policy2_name}为{diff['time2']}\n"
            summary += "\n"

        # 添加地点差异
        if location_diff:
            summary += "地点差异：\n"
            for diff in location_diff:
                if 'description' in diff:
                    summary += f"- {diff['description']}\n"
                else:
                    summary += f"- {diff['type']}：{policy1_name}为{diff['location1']}，{policy2_name}为{diff['location2']}\n"
            summary += "\n"

        # 总结
        if not common_types and not similar_points:
            summary += f"{policy1_name}和{policy2_name}几乎没有共同点，是完全不同的政策。\n"
        elif len(common_types) > len(diff_types1) + len(diff_types2) and len(similar_points) > len(unique_points1) + len(unique_points2):
            summary += f"{policy1_name}和{policy2_name}有很多共同点，可能是相似的政策或同一政策的不同版本。\n"
        else:
            summary += f"{policy1_name}和{policy2_name}有一些共同点，但也有明显的差异。\n"

        return summary

    def _compare_policies_with_api(self, policy_text1: str, policy_text2: str, policy1_name: str, policy2_name: str) -> Dict[str, Any]:
        """
        使用DeepSeek API比较两个政策

        Args:
            policy_text1: 第一个政策文本
            policy_text2: 第二个政策文本
            policy1_name: 第一个政策的名称
            policy2_name: 第二个政策的名称

        Returns:
            比较结果
        """
        prompt = f"""
        请比较以下两个事业编制招聘政策的异同点：

        政策1（{policy1_name}）：
        {policy_text1}

        政策2（{policy2_name}）：
        {policy_text2}

        请分析以下方面：
        1. 共同的政策类型
        2. 各自特有的政策类型
        3. 相似的关键点
        4. 各自特有的关键点
        5. 时间差异（如报名时间、考试时间等）
        6. 地点差异（如报名地点、考试地点等）
        7. 应届生政策差异（是否针对应届生）
        8. 总体比较摘要

        特别注意：
        - 请特别关注政策是否针对应届生的招聘，如果一个是应届生政策而另一个不是，请在graduate_policy_diff中说明
        - 请详细比较时间信息，包括报名时间、考试时间、面试时间、公示时间等
        - 请详细比较地点信息，包括报名地点、考试地点、面试地点等

        请以JSON格式返回结果，键名分别为：common_types, diff_types, similar_points, unique_points, time_differences, location_differences, graduate_policy_diff, comparison_summary。
        其中common_types为字符串数组，diff_types为包含policy1_only和policy2_only两个字符串数组的对象，
        similar_points为包含point1、point2和similarity的对象数组，
        unique_points为包含policy1_only和policy2_only两个字符串数组的对象，
        time_differences为包含type、time1、time2和description的对象数组，
        location_differences为包含type、location1、location2和description的对象数组，
        graduate_policy_diff为字符串（如果一个是应届生政策而另一个不是，则说明差异；如果两者都是或都不是应届生政策，则为null），
        comparison_summary为字符串。
        """

        try:
            response = self.deepseek_client.chat_completion(prompt)
            # 尝试从响应中提取JSON
            result = self._extract_json_from_response(response)

            # 添加政策名称
            result["policy1_name"] = policy1_name
            result["policy2_name"] = policy2_name

            return result
        except Exception as e:
            self.logger.error(f"Error using DeepSeek API for policy comparison: {e}")
            # 如果API调用失败，返回空结果
            return {
                "policy1_name": policy1_name,
                "policy2_name": policy2_name,
                "common_types": [],
                "diff_types": {
                    "policy1_only": [],
                    "policy2_only": []
                },
                "similar_points": [],
                "unique_points": {
                    "policy1_only": [],
                    "policy2_only": []
                },
                "time_differences": [],
                "location_differences": [],
                "graduate_policy_diff": None,
                "comparison_summary": f"无法比较{policy1_name}和{policy2_name}，API调用失败。"
            }

    def interpret_policy_for_user(self, policy_text: str, user_profile: Dict[str, Any]) -> Dict[str, Any]:
        """
        为特定用户解读招聘政策，提供个性化解读

        Args:
            policy_text: 政策文本
            user_profile: 用户资料，包含education, major, experience等字段

        Returns:
            个性化解读结果
        """
        # 检查缓存
        cache_key = f"policy_user_{hash(policy_text)}_{hash(json.dumps(user_profile, sort_keys=True))}"
        cached_result = self.cache_manager.get(cache_key)
        if cached_result:
            self.logger.info("Using cached personalized policy interpretation result")
            return cached_result

        # 先获取通用解读
        general_interpretation = self.interpret_policy(policy_text)

        # 如果本地解读可用，使用本地解读
        if self.use_local_components:
            result = self._interpret_policy_for_user_locally(general_interpretation, user_profile)

            # 如果本地解读结果不完整，尝试使用DeepSeek API
            if not self._is_personalized_interpretation_complete(result) and self.deepseek_client.is_available():
                self.logger.info("Local personalized interpretation incomplete, trying DeepSeek API")
                api_result = self._interpret_policy_for_user_with_api(policy_text, user_profile)
                # 合并结果，API结果优先
                for key, value in api_result.items():
                    if value and (key not in result or not result[key]):
                        result[key] = value
        else:
            # 直接使用DeepSeek API
            result = self._interpret_policy_for_user_with_api(policy_text, user_profile)

        # 缓存结果
        self.cache_manager.set(cache_key, result)

        return result

    def _interpret_policy_for_user_locally(self, general_interpretation: Dict[str, Any], user_profile: Dict[str, Any]) -> Dict[str, Any]:
        """
        本地为特定用户解读招聘政策

        Args:
            general_interpretation: 通用解读结果
            user_profile: 用户资料

        Returns:
            个性化解读结果
        """
        # 提取用户资料
        user_education = user_profile.get("education", "")
        user_major = user_profile.get("major", "")
        user_experience = user_profile.get("experience", "")

        # 提取政策信息
        policy_types = general_interpretation.get("policy_types", [])
        key_points = general_interpretation.get("key_points", [])
        time_sensitive_info = general_interpretation.get("time_sensitive_info", [])

        # 分析用户与政策的匹配度
        matches = []
        mismatches = []

        # 检查学历要求
        education_keywords = {
            "博士": ["博士", "博士研究生", "博士及以上"],
            "硕士": ["硕士", "硕士研究生", "研究生", "硕士及以上"],
            "本科": ["本科", "学士", "大学本科", "本科及以上"],
            "大专": ["大专", "专科", "高职", "大专及以上"],
            "高中": ["高中", "中专", "职高", "高中及以上"]
        }

        user_education_level = None
        for level, keywords in education_keywords.items():
            if any(keyword in user_education for keyword in keywords):
                user_education_level = level
                break

        # 在关键点中查找学历要求
        education_requirement = None
        for point in key_points:
            for level, keywords in education_keywords.items():
                if any(keyword in point for keyword in keywords):
                    education_requirement = level
                    break
            if education_requirement:
                break

        # 比较学历
        if user_education_level and education_requirement:
            education_levels = ["高中", "大专", "本科", "硕士", "博士"]
            user_level_index = education_levels.index(user_education_level)
            req_level_index = education_levels.index(education_requirement)

            if user_level_index >= req_level_index:
                matches.append(f"您的学历（{user_education_level}）符合政策要求（{education_requirement}）")
            else:
                mismatches.append(f"您的学历（{user_education_level}）低于政策要求（{education_requirement}）")

        # 检查专业要求
        for point in key_points:
            if "专业" in point and user_major:
                if user_major in point:
                    matches.append(f"您的专业（{user_major}）符合政策要求")
                    break

                # 检查相近专业
                similar_majors = {
                    "计算机科学与技术": ["软件工程", "信息技术", "电子信息", "通信工程", "网络工程", "信息安全"],
                    "软件工程": ["计算机科学与技术", "信息技术", "网络工程", "信息安全"],
                    "电子信息工程": ["通信工程", "电子科学与技术", "电气工程", "自动化"],
                    "会计学": ["财务管理", "审计学", "金融学", "经济学"],
                    "金融学": ["经济学", "会计学", "财务管理", "国际经济与贸易"],
                    "法学": ["知识产权", "国际法", "经济法", "政治学与行政学"],
                    "医学": ["临床医学", "基础医学", "口腔医学", "中医学", "护理学"]
                }

                # 检查用户专业是否在相似专业字典中
                if user_major in similar_majors:
                    similar_list = similar_majors[user_major]
                    if any(major in point for major in similar_list):
                        matches.append(f"您的专业（{user_major}）与政策要求的专业相近")
                        break

                # 检查政策要求的专业是否在相似专业字典中
                for policy_major, similar_list in similar_majors.items():
                    if policy_major in point and user_major in similar_list:
                        matches.append(f"您的专业（{user_major}）与政策要求的专业（{policy_major}）相近")
                        break

        # 检查工作经验要求
        experience_patterns = [
            r'(\d+)[-~](\d+)年.*经验',
            r'(\d+)年(以上|及以上).*经验',
            r'至少(\d+)年.*经验'
        ]

        user_experience_years = 0
        if user_experience:
            # 尝试从用户经验中提取年数
            for pattern in experience_patterns:
                match = re.search(pattern, user_experience)
                if match:
                    if len(match.groups()) == 2 and match.group(2).isdigit():
                        # 范围，取最小值
                        user_experience_years = int(match.group(1))
                    else:
                        # 单一值
                        user_experience_years = int(match.group(1))
                    break

        # 在关键点中查找经验要求
        required_experience_years = 0
        for point in key_points:
            for pattern in experience_patterns:
                match = re.search(pattern, point)
                if match:
                    if len(match.groups()) == 2 and match.group(2).isdigit():
                        # 范围，取最小值
                        required_experience_years = int(match.group(1))
                    else:
                        # 单一值
                        required_experience_years = int(match.group(1))
                    break
            if required_experience_years > 0:
                break

        # 比较工作经验
        if required_experience_years > 0:
            if user_experience_years >= required_experience_years:
                matches.append(f"您的工作经验（{user_experience_years}年）符合政策要求（{required_experience_years}年）")
            else:
                mismatches.append(f"您的工作经验（{user_experience_years}年）低于政策要求（{required_experience_years}年）")

        # 特殊处理：应届生政策
        is_graduate_policy = False
        is_user_graduate = False

        # 检查是否是应届生政策
        policy_text = general_interpretation.get("plain_interpretation", "")
        if "应届生政策" in policy_types or any(keyword in policy_text for keyword in ["应届", "应届生", "应届毕业生", "毕业生"]):
            is_graduate_policy = True

        # 检查用户是否是应届生
        if "应届" in user_experience or "毕业生" in user_experience or "在校生" in user_experience:
            is_user_graduate = True
            # 添加默认匹配项，确保应届生至少有一个匹配项
            matches.append("您是应届毕业生")

        # 应届生政策匹配
        if is_graduate_policy and is_user_graduate:
            matches.append("您是应届毕业生，符合应届生招聘政策要求")
        elif is_graduate_policy and not is_user_graduate:
            mismatches.append("该政策针对应届毕业生，而您已有工作经验，可能不符合应届生招聘要求")
        elif not is_graduate_policy and is_user_graduate:
            # 如果不是应届生政策但用户是应届生，检查是否有工作经验要求
            if required_experience_years > 0:
                mismatches.append(f"您是应届毕业生，但该岗位要求{required_experience_years}年工作经验")

        # 如果用户是计算机专业的应届生，添加一个默认匹配项
        if is_user_graduate and "计算机" in user_major:
            matches.append("您的计算机相关专业背景符合许多技术岗位的要求")

        # 生成个性化建议
        suggestions = []

        if mismatches:
            suggestions.append("根据您的资料，您可能不完全符合该政策的要求，建议：")
            if any("学历" in mismatch for mismatch in mismatches):
                suggestions.append("- 考虑提升您的学历，或寻找学历要求较低的岗位")
            if any("经验" in mismatch for mismatch in mismatches):
                suggestions.append("- 积累更多相关工作经验，或寻找经验要求较低的岗位")
            if any("专业" in mismatch for mismatch in mismatches):
                suggestions.append("- 考虑参加相关专业的培训或认证，增强跨专业竞争力")
            if any("应届" in mismatch for mismatch in mismatches):
                if is_user_graduate:
                    suggestions.append("- 寻找更适合应届生的岗位，或参加实习积累相关经验")
                else:
                    suggestions.append("- 寻找非应届生专项招聘的岗位")
        else:
            suggestions.append("根据您的资料，您基本符合该政策的要求，建议：")
            suggestions.append("- 准备一份突出您优势的简历和求职信")
            suggestions.append("- 提前了解面试可能涉及的问题，做好充分准备")

        # 根据政策类型添加特定建议
        if "报名政策" in policy_types:
            suggestions.append("- 注意报名截止时间，提前准备好所有申请材料")
        if "考试政策" in policy_types:
            suggestions.append("- 了解考试内容和形式，有针对性地进行复习准备")
        if "面试政策" in policy_types:
            suggestions.append("- 提前了解面试地点和路线，避免迟到")
            suggestions.append("- 准备好自我介绍和常见面试问题的回答")

        # 添加时间相关建议
        if time_sensitive_info:
            report_times = [info for info in time_sensitive_info if "报名" in info.get("description", "")]
            if report_times:
                suggestions.append(f"- 请注意报名时间：{report_times[0]['time']}，提前准备好申请材料")

        # 生成个性化解读
        personalized_interpretation = general_interpretation.get("plain_interpretation", "")
        if matches or mismatches:
            personalized_interpretation += "\n\n针对您的个人情况的分析：\n"
            if matches:
                personalized_interpretation += "\n符合项：\n"
                for i, match in enumerate(matches):
                    personalized_interpretation += f"{i+1}. {match}\n"
            if mismatches:
                personalized_interpretation += "\n不符合项：\n"
                for i, mismatch in enumerate(mismatches):
                    personalized_interpretation += f"{i+1}. {mismatch}\n"

        # 添加建议
        if suggestions:
            personalized_interpretation += "\n个性化建议：\n"
            for suggestion in suggestions:
                personalized_interpretation += f"{suggestion}\n"

        # 构建结果
        result = {
            "general_interpretation": general_interpretation,
            "matches": matches,
            "mismatches": mismatches,
            "suggestions": suggestions,
            "personalized_interpretation": personalized_interpretation
        }

        return result

    def _interpret_policy_for_user_with_api(self, policy_text: str, user_profile: Dict[str, Any]) -> Dict[str, Any]:
        """
        使用DeepSeek API为特定用户解读招聘政策

        Args:
            policy_text: 政策文本
            user_profile: 用户资料

        Returns:
            个性化解读结果
        """
        # 提取用户资料
        user_education = user_profile.get("education", "")
        user_major = user_profile.get("major", "")
        user_experience = user_profile.get("experience", "")
        user_skills = user_profile.get("skills", [])
        user_age = user_profile.get("age", 0)

        prompt = f"""
        请为以下用户解读事业编制招聘政策，提供个性化解读和建议。

        用户资料：
        - 学历：{user_education}
        - 专业：{user_major}
        - 工作经验：{user_experience}
        - 技能：{', '.join(user_skills) if user_skills else '无'}
        - 年龄：{user_age if user_age else '未知'}

        招聘政策：
        {policy_text}

        请分析以下内容：
        1. 用户与政策的匹配项（用户符合政策的要求）
        2. 用户与政策的不匹配项（用户不符合政策的要求）
        3. 针对用户的个性化建议
        4. 完整的个性化政策解读

        请以JSON格式返回结果，键名分别为：matches, mismatches, suggestions, personalized_interpretation。
        其中matches, mismatches, suggestions为字符串数组，personalized_interpretation为字符串。
        """

        try:
            response = self.deepseek_client.chat_completion(prompt)
            # 尝试从响应中提取JSON
            api_result = self._extract_json_from_response(response)

            # 获取通用解读
            general_interpretation = self.interpret_policy(policy_text)

            # 合并结果
            result = {
                "general_interpretation": general_interpretation,
                "matches": api_result.get("matches", []),
                "mismatches": api_result.get("mismatches", []),
                "suggestions": api_result.get("suggestions", []),
                "personalized_interpretation": api_result.get("personalized_interpretation", "")
            }

            return result
        except Exception as e:
            self.logger.error(f"Error using DeepSeek API for personalized policy interpretation: {e}")
            # 如果API调用失败，返回空结果
            return {
                "general_interpretation": self.interpret_policy(policy_text),
                "matches": [],
                "mismatches": [],
                "suggestions": ["无法提供个性化建议，API调用失败"],
                "personalized_interpretation": ""
            }

    def _is_personalized_interpretation_complete(self, result: Dict[str, Any]) -> bool:
        """
        检查个性化解读结果是否完整

        Args:
            result: 个性化解读结果

        Returns:
            个性化解读结果是否完整
        """
        # 检查关键字段是否有值
        required_fields = ["general_interpretation", "matches", "mismatches", "suggestions", "personalized_interpretation"]
        for field in required_fields:
            if field not in result:
                return False

        # 检查是否有个性化内容
        if not result["matches"] and not result["mismatches"] and not result["suggestions"]:
            return False

        return True