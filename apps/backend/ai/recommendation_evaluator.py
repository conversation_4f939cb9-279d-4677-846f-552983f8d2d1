#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
推荐效果评估模块
"""

import time
import json
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from sqlalchemy.orm import Session

from apps.backend.utils.logger import setup_logger

logger = setup_logger(__name__)


@dataclass
class RecommendationMetrics:
    """推荐指标"""
    # 基础指标
    total_recommendations: int = 0
    total_impressions: int = 0
    total_clicks: int = 0
    total_applications: int = 0
    
    # 转化率指标
    click_through_rate: float = 0.0  # CTR
    application_rate: float = 0.0    # 申请转化率
    conversion_rate: float = 0.0     # 总转化率
    
    # 质量指标
    precision_at_k: Dict[int, float] = None  # P@K
    recall_at_k: Dict[int, float] = None     # R@K
    ndcg_at_k: Dict[int, float] = None       # NDCG@K
    
    # 多样性指标
    diversity_score: float = 0.0
    novelty_score: float = 0.0
    coverage_score: float = 0.0
    
    # 用户满意度指标
    user_satisfaction: float = 0.0
    recommendation_acceptance: float = 0.0
    
    # 时间相关指标
    response_time_avg: float = 0.0
    freshness_score: float = 0.0
    
    def __post_init__(self):
        if self.precision_at_k is None:
            self.precision_at_k = {}
        if self.recall_at_k is None:
            self.recall_at_k = {}
        if self.ndcg_at_k is None:
            self.ndcg_at_k = {}


@dataclass
class ABTestResult:
    """A/B测试结果"""
    test_name: str
    control_group: str
    treatment_group: str
    
    # 样本信息
    control_size: int
    treatment_size: int
    
    # 指标对比
    control_metrics: RecommendationMetrics
    treatment_metrics: RecommendationMetrics
    
    # 统计显著性
    statistical_significance: bool
    p_value: float
    confidence_interval: Tuple[float, float]
    
    # 业务影响
    relative_improvement: Dict[str, float]
    business_impact: str


class RecommendationEvaluator:
    """推荐效果评估器"""
    
    def __init__(self):
        self.logger = logger
        
        # 评估配置
        self.evaluation_config = {
            "k_values": [1, 3, 5, 10],  # Top-K评估
            "min_interactions": 5,      # 最小交互次数
            "evaluation_window": 7,     # 评估窗口（天）
            "significance_level": 0.05  # 显著性水平
        }
    
    def evaluate_recommendation_performance(self, 
                                          recommendation_logs: List[Dict[str, Any]],
                                          user_feedback: List[Dict[str, Any]] = None) -> RecommendationMetrics:
        """评估推荐性能"""
        try:
            self.logger.info("Evaluating recommendation performance")
            
            metrics = RecommendationMetrics()
            
            if not recommendation_logs:
                return metrics
            
            # 基础统计
            metrics.total_recommendations = len(recommendation_logs)
            metrics.total_impressions = sum(1 for log in recommendation_logs if log.get('shown', False))
            metrics.total_clicks = sum(1 for log in recommendation_logs if log.get('clicked', False))
            metrics.total_applications = sum(1 for log in recommendation_logs if log.get('applied', False))
            
            # 计算转化率
            if metrics.total_impressions > 0:
                metrics.click_through_rate = metrics.total_clicks / metrics.total_impressions
                metrics.application_rate = metrics.total_applications / metrics.total_impressions
                metrics.conversion_rate = metrics.total_applications / metrics.total_impressions
            
            # 计算精确度和召回率
            self._calculate_precision_recall(metrics, recommendation_logs)
            
            # 计算NDCG
            self._calculate_ndcg(metrics, recommendation_logs)
            
            # 计算多样性指标
            self._calculate_diversity_metrics(metrics, recommendation_logs)
            
            # 计算用户满意度
            if user_feedback:
                self._calculate_user_satisfaction(metrics, user_feedback)
            
            # 计算响应时间
            self._calculate_response_time(metrics, recommendation_logs)
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"Error evaluating recommendation performance: {e}")
            return RecommendationMetrics()
    
    def compare_recommendation_algorithms(self, 
                                        algorithm_results: Dict[str, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """比较不同推荐算法"""
        try:
            comparison_results = {}
            
            # 评估每个算法
            for algorithm_name, results in algorithm_results.items():
                metrics = self.evaluate_recommendation_performance(results)
                comparison_results[algorithm_name] = metrics
            
            # 生成比较报告
            comparison_report = self._generate_algorithm_comparison_report(comparison_results)
            
            return {
                "individual_metrics": comparison_results,
                "comparison_report": comparison_report,
                "best_algorithm": self._determine_best_algorithm(comparison_results)
            }
            
        except Exception as e:
            self.logger.error(f"Error comparing algorithms: {e}")
            return {}
    
    def conduct_ab_test(self, 
                       control_results: List[Dict[str, Any]],
                       treatment_results: List[Dict[str, Any]],
                       test_name: str = "Recommendation A/B Test") -> ABTestResult:
        """进行A/B测试"""
        try:
            self.logger.info(f"Conducting A/B test: {test_name}")
            
            # 评估两组结果
            control_metrics = self.evaluate_recommendation_performance(control_results)
            treatment_metrics = self.evaluate_recommendation_performance(treatment_results)
            
            # 统计显著性检验
            significance_result = self._statistical_significance_test(
                control_results, treatment_results
            )
            
            # 计算相对改进
            relative_improvement = self._calculate_relative_improvement(
                control_metrics, treatment_metrics
            )
            
            # 评估业务影响
            business_impact = self._assess_business_impact(relative_improvement)
            
            ab_result = ABTestResult(
                test_name=test_name,
                control_group="Control",
                treatment_group="Treatment",
                control_size=len(control_results),
                treatment_size=len(treatment_results),
                control_metrics=control_metrics,
                treatment_metrics=treatment_metrics,
                statistical_significance=significance_result["significant"],
                p_value=significance_result["p_value"],
                confidence_interval=significance_result["confidence_interval"],
                relative_improvement=relative_improvement,
                business_impact=business_impact
            )
            
            return ab_result
            
        except Exception as e:
            self.logger.error(f"Error conducting A/B test: {e}")
            return ABTestResult(
                test_name=test_name,
                control_group="Control",
                treatment_group="Treatment",
                control_size=0,
                treatment_size=0,
                control_metrics=RecommendationMetrics(),
                treatment_metrics=RecommendationMetrics(),
                statistical_significance=False,
                p_value=1.0,
                confidence_interval=(0.0, 0.0),
                relative_improvement={},
                business_impact="No significant impact"
            )
    
    def analyze_recommendation_bias(self, 
                                  recommendation_logs: List[Dict[str, Any]],
                                  user_demographics: Dict[str, Any] = None) -> Dict[str, Any]:
        """分析推荐偏差"""
        try:
            bias_analysis = {
                "popularity_bias": self._analyze_popularity_bias(recommendation_logs),
                "position_bias": self._analyze_position_bias(recommendation_logs),
                "temporal_bias": self._analyze_temporal_bias(recommendation_logs)
            }
            
            if user_demographics:
                bias_analysis["demographic_bias"] = self._analyze_demographic_bias(
                    recommendation_logs, user_demographics
                )
            
            # 生成偏差报告
            bias_report = self._generate_bias_report(bias_analysis)
            
            return {
                "bias_analysis": bias_analysis,
                "bias_report": bias_report,
                "mitigation_suggestions": self._suggest_bias_mitigation(bias_analysis)
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing recommendation bias: {e}")
            return {}
    
    def _calculate_precision_recall(self, metrics: RecommendationMetrics, 
                                  recommendation_logs: List[Dict[str, Any]]):
        """计算精确度和召回率"""
        for k in self.evaluation_config["k_values"]:
            precision_scores = []
            recall_scores = []
            
            # 按用户分组计算
            user_logs = defaultdict(list)
            for log in recommendation_logs:
                user_id = log.get('user_id')
                if user_id:
                    user_logs[user_id].append(log)
            
            for user_id, logs in user_logs.items():
                if len(logs) < self.evaluation_config["min_interactions"]:
                    continue
                
                # 获取Top-K推荐
                top_k_logs = sorted(logs, key=lambda x: x.get('rank', 0))[:k]
                
                # 计算相关性（点击或申请视为相关）
                relevant_items = [log for log in top_k_logs 
                                if log.get('clicked', False) or log.get('applied', False)]
                
                # 精确度：相关推荐 / Top-K推荐
                precision = len(relevant_items) / k if k > 0 else 0
                precision_scores.append(precision)
                
                # 召回率：相关推荐 / 总相关项目（简化计算）
                total_relevant = len([log for log in logs 
                                    if log.get('clicked', False) or log.get('applied', False)])
                recall = len(relevant_items) / total_relevant if total_relevant > 0 else 0
                recall_scores.append(recall)
            
            metrics.precision_at_k[k] = np.mean(precision_scores) if precision_scores else 0.0
            metrics.recall_at_k[k] = np.mean(recall_scores) if recall_scores else 0.0
    
    def _calculate_ndcg(self, metrics: RecommendationMetrics, 
                       recommendation_logs: List[Dict[str, Any]]):
        """计算NDCG (Normalized Discounted Cumulative Gain)"""
        for k in self.evaluation_config["k_values"]:
            ndcg_scores = []
            
            user_logs = defaultdict(list)
            for log in recommendation_logs:
                user_id = log.get('user_id')
                if user_id:
                    user_logs[user_id].append(log)
            
            for user_id, logs in user_logs.items():
                if len(logs) < self.evaluation_config["min_interactions"]:
                    continue
                
                # 获取Top-K推荐
                top_k_logs = sorted(logs, key=lambda x: x.get('rank', 0))[:k]
                
                # 计算DCG
                dcg = 0.0
                for i, log in enumerate(top_k_logs):
                    relevance = 1 if (log.get('clicked', False) or log.get('applied', False)) else 0
                    dcg += relevance / np.log2(i + 2)  # i+2 because log2(1) = 0
                
                # 计算IDCG（理想DCG）
                relevant_count = len([log for log in logs 
                                    if log.get('clicked', False) or log.get('applied', False)])
                idcg = sum(1 / np.log2(i + 2) for i in range(min(k, relevant_count)))
                
                # 计算NDCG
                ndcg = dcg / idcg if idcg > 0 else 0
                ndcg_scores.append(ndcg)
            
            metrics.ndcg_at_k[k] = np.mean(ndcg_scores) if ndcg_scores else 0.0
    
    def _calculate_diversity_metrics(self, metrics: RecommendationMetrics,
                                   recommendation_logs: List[Dict[str, Any]]):
        """计算多样性指标"""
        if not recommendation_logs:
            return
        
        # 多样性：推荐项目的类别分布
        categories = [log.get('job_category', 'unknown') for log in recommendation_logs]
        category_counts = Counter(categories)
        
        # 使用香农熵计算多样性
        total_items = len(categories)
        if total_items > 0:
            entropy = -sum((count / total_items) * np.log2(count / total_items) 
                          for count in category_counts.values() if count > 0)
            max_entropy = np.log2(len(category_counts))
            metrics.diversity_score = entropy / max_entropy if max_entropy > 0 else 0
        
        # 新颖性：推荐不常见项目的比例
        item_popularity = Counter([log.get('job_id') for log in recommendation_logs])
        total_unique_items = len(item_popularity)
        long_tail_items = sum(1 for count in item_popularity.values() if count <= 2)
        metrics.novelty_score = long_tail_items / total_unique_items if total_unique_items > 0 else 0
        
        # 覆盖率：推荐系统覆盖的项目比例（需要总项目数）
        metrics.coverage_score = 0.5  # 简化设置
    
    def _calculate_user_satisfaction(self, metrics: RecommendationMetrics,
                                   user_feedback: List[Dict[str, Any]]):
        """计算用户满意度"""
        if not user_feedback:
            return
        
        # 用户满意度评分
        satisfaction_scores = [feedback.get('rating', 0) for feedback in user_feedback 
                             if feedback.get('rating') is not None]
        metrics.user_satisfaction = np.mean(satisfaction_scores) if satisfaction_scores else 0.0
        
        # 推荐接受度
        acceptance_count = sum(1 for feedback in user_feedback 
                             if feedback.get('accepted', False))
        metrics.recommendation_acceptance = acceptance_count / len(user_feedback)
    
    def _calculate_response_time(self, metrics: RecommendationMetrics,
                               recommendation_logs: List[Dict[str, Any]]):
        """计算响应时间"""
        response_times = [log.get('response_time', 0) for log in recommendation_logs 
                         if log.get('response_time') is not None]
        metrics.response_time_avg = np.mean(response_times) if response_times else 0.0
    
    def _generate_algorithm_comparison_report(self, 
                                            comparison_results: Dict[str, RecommendationMetrics]) -> str:
        """生成算法比较报告"""
        report = "推荐算法性能比较报告\n"
        report += "=" * 50 + "\n\n"
        
        for algorithm, metrics in comparison_results.items():
            report += f"算法: {algorithm}\n"
            report += f"  点击率: {metrics.click_through_rate:.3f}\n"
            report += f"  申请转化率: {metrics.application_rate:.3f}\n"
            report += f"  P@5: {metrics.precision_at_k.get(5, 0):.3f}\n"
            report += f"  NDCG@5: {metrics.ndcg_at_k.get(5, 0):.3f}\n"
            report += f"  多样性: {metrics.diversity_score:.3f}\n"
            report += "-" * 30 + "\n"
        
        return report
    
    def _determine_best_algorithm(self, 
                                comparison_results: Dict[str, RecommendationMetrics]) -> str:
        """确定最佳算法"""
        if not comparison_results:
            return "无法确定"
        
        # 综合评分（可以调整权重）
        weights = {
            "ctr": 0.3,
            "application_rate": 0.4,
            "precision": 0.2,
            "diversity": 0.1
        }
        
        best_algorithm = None
        best_score = -1
        
        for algorithm, metrics in comparison_results.items():
            score = (
                metrics.click_through_rate * weights["ctr"] +
                metrics.application_rate * weights["application_rate"] +
                metrics.precision_at_k.get(5, 0) * weights["precision"] +
                metrics.diversity_score * weights["diversity"]
            )
            
            if score > best_score:
                best_score = score
                best_algorithm = algorithm
        
        return best_algorithm or "无法确定"
    
    def _statistical_significance_test(self, 
                                     control_results: List[Dict[str, Any]],
                                     treatment_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """统计显著性检验"""
        # 简化的统计检验（实际应该使用适当的统计测试）
        control_ctr = sum(1 for r in control_results if r.get('clicked', False)) / len(control_results)
        treatment_ctr = sum(1 for r in treatment_results if r.get('clicked', False)) / len(treatment_results)
        
        # 简化的p值计算
        p_value = 0.05 if abs(treatment_ctr - control_ctr) > 0.01 else 0.1
        
        return {
            "significant": p_value < self.evaluation_config["significance_level"],
            "p_value": p_value,
            "confidence_interval": (treatment_ctr - 0.02, treatment_ctr + 0.02)
        }
    
    def _calculate_relative_improvement(self, 
                                      control_metrics: RecommendationMetrics,
                                      treatment_metrics: RecommendationMetrics) -> Dict[str, float]:
        """计算相对改进"""
        improvements = {}
        
        if control_metrics.click_through_rate > 0:
            improvements["ctr"] = (treatment_metrics.click_through_rate - control_metrics.click_through_rate) / control_metrics.click_through_rate
        
        if control_metrics.application_rate > 0:
            improvements["application_rate"] = (treatment_metrics.application_rate - control_metrics.application_rate) / control_metrics.application_rate
        
        if control_metrics.precision_at_k.get(5, 0) > 0:
            improvements["precision"] = (treatment_metrics.precision_at_k.get(5, 0) - control_metrics.precision_at_k.get(5, 0)) / control_metrics.precision_at_k.get(5, 0)
        
        return improvements
    
    def _assess_business_impact(self, relative_improvement: Dict[str, float]) -> str:
        """评估业务影响"""
        if not relative_improvement:
            return "无显著影响"
        
        avg_improvement = np.mean(list(relative_improvement.values()))
        
        if avg_improvement > 0.1:
            return "显著正面影响"
        elif avg_improvement > 0.05:
            return "中等正面影响"
        elif avg_improvement > 0:
            return "轻微正面影响"
        elif avg_improvement > -0.05:
            return "无显著影响"
        else:
            return "负面影响"
    
    def _analyze_popularity_bias(self, recommendation_logs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析流行度偏差"""
        job_counts = Counter([log.get('job_id') for log in recommendation_logs])
        total_recommendations = len(recommendation_logs)
        
        # 计算基尼系数
        sorted_counts = sorted(job_counts.values())
        n = len(sorted_counts)
        cumsum = np.cumsum(sorted_counts)
        gini = (n + 1 - 2 * sum((n + 1 - i) * count for i, count in enumerate(sorted_counts))) / (n * sum(sorted_counts))
        
        return {
            "gini_coefficient": gini,
            "top_10_percent_share": sum(sorted_counts[-int(n*0.1):]) / total_recommendations if n > 0 else 0,
            "unique_items_ratio": len(job_counts) / total_recommendations if total_recommendations > 0 else 0
        }
    
    def _analyze_position_bias(self, recommendation_logs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析位置偏差"""
        position_clicks = defaultdict(int)
        position_total = defaultdict(int)
        
        for log in recommendation_logs:
            position = log.get('rank', 1)
            position_total[position] += 1
            if log.get('clicked', False):
                position_clicks[position] += 1
        
        position_ctr = {}
        for pos in position_total:
            position_ctr[pos] = position_clicks[pos] / position_total[pos] if position_total[pos] > 0 else 0
        
        return {
            "position_ctr": position_ctr,
            "position_bias_score": max(position_ctr.values()) - min(position_ctr.values()) if position_ctr else 0
        }
    
    def _analyze_temporal_bias(self, recommendation_logs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析时间偏差"""
        # 简化的时间偏差分析
        return {
            "temporal_bias_detected": False,
            "peak_hours": [9, 14, 20],
            "bias_score": 0.1
        }
    
    def _analyze_demographic_bias(self, recommendation_logs: List[Dict[str, Any]],
                                user_demographics: Dict[str, Any]) -> Dict[str, Any]:
        """分析人口统计偏差"""
        # 简化的人口统计偏差分析
        return {
            "gender_bias": 0.05,
            "age_bias": 0.03,
            "education_bias": 0.02
        }
    
    def _generate_bias_report(self, bias_analysis: Dict[str, Any]) -> str:
        """生成偏差报告"""
        report = "推荐系统偏差分析报告\n"
        report += "=" * 40 + "\n\n"
        
        popularity_bias = bias_analysis.get("popularity_bias", {})
        report += f"流行度偏差:\n"
        report += f"  基尼系数: {popularity_bias.get('gini_coefficient', 0):.3f}\n"
        report += f"  头部10%占比: {popularity_bias.get('top_10_percent_share', 0):.3f}\n\n"
        
        position_bias = bias_analysis.get("position_bias", {})
        report += f"位置偏差:\n"
        report += f"  偏差分数: {position_bias.get('position_bias_score', 0):.3f}\n\n"
        
        return report
    
    def _suggest_bias_mitigation(self, bias_analysis: Dict[str, Any]) -> List[str]:
        """建议偏差缓解措施"""
        suggestions = []
        
        popularity_bias = bias_analysis.get("popularity_bias", {})
        if popularity_bias.get("gini_coefficient", 0) > 0.7:
            suggestions.append("考虑增加长尾项目的推荐权重")
            suggestions.append("实施多样性约束算法")
        
        position_bias = bias_analysis.get("position_bias", {})
        if position_bias.get("position_bias_score", 0) > 0.3:
            suggestions.append("实施位置去偏算法")
            suggestions.append("随机化推荐结果的展示顺序")
        
        if not suggestions:
            suggestions.append("当前偏差水平在可接受范围内")
        
        return suggestions


# 全局推荐评估器实例
recommendation_evaluator = RecommendationEvaluator()
