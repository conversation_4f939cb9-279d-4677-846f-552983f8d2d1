#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
个性化推荐系统升级版
"""

import time
import json
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.decomposition import TruncatedSVD
from sklearn.preprocessing import StandardScaler

from apps.backend.utils.logger import setup_logger
from apps.backend.ai.major_matcher import MajorMatcher

logger = setup_logger(__name__)


@dataclass
class UserProfile:
    """用户画像"""
    user_id: str
    education: str = ""
    major: str = ""
    skills: List[str] = None
    experience_years: int = 0
    location_preference: List[str] = None
    job_type_preference: List[str] = None
    salary_expectation: Tuple[int, int] = (0, 0)
    
    # 行为特征
    viewed_jobs: List[str] = None
    applied_jobs: List[str] = None
    favorited_jobs: List[str] = None
    search_keywords: List[str] = None
    
    # 兴趣偏好
    preferred_companies: List[str] = None
    preferred_industries: List[str] = None
    work_style_preference: str = ""  # remote, onsite, hybrid
    
    # 动态特征
    activity_score: float = 0.0
    engagement_level: str = "low"  # low, medium, high
    last_active: datetime = None
    
    def __post_init__(self):
        if self.skills is None:
            self.skills = []
        if self.location_preference is None:
            self.location_preference = []
        if self.job_type_preference is None:
            self.job_type_preference = []
        if self.viewed_jobs is None:
            self.viewed_jobs = []
        if self.applied_jobs is None:
            self.applied_jobs = []
        if self.favorited_jobs is None:
            self.favorited_jobs = []
        if self.search_keywords is None:
            self.search_keywords = []
        if self.preferred_companies is None:
            self.preferred_companies = []
        if self.preferred_industries is None:
            self.preferred_industries = []
        if self.last_active is None:
            self.last_active = datetime.now()


@dataclass
class JobFeatures:
    """岗位特征"""
    job_id: str
    title: str
    company: str
    industry: str
    location: str
    job_type: str
    education_requirement: str
    major_requirement: str
    experience_requirement: str
    salary_range: Tuple[int, int]
    skills_required: List[str]
    description: str
    
    # 统计特征
    view_count: int = 0
    apply_count: int = 0
    favorite_count: int = 0
    click_through_rate: float = 0.0
    
    # 质量特征
    company_rating: float = 0.0
    job_freshness: int = 0  # 发布天数
    urgency_level: str = "normal"  # urgent, normal, low
    
    def __post_init__(self):
        if self.skills_required is None:
            self.skills_required = []


@dataclass
class RecommendationResult:
    """推荐结果"""
    job_id: str
    job_title: str
    company: str
    recommendation_score: float
    match_reasons: List[str]
    confidence_level: str  # high, medium, low
    recommendation_type: str  # content_based, collaborative, hybrid
    
    # 详细匹配信息
    major_match_score: float = 0.0
    skill_match_score: float = 0.0
    location_match_score: float = 0.0
    experience_match_score: float = 0.0
    salary_match_score: float = 0.0
    
    # 个性化因子
    user_interest_score: float = 0.0
    behavioral_score: float = 0.0
    trending_score: float = 0.0


class PersonalizedRecommendationSystem:
    """个性化推荐系统"""
    
    def __init__(self):
        self.logger = logger
        self.major_matcher = MajorMatcher()
        
        # 推荐算法权重配置
        self.algorithm_weights = {
            "content_based": 0.4,
            "collaborative_filtering": 0.3,
            "behavioral": 0.2,
            "trending": 0.1
        }
        
        # 特征权重配置
        self.feature_weights = {
            "major_match": 0.25,
            "skill_match": 0.20,
            "experience_match": 0.15,
            "location_match": 0.15,
            "salary_match": 0.10,
            "company_preference": 0.10,
            "job_quality": 0.05
        }
        
        # 缓存
        self.user_profiles_cache = {}
        self.job_features_cache = {}
        self.similarity_matrix_cache = {}
        
        # 向量化器
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=1000,
            stop_words='english',
            ngram_range=(1, 2)
        )
        self.scaler = StandardScaler()
        
        # 协同过滤模型
        self.svd_model = TruncatedSVD(n_components=50, random_state=42)
        
    def build_user_profile(self, user_data: Dict[str, Any], 
                          interaction_data: Dict[str, Any] = None) -> UserProfile:
        """构建用户画像"""
        try:
            # 基础信息
            profile = UserProfile(
                user_id=str(user_data.get('user_id', '')),
                education=user_data.get('education', ''),
                major=user_data.get('major', ''),
                skills=user_data.get('skills', []),
                experience_years=user_data.get('experience_years', 0),
                location_preference=user_data.get('location_preference', []),
                job_type_preference=user_data.get('job_type_preference', []),
                salary_expectation=tuple(user_data.get('salary_expectation', [0, 0]))
            )
            
            # 行为数据
            if interaction_data:
                profile.viewed_jobs = interaction_data.get('viewed_jobs', [])
                profile.applied_jobs = interaction_data.get('applied_jobs', [])
                profile.favorited_jobs = interaction_data.get('favorited_jobs', [])
                profile.search_keywords = interaction_data.get('search_keywords', [])
                
                # 计算活跃度分数
                profile.activity_score = self._calculate_activity_score(interaction_data)
                profile.engagement_level = self._determine_engagement_level(profile.activity_score)
                
                # 推断偏好
                profile.preferred_companies = self._infer_company_preferences(interaction_data)
                profile.preferred_industries = self._infer_industry_preferences(interaction_data)
            
            # 缓存用户画像
            self.user_profiles_cache[profile.user_id] = profile
            
            return profile
            
        except Exception as e:
            self.logger.error(f"Error building user profile: {e}")
            return UserProfile(user_id=str(user_data.get('user_id', '')))
    
    def extract_job_features(self, job_data: Dict[str, Any]) -> JobFeatures:
        """提取岗位特征"""
        try:
            features = JobFeatures(
                job_id=str(job_data.get('job_id', '')),
                title=job_data.get('title', ''),
                company=job_data.get('company', ''),
                industry=job_data.get('industry', ''),
                location=job_data.get('location', ''),
                job_type=job_data.get('job_type', ''),
                education_requirement=job_data.get('education_requirement', ''),
                major_requirement=job_data.get('major_requirement', ''),
                experience_requirement=job_data.get('experience_requirement', ''),
                salary_range=tuple(job_data.get('salary_range', [0, 0])),
                skills_required=job_data.get('skills_required', []),
                description=job_data.get('description', ''),
                view_count=job_data.get('view_count', 0),
                apply_count=job_data.get('apply_count', 0),
                favorite_count=job_data.get('favorite_count', 0),
                company_rating=job_data.get('company_rating', 0.0),
                job_freshness=job_data.get('job_freshness', 0)
            )
            
            # 计算点击率
            if features.view_count > 0:
                features.click_through_rate = features.apply_count / features.view_count
            
            # 缓存岗位特征
            self.job_features_cache[features.job_id] = features
            
            return features
            
        except Exception as e:
            self.logger.error(f"Error extracting job features: {e}")
            return JobFeatures(job_id=str(job_data.get('job_id', '')), title='', company='', 
                             industry='', location='', job_type='', education_requirement='',
                             major_requirement='', experience_requirement='', 
                             salary_range=(0, 0), skills_required=[], description='')
    
    def recommend_jobs(self, user_profile: UserProfile, 
                      available_jobs: List[JobFeatures],
                      max_recommendations: int = 10) -> List[RecommendationResult]:
        """生成个性化推荐"""
        try:
            self.logger.info(f"Generating recommendations for user {user_profile.user_id}")
            
            # 1. 基于内容的推荐
            content_scores = self._content_based_recommendation(user_profile, available_jobs)
            
            # 2. 协同过滤推荐
            collaborative_scores = self._collaborative_filtering_recommendation(
                user_profile, available_jobs
            )
            
            # 3. 基于行为的推荐
            behavioral_scores = self._behavioral_recommendation(user_profile, available_jobs)
            
            # 4. 热门趋势推荐
            trending_scores = self._trending_recommendation(available_jobs)
            
            # 5. 混合推荐算法
            final_recommendations = self._hybrid_recommendation(
                user_profile, available_jobs,
                content_scores, collaborative_scores, behavioral_scores, trending_scores
            )
            
            # 6. 排序和筛选
            final_recommendations.sort(key=lambda x: x.recommendation_score, reverse=True)
            
            return final_recommendations[:max_recommendations]
            
        except Exception as e:
            self.logger.error(f"Error generating recommendations: {e}")
            return []
    
    def _content_based_recommendation(self, user_profile: UserProfile, 
                                    jobs: List[JobFeatures]) -> Dict[str, float]:
        """基于内容的推荐"""
        scores = {}
        
        for job in jobs:
            score = 0.0
            
            # 专业匹配
            if user_profile.major and job.major_requirement:
                major_match = self.major_matcher.analyze_major_match(
                    job.major_requirement, user_profile.major
                )
                score += major_match.get('match_score', 0) / 100 * self.feature_weights['major_match']
            
            # 技能匹配
            if user_profile.skills and job.skills_required:
                skill_match = self._calculate_skill_match(user_profile.skills, job.skills_required)
                score += skill_match * self.feature_weights['skill_match']
            
            # 地点匹配
            if user_profile.location_preference and job.location:
                location_match = self._calculate_location_match(
                    user_profile.location_preference, job.location
                )
                score += location_match * self.feature_weights['location_match']
            
            # 经验匹配
            experience_match = self._calculate_experience_match(
                user_profile.experience_years, job.experience_requirement
            )
            score += experience_match * self.feature_weights['experience_match']
            
            # 薪资匹配
            if user_profile.salary_expectation != (0, 0) and job.salary_range != (0, 0):
                salary_match = self._calculate_salary_match(
                    user_profile.salary_expectation, job.salary_range
                )
                score += salary_match * self.feature_weights['salary_match']
            
            scores[job.job_id] = score
        
        return scores
    
    def _collaborative_filtering_recommendation(self, user_profile: UserProfile,
                                              jobs: List[JobFeatures]) -> Dict[str, float]:
        """协同过滤推荐 - 增强版"""
        scores = {}

        try:
            # 1. 基于用户相似度的协同过滤
            similar_users = self._find_similar_users(user_profile)
            user_based_scores = self._user_based_collaborative_filtering(
                user_profile, jobs, similar_users
            )

            # 2. 基于物品相似度的协同过滤
            item_based_scores = self._item_based_collaborative_filtering(
                user_profile, jobs
            )

            # 3. 矩阵分解协同过滤
            matrix_factorization_scores = self._matrix_factorization_recommendation(
                user_profile, jobs
            )

            # 4. 混合协同过滤结果
            for job in jobs:
                job_id = job.job_id

                user_based_score = user_based_scores.get(job_id, 0.0)
                item_based_score = item_based_scores.get(job_id, 0.0)
                mf_score = matrix_factorization_scores.get(job_id, 0.0)

                # 加权合并
                combined_score = (
                    user_based_score * 0.4 +
                    item_based_score * 0.3 +
                    mf_score * 0.3
                )

                scores[job_id] = combined_score

            return scores

        except Exception as e:
            self.logger.error(f"协同过滤推荐失败: {e}")
            # 降级到简化版本
            return self._simple_collaborative_filtering(jobs)
    
    def _behavioral_recommendation(self, user_profile: UserProfile,
                                 jobs: List[JobFeatures]) -> Dict[str, float]:
        """基于行为的推荐"""
        scores = {}
        
        for job in jobs:
            score = 0.0
            
            # 基于用户历史行为
            if job.company in user_profile.preferred_companies:
                score += 0.3
            
            if job.industry in user_profile.preferred_industries:
                score += 0.2
            
            # 基于搜索关键词匹配
            if user_profile.search_keywords:
                keyword_match = self._calculate_keyword_match(
                    user_profile.search_keywords, job.title + " " + job.description
                )
                score += keyword_match * 0.5
            
            scores[job.job_id] = score
        
        return scores
    
    def _trending_recommendation(self, jobs: List[JobFeatures]) -> Dict[str, float]:
        """热门趋势推荐"""
        scores = {}
        
        # 计算热门度分数
        max_views = max([job.view_count for job in jobs] + [1])
        max_applies = max([job.apply_count for job in jobs] + [1])
        
        for job in jobs:
            # 基于浏览量和申请量的热门度
            view_score = job.view_count / max_views
            apply_score = job.apply_count / max_applies
            
            # 新鲜度加权（新发布的岗位获得更高分数）
            freshness_score = max(0, 1 - job.job_freshness / 30)  # 30天内的岗位
            
            trending_score = (view_score * 0.4 + apply_score * 0.4 + freshness_score * 0.2)
            scores[job.job_id] = trending_score
        
        return scores
    
    def _hybrid_recommendation(self, user_profile: UserProfile, jobs: List[JobFeatures],
                             content_scores: Dict[str, float],
                             collaborative_scores: Dict[str, float],
                             behavioral_scores: Dict[str, float],
                             trending_scores: Dict[str, float]) -> List[RecommendationResult]:
        """混合推荐算法"""
        recommendations = []
        
        for job in jobs:
            job_id = job.job_id
            
            # 加权合并各种推荐分数
            final_score = (
                content_scores.get(job_id, 0) * self.algorithm_weights['content_based'] +
                collaborative_scores.get(job_id, 0) * self.algorithm_weights['collaborative_filtering'] +
                behavioral_scores.get(job_id, 0) * self.algorithm_weights['behavioral'] +
                trending_scores.get(job_id, 0) * self.algorithm_weights['trending']
            )
            
            # 生成匹配原因
            match_reasons = self._generate_match_reasons(user_profile, job, content_scores.get(job_id, 0))
            
            # 确定置信度
            confidence_level = self._determine_confidence_level(final_score)
            
            # 确定推荐类型
            recommendation_type = self._determine_recommendation_type(
                content_scores.get(job_id, 0),
                collaborative_scores.get(job_id, 0),
                behavioral_scores.get(job_id, 0)
            )
            
            recommendation = RecommendationResult(
                job_id=job_id,
                job_title=job.title,
                company=job.company,
                recommendation_score=final_score,
                match_reasons=match_reasons,
                confidence_level=confidence_level,
                recommendation_type=recommendation_type,
                major_match_score=content_scores.get(job_id, 0),
                behavioral_score=behavioral_scores.get(job_id, 0),
                trending_score=trending_scores.get(job_id, 0)
            )
            
            recommendations.append(recommendation)
        
        return recommendations
    
    def _calculate_skill_match(self, user_skills: List[str], required_skills: List[str]) -> float:
        """计算技能匹配度"""
        if not user_skills or not required_skills:
            return 0.0
        
        user_skills_lower = [skill.lower() for skill in user_skills]
        required_skills_lower = [skill.lower() for skill in required_skills]
        
        matched_skills = set(user_skills_lower) & set(required_skills_lower)
        return len(matched_skills) / len(required_skills_lower)
    
    def _calculate_location_match(self, preferred_locations: List[str], job_location: str) -> float:
        """计算地点匹配度"""
        if not preferred_locations or not job_location:
            return 0.0
        
        job_location_lower = job_location.lower()
        for location in preferred_locations:
            if location.lower() in job_location_lower:
                return 1.0
        return 0.0
    
    def _calculate_experience_match(self, user_experience: int, required_experience: str) -> float:
        """计算经验匹配度"""
        if not required_experience:
            return 1.0
        
        # 简化的经验匹配逻辑
        if "不限" in required_experience or "无要求" in required_experience:
            return 1.0
        
        # 提取数字
        import re
        numbers = re.findall(r'\d+', required_experience)
        if numbers:
            required_years = int(numbers[0])
            if user_experience >= required_years:
                return 1.0
            elif user_experience >= required_years * 0.8:
                return 0.8
            else:
                return 0.5
        
        return 0.5
    
    def _calculate_salary_match(self, expected_salary: Tuple[int, int], 
                              offered_salary: Tuple[int, int]) -> float:
        """计算薪资匹配度"""
        if expected_salary == (0, 0) or offered_salary == (0, 0):
            return 0.5
        
        expected_min, expected_max = expected_salary
        offered_min, offered_max = offered_salary
        
        # 如果期望薪资在提供范围内
        if offered_min <= expected_min <= offered_max or offered_min <= expected_max <= offered_max:
            return 1.0
        
        # 如果有重叠
        overlap_min = max(expected_min, offered_min)
        overlap_max = min(expected_max, offered_max)
        
        if overlap_min <= overlap_max:
            overlap_range = overlap_max - overlap_min
            expected_range = expected_max - expected_min
            return overlap_range / expected_range if expected_range > 0 else 0.5
        
        return 0.0
    
    def _calculate_keyword_match(self, search_keywords: List[str], job_text: str) -> float:
        """计算关键词匹配度"""
        if not search_keywords or not job_text:
            return 0.0
        
        job_text_lower = job_text.lower()
        matched_keywords = 0
        
        for keyword in search_keywords:
            if keyword.lower() in job_text_lower:
                matched_keywords += 1
        
        return matched_keywords / len(search_keywords)
    
    def _calculate_activity_score(self, interaction_data: Dict[str, Any]) -> float:
        """计算用户活跃度分数"""
        score = 0.0
        
        # 浏览行为
        viewed_count = len(interaction_data.get('viewed_jobs', []))
        score += min(viewed_count / 50, 1.0) * 0.3
        
        # 申请行为
        applied_count = len(interaction_data.get('applied_jobs', []))
        score += min(applied_count / 10, 1.0) * 0.4
        
        # 收藏行为
        favorited_count = len(interaction_data.get('favorited_jobs', []))
        score += min(favorited_count / 20, 1.0) * 0.3
        
        return min(score, 1.0)
    
    def _determine_engagement_level(self, activity_score: float) -> str:
        """确定用户参与度级别"""
        if activity_score >= 0.7:
            return "high"
        elif activity_score >= 0.4:
            return "medium"
        else:
            return "low"
    
    def _infer_company_preferences(self, interaction_data: Dict[str, Any]) -> List[str]:
        """推断公司偏好"""
        # 基于用户交互历史推断偏好公司
        # 这里需要实际的交互数据来实现
        return []
    
    def _infer_industry_preferences(self, interaction_data: Dict[str, Any]) -> List[str]:
        """推断行业偏好"""
        # 基于用户交互历史推断偏好行业
        # 这里需要实际的交互数据来实现
        return []
    
    def _generate_match_reasons(self, user_profile: UserProfile, job: JobFeatures, 
                              content_score: float) -> List[str]:
        """生成匹配原因"""
        reasons = []
        
        # 专业匹配
        if user_profile.major and job.major_requirement:
            major_match = self.major_matcher.analyze_major_match(
                job.major_requirement, user_profile.major
            )
            if major_match.get('match_score', 0) > 70:
                reasons.append(f"专业高度匹配：{user_profile.major}")
        
        # 技能匹配
        if user_profile.skills and job.skills_required:
            matched_skills = set([s.lower() for s in user_profile.skills]) & \
                           set([s.lower() for s in job.skills_required])
            if matched_skills:
                reasons.append(f"技能匹配：{', '.join(list(matched_skills)[:3])}")
        
        # 地点匹配
        if user_profile.location_preference and job.location:
            for location in user_profile.location_preference:
                if location.lower() in job.location.lower():
                    reasons.append(f"地点偏好匹配：{job.location}")
                    break
        
        # 如果没有具体原因，给出通用原因
        if not reasons:
            if content_score > 0.6:
                reasons.append("综合条件匹配度较高")
            else:
                reasons.append("可能符合您的求职需求")
        
        return reasons
    
    def _determine_confidence_level(self, score: float) -> str:
        """确定置信度级别"""
        if score >= 0.7:
            return "high"
        elif score >= 0.4:
            return "medium"
        else:
            return "low"
    
    def _determine_recommendation_type(self, content_score: float, 
                                     collaborative_score: float,
                                     behavioral_score: float) -> str:
        """确定推荐类型"""
        scores = {
            "content_based": content_score,
            "collaborative": collaborative_score,
            "behavioral": behavioral_score
        }
        
        max_type = max(scores, key=scores.get)
        
        if max(scores.values()) > 0.5:
            return max_type
        else:
            return "hybrid"

    def _find_similar_users(self, user_profile: UserProfile, top_k: int = 50) -> List[Dict[str, Any]]:
        """查找相似用户"""
        try:
            # 这里应该从数据库查询相似用户
            # 基于用户特征计算相似度：专业、技能、地点偏好、行为模式等

            # 模拟相似用户数据
            similar_users = []

            # 实际实现中，这里应该：
            # 1. 从数据库获取所有用户的特征向量
            # 2. 计算当前用户与其他用户的相似度
            # 3. 返回最相似的top_k个用户

            return similar_users

        except Exception as e:
            self.logger.error(f"查找相似用户失败: {e}")
            return []

    def _user_based_collaborative_filtering(self, user_profile: UserProfile,
                                          jobs: List[JobFeatures],
                                          similar_users: List[Dict[str, Any]]) -> Dict[str, float]:
        """基于用户的协同过滤"""
        scores = {}

        try:
            if not similar_users:
                return {job.job_id: 0.0 for job in jobs}

            # 计算相似用户对各岗位的偏好
            for job in jobs:
                weighted_score = 0.0
                total_weight = 0.0

                for similar_user in similar_users:
                    similarity = similar_user.get('similarity', 0.0)
                    user_rating = self._get_user_job_rating(
                        similar_user.get('user_id'), job.job_id
                    )

                    if user_rating > 0:
                        weighted_score += similarity * user_rating
                        total_weight += similarity

                if total_weight > 0:
                    scores[job.job_id] = weighted_score / total_weight
                else:
                    scores[job.job_id] = 0.0

            return scores

        except Exception as e:
            self.logger.error(f"基于用户的协同过滤失败: {e}")
            return {job.job_id: 0.0 for job in jobs}

    def _item_based_collaborative_filtering(self, user_profile: UserProfile,
                                          jobs: List[JobFeatures]) -> Dict[str, float]:
        """基于物品的协同过滤"""
        scores = {}

        try:
            # 获取用户历史交互的岗位
            user_interacted_jobs = (
                user_profile.viewed_jobs +
                user_profile.applied_jobs +
                user_profile.favorited_jobs
            )

            if not user_interacted_jobs:
                return {job.job_id: 0.0 for job in jobs}

            # 计算岗位间相似度并推荐
            for job in jobs:
                if job.job_id in user_interacted_jobs:
                    scores[job.job_id] = 0.0  # 已交互过的岗位不推荐
                    continue

                similarity_score = 0.0
                for interacted_job_id in user_interacted_jobs:
                    job_similarity = self._calculate_job_similarity(
                        job.job_id, interacted_job_id
                    )
                    user_rating = self._get_user_job_rating(
                        user_profile.user_id, interacted_job_id
                    )
                    similarity_score += job_similarity * user_rating

                scores[job.job_id] = similarity_score / len(user_interacted_jobs)

            return scores

        except Exception as e:
            self.logger.error(f"基于物品的协同过滤失败: {e}")
            return {job.job_id: 0.0 for job in jobs}

    def _matrix_factorization_recommendation(self, user_profile: UserProfile,
                                           jobs: List[JobFeatures]) -> Dict[str, float]:
        """矩阵分解推荐"""
        scores = {}

        try:
            # 这里应该使用预训练的矩阵分解模型
            # 如SVD、NMF等算法

            # 模拟矩阵分解结果
            for job in jobs:
                # 基于用户和岗位的潜在因子计算分数
                user_factors = self._get_user_latent_factors(user_profile.user_id)
                job_factors = self._get_job_latent_factors(job.job_id)

                if user_factors is not None and job_factors is not None:
                    score = np.dot(user_factors, job_factors)
                    scores[job.job_id] = max(0, min(1, score))  # 归一化到[0,1]
                else:
                    scores[job.job_id] = 0.0

            return scores

        except Exception as e:
            self.logger.error(f"矩阵分解推荐失败: {e}")
            return {job.job_id: 0.0 for job in jobs}

    def _simple_collaborative_filtering(self, jobs: List[JobFeatures]) -> Dict[str, float]:
        """简化的协同过滤（降级方案）"""
        scores = {}

        for job in jobs:
            score = 0.0

            # 基于岗位统计信息的简单协同过滤
            if job.apply_count > 0:
                score += min(job.apply_count / 100, 1.0) * 0.3

            if job.favorite_count > 0:
                score += min(job.favorite_count / 50, 1.0) * 0.4

            if job.view_count > 0:
                score += min(job.view_count / 1000, 1.0) * 0.3

            scores[job.job_id] = score

        return scores

    def _get_user_job_rating(self, user_id: str, job_id: str) -> float:
        """获取用户对岗位的评分"""
        try:
            # 这里应该从数据库查询用户对岗位的隐式评分
            # 基于用户行为计算：浏览=1分，收藏=3分，申请=5分

            # 模拟评分
            return 0.0

        except Exception as e:
            self.logger.error(f"获取用户岗位评分失败: {e}")
            return 0.0

    def _calculate_job_similarity(self, job_id1: str, job_id2: str) -> float:
        """计算岗位相似度"""
        try:
            # 从缓存获取岗位特征
            job1 = self.job_features_cache.get(job_id1)
            job2 = self.job_features_cache.get(job_id2)

            if not job1 or not job2:
                return 0.0

            # 计算多维度相似度
            similarity = 0.0

            # 行业相似度
            if job1.industry == job2.industry:
                similarity += 0.3

            # 地点相似度
            if job1.location == job2.location:
                similarity += 0.2

            # 技能相似度
            if job1.skills_required and job2.skills_required:
                skill_similarity = self._calculate_skill_similarity(
                    job1.skills_required, job2.skills_required
                )
                similarity += skill_similarity * 0.3

            # 薪资相似度
            salary_similarity = self._calculate_salary_similarity(
                job1.salary_range, job2.salary_range
            )
            similarity += salary_similarity * 0.2

            return min(similarity, 1.0)

        except Exception as e:
            self.logger.error(f"计算岗位相似度失败: {e}")
            return 0.0

    def _calculate_skill_similarity(self, skills1: List[str], skills2: List[str]) -> float:
        """计算技能相似度"""
        if not skills1 or not skills2:
            return 0.0

        skills1_set = set([s.lower() for s in skills1])
        skills2_set = set([s.lower() for s in skills2])

        intersection = skills1_set & skills2_set
        union = skills1_set | skills2_set

        return len(intersection) / len(union) if union else 0.0

    def _calculate_salary_similarity(self, salary1: Tuple[int, int],
                                   salary2: Tuple[int, int]) -> float:
        """计算薪资相似度"""
        if salary1 == (0, 0) or salary2 == (0, 0):
            return 0.5

        min1, max1 = salary1
        min2, max2 = salary2

        # 计算重叠度
        overlap_min = max(min1, min2)
        overlap_max = min(max1, max2)

        if overlap_min <= overlap_max:
            overlap = overlap_max - overlap_min
            total_range = max(max1, max2) - min(min1, min2)
            return overlap / total_range if total_range > 0 else 1.0

        return 0.0

    def _get_user_latent_factors(self, user_id: str) -> Optional[np.ndarray]:
        """获取用户潜在因子"""
        try:
            # 这里应该从预训练的模型中获取用户的潜在因子向量
            # 模拟返回50维的因子向量
            return np.random.random(50) * 0.1  # 小的随机值

        except Exception as e:
            self.logger.error(f"获取用户潜在因子失败: {e}")
            return None

    def _get_job_latent_factors(self, job_id: str) -> Optional[np.ndarray]:
        """获取岗位潜在因子"""
        try:
            # 这里应该从预训练的模型中获取岗位的潜在因子向量
            # 模拟返回50维的因子向量
            return np.random.random(50) * 0.1  # 小的随机值

        except Exception as e:
            self.logger.error(f"获取岗位潜在因子失败: {e}")
            return None


# 全局推荐系统实例
recommendation_system = PersonalizedRecommendationSystem()
