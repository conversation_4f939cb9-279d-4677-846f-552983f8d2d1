#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
工单管理系统
"""

import time
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime, timed<PERSON>ta
from enum import Enum
from sqlalchemy.orm import Session

from apps.backend.utils.logger import setup_logger
from apps.backend.ai.llm import LLMRouter
from apps.backend.config import LLM_CONFIG

logger = setup_logger(__name__)


class TicketStatus(Enum):
    """工单状态"""
    OPEN = "open"
    IN_PROGRESS = "in_progress"
    PENDING = "pending"
    RESOLVED = "resolved"
    CLOSED = "closed"
    CANCELLED = "cancelled"


class TicketPriority(Enum):
    """工单优先级"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"
    CRITICAL = "critical"


class TicketCategory(Enum):
    """工单分类"""
    TECHNICAL_ISSUE = "technical_issue"
    ACCOUNT_PROBLEM = "account_problem"
    JOB_SEARCH_HELP = "job_search_help"
    APPLICATION_ISSUE = "application_issue"
    POLICY_QUESTION = "policy_question"
    FEATURE_REQUEST = "feature_request"
    BUG_REPORT = "bug_report"
    COMPLAINT = "complaint"
    GENERAL_INQUIRY = "general_inquiry"


@dataclass
class TicketAttachment:
    """工单附件"""
    attachment_id: str
    filename: str
    file_type: str
    file_size: int
    file_url: str
    uploaded_at: datetime


@dataclass
class TicketComment:
    """工单评论"""
    comment_id: str
    ticket_id: str
    author_id: str
    author_type: str  # user, agent, system
    content: str
    is_internal: bool
    created_at: datetime
    attachments: List[TicketAttachment] = None
    
    def __post_init__(self):
        if self.attachments is None:
            self.attachments = []


@dataclass
class Ticket:
    """工单"""
    ticket_id: str
    user_id: str
    title: str
    description: str
    category: TicketCategory
    priority: TicketPriority
    status: TicketStatus
    
    # 分配信息
    assigned_agent_id: Optional[str] = None
    assigned_at: Optional[datetime] = None
    
    # 时间信息
    created_at: datetime = None
    updated_at: datetime = None
    resolved_at: Optional[datetime] = None
    closed_at: Optional[datetime] = None
    
    # 附加信息
    tags: List[str] = None
    attachments: List[TicketAttachment] = None
    comments: List[TicketComment] = None
    
    # 统计信息
    response_time: Optional[int] = None  # 首次响应时间（秒）
    resolution_time: Optional[int] = None  # 解决时间（秒）
    satisfaction_rating: Optional[int] = None  # 满意度评分（1-5）
    
    # 自动化信息
    auto_categorized: bool = False
    auto_priority_assigned: bool = False
    escalation_count: int = 0
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()
        if self.tags is None:
            self.tags = []
        if self.attachments is None:
            self.attachments = []
        if self.comments is None:
            self.comments = []


class TicketClassifier:
    """工单分类器"""
    
    def __init__(self):
        self.logger = logger
        self.llm_router = LLMRouter(config=LLM_CONFIG)
        
        # 分类关键词
        self.category_keywords = {
            TicketCategory.TECHNICAL_ISSUE: [
                "bug", "错误", "故障", "无法", "打不开", "卡顿", "崩溃", 
                "系统", "技术", "异常", "问题"
            ],
            TicketCategory.ACCOUNT_PROBLEM: [
                "账号", "密码", "登录", "注册", "个人信息", "修改", 
                "忘记", "绑定", "解绑", "验证"
            ],
            TicketCategory.JOB_SEARCH_HELP: [
                "找工作", "求职", "岗位", "职位", "招聘", "面试", 
                "简历", "投递", "申请"
            ],
            TicketCategory.APPLICATION_ISSUE: [
                "申请", "投递", "简历", "状态", "进度", "结果", 
                "通知", "录用", "面试"
            ],
            TicketCategory.POLICY_QUESTION: [
                "政策", "规定", "要求", "条件", "资格", "标准", 
                "流程", "程序", "办法"
            ],
            TicketCategory.FEATURE_REQUEST: [
                "建议", "功能", "新增", "改进", "优化", "希望", 
                "需要", "能否", "可以"
            ],
            TicketCategory.BUG_REPORT: [
                "bug", "漏洞", "错误", "异常", "故障", "问题", 
                "不正常", "有问题"
            ],
            TicketCategory.COMPLAINT: [
                "投诉", "举报", "不满", "抱怨", "差评", "意见", 
                "反馈", "问题"
            ]
        }
        
        # 优先级关键词
        self.priority_keywords = {
            TicketPriority.CRITICAL: [
                "紧急", "严重", "重要", "立即", "马上", "急", 
                "关键", "致命", "无法使用"
            ],
            TicketPriority.HIGH: [
                "高", "重要", "尽快", "优先", "着急", "需要"
            ],
            TicketPriority.MEDIUM: [
                "一般", "普通", "正常", "中等"
            ],
            TicketPriority.LOW: [
                "低", "不急", "有时间", "方便时"
            ]
        }
    
    def classify_ticket(self, title: str, description: str) -> Dict[str, Any]:
        """
        分类工单
        
        Args:
            title: 工单标题
            description: 工单描述
            
        Returns:
            Dict[str, Any]: 分类结果
        """
        try:
            # 1. 基于关键词的快速分类
            keyword_result = self._classify_by_keywords(title, description)
            
            # 2. 使用LLM进行更精确的分类
            llm_result = self._classify_by_llm(title, description)
            
            # 3. 综合结果
            final_result = self._combine_classification_results(keyword_result, llm_result)
            
            return final_result
            
        except Exception as e:
            self.logger.error(f"Error classifying ticket: {e}")
            return {
                "category": TicketCategory.GENERAL_INQUIRY,
                "priority": TicketPriority.MEDIUM,
                "confidence": 0.5,
                "tags": []
            }
    
    def _classify_by_keywords(self, title: str, description: str) -> Dict[str, Any]:
        """基于关键词分类"""
        text = f"{title} {description}".lower()
        
        # 分类评分
        category_scores = {}
        for category, keywords in self.category_keywords.items():
            score = sum(1 for keyword in keywords if keyword in text)
            if score > 0:
                category_scores[category] = score / len(keywords)
        
        # 优先级评分
        priority_scores = {}
        for priority, keywords in self.priority_keywords.items():
            score = sum(1 for keyword in keywords if keyword in text)
            if score > 0:
                priority_scores[priority] = score / len(keywords)
        
        # 确定分类
        category = max(category_scores, key=category_scores.get) if category_scores else TicketCategory.GENERAL_INQUIRY
        priority = max(priority_scores, key=priority_scores.get) if priority_scores else TicketPriority.MEDIUM
        
        # 计算置信度
        category_confidence = category_scores.get(category, 0)
        priority_confidence = priority_scores.get(priority, 0)
        overall_confidence = (category_confidence + priority_confidence) / 2
        
        return {
            "category": category,
            "priority": priority,
            "confidence": overall_confidence,
            "tags": []
        }
    
    def _classify_by_llm(self, title: str, description: str) -> Dict[str, Any]:
        """使用LLM分类"""
        try:
            prompt = f"""
            请分析以下工单内容，进行分类和优先级判断：
            
            标题：{title}
            描述：{description}
            
            请从以下分类中选择最合适的：
            1. technical_issue - 技术问题
            2. account_problem - 账号问题
            3. job_search_help - 求职帮助
            4. application_issue - 申请问题
            5. policy_question - 政策咨询
            6. feature_request - 功能建议
            7. bug_report - 错误报告
            8. complaint - 投诉建议
            9. general_inquiry - 一般咨询
            
            优先级选择：
            1. critical - 严重紧急
            2. high - 高优先级
            3. medium - 中等优先级
            4. low - 低优先级
            
            请返回JSON格式：
            {{
                "category": "分类",
                "priority": "优先级",
                "confidence": 置信度(0-1),
                "tags": ["标签1", "标签2"],
                "reasoning": "分类理由"
            }}
            """
            
            response = self.llm_router.chat_completion(prompt, temperature=0.1)
            result = self.llm_router.extract_json(response)
            
            # 转换为枚举类型
            try:
                result["category"] = TicketCategory(result.get("category", "general_inquiry"))
                result["priority"] = TicketPriority(result.get("priority", "medium"))
            except ValueError:
                result["category"] = TicketCategory.GENERAL_INQUIRY
                result["priority"] = TicketPriority.MEDIUM
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error in LLM ticket classification: {e}")
            return {
                "category": TicketCategory.GENERAL_INQUIRY,
                "priority": TicketPriority.MEDIUM,
                "confidence": 0.5,
                "tags": []
            }
    
    def _combine_classification_results(self, keyword_result: Dict[str, Any], 
                                      llm_result: Dict[str, Any]) -> Dict[str, Any]:
        """综合分类结果"""
        # 如果LLM置信度较高，优先使用LLM结果
        if llm_result.get("confidence", 0) > 0.7:
            return llm_result
        
        # 如果关键词置信度较高，使用关键词结果
        if keyword_result.get("confidence", 0) > 0.6:
            return keyword_result
        
        # 否则使用LLM结果但降低置信度
        llm_result["confidence"] = min(llm_result.get("confidence", 0.5), 0.6)
        return llm_result


class TicketManager:
    """工单管理器"""
    
    def __init__(self):
        self.logger = logger
        self.classifier = TicketClassifier()
        self.llm_router = LLMRouter(config=LLM_CONFIG)
        
        # 工单存储（实际应该使用数据库）
        self.tickets: Dict[str, Ticket] = {}
        
        # 配置
        self.config = {
            "auto_assign_enabled": True,
            "escalation_threshold": 24,  # 24小时未响应自动升级
            "sla_response_time": {
                TicketPriority.CRITICAL: 1,  # 1小时
                TicketPriority.HIGH: 4,      # 4小时
                TicketPriority.MEDIUM: 24,   # 24小时
                TicketPriority.LOW: 72       # 72小时
            }
        }
    
    def create_ticket(self, user_id: str, title: str, description: str, 
                     attachments: List[TicketAttachment] = None) -> Ticket:
        """
        创建工单
        
        Args:
            user_id: 用户ID
            title: 工单标题
            description: 工单描述
            attachments: 附件列表
            
        Returns:
            Ticket: 创建的工单
        """
        try:
            # 生成工单ID
            ticket_id = f"TK{int(time.time())}{user_id[-4:]}"
            
            # 自动分类
            classification = self.classifier.classify_ticket(title, description)
            
            # 创建工单
            ticket = Ticket(
                ticket_id=ticket_id,
                user_id=user_id,
                title=title,
                description=description,
                category=classification["category"],
                priority=classification["priority"],
                status=TicketStatus.OPEN,
                tags=classification.get("tags", []),
                attachments=attachments or [],
                auto_categorized=True,
                auto_priority_assigned=True
            )
            
            # 存储工单
            self.tickets[ticket_id] = ticket
            
            # 自动分配
            if self.config["auto_assign_enabled"]:
                self._auto_assign_ticket(ticket)
            
            # 添加系统评论
            self._add_system_comment(ticket, f"工单已创建，分类：{classification['category'].value}，优先级：{classification['priority'].value}")
            
            self.logger.info(f"Ticket created: {ticket_id}")
            return ticket
            
        except Exception as e:
            self.logger.error(f"Error creating ticket: {e}")
            raise
    
    def update_ticket_status(self, ticket_id: str, status: TicketStatus, 
                           agent_id: str = None, comment: str = None) -> bool:
        """
        更新工单状态
        
        Args:
            ticket_id: 工单ID
            status: 新状态
            agent_id: 操作员ID
            comment: 备注
            
        Returns:
            bool: 是否成功
        """
        try:
            if ticket_id not in self.tickets:
                return False
            
            ticket = self.tickets[ticket_id]
            old_status = ticket.status
            ticket.status = status
            ticket.updated_at = datetime.now()
            
            # 更新时间戳
            if status == TicketStatus.RESOLVED:
                ticket.resolved_at = datetime.now()
                if ticket.created_at:
                    ticket.resolution_time = int((ticket.resolved_at - ticket.created_at).total_seconds())
            elif status == TicketStatus.CLOSED:
                ticket.closed_at = datetime.now()
            
            # 添加状态变更评论
            status_comment = f"工单状态从 {old_status.value} 变更为 {status.value}"
            if comment:
                status_comment += f"。备注：{comment}"
            
            self._add_system_comment(ticket, status_comment)
            
            self.logger.info(f"Ticket {ticket_id} status updated: {old_status.value} -> {status.value}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating ticket status: {e}")
            return False
    
    def assign_ticket(self, ticket_id: str, agent_id: str) -> bool:
        """
        分配工单
        
        Args:
            ticket_id: 工单ID
            agent_id: 客服ID
            
        Returns:
            bool: 是否成功
        """
        try:
            if ticket_id not in self.tickets:
                return False
            
            ticket = self.tickets[ticket_id]
            old_agent = ticket.assigned_agent_id
            ticket.assigned_agent_id = agent_id
            ticket.assigned_at = datetime.now()
            ticket.updated_at = datetime.now()
            
            # 更新状态为处理中
            if ticket.status == TicketStatus.OPEN:
                ticket.status = TicketStatus.IN_PROGRESS
            
            # 添加分配评论
            assign_comment = f"工单已分配给客服 {agent_id}"
            if old_agent:
                assign_comment = f"工单从客服 {old_agent} 重新分配给客服 {agent_id}"
            
            self._add_system_comment(ticket, assign_comment)
            
            self.logger.info(f"Ticket {ticket_id} assigned to agent {agent_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error assigning ticket: {e}")
            return False
    
    def add_comment(self, ticket_id: str, author_id: str, author_type: str, 
                   content: str, is_internal: bool = False, 
                   attachments: List[TicketAttachment] = None) -> bool:
        """
        添加工单评论
        
        Args:
            ticket_id: 工单ID
            author_id: 作者ID
            author_type: 作者类型
            content: 评论内容
            is_internal: 是否内部评论
            attachments: 附件列表
            
        Returns:
            bool: 是否成功
        """
        try:
            if ticket_id not in self.tickets:
                return False
            
            ticket = self.tickets[ticket_id]
            
            comment = TicketComment(
                comment_id=f"CM{int(time.time())}{len(ticket.comments)}",
                ticket_id=ticket_id,
                author_id=author_id,
                author_type=author_type,
                content=content,
                is_internal=is_internal,
                created_at=datetime.now(),
                attachments=attachments or []
            )
            
            ticket.comments.append(comment)
            ticket.updated_at = datetime.now()
            
            # 计算首次响应时间
            if author_type == "agent" and ticket.response_time is None:
                ticket.response_time = int((datetime.now() - ticket.created_at).total_seconds())
            
            self.logger.info(f"Comment added to ticket {ticket_id} by {author_type} {author_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error adding comment: {e}")
            return False
    
    def escalate_ticket(self, ticket_id: str, reason: str = "") -> bool:
        """
        升级工单
        
        Args:
            ticket_id: 工单ID
            reason: 升级原因
            
        Returns:
            bool: 是否成功
        """
        try:
            if ticket_id not in self.tickets:
                return False
            
            ticket = self.tickets[ticket_id]
            ticket.escalation_count += 1
            ticket.updated_at = datetime.now()
            
            # 提升优先级
            if ticket.priority == TicketPriority.LOW:
                ticket.priority = TicketPriority.MEDIUM
            elif ticket.priority == TicketPriority.MEDIUM:
                ticket.priority = TicketPriority.HIGH
            elif ticket.priority == TicketPriority.HIGH:
                ticket.priority = TicketPriority.CRITICAL
            
            # 添加升级评论
            escalation_comment = f"工单已升级（第{ticket.escalation_count}次），优先级提升为 {ticket.priority.value}"
            if reason:
                escalation_comment += f"。原因：{reason}"
            
            self._add_system_comment(ticket, escalation_comment)
            
            self.logger.info(f"Ticket {ticket_id} escalated (count: {ticket.escalation_count})")
            return True
            
        except Exception as e:
            self.logger.error(f"Error escalating ticket: {e}")
            return False
    
    def get_ticket(self, ticket_id: str) -> Optional[Ticket]:
        """获取工单"""
        return self.tickets.get(ticket_id)
    
    def get_user_tickets(self, user_id: str, status: TicketStatus = None) -> List[Ticket]:
        """获取用户工单列表"""
        user_tickets = [ticket for ticket in self.tickets.values() if ticket.user_id == user_id]
        
        if status:
            user_tickets = [ticket for ticket in user_tickets if ticket.status == status]
        
        return sorted(user_tickets, key=lambda x: x.created_at, reverse=True)
    
    def get_agent_tickets(self, agent_id: str, status: TicketStatus = None) -> List[Ticket]:
        """获取客服工单列表"""
        agent_tickets = [ticket for ticket in self.tickets.values() if ticket.assigned_agent_id == agent_id]
        
        if status:
            agent_tickets = [ticket for ticket in agent_tickets if ticket.status == status]
        
        return sorted(agent_tickets, key=lambda x: x.updated_at, reverse=True)
    
    def get_ticket_statistics(self) -> Dict[str, Any]:
        """获取工单统计"""
        try:
            total_tickets = len(self.tickets)
            
            # 状态统计
            status_counts = {}
            for status in TicketStatus:
                status_counts[status.value] = len([t for t in self.tickets.values() if t.status == status])
            
            # 优先级统计
            priority_counts = {}
            for priority in TicketPriority:
                priority_counts[priority.value] = len([t for t in self.tickets.values() if t.priority == priority])
            
            # 分类统计
            category_counts = {}
            for category in TicketCategory:
                category_counts[category.value] = len([t for t in self.tickets.values() if t.category == category])
            
            # 响应时间统计
            response_times = [t.response_time for t in self.tickets.values() if t.response_time is not None]
            avg_response_time = sum(response_times) / len(response_times) if response_times else 0
            
            # 解决时间统计
            resolution_times = [t.resolution_time for t in self.tickets.values() if t.resolution_time is not None]
            avg_resolution_time = sum(resolution_times) / len(resolution_times) if resolution_times else 0
            
            # 满意度统计
            satisfaction_ratings = [t.satisfaction_rating for t in self.tickets.values() if t.satisfaction_rating is not None]
            avg_satisfaction = sum(satisfaction_ratings) / len(satisfaction_ratings) if satisfaction_ratings else 0
            
            return {
                "total_tickets": total_tickets,
                "status_distribution": status_counts,
                "priority_distribution": priority_counts,
                "category_distribution": category_counts,
                "avg_response_time_seconds": avg_response_time,
                "avg_resolution_time_seconds": avg_resolution_time,
                "avg_satisfaction_rating": avg_satisfaction,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error getting ticket statistics: {e}")
            return {}
    
    def _auto_assign_ticket(self, ticket: Ticket):
        """自动分配工单"""
        # 简化的自动分配逻辑
        # 实际应该根据客服负载、专业领域等进行分配
        available_agents = ["agent_001", "agent_002", "agent_003"]
        
        if available_agents:
            # 简单轮询分配
            agent_id = available_agents[len(self.tickets) % len(available_agents)]
            self.assign_ticket(ticket.ticket_id, agent_id)
    
    def _add_system_comment(self, ticket: Ticket, content: str):
        """添加系统评论"""
        comment = TicketComment(
            comment_id=f"SYS{int(time.time())}{len(ticket.comments)}",
            ticket_id=ticket.ticket_id,
            author_id="system",
            author_type="system",
            content=content,
            is_internal=True,
            created_at=datetime.now()
        )
        
        ticket.comments.append(comment)


# 全局工单管理器实例
ticket_manager = TicketManager()
