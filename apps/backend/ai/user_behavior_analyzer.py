#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
用户行为分析模块
"""

import time
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import numpy as np
from sqlalchemy.orm import Session

from apps.backend.utils.logger import setup_logger
from apps.backend.api.models.interaction import BrowseHistory, Favorite, Subscription

logger = setup_logger(__name__)


@dataclass
class UserBehaviorPattern:
    """用户行为模式"""
    user_id: str
    
    # 浏览行为
    total_views: int = 0
    avg_view_duration: float = 0.0
    favorite_view_times: List[str] = None  # 偏好浏览时间段
    
    # 搜索行为
    search_frequency: float = 0.0
    common_keywords: List[str] = None
    search_refinement_rate: float = 0.0  # 搜索细化率
    
    # 申请行为
    application_rate: float = 0.0  # 申请转化率
    application_pattern: str = "conservative"  # conservative, moderate, aggressive
    
    # 偏好分析
    preferred_job_types: List[str] = None
    preferred_companies: List[str] = None
    preferred_locations: List[str] = None
    preferred_salary_range: Tuple[int, int] = (0, 0)
    
    # 活跃度分析
    daily_activity_score: float = 0.0
    weekly_activity_pattern: List[float] = None  # 一周7天的活跃度
    session_duration_avg: float = 0.0
    
    # 兴趣演化
    interest_stability: float = 0.0  # 兴趣稳定性
    interest_diversity: float = 0.0  # 兴趣多样性
    
    def __post_init__(self):
        if self.favorite_view_times is None:
            self.favorite_view_times = []
        if self.common_keywords is None:
            self.common_keywords = []
        if self.preferred_job_types is None:
            self.preferred_job_types = []
        if self.preferred_companies is None:
            self.preferred_companies = []
        if self.preferred_locations is None:
            self.preferred_locations = []
        if self.weekly_activity_pattern is None:
            self.weekly_activity_pattern = [0.0] * 7


@dataclass
class BehaviorInsight:
    """行为洞察"""
    insight_type: str  # preference, pattern, anomaly, trend
    title: str
    description: str
    confidence: float
    actionable_suggestions: List[str]
    data_points: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.data_points is None:
            self.data_points = {}


class UserBehaviorAnalyzer:
    """用户行为分析器"""
    
    def __init__(self):
        self.logger = logger
        
        # 行为权重配置
        self.behavior_weights = {
            "view": 1.0,
            "favorite": 3.0,
            "apply": 5.0,
            "search": 2.0,
            "share": 2.5
        }
        
        # 时间窗口配置
        self.analysis_windows = {
            "short_term": 7,    # 7天
            "medium_term": 30,  # 30天
            "long_term": 90     # 90天
        }
    
    def analyze_user_behavior(self, user_id: str, db: Session, 
                            analysis_period: int = 30) -> UserBehaviorPattern:
        """分析用户行为模式"""
        try:
            self.logger.info(f"Analyzing behavior for user {user_id}")
            
            # 获取用户行为数据
            behavior_data = self._collect_user_behavior_data(user_id, db, analysis_period)
            
            # 分析各种行为模式
            pattern = UserBehaviorPattern(user_id=user_id)
            
            # 浏览行为分析
            self._analyze_browsing_behavior(pattern, behavior_data)
            
            # 搜索行为分析
            self._analyze_search_behavior(pattern, behavior_data)
            
            # 申请行为分析
            self._analyze_application_behavior(pattern, behavior_data)
            
            # 偏好分析
            self._analyze_preferences(pattern, behavior_data)
            
            # 活跃度分析
            self._analyze_activity_patterns(pattern, behavior_data)
            
            # 兴趣演化分析
            self._analyze_interest_evolution(pattern, behavior_data)
            
            return pattern
            
        except Exception as e:
            self.logger.error(f"Error analyzing user behavior: {e}")
            return UserBehaviorPattern(user_id=user_id)
    
    def generate_behavior_insights(self, pattern: UserBehaviorPattern) -> List[BehaviorInsight]:
        """生成行为洞察"""
        insights = []
        
        try:
            # 偏好洞察
            insights.extend(self._generate_preference_insights(pattern))
            
            # 行为模式洞察
            insights.extend(self._generate_pattern_insights(pattern))
            
            # 异常检测洞察
            insights.extend(self._generate_anomaly_insights(pattern))
            
            # 趋势洞察
            insights.extend(self._generate_trend_insights(pattern))
            
            # 按置信度排序
            insights.sort(key=lambda x: x.confidence, reverse=True)
            
            return insights
            
        except Exception as e:
            self.logger.error(f"Error generating behavior insights: {e}")
            return []
    
    def predict_user_intent(self, pattern: UserBehaviorPattern) -> Dict[str, Any]:
        """预测用户意图 - 增强版"""
        try:
            intent_scores = {
                "job_seeking": 0.0,
                "career_exploration": 0.0,
                "skill_development": 0.0,
                "market_research": 0.0,
                "passive_browsing": 0.0,
                "urgent_job_hunting": 0.0
            }

            # 1. 基于申请率和活跃度的综合判断
            if pattern.application_rate > 0.15 and pattern.daily_activity_score > 5:
                intent_scores["urgent_job_hunting"] = min(
                    (pattern.application_rate * 3 + pattern.daily_activity_score / 10), 1.0
                )
            elif pattern.application_rate > 0.05:
                intent_scores["job_seeking"] = min(pattern.application_rate * 8, 1.0)

            # 2. 基于浏览模式判断探索意图
            if pattern.interest_diversity > 0.6 and pattern.application_rate < 0.1:
                intent_scores["career_exploration"] = pattern.interest_diversity

            # 3. 基于搜索关键词和行为模式判断技能发展意图
            skill_keywords = ["技能", "培训", "学习", "课程", "证书", "资格", "考试"]
            skill_mentions = sum(1 for keyword in pattern.common_keywords
                               if any(sk in keyword for sk in skill_keywords))
            if skill_mentions > 0:
                base_score = min(skill_mentions / 5, 1.0)
                # 如果浏览时间长但申请少，更可能是技能发展
                if pattern.avg_view_duration > 300 and pattern.application_rate < 0.05:
                    base_score *= 1.5
                intent_scores["skill_development"] = min(base_score, 1.0)

            # 4. 基于浏览频率和深度判断市场研究意图
            if (pattern.total_views > 100 and pattern.application_rate < 0.03 and
                pattern.avg_view_duration > 180):
                research_score = min(pattern.total_views / 500, 1.0)
                # 如果浏览很多但很少收藏，更可能是市场研究
                if len(pattern.preferred_companies) < 3:
                    research_score *= 1.2
                intent_scores["market_research"] = min(research_score, 1.0)

            # 5. 基于低活跃度判断被动浏览
            if (pattern.daily_activity_score < 2 and pattern.application_rate < 0.02 and
                pattern.avg_view_duration < 120):
                intent_scores["passive_browsing"] = 0.7

            # 6. 时间模式分析增强意图判断
            intent_scores = self._enhance_intent_with_temporal_patterns(
                intent_scores, pattern
            )

            # 确定主要意图和次要意图
            sorted_intents = sorted(intent_scores.items(), key=lambda x: x[1], reverse=True)
            primary_intent = sorted_intents[0][0]
            primary_confidence = sorted_intents[0][1]

            secondary_intent = None
            secondary_confidence = 0.0
            if len(sorted_intents) > 1 and sorted_intents[1][1] > 0.3:
                secondary_intent = sorted_intents[1][0]
                secondary_confidence = sorted_intents[1][1]

            return {
                "primary_intent": primary_intent,
                "primary_confidence": primary_confidence,
                "secondary_intent": secondary_intent,
                "secondary_confidence": secondary_confidence,
                "intent_scores": intent_scores,
                "intent_evolution": self._analyze_intent_evolution(pattern),
                "recommendations": self._generate_enhanced_intent_recommendations(
                    primary_intent, primary_confidence, secondary_intent
                )
            }

        except Exception as e:
            self.logger.error(f"Error predicting user intent: {e}")
            return {"primary_intent": "job_seeking", "primary_confidence": 0.5, "intent_scores": {}}
    
    def _collect_user_behavior_data(self, user_id: str, db: Session, 
                                  days: int) -> Dict[str, Any]:
        """收集用户行为数据"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        behavior_data = {
            "browse_history": [],
            "favorites": [],
            "applications": [],
            "searches": [],
            "time_range": (start_date, end_date)
        }
        
        try:
            # 浏览历史
            browse_records = db.query(BrowseHistory).filter(
                BrowseHistory.user_id == user_id,
                BrowseHistory.created_at >= start_date
            ).all()
            
            behavior_data["browse_history"] = [
                {
                    "job_id": record.job_id,
                    "timestamp": record.created_at,
                    "duration": getattr(record, 'duration', 0),
                    "source": getattr(record, 'source', 'unknown')
                }
                for record in browse_records
            ]
            
            # 收藏记录
            favorite_records = db.query(Favorite).filter(
                Favorite.user_id == user_id,
                Favorite.created_at >= start_date
            ).all()
            
            behavior_data["favorites"] = [
                {
                    "job_id": record.job_id,
                    "timestamp": record.created_at
                }
                for record in favorite_records
            ]
            
            # 订阅记录（可以作为兴趣指标）
            subscription_records = db.query(Subscription).filter(
                Subscription.user_id == user_id,
                Subscription.created_at >= start_date
            ).all()
            
            behavior_data["subscriptions"] = [
                {
                    "keywords": record.keywords,
                    "location": record.location,
                    "timestamp": record.created_at
                }
                for record in subscription_records
            ]
            
        except Exception as e:
            self.logger.error(f"Error collecting behavior data: {e}")
        
        return behavior_data
    
    def _analyze_browsing_behavior(self, pattern: UserBehaviorPattern, 
                                 behavior_data: Dict[str, Any]):
        """分析浏览行为"""
        browse_history = behavior_data.get("browse_history", [])
        
        if not browse_history:
            return
        
        # 总浏览量
        pattern.total_views = len(browse_history)
        
        # 平均浏览时长
        durations = [record.get("duration", 0) for record in browse_history]
        pattern.avg_view_duration = np.mean(durations) if durations else 0.0
        
        # 偏好浏览时间段
        hours = [record["timestamp"].hour for record in browse_history]
        hour_counter = Counter(hours)
        
        # 找出最活跃的时间段
        if hour_counter:
            most_common_hours = hour_counter.most_common(3)
            pattern.favorite_view_times = [
                self._hour_to_time_period(hour) for hour, _ in most_common_hours
            ]
        
        # 会话时长分析
        sessions = self._group_into_sessions(browse_history)
        session_durations = [session["duration"] for session in sessions]
        pattern.session_duration_avg = np.mean(session_durations) if session_durations else 0.0
    
    def _analyze_search_behavior(self, pattern: UserBehaviorPattern,
                               behavior_data: Dict[str, Any]):
        """分析搜索行为"""
        # 这里需要实际的搜索日志数据
        # 暂时使用订阅数据作为搜索意图的代理
        subscriptions = behavior_data.get("subscriptions", [])
        
        if subscriptions:
            # 搜索频率
            pattern.search_frequency = len(subscriptions) / 30  # 每天平均搜索次数
            
            # 常用关键词
            all_keywords = []
            for sub in subscriptions:
                if sub.get("keywords"):
                    all_keywords.extend(sub["keywords"].split(","))
            
            keyword_counter = Counter([kw.strip() for kw in all_keywords if kw.strip()])
            pattern.common_keywords = [kw for kw, _ in keyword_counter.most_common(10)]
    
    def _analyze_application_behavior(self, pattern: UserBehaviorPattern,
                                    behavior_data: Dict[str, Any]):
        """分析申请行为"""
        browse_count = len(behavior_data.get("browse_history", []))
        # 这里需要实际的申请数据，暂时使用收藏作为申请意图的代理
        favorite_count = len(behavior_data.get("favorites", []))
        
        if browse_count > 0:
            pattern.application_rate = favorite_count / browse_count
            
            # 申请模式分类
            if pattern.application_rate > 0.2:
                pattern.application_pattern = "aggressive"
            elif pattern.application_rate > 0.1:
                pattern.application_pattern = "moderate"
            else:
                pattern.application_pattern = "conservative"
    
    def _analyze_preferences(self, pattern: UserBehaviorPattern,
                           behavior_data: Dict[str, Any]):
        """分析用户偏好"""
        # 这里需要结合岗位数据来分析偏好
        # 暂时基于订阅数据分析
        subscriptions = behavior_data.get("subscriptions", [])
        
        if subscriptions:
            # 偏好地点
            locations = [sub.get("location", "") for sub in subscriptions if sub.get("location")]
            location_counter = Counter(locations)
            pattern.preferred_locations = [loc for loc, _ in location_counter.most_common(5)]
    
    def _analyze_activity_patterns(self, pattern: UserBehaviorPattern,
                                 behavior_data: Dict[str, Any]):
        """分析活跃度模式"""
        browse_history = behavior_data.get("browse_history", [])
        
        if not browse_history:
            return
        
        # 日活跃度分数
        total_days = (behavior_data["time_range"][1] - behavior_data["time_range"][0]).days
        pattern.daily_activity_score = len(browse_history) / max(total_days, 1)
        
        # 周活跃度模式
        weekday_counts = [0] * 7
        for record in browse_history:
            weekday = record["timestamp"].weekday()
            weekday_counts[weekday] += 1
        
        max_count = max(weekday_counts) if weekday_counts else 1
        pattern.weekly_activity_pattern = [count / max_count for count in weekday_counts]
    
    def _analyze_interest_evolution(self, pattern: UserBehaviorPattern,
                                  behavior_data: Dict[str, Any]):
        """分析兴趣演化"""
        # 简化的兴趣稳定性和多样性分析
        subscriptions = behavior_data.get("subscriptions", [])
        
        if len(subscriptions) > 1:
            # 兴趣多样性：基于关键词的多样性
            all_keywords = set()
            for sub in subscriptions:
                if sub.get("keywords"):
                    keywords = [kw.strip() for kw in sub["keywords"].split(",")]
                    all_keywords.update(keywords)
            
            pattern.interest_diversity = min(len(all_keywords) / 20, 1.0)
            
            # 兴趣稳定性：基于时间序列的一致性
            # 这里需要更复杂的时间序列分析
            pattern.interest_stability = 0.7  # 暂时设为固定值
    
    def _generate_preference_insights(self, pattern: UserBehaviorPattern) -> List[BehaviorInsight]:
        """生成偏好洞察"""
        insights = []
        
        # 地点偏好洞察
        if pattern.preferred_locations:
            insights.append(BehaviorInsight(
                insight_type="preference",
                title="地点偏好明确",
                description=f"您主要关注 {', '.join(pattern.preferred_locations[:3])} 等地区的岗位",
                confidence=0.8,
                actionable_suggestions=[
                    f"为您优先推荐 {pattern.preferred_locations[0]} 地区的岗位",
                    "设置地点提醒，第一时间获取相关岗位信息"
                ],
                data_points={"preferred_locations": pattern.preferred_locations}
            ))
        
        # 活跃时间洞察
        if pattern.favorite_view_times:
            insights.append(BehaviorInsight(
                insight_type="preference",
                title="浏览时间偏好",
                description=f"您通常在 {', '.join(pattern.favorite_view_times)} 时段浏览岗位",
                confidence=0.7,
                actionable_suggestions=[
                    "在您活跃的时间段推送新岗位通知",
                    "建议在这些时段进行深度求职规划"
                ],
                data_points={"favorite_times": pattern.favorite_view_times}
            ))
        
        return insights
    
    def _generate_pattern_insights(self, pattern: UserBehaviorPattern) -> List[BehaviorInsight]:
        """生成行为模式洞察"""
        insights = []
        
        # 申请模式洞察
        if pattern.application_pattern == "conservative":
            insights.append(BehaviorInsight(
                insight_type="pattern",
                title="谨慎型求职者",
                description="您倾向于仔细筛选后再申请岗位，申请转化率较低",
                confidence=0.8,
                actionable_suggestions=[
                    "为您提供更详细的岗位匹配分析",
                    "推荐高匹配度的精选岗位",
                    "提供岗位申请成功率预测"
                ],
                data_points={"application_rate": pattern.application_rate}
            ))
        elif pattern.application_pattern == "aggressive":
            insights.append(BehaviorInsight(
                insight_type="pattern",
                title="积极型求职者",
                description="您申请岗位较为积极，转化率较高",
                confidence=0.8,
                actionable_suggestions=[
                    "为您推荐更多符合条件的岗位",
                    "提供申请进度跟踪功能",
                    "推荐面试技巧和准备资料"
                ],
                data_points={"application_rate": pattern.application_rate}
            ))
        
        return insights
    
    def _generate_anomaly_insights(self, pattern: UserBehaviorPattern) -> List[BehaviorInsight]:
        """生成异常检测洞察"""
        insights = []
        
        # 活跃度异常
        if pattern.daily_activity_score > 10:  # 每天浏览超过10个岗位
            insights.append(BehaviorInsight(
                insight_type="anomaly",
                title="高频浏览行为",
                description="您的岗位浏览频率较高，可能处于密集求职期",
                confidence=0.7,
                actionable_suggestions=[
                    "建议制定求职计划，避免信息过载",
                    "使用筛选功能精准定位目标岗位",
                    "考虑设置求职目标和优先级"
                ],
                data_points={"daily_activity": pattern.daily_activity_score}
            ))
        
        return insights
    
    def _generate_trend_insights(self, pattern: UserBehaviorPattern) -> List[BehaviorInsight]:
        """生成趋势洞察"""
        insights = []
        
        # 兴趣多样性趋势
        if pattern.interest_diversity > 0.7:
            insights.append(BehaviorInsight(
                insight_type="trend",
                title="兴趣多元化趋势",
                description="您的求职兴趣较为多样化，涉及多个领域",
                confidence=0.6,
                actionable_suggestions=[
                    "为您推荐跨领域的复合型岗位",
                    "提供职业发展路径规划建议",
                    "推荐相关技能提升课程"
                ],
                data_points={"interest_diversity": pattern.interest_diversity}
            ))
        
        return insights
    
    def _generate_intent_based_recommendations(self, intent: str, confidence: float) -> List[str]:
        """基于意图生成推荐"""
        recommendations = []
        
        if intent == "job_seeking" and confidence > 0.6:
            recommendations.extend([
                "为您推荐高匹配度的岗位",
                "提供简历优化建议",
                "推荐面试技巧和准备资料",
                "设置岗位申请提醒"
            ])
        elif intent == "career_exploration":
            recommendations.extend([
                "为您推荐不同类型的岗位进行探索",
                "提供行业发展趋势分析",
                "推荐职业规划咨询服务",
                "提供技能评估和发展建议"
            ])
        elif intent == "skill_development":
            recommendations.extend([
                "推荐相关技能培训课程",
                "提供技能发展路径规划",
                "推荐实践项目和案例",
                "连接行业专家和导师"
            ])
        elif intent == "market_research":
            recommendations.extend([
                "提供行业薪资报告",
                "推荐市场趋势分析",
                "提供竞争对手分析",
                "推荐行业报告和白皮书"
            ])
        
        return recommendations
    
    def _hour_to_time_period(self, hour: int) -> str:
        """将小时转换为时间段"""
        if 6 <= hour < 12:
            return "上午"
        elif 12 <= hour < 18:
            return "下午"
        elif 18 <= hour < 22:
            return "晚上"
        else:
            return "深夜"
    
    def _group_into_sessions(self, browse_history: List[Dict]) -> List[Dict]:
        """将浏览记录分组为会话"""
        if not browse_history:
            return []
        
        sessions = []
        current_session = {"start": None, "end": None, "duration": 0, "views": 0}
        
        sorted_history = sorted(browse_history, key=lambda x: x["timestamp"])
        
        for i, record in enumerate(sorted_history):
            if current_session["start"] is None:
                current_session["start"] = record["timestamp"]
                current_session["end"] = record["timestamp"]
                current_session["views"] = 1
            else:
                time_gap = (record["timestamp"] - current_session["end"]).total_seconds()
                
                if time_gap > 1800:  # 30分钟间隔认为是新会话
                    current_session["duration"] = (current_session["end"] - current_session["start"]).total_seconds()
                    sessions.append(current_session.copy())
                    
                    current_session = {
                        "start": record["timestamp"],
                        "end": record["timestamp"],
                        "duration": 0,
                        "views": 1
                    }
                else:
                    current_session["end"] = record["timestamp"]
                    current_session["views"] += 1
        
        # 添加最后一个会话
        if current_session["start"] is not None:
            current_session["duration"] = (current_session["end"] - current_session["start"]).total_seconds()
            sessions.append(current_session)
        
        return sessions

    def _enhance_intent_with_temporal_patterns(self, intent_scores: Dict[str, float],
                                             pattern: UserBehaviorPattern) -> Dict[str, float]:
        """基于时间模式增强意图判断"""
        try:
            # 分析周活跃度模式
            if pattern.weekly_activity_pattern:
                weekday_activity = sum(pattern.weekly_activity_pattern[:5])  # 工作日
                weekend_activity = sum(pattern.weekly_activity_pattern[5:])   # 周末

                # 如果主要在工作日活跃，可能是在职求职
                if weekday_activity > weekend_activity * 2:
                    intent_scores["job_seeking"] *= 1.2
                    intent_scores["market_research"] *= 1.1

                # 如果主要在周末活跃，可能是认真求职或技能发展
                elif weekend_activity > weekday_activity:
                    intent_scores["urgent_job_hunting"] *= 1.3
                    intent_scores["skill_development"] *= 1.2

            # 分析偏好浏览时间
            if pattern.favorite_view_times:
                # 如果主要在深夜浏览，可能是紧急求职
                if "深夜" in pattern.favorite_view_times:
                    intent_scores["urgent_job_hunting"] *= 1.4

                # 如果主要在上午浏览，可能是有计划的求职
                if "上午" in pattern.favorite_view_times:
                    intent_scores["job_seeking"] *= 1.2
                    intent_scores["career_exploration"] *= 1.1

            return intent_scores

        except Exception as e:
            self.logger.error(f"时间模式分析失败: {e}")
            return intent_scores

    def _analyze_intent_evolution(self, pattern: UserBehaviorPattern) -> Dict[str, Any]:
        """分析意图演化趋势"""
        try:
            evolution = {
                "trend": "stable",  # stable, increasing, decreasing
                "phase": "exploration",  # exploration, evaluation, decision
                "urgency_level": "normal",  # low, normal, high, urgent
                "commitment_level": "medium"  # low, medium, high
            }

            # 基于申请率变化判断趋势
            if pattern.application_rate > 0.2:
                evolution["trend"] = "increasing"
                evolution["phase"] = "decision"
                evolution["urgency_level"] = "high"
            elif pattern.application_rate > 0.1:
                evolution["phase"] = "evaluation"
                evolution["urgency_level"] = "normal"
            else:
                evolution["phase"] = "exploration"
                evolution["urgency_level"] = "low"

            # 基于活跃度判断紧急程度
            if pattern.daily_activity_score > 8:
                evolution["urgency_level"] = "urgent"
            elif pattern.daily_activity_score > 5:
                evolution["urgency_level"] = "high"

            # 基于兴趣稳定性判断承诺度
            if pattern.interest_stability > 0.8:
                evolution["commitment_level"] = "high"
            elif pattern.interest_stability > 0.5:
                evolution["commitment_level"] = "medium"
            else:
                evolution["commitment_level"] = "low"

            return evolution

        except Exception as e:
            self.logger.error(f"意图演化分析失败: {e}")
            return {"trend": "stable", "phase": "exploration", "urgency_level": "normal"}

    def _generate_enhanced_intent_recommendations(self, primary_intent: str,
                                                primary_confidence: float,
                                                secondary_intent: Optional[str]) -> List[str]:
        """生成增强的意图推荐"""
        recommendations = []

        try:
            # 基于主要意图的推荐
            if primary_intent == "urgent_job_hunting" and primary_confidence > 0.7:
                recommendations.extend([
                    "🚀 为您优先推荐高匹配度岗位，加快求职进度",
                    "📝 提供快速简历优化服务",
                    "⚡ 开启岗位即时通知，第一时间获取新机会",
                    "🎯 推荐一键投递功能，提高申请效率",
                    "📞 提供紧急面试辅导服务"
                ])
            elif primary_intent == "job_seeking" and primary_confidence > 0.6:
                recommendations.extend([
                    "🎯 为您推荐精准匹配的岗位",
                    "📊 提供个人竞争力分析报告",
                    "💼 推荐简历和求职信优化服务",
                    "📈 设置求职进度跟踪",
                    "🤝 推荐行业人脉拓展机会"
                ])
            elif primary_intent == "career_exploration":
                recommendations.extend([
                    "🔍 为您推荐多元化的职业选择",
                    "📚 提供行业发展趋势分析",
                    "🗺️ 推荐职业发展路径规划",
                    "💡 提供转行指导和建议",
                    "🎓 推荐相关技能提升课程"
                ])
            elif primary_intent == "skill_development":
                recommendations.extend([
                    "📖 推荐个性化技能学习计划",
                    "🏆 提供技能认证和考试指导",
                    "💻 推荐实战项目和练习机会",
                    "👨‍🏫 连接行业导师和专家",
                    "📊 提供技能市场价值分析"
                ])
            elif primary_intent == "market_research":
                recommendations.extend([
                    "📈 提供详细的行业薪资报告",
                    "🏢 推荐公司文化和发展前景分析",
                    "📊 提供市场趋势和就业数据",
                    "🔍 推荐竞争对手和标杆企业分析",
                    "📋 提供行业白皮书和研究报告"
                ])
            elif primary_intent == "passive_browsing":
                recommendations.extend([
                    "🌟 为您推荐可能感兴趣的优质岗位",
                    "📱 设置轻量级的岗位推送通知",
                    "💡 提供职业发展灵感和建议",
                    "🎯 推荐个性化的职业测评",
                    "📚 分享行业动态和职场资讯"
                ])

            # 基于次要意图的补充推荐
            if secondary_intent and secondary_intent != primary_intent:
                if secondary_intent == "skill_development":
                    recommendations.append("📚 同时为您推荐技能提升机会")
                elif secondary_intent == "career_exploration":
                    recommendations.append("🔍 为您提供更多职业探索选项")
                elif secondary_intent == "market_research":
                    recommendations.append("📊 提供相关市场信息和数据")

            # 通用推荐
            recommendations.extend([
                "🤖 使用AI智能匹配，提高求职成功率",
                "📱 下载移动应用，随时随地求职",
                "🔔 设置个性化提醒，不错过任何机会"
            ])

            return recommendations[:8]  # 限制推荐数量

        except Exception as e:
            self.logger.error(f"生成增强推荐失败: {e}")
            return ["为您推荐个性化的求职服务"]

    def calculate_user_similarity(self, user1_pattern: UserBehaviorPattern,
                                user2_pattern: UserBehaviorPattern) -> float:
        """计算用户相似度"""
        try:
            similarity_score = 0.0

            # 1. 偏好相似度 (30%)
            preference_similarity = self._calculate_preference_similarity(
                user1_pattern, user2_pattern
            )
            similarity_score += preference_similarity * 0.3

            # 2. 行为模式相似度 (25%)
            behavior_similarity = self._calculate_behavior_similarity(
                user1_pattern, user2_pattern
            )
            similarity_score += behavior_similarity * 0.25

            # 3. 活跃度相似度 (20%)
            activity_similarity = self._calculate_activity_similarity(
                user1_pattern, user2_pattern
            )
            similarity_score += activity_similarity * 0.2

            # 4. 兴趣相似度 (15%)
            interest_similarity = self._calculate_interest_similarity(
                user1_pattern, user2_pattern
            )
            similarity_score += interest_similarity * 0.15

            # 5. 时间模式相似度 (10%)
            temporal_similarity = self._calculate_temporal_similarity(
                user1_pattern, user2_pattern
            )
            similarity_score += temporal_similarity * 0.1

            return min(similarity_score, 1.0)

        except Exception as e:
            self.logger.error(f"计算用户相似度失败: {e}")
            return 0.0

    def _calculate_preference_similarity(self, user1: UserBehaviorPattern,
                                       user2: UserBehaviorPattern) -> float:
        """计算偏好相似度"""
        similarity = 0.0

        # 地点偏好相似度
        if user1.preferred_locations and user2.preferred_locations:
            common_locations = set(user1.preferred_locations) & set(user2.preferred_locations)
            total_locations = set(user1.preferred_locations) | set(user2.preferred_locations)
            if total_locations:
                similarity += (len(common_locations) / len(total_locations)) * 0.4

        # 公司偏好相似度
        if user1.preferred_companies and user2.preferred_companies:
            common_companies = set(user1.preferred_companies) & set(user2.preferred_companies)
            total_companies = set(user1.preferred_companies) | set(user2.preferred_companies)
            if total_companies:
                similarity += (len(common_companies) / len(total_companies)) * 0.3

        # 岗位类型偏好相似度
        if user1.preferred_job_types and user2.preferred_job_types:
            common_types = set(user1.preferred_job_types) & set(user2.preferred_job_types)
            total_types = set(user1.preferred_job_types) | set(user2.preferred_job_types)
            if total_types:
                similarity += (len(common_types) / len(total_types)) * 0.3

        return similarity

    def _calculate_behavior_similarity(self, user1: UserBehaviorPattern,
                                     user2: UserBehaviorPattern) -> float:
        """计算行为模式相似度"""
        similarity = 0.0

        # 申请模式相似度
        if user1.application_pattern == user2.application_pattern:
            similarity += 0.4

        # 申请率相似度
        rate_diff = abs(user1.application_rate - user2.application_rate)
        rate_similarity = max(0, 1 - rate_diff * 2)  # 差异越小相似度越高
        similarity += rate_similarity * 0.3

        # 搜索频率相似度
        freq_diff = abs(user1.search_frequency - user2.search_frequency)
        freq_similarity = max(0, 1 - freq_diff)
        similarity += freq_similarity * 0.3

        return similarity

    def _calculate_activity_similarity(self, user1: UserBehaviorPattern,
                                     user2: UserBehaviorPattern) -> float:
        """计算活跃度相似度"""
        # 日活跃度相似度
        activity_diff = abs(user1.daily_activity_score - user2.daily_activity_score)
        activity_similarity = max(0, 1 - activity_diff / 10)

        # 会话时长相似度
        session_diff = abs(user1.session_duration_avg - user2.session_duration_avg)
        session_similarity = max(0, 1 - session_diff / 3600)  # 以1小时为基准

        return (activity_similarity + session_similarity) / 2

    def _calculate_interest_similarity(self, user1: UserBehaviorPattern,
                                     user2: UserBehaviorPattern) -> float:
        """计算兴趣相似度"""
        # 兴趣多样性相似度
        diversity_diff = abs(user1.interest_diversity - user2.interest_diversity)
        diversity_similarity = max(0, 1 - diversity_diff)

        # 兴趣稳定性相似度
        stability_diff = abs(user1.interest_stability - user2.interest_stability)
        stability_similarity = max(0, 1 - stability_diff)

        return (diversity_similarity + stability_similarity) / 2

    def _calculate_temporal_similarity(self, user1: UserBehaviorPattern,
                                     user2: UserBehaviorPattern) -> float:
        """计算时间模式相似度"""
        similarity = 0.0

        # 偏好浏览时间相似度
        if user1.favorite_view_times and user2.favorite_view_times:
            common_times = set(user1.favorite_view_times) & set(user2.favorite_view_times)
            total_times = set(user1.favorite_view_times) | set(user2.favorite_view_times)
            if total_times:
                similarity += (len(common_times) / len(total_times)) * 0.5

        # 周活跃度模式相似度
        if (user1.weekly_activity_pattern and user2.weekly_activity_pattern and
            len(user1.weekly_activity_pattern) == len(user2.weekly_activity_pattern)):
            pattern_similarity = 1 - np.mean([
                abs(a - b) for a, b in zip(user1.weekly_activity_pattern, user2.weekly_activity_pattern)
            ])
            similarity += pattern_similarity * 0.5

        return similarity


# 全局用户行为分析器实例
user_behavior_analyzer = UserBehaviorAnalyzer()
