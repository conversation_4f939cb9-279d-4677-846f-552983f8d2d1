#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
分类工具类
提供与内容分类器相关的工具函数
"""

import os
import logging
from typing import Dict, List, Any, Optional, Union, cast

from ..content_classifier import ContentClassifier
from ...models.base_job_model import BaseJobModel
from ...models.job_info import JobInfo
from ...models.institution_job import InstitutionJob
from ...models.graduate_job import GraduateJob


class ClassificationUtils:
    """分类工具类"""

    def __init__(self, classifier: Optional[ContentClassifier] = None, logger: Optional[logging.Logger] = None):
        """
        初始化分类工具类

        Args:
            classifier: 内容分类器，如果为None则创建新的分类器
            logger: 日志记录器
        """
        self.logger = logger or logging.getLogger(__name__)
        self.classifier = classifier or ContentClassifier(logger=self.logger)

    def classify_job_model(self, job: Union[BaseJobModel, Dict[str, Any]]) -> Union[BaseJobModel, Dict[str, Any]]:
        """
        对岗位模型进行分类

        Args:
            job: 岗位模型或岗位信息字典

        Returns:
            Union[BaseJobModel, Dict[str, Any]]: 带有分类结果的岗位模型或字典
        """
        # 如果是字典，直接分类
        if isinstance(job, dict):
            job_dict = job
            is_dict = True
        else:
            # 如果是模型，转换为字典
            job_dict = self.get_job_dict_from_model(job)
            is_dict = False

        # 对岗位进行分类
        classification = self.classifier.classify_job(job_dict)

        # 将分类结果添加到岗位信息中
        job_dict.update({
            "industry_category": classification.get("industry", ""),
            "function_category": classification.get("function", ""),
            "level_category": classification.get("level", ""),
            "job_type_category": classification.get("job_type", ""),
            "education_category": classification.get("education", ""),
            "tags": classification.get("tags", [])
        })

        # 如果原始输入是字典，返回字典
        if is_dict:
            return job_dict

        # 如果原始输入是模型，更新模型并返回
        # 添加分类属性并更新值
        setattr(job, "industry_category", job_dict["industry_category"])
        setattr(job, "function_category", job_dict["function_category"])
        setattr(job, "level_category", job_dict["level_category"])
        setattr(job, "job_type_category", job_dict["job_type_category"])
        setattr(job, "education_category", job_dict["education_category"])
        setattr(job, "tags", job_dict["tags"])

        return job

    def batch_classify_job_models(self, jobs: List[Union[BaseJobModel, Dict[str, Any]]]) -> List[Union[BaseJobModel, Dict[str, Any]]]:
        """
        批量对岗位模型进行分类

        Args:
            jobs: 岗位模型或岗位信息字典列表

        Returns:
            List[Union[BaseJobModel, Dict[str, Any]]]: 带有分类结果的岗位模型或字典列表
        """
        classified_jobs = []

        for job in jobs:
            classified_job = self.classify_job_model(job)
            classified_jobs.append(classified_job)

        return classified_jobs

    def get_job_dict_from_model(self, job: BaseJobModel) -> Dict[str, Any]:
        """
        从岗位模型获取岗位信息字典

        Args:
            job: 岗位模型

        Returns:
            Dict[str, Any]: 岗位信息字典
        """
        # 基本信息
        job_dict: Dict[str, Any] = {
            "job_id": job.job_id,
            "job_title": job.job_title,
            "company_name": job.company_name,
            "salary_range": job.salary_range,
            "work_location": job.work_location,
            "education_required": job.education_required,
            "job_description": job.job_description,
            "company_description": job.company_description,
            "source_url": job.source_url,
            "data_source": job.data_source
        }

        # 根据不同类型的岗位模型添加特定字段
        if isinstance(job, JobInfo):
            job_dict["job_type"] = job.job_type
            job_dict["industry"] = job.industry
            job_dict["company_type"] = job.company_type
            job_dict["experience_requirement"] = job.experience_requirement
            job_dict["is_graduate_friendly"] = job.is_graduate_friendly
            job_dict["intern_available"] = job.intern_available
        elif isinstance(job, InstitutionJob):
            job_dict["institution_name"] = job.institution_name
            job_dict["department"] = job.department
            job_dict["recruitment_number"] = job.recruitment_number
            job_dict["qualification_requirement"] = job.qualification_requirement
            job_dict["major_requirement"] = job.major_requirement
            job_dict["other_requirement"] = job.other_requirement
            job_dict["is_graduate_friendly"] = job.is_graduate_friendly
        elif isinstance(job, GraduateJob):
            job_dict["publish_organization"] = job.publish_organization
            job_dict["major_required"] = job.major_required
            job_dict["headcount"] = job.headcount
            job_dict["experience_required"] = job.experience_required
            job_dict["is_for_graduates"] = job.is_for_graduates

        return job_dict

    def update_model_from_dict(self, job: BaseJobModel, job_dict: Dict[str, Any]) -> BaseJobModel:
        """
        从字典更新岗位模型

        Args:
            job: 岗位模型
            job_dict: 岗位信息字典

        Returns:
            BaseJobModel: 更新后的岗位模型
        """
        for key, value in job_dict.items():
            if hasattr(job, key):
                setattr(job, key, value)
        return job

    def get_industry_categories(self) -> List[str]:
        """
        获取所有行业分类

        Returns:
            List[str]: 行业分类列表
        """
        return list(self.classifier.INDUSTRY_KEYWORDS.keys())

    def get_function_categories(self) -> List[str]:
        """
        获取所有职能分类

        Returns:
            List[str]: 职能分类列表
        """
        return list(self.classifier.FUNCTION_KEYWORDS.keys())

    def get_level_categories(self) -> List[str]:
        """
        获取所有级别分类

        Returns:
            List[str]: 级别分类列表
        """
        return list(self.classifier.LEVEL_KEYWORDS.keys())

    def get_job_type_categories(self) -> List[str]:
        """
        获取所有工作类型分类

        Returns:
            List[str]: 工作类型分类列表
        """
        return list(self.classifier.JOB_TYPE_KEYWORDS.keys())

    def get_education_categories(self) -> List[str]:
        """
        获取所有学历要求分类

        Returns:
            List[str]: 学历要求分类列表
        """
        return list(self.classifier.EDUCATION_KEYWORDS.keys())
