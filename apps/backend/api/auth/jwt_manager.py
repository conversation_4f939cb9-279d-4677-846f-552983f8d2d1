"""
JWT认证管理器
处理JWT令牌的生成、验证和管理
"""
import jwt
import time
import secrets
import hashlib
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
import redis
import logging

logger = logging.getLogger(__name__)

class JWTManager:
    """JWT管理器"""
    
    def __init__(self, secret_key: str, redis_client: Optional[redis.Redis] = None):
        self.secret_key = secret_key
        self.algorithm = 'HS256'
        self.redis_client = redis_client
        
        # 令牌配置
        self.access_token_expire_minutes = 30  # 访问令牌30分钟
        self.refresh_token_expire_days = 7     # 刷新令牌7天
        self.remember_me_expire_days = 30      # 记住我30天
        
        # 令牌类型
        self.TOKEN_TYPE_ACCESS = 'access'
        self.TOKEN_TYPE_REFRESH = 'refresh'
    
    def generate_access_token(self, user_data: Dict[str, Any], remember_me: bool = False) -> str:
        """生成访问令牌"""
        now = datetime.utcnow()
        
        # 设置过期时间
        if remember_me:
            expire = now + timedelta(days=self.remember_me_expire_days)
        else:
            expire = now + timedelta(minutes=self.access_token_expire_minutes)
        
        # 构建载荷
        payload = {
            'user_id': user_data['user_id'],
            'username': user_data['username'],
            'email': user_data.get('email'),
            'role': user_data.get('role', 'user'),
            'permissions': user_data.get('permissions', []),
            'token_type': self.TOKEN_TYPE_ACCESS,
            'iat': now,
            'exp': expire,
            'jti': self._generate_jti(),  # JWT ID
            'remember_me': remember_me
        }
        
        # 生成令牌
        token = jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
        
        # 如果有Redis，存储令牌信息
        if self.redis_client:
            self._store_token_info(payload['jti'], user_data['user_id'], expire, self.TOKEN_TYPE_ACCESS)
        
        logger.info(f"生成访问令牌: user_id={user_data['user_id']}, jti={payload['jti']}")
        
        return token
    
    def generate_refresh_token(self, user_id: int) -> str:
        """生成刷新令牌"""
        now = datetime.utcnow()
        expire = now + timedelta(days=self.refresh_token_expire_days)
        
        payload = {
            'user_id': user_id,
            'token_type': self.TOKEN_TYPE_REFRESH,
            'iat': now,
            'exp': expire,
            'jti': self._generate_jti()
        }
        
        token = jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
        
        # 存储刷新令牌信息
        if self.redis_client:
            self._store_token_info(payload['jti'], user_id, expire, self.TOKEN_TYPE_REFRESH)
        
        logger.info(f"生成刷新令牌: user_id={user_id}, jti={payload['jti']}")
        
        return token
    
    def verify_token(self, token: str, token_type: str = None) -> Optional[Dict[str, Any]]:
        """验证令牌"""
        try:
            # 解码令牌
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            
            # 检查令牌类型
            if token_type and payload.get('token_type') != token_type:
                logger.warning(f"令牌类型不匹配: expected={token_type}, got={payload.get('token_type')}")
                return None
            
            # 检查令牌是否在黑名单中
            if self.redis_client and self._is_token_blacklisted(payload.get('jti')):
                logger.warning(f"令牌已被拉黑: jti={payload.get('jti')}")
                return None
            
            # 检查令牌是否仍然有效（Redis中存在）
            if self.redis_client and not self._is_token_valid(payload.get('jti')):
                logger.warning(f"令牌已失效: jti={payload.get('jti')}")
                return None
            
            logger.debug(f"令牌验证成功: user_id={payload.get('user_id')}, jti={payload.get('jti')}")
            
            return payload
            
        except jwt.ExpiredSignatureError:
            logger.warning("令牌已过期")
            return None
        except jwt.InvalidTokenError as e:
            logger.warning(f"无效令牌: {e}")
            return None
        except Exception as e:
            logger.error(f"令牌验证失败: {e}")
            return None
    
    def refresh_access_token(self, refresh_token: str, user_data: Dict[str, Any]) -> Optional[str]:
        """使用刷新令牌生成新的访问令牌"""
        # 验证刷新令牌
        payload = self.verify_token(refresh_token, self.TOKEN_TYPE_REFRESH)
        if not payload:
            return None
        
        # 检查用户ID匹配
        if payload['user_id'] != user_data['user_id']:
            logger.warning(f"刷新令牌用户ID不匹配: token_user={payload['user_id']}, user={user_data['user_id']}")
            return None
        
        # 生成新的访问令牌
        new_access_token = self.generate_access_token(user_data)
        
        logger.info(f"刷新访问令牌成功: user_id={user_data['user_id']}")
        
        return new_access_token
    
    def revoke_token(self, token: str) -> bool:
        """撤销令牌"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            jti = payload.get('jti')
            
            if jti and self.redis_client:
                # 添加到黑名单
                self._blacklist_token(jti, payload.get('exp', time.time() + 3600))
                
                # 从有效令牌列表中移除
                self._remove_token_info(jti)
                
                logger.info(f"令牌已撤销: jti={jti}")
                return True
            
        except Exception as e:
            logger.error(f"撤销令牌失败: {e}")
        
        return False
    
    def revoke_all_user_tokens(self, user_id: int) -> bool:
        """撤销用户的所有令牌"""
        if not self.redis_client:
            return False
        
        try:
            # 获取用户的所有令牌
            pattern = f"token_info:*:user:{user_id}"
            token_keys = self.redis_client.keys(pattern)
            
            revoked_count = 0
            for key in token_keys:
                # 获取令牌信息
                token_info = self.redis_client.hgetall(key)
                if token_info:
                    jti = token_info.get('jti')
                    exp = float(token_info.get('exp', time.time() + 3600))
                    
                    # 添加到黑名单
                    self._blacklist_token(jti, exp)
                    
                    # 删除令牌信息
                    self.redis_client.delete(key)
                    revoked_count += 1
            
            logger.info(f"撤销用户所有令牌: user_id={user_id}, count={revoked_count}")
            return True
            
        except Exception as e:
            logger.error(f"撤销用户令牌失败: {e}")
            return False
    
    def get_token_info(self, token: str) -> Optional[Dict[str, Any]]:
        """获取令牌信息"""
        payload = self.verify_token(token)
        if not payload:
            return None
        
        return {
            'user_id': payload.get('user_id'),
            'username': payload.get('username'),
            'email': payload.get('email'),
            'role': payload.get('role'),
            'permissions': payload.get('permissions', []),
            'token_type': payload.get('token_type'),
            'issued_at': datetime.fromtimestamp(payload.get('iat', 0)),
            'expires_at': datetime.fromtimestamp(payload.get('exp', 0)),
            'jti': payload.get('jti'),
            'remember_me': payload.get('remember_me', False)
        }
    
    def _generate_jti(self) -> str:
        """生成JWT ID"""
        return secrets.token_urlsafe(32)
    
    def _store_token_info(self, jti: str, user_id: int, expire: datetime, token_type: str):
        """存储令牌信息到Redis"""
        if not self.redis_client:
            return
        
        key = f"token_info:{jti}:user:{user_id}"
        token_info = {
            'jti': jti,
            'user_id': user_id,
            'token_type': token_type,
            'exp': expire.timestamp(),
            'created_at': time.time()
        }
        
        # 设置过期时间
        ttl = int((expire - datetime.utcnow()).total_seconds())
        
        self.redis_client.hmset(key, token_info)
        self.redis_client.expire(key, ttl)
    
    def _is_token_valid(self, jti: str) -> bool:
        """检查令牌是否有效"""
        if not jti or not self.redis_client:
            return True  # 如果没有Redis，默认有效
        
        pattern = f"token_info:{jti}:*"
        keys = self.redis_client.keys(pattern)
        return len(keys) > 0
    
    def _is_token_blacklisted(self, jti: str) -> bool:
        """检查令牌是否在黑名单中"""
        if not jti or not self.redis_client:
            return False
        
        return self.redis_client.exists(f"blacklist:{jti}")
    
    def _blacklist_token(self, jti: str, exp: float):
        """将令牌添加到黑名单"""
        if not self.redis_client:
            return
        
        key = f"blacklist:{jti}"
        ttl = max(1, int(exp - time.time()))  # 确保TTL至少为1秒
        
        self.redis_client.setex(key, ttl, "blacklisted")
    
    def _remove_token_info(self, jti: str):
        """移除令牌信息"""
        if not self.redis_client:
            return
        
        pattern = f"token_info:{jti}:*"
        keys = self.redis_client.keys(pattern)
        
        if keys:
            self.redis_client.delete(*keys)
    
    def cleanup_expired_tokens(self):
        """清理过期的令牌信息"""
        if not self.redis_client:
            return
        
        try:
            # Redis的过期机制会自动清理，这里主要是统计
            pattern = "token_info:*"
            keys = self.redis_client.keys(pattern)
            
            logger.info(f"当前活跃令牌数量: {len(keys)}")
            
        except Exception as e:
            logger.error(f"清理过期令牌失败: {e}")

class JWTAuthenticator:
    """JWT认证器"""
    
    def __init__(self, jwt_manager: JWTManager):
        self.jwt_manager = jwt_manager
    
    def authenticate_user(self, username: str, password: str, user_service) -> Optional[Dict[str, Any]]:
        """认证用户"""
        try:
            # 验证用户凭据
            user = user_service.verify_credentials(username, password)
            if not user:
                logger.warning(f"用户认证失败: username={username}")
                return None
            
            # 检查用户状态
            if user.get('status') != 'active':
                logger.warning(f"用户账户未激活: username={username}, status={user.get('status')}")
                return None
            
            # 准备用户数据
            user_data = {
                'user_id': user['id'],
                'username': user['username'],
                'email': user['email'],
                'role': user.get('role', 'user'),
                'permissions': user.get('permissions', [])
            }
            
            logger.info(f"用户认证成功: username={username}, user_id={user['id']}")
            
            return user_data
            
        except Exception as e:
            logger.error(f"用户认证异常: {e}")
            return None
    
    def login(self, username: str, password: str, remember_me: bool, user_service) -> Optional[Dict[str, str]]:
        """用户登录"""
        # 认证用户
        user_data = self.authenticate_user(username, password, user_service)
        if not user_data:
            return None
        
        # 生成令牌
        access_token = self.jwt_manager.generate_access_token(user_data, remember_me)
        refresh_token = self.jwt_manager.generate_refresh_token(user_data['user_id'])
        
        return {
            'access_token': access_token,
            'refresh_token': refresh_token,
            'token_type': 'bearer'
        }
    
    def logout(self, access_token: str, refresh_token: str = None) -> bool:
        """用户登出"""
        success = True
        
        # 撤销访问令牌
        if not self.jwt_manager.revoke_token(access_token):
            success = False
        
        # 撤销刷新令牌
        if refresh_token and not self.jwt_manager.revoke_token(refresh_token):
            success = False
        
        return success
    
    def logout_all_devices(self, user_id: int) -> bool:
        """从所有设备登出"""
        return self.jwt_manager.revoke_all_user_tokens(user_id)
