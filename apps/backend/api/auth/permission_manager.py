"""
权限管理系统
处理用户权限验证和角色管理
"""
from typing import List, Dict, Any, Optional
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class Permission(Enum):
    """权限枚举"""
    # 岗位相关权限
    JOB_VIEW = "job:view"
    JOB_CREATE = "job:create"
    JOB_UPDATE = "job:update"
    JOB_DELETE = "job:delete"
    JOB_PUBLISH = "job:publish"
    
    # 用户相关权限
    USER_VIEW = "user:view"
    USER_CREATE = "user:create"
    USER_UPDATE = "user:update"
    USER_DELETE = "user:delete"
    USER_MANAGE = "user:manage"
    
    # 申请相关权限
    APPLICATION_VIEW = "application:view"
    APPLICATION_CREATE = "application:create"
    APPLICATION_UPDATE = "application:update"
    APPLICATION_DELETE = "application:delete"
    APPLICATION_REVIEW = "application:review"
    
    # 系统管理权限
    SYSTEM_CONFIG = "system:config"
    SYSTEM_MONITOR = "system:monitor"
    SYSTEM_BACKUP = "system:backup"
    
    # 数据权限
    DATA_EXPORT = "data:export"
    DATA_IMPORT = "data:import"
    DATA_ANALYTICS = "data:analytics"

class Role(Enum):
    """角色枚举"""
    ADMIN = "admin"
    RECRUITER = "recruiter"
    USER = "user"
    GUEST = "guest"

class PermissionManager:
    """权限管理器"""
    
    def __init__(self):
        # 角色权限映射
        self.role_permissions = {
            Role.ADMIN: [
                # 管理员拥有所有权限
                Permission.JOB_VIEW, Permission.JOB_CREATE, Permission.JOB_UPDATE, 
                Permission.JOB_DELETE, Permission.JOB_PUBLISH,
                Permission.USER_VIEW, Permission.USER_CREATE, Permission.USER_UPDATE, 
                Permission.USER_DELETE, Permission.USER_MANAGE,
                Permission.APPLICATION_VIEW, Permission.APPLICATION_CREATE, 
                Permission.APPLICATION_UPDATE, Permission.APPLICATION_DELETE, Permission.APPLICATION_REVIEW,
                Permission.SYSTEM_CONFIG, Permission.SYSTEM_MONITOR, Permission.SYSTEM_BACKUP,
                Permission.DATA_EXPORT, Permission.DATA_IMPORT, Permission.DATA_ANALYTICS
            ],
            Role.RECRUITER: [
                # 招聘者权限
                Permission.JOB_VIEW, Permission.JOB_CREATE, Permission.JOB_UPDATE, Permission.JOB_PUBLISH,
                Permission.APPLICATION_VIEW, Permission.APPLICATION_REVIEW,
                Permission.USER_VIEW,
                Permission.DATA_ANALYTICS
            ],
            Role.USER: [
                # 普通用户权限
                Permission.JOB_VIEW,
                Permission.APPLICATION_VIEW, Permission.APPLICATION_CREATE, Permission.APPLICATION_UPDATE,
                Permission.USER_VIEW  # 只能查看自己的信息
            ],
            Role.GUEST: [
                # 访客权限
                Permission.JOB_VIEW
            ]
        }
        
        # 权限层级关系
        self.permission_hierarchy = {
            Permission.USER_MANAGE: [Permission.USER_VIEW, Permission.USER_UPDATE],
            Permission.JOB_DELETE: [Permission.JOB_UPDATE, Permission.JOB_VIEW],
            Permission.JOB_UPDATE: [Permission.JOB_VIEW],
            Permission.APPLICATION_REVIEW: [Permission.APPLICATION_VIEW],
            Permission.APPLICATION_DELETE: [Permission.APPLICATION_UPDATE, Permission.APPLICATION_VIEW],
            Permission.APPLICATION_UPDATE: [Permission.APPLICATION_VIEW],
            Permission.SYSTEM_CONFIG: [Permission.SYSTEM_MONITOR],
            Permission.DATA_EXPORT: [Permission.DATA_ANALYTICS],
            Permission.DATA_IMPORT: [Permission.DATA_ANALYTICS]
        }
    
    def get_role_permissions(self, role: str) -> List[Permission]:
        """获取角色的所有权限"""
        try:
            role_enum = Role(role)
            permissions = self.role_permissions.get(role_enum, [])
            
            # 展开权限层级
            expanded_permissions = set(permissions)
            for permission in permissions:
                expanded_permissions.update(self._get_inherited_permissions(permission))
            
            return list(expanded_permissions)
            
        except ValueError:
            logger.warning(f"未知角色: {role}")
            return []
    
    def has_permission(self, user_permissions: List[str], required_permission: str) -> bool:
        """检查用户是否拥有指定权限"""
        try:
            required_perm = Permission(required_permission)
            user_perms = [Permission(p) for p in user_permissions if self._is_valid_permission(p)]
            
            # 直接权限检查
            if required_perm in user_perms:
                return True
            
            # 检查是否有更高级的权限
            for user_perm in user_perms:
                if self._has_higher_permission(user_perm, required_perm):
                    return True
            
            return False
            
        except ValueError:
            logger.warning(f"无效权限: {required_permission}")
            return False
    
    def has_any_permission(self, user_permissions: List[str], required_permissions: List[str]) -> bool:
        """检查用户是否拥有任一指定权限"""
        for permission in required_permissions:
            if self.has_permission(user_permissions, permission):
                return True
        return False
    
    def has_all_permissions(self, user_permissions: List[str], required_permissions: List[str]) -> bool:
        """检查用户是否拥有所有指定权限"""
        for permission in required_permissions:
            if not self.has_permission(user_permissions, permission):
                return False
        return True
    
    def can_access_resource(self, user_permissions: List[str], resource_type: str, action: str) -> bool:
        """检查用户是否可以访问特定资源"""
        permission_string = f"{resource_type}:{action}"
        return self.has_permission(user_permissions, permission_string)
    
    def filter_accessible_resources(self, user_permissions: List[str], resources: List[Dict[str, Any]], 
                                  resource_type: str) -> List[Dict[str, Any]]:
        """过滤用户可访问的资源"""
        accessible_resources = []
        
        for resource in resources:
            # 基本查看权限
            if self.can_access_resource(user_permissions, resource_type, "view"):
                accessible_resources.append(resource)
            # 或者用户是资源的所有者
            elif resource.get('owner_id') and self._is_resource_owner(user_permissions, resource):
                accessible_resources.append(resource)
        
        return accessible_resources
    
    def get_user_role_hierarchy(self, role: str) -> List[str]:
        """获取用户角色层级"""
        role_hierarchy = {
            Role.ADMIN: [Role.ADMIN, Role.RECRUITER, Role.USER, Role.GUEST],
            Role.RECRUITER: [Role.RECRUITER, Role.USER, Role.GUEST],
            Role.USER: [Role.USER, Role.GUEST],
            Role.GUEST: [Role.GUEST]
        }
        
        try:
            role_enum = Role(role)
            return [r.value for r in role_hierarchy.get(role_enum, [Role.GUEST])]
        except ValueError:
            return [Role.GUEST.value]
    
    def can_manage_user(self, manager_role: str, target_role: str) -> bool:
        """检查是否可以管理目标用户"""
        manager_hierarchy = self.get_user_role_hierarchy(manager_role)
        return target_role in manager_hierarchy
    
    def validate_permission_assignment(self, role: str, permissions: List[str]) -> Dict[str, Any]:
        """验证权限分配的合法性"""
        try:
            role_enum = Role(role)
            role_permissions = [p.value for p in self.role_permissions.get(role_enum, [])]
            
            valid_permissions = []
            invalid_permissions = []
            
            for permission in permissions:
                if permission in role_permissions:
                    valid_permissions.append(permission)
                else:
                    invalid_permissions.append(permission)
            
            return {
                'valid': len(invalid_permissions) == 0,
                'valid_permissions': valid_permissions,
                'invalid_permissions': invalid_permissions,
                'message': f"角色 {role} 权限验证完成"
            }
            
        except ValueError:
            return {
                'valid': False,
                'valid_permissions': [],
                'invalid_permissions': permissions,
                'message': f"未知角色: {role}"
            }
    
    def _get_inherited_permissions(self, permission: Permission) -> List[Permission]:
        """获取权限继承的子权限"""
        return self.permission_hierarchy.get(permission, [])
    
    def _has_higher_permission(self, user_permission: Permission, required_permission: Permission) -> bool:
        """检查用户权限是否高于所需权限"""
        # 检查权限层级关系
        for higher_perm, lower_perms in self.permission_hierarchy.items():
            if user_permission == higher_perm and required_permission in lower_perms:
                return True
        return False
    
    def _is_valid_permission(self, permission: str) -> bool:
        """检查权限是否有效"""
        try:
            Permission(permission)
            return True
        except ValueError:
            return False
    
    def _is_resource_owner(self, user_permissions: List[str], resource: Dict[str, Any]) -> bool:
        """检查用户是否是资源所有者"""
        # 这里需要根据实际业务逻辑实现
        # 例如检查JWT中的user_id是否与resource的owner_id匹配
        return False

class PermissionDecorator:
    """权限装饰器"""
    
    def __init__(self, permission_manager: PermissionManager):
        self.permission_manager = permission_manager
    
    def require_permission(self, required_permission: str):
        """要求特定权限的装饰器"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                # 从请求上下文获取用户权限
                user_permissions = self._get_user_permissions_from_context()
                
                if not self.permission_manager.has_permission(user_permissions, required_permission):
                    raise PermissionError(f"缺少权限: {required_permission}")
                
                return func(*args, **kwargs)
            return wrapper
        return decorator
    
    def require_any_permission(self, required_permissions: List[str]):
        """要求任一权限的装饰器"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                user_permissions = self._get_user_permissions_from_context()
                
                if not self.permission_manager.has_any_permission(user_permissions, required_permissions):
                    raise PermissionError(f"缺少权限: {required_permissions}")
                
                return func(*args, **kwargs)
            return wrapper
        return decorator
    
    def require_role(self, required_role: str):
        """要求特定角色的装饰器"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                user_role = self._get_user_role_from_context()
                
                if user_role != required_role:
                    # 检查角色层级
                    role_hierarchy = self.permission_manager.get_user_role_hierarchy(user_role)
                    if required_role not in role_hierarchy:
                        raise PermissionError(f"需要角色: {required_role}")
                
                return func(*args, **kwargs)
            return wrapper
        return decorator
    
    def _get_user_permissions_from_context(self) -> List[str]:
        """从上下文获取用户权限"""
        # 这里需要根据实际框架实现
        # 例如从FastAPI的request.state或Flask的g对象获取
        return []
    
    def _get_user_role_from_context(self) -> str:
        """从上下文获取用户角色"""
        # 这里需要根据实际框架实现
        return Role.GUEST.value

# 权限检查异常
class PermissionDeniedError(Exception):
    """权限拒绝异常"""
    
    def __init__(self, message: str, required_permission: str = None):
        self.message = message
        self.required_permission = required_permission
        super().__init__(message)

# 全局权限管理器实例
permission_manager = PermissionManager()
permission_decorator = PermissionDecorator(permission_manager)
