#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
多层缓存架构设计
"""

import time
from typing import Dict, Any, Optional, List, Union
from enum import Enum
from dataclasses import dataclass
from apps.backend.utils.logger import setup_logger

logger = setup_logger(__name__)


class CacheLevel(Enum):
    """缓存层级"""
    L1_MEMORY = "l1_memory"      # 内存缓存（最快）
    L2_REDIS = "l2_redis"        # Redis缓存（中等速度）
    L3_DATABASE = "l3_database"  # 数据库缓存（最慢）


class CacheStrategy(Enum):
    """缓存策略"""
    WRITE_THROUGH = "write_through"    # 写穿透
    WRITE_BACK = "write_back"          # 写回
    WRITE_AROUND = "write_around"      # 写绕过
    READ_THROUGH = "read_through"      # 读穿透
    CACHE_ASIDE = "cache_aside"        # 缓存旁路


@dataclass
class CacheConfig:
    """缓存配置"""
    name: str
    levels: List[CacheLevel]
    strategy: CacheStrategy
    ttl: Dict[CacheLevel, int]  # 各层级的TTL
    max_size: Dict[CacheLevel, int]  # 各层级的最大大小
    eviction_policy: str = "LRU"  # 淘汰策略
    compression: bool = False  # 是否压缩
    serialization: str = "json"  # 序列化方式
    monitoring: bool = True  # 是否监控


# 缓存架构配置
CACHE_ARCHITECTURE = {
    # 岗位搜索缓存 - 高频访问，多层缓存
    "job_search": CacheConfig(
        name="job_search",
        levels=[CacheLevel.L1_MEMORY, CacheLevel.L2_REDIS],
        strategy=CacheStrategy.CACHE_ASIDE,
        ttl={
            CacheLevel.L1_MEMORY: 60,    # 1分钟
            CacheLevel.L2_REDIS: 600,    # 10分钟
        },
        max_size={
            CacheLevel.L1_MEMORY: 1000,  # 1000个条目
            CacheLevel.L2_REDIS: 10000,  # 10000个条目
        },
        compression=True,
        monitoring=True
    ),
    
    # 岗位详情缓存 - 中频访问，Redis缓存
    "job_detail": CacheConfig(
        name="job_detail",
        levels=[CacheLevel.L2_REDIS],
        strategy=CacheStrategy.CACHE_ASIDE,
        ttl={
            CacheLevel.L2_REDIS: 1800,   # 30分钟
        },
        max_size={
            CacheLevel.L2_REDIS: 50000,  # 50000个条目
        },
        compression=True,
        monitoring=True
    ),
    
    # 用户资料缓存 - 中频访问，长期缓存
    "user_profile": CacheConfig(
        name="user_profile",
        levels=[CacheLevel.L1_MEMORY, CacheLevel.L2_REDIS],
        strategy=CacheStrategy.WRITE_THROUGH,
        ttl={
            CacheLevel.L1_MEMORY: 300,   # 5分钟
            CacheLevel.L2_REDIS: 3600,   # 1小时
        },
        max_size={
            CacheLevel.L1_MEMORY: 500,   # 500个用户
            CacheLevel.L2_REDIS: 10000,  # 10000个用户
        },
        monitoring=True
    ),
    
    # 热门岗位缓存 - 高频访问，短期缓存
    "hot_jobs": CacheConfig(
        name="hot_jobs",
        levels=[CacheLevel.L1_MEMORY, CacheLevel.L2_REDIS],
        strategy=CacheStrategy.READ_THROUGH,
        ttl={
            CacheLevel.L1_MEMORY: 120,   # 2分钟
            CacheLevel.L2_REDIS: 900,    # 15分钟
        },
        max_size={
            CacheLevel.L1_MEMORY: 100,   # 100个条目
            CacheLevel.L2_REDIS: 1000,   # 1000个条目
        },
        monitoring=True
    ),
    
    # API响应缓存 - 高频访问，短期缓存
    "api_response": CacheConfig(
        name="api_response",
        levels=[CacheLevel.L1_MEMORY, CacheLevel.L2_REDIS],
        strategy=CacheStrategy.CACHE_ASIDE,
        ttl={
            CacheLevel.L1_MEMORY: 30,    # 30秒
            CacheLevel.L2_REDIS: 300,    # 5分钟
        },
        max_size={
            CacheLevel.L1_MEMORY: 2000,  # 2000个响应
            CacheLevel.L2_REDIS: 20000,  # 20000个响应
        },
        compression=True,
        monitoring=True
    ),
    
    # 统计数据缓存 - 低频访问，长期缓存
    "statistics": CacheConfig(
        name="statistics",
        levels=[CacheLevel.L2_REDIS],
        strategy=CacheStrategy.WRITE_BACK,
        ttl={
            CacheLevel.L2_REDIS: 7200,   # 2小时
        },
        max_size={
            CacheLevel.L2_REDIS: 5000,   # 5000个统计
        },
        monitoring=True
    ),
    
    # 配置缓存 - 低频访问，长期缓存
    "system_config": CacheConfig(
        name="system_config",
        levels=[CacheLevel.L1_MEMORY, CacheLevel.L2_REDIS],
        strategy=CacheStrategy.WRITE_THROUGH,
        ttl={
            CacheLevel.L1_MEMORY: 3600,  # 1小时
            CacheLevel.L2_REDIS: 86400,  # 24小时
        },
        max_size={
            CacheLevel.L1_MEMORY: 100,   # 100个配置
            CacheLevel.L2_REDIS: 1000,   # 1000个配置
        },
        monitoring=True
    ),
    
    # 会话缓存 - 高频访问，中期缓存
    "user_session": CacheConfig(
        name="user_session",
        levels=[CacheLevel.L2_REDIS],
        strategy=CacheStrategy.CACHE_ASIDE,
        ttl={
            CacheLevel.L2_REDIS: 7200,   # 2小时
        },
        max_size={
            CacheLevel.L2_REDIS: 50000,  # 50000个会话
        },
        monitoring=True
    )
}


class CacheMetrics:
    """缓存指标"""
    
    def __init__(self):
        self.reset()
    
    def reset(self):
        """重置指标"""
        self.hits = 0
        self.misses = 0
        self.sets = 0
        self.deletes = 0
        self.errors = 0
        self.total_time = 0.0
        self.start_time = time.time()
    
    def record_hit(self, response_time: float = 0.0):
        """记录缓存命中"""
        self.hits += 1
        self.total_time += response_time
    
    def record_miss(self, response_time: float = 0.0):
        """记录缓存未命中"""
        self.misses += 1
        self.total_time += response_time
    
    def record_set(self, response_time: float = 0.0):
        """记录缓存设置"""
        self.sets += 1
        self.total_time += response_time
    
    def record_delete(self, response_time: float = 0.0):
        """记录缓存删除"""
        self.deletes += 1
        self.total_time += response_time
    
    def record_error(self):
        """记录错误"""
        self.errors += 1
    
    @property
    def hit_rate(self) -> float:
        """命中率"""
        total = self.hits + self.misses
        return self.hits / total if total > 0 else 0.0
    
    @property
    def miss_rate(self) -> float:
        """未命中率"""
        return 1.0 - self.hit_rate
    
    @property
    def average_response_time(self) -> float:
        """平均响应时间"""
        total_operations = self.hits + self.misses + self.sets + self.deletes
        return self.total_time / total_operations if total_operations > 0 else 0.0
    
    @property
    def operations_per_second(self) -> float:
        """每秒操作数"""
        elapsed = time.time() - self.start_time
        total_operations = self.hits + self.misses + self.sets + self.deletes
        return total_operations / elapsed if elapsed > 0 else 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "hits": self.hits,
            "misses": self.misses,
            "sets": self.sets,
            "deletes": self.deletes,
            "errors": self.errors,
            "hit_rate": self.hit_rate,
            "miss_rate": self.miss_rate,
            "average_response_time": self.average_response_time,
            "operations_per_second": self.operations_per_second,
            "uptime": time.time() - self.start_time
        }


# 全局缓存指标
cache_metrics = {
    cache_name: CacheMetrics() 
    for cache_name in CACHE_ARCHITECTURE.keys()
}


def get_cache_config(cache_name: str) -> Optional[CacheConfig]:
    """获取缓存配置"""
    return CACHE_ARCHITECTURE.get(cache_name)


def get_cache_metrics(cache_name: str) -> Optional[CacheMetrics]:
    """获取缓存指标"""
    return cache_metrics.get(cache_name)


def get_all_cache_metrics() -> Dict[str, Dict[str, Any]]:
    """获取所有缓存指标"""
    return {
        cache_name: metrics.to_dict()
        for cache_name, metrics in cache_metrics.items()
    }


# 缓存键命名规范
CACHE_KEY_PATTERNS = {
    "job_search": "job:search:{query_hash}:{page}:{page_size}",
    "job_detail": "job:detail:{job_id}",
    "job_list": "job:list:{filters_hash}:{page}:{page_size}",
    "user_profile": "user:profile:{user_id}",
    "user_session": "user:session:{session_id}",
    "hot_jobs": "job:hot:{category}:{limit}",
    "api_response": "api:response:{endpoint}:{params_hash}",
    "statistics": "stats:{type}:{period}:{filters_hash}",
    "system_config": "config:{module}:{key}",
    "banner_list": "banner:list:{status}",
    "policy_list": "policy:list:{category}:{status}"
}


def generate_cache_key(pattern_name: str, **kwargs) -> str:
    """生成缓存键"""
    pattern = CACHE_KEY_PATTERNS.get(pattern_name)
    if not pattern:
        raise ValueError(f"Unknown cache key pattern: {pattern_name}")
    
    try:
        return pattern.format(**kwargs)
    except KeyError as e:
        raise ValueError(f"Missing parameter for cache key pattern {pattern_name}: {e}")


# 缓存预热配置
CACHE_WARMUP_CONFIG = {
    "hot_jobs": {
        "enabled": True,
        "schedule": "0 */6 * * *",  # 每6小时
        "categories": ["软件开发", "产品经理", "数据分析", "UI设计"],
        "limit": 50
    },
    "popular_searches": {
        "enabled": True,
        "schedule": "0 */4 * * *",  # 每4小时
        "keywords": ["软件工程师", "产品经理", "前端开发", "后端开发", "数据分析师"],
        "locations": ["北京", "上海", "深圳", "杭州", "广州"]
    },
    "system_config": {
        "enabled": True,
        "schedule": "0 0 * * *",   # 每天
        "modules": ["banner", "policy", "category"]
    }
}


# 缓存清理配置
CACHE_CLEANUP_CONFIG = {
    "expired_keys": {
        "enabled": True,
        "schedule": "0 2 * * *",   # 每天凌晨2点
        "batch_size": 1000
    },
    "memory_pressure": {
        "enabled": True,
        "threshold": 0.8,          # 内存使用率阈值
        "cleanup_ratio": 0.2       # 清理比例
    },
    "low_hit_rate": {
        "enabled": True,
        "threshold": 0.1,          # 命中率阈值
        "min_age": 3600            # 最小存在时间（秒）
    }
}
