#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库优化配置
"""

import os
from typing import Dict, Any, List, Optional
from enum import Enum

# 索引类型
class IndexType(Enum):
    """索引类型枚举"""
    BTREE = "BTREE"
    HASH = "HASH"
    FULLTEXT = "FULLTEXT"
    SPATIAL = "SPATIAL"

# 查询优化策略
class QueryOptimizationStrategy(Enum):
    """查询优化策略枚举"""
    INDEX_OPTIMIZATION = "index_optimization"
    QUERY_REWRITE = "query_rewrite"
    CACHING = "caching"
    PARTITIONING = "partitioning"

# 数据库优化配置
DATABASE_OPTIMIZATION_CONFIG = {
    # 连接池配置
    "connection_pool": {
        "enabled": True,
        "min_connections": int(os.getenv("DB_MIN_CONNECTIONS", "5")),
        "max_connections": int(os.getenv("DB_MAX_CONNECTIONS", "20")),
        "max_idle_time": int(os.getenv("DB_MAX_IDLE_TIME", "300")),  # 5分钟
        "connection_timeout": int(os.getenv("DB_CONNECTION_TIMEOUT", "30")),
        "retry_attempts": int(os.getenv("DB_RETRY_ATTEMPTS", "3")),
        "retry_delay": float(os.getenv("DB_RETRY_DELAY", "1.0")),
        "health_check_interval": int(os.getenv("DB_HEALTH_CHECK_INTERVAL", "60")),
        "validate_connections": True,
        "auto_reconnect": True
    },
    
    # 查询优化配置
    "query_optimization": {
        "enabled": True,
        "slow_query_threshold": float(os.getenv("SLOW_QUERY_THRESHOLD", "1.0")),  # 1秒
        "query_cache_enabled": True,
        "query_cache_size": int(os.getenv("QUERY_CACHE_SIZE", "1000")),
        "query_cache_ttl": int(os.getenv("QUERY_CACHE_TTL", "300")),  # 5分钟
        "explain_slow_queries": True,
        "log_slow_queries": True,
        "optimize_joins": True,
        "use_prepared_statements": True
    },
    
    # 索引优化配置
    "index_optimization": {
        "enabled": True,
        "auto_create_indexes": False,  # 生产环境建议关闭
        "analyze_query_patterns": True,
        "suggest_missing_indexes": True,
        "monitor_index_usage": True,
        "remove_unused_indexes": False,  # 需要谨慎操作
        "index_maintenance_interval": 86400,  # 24小时
    },
    
    # 表优化配置
    "table_optimization": {
        "enabled": True,
        "auto_analyze": True,
        "analyze_threshold": 0.1,  # 10%数据变化时分析
        "vacuum_enabled": True,
        "vacuum_threshold": 0.2,  # 20%死元组时清理
        "partition_large_tables": True,
        "partition_threshold": 1000000,  # 100万行
    },
    
    # 性能监控配置
    "performance_monitoring": {
        "enabled": True,
        "monitor_slow_queries": True,
        "monitor_lock_waits": True,
        "monitor_connection_usage": True,
        "monitor_index_usage": True,
        "collect_query_stats": True,
        "stats_retention_days": 30,
        "alert_on_performance_issues": True
    },
    
    # 数据库特定配置
    "mysql_optimization": {
        "innodb_buffer_pool_size": "1G",
        "innodb_log_file_size": "256M",
        "innodb_flush_log_at_trx_commit": 2,
        "query_cache_type": 1,
        "query_cache_size": "256M",
        "max_connections": 200,
        "thread_cache_size": 16,
        "table_open_cache": 2000,
        "tmp_table_size": "64M",
        "max_heap_table_size": "64M"
    },
    
    # 索引定义
    "indexes": {
        "jobs": [
            {
                "name": "idx_jobs_status_created",
                "columns": ["status", "created_at"],
                "type": IndexType.BTREE.value,
                "unique": False,
                "description": "岗位状态和创建时间复合索引"
            },
            {
                "name": "idx_jobs_company_status",
                "columns": ["company_id", "status"],
                "type": IndexType.BTREE.value,
                "unique": False,
                "description": "公司和状态复合索引"
            },
            {
                "name": "idx_jobs_location",
                "columns": ["location"],
                "type": IndexType.BTREE.value,
                "unique": False,
                "description": "工作地点索引"
            },
            {
                "name": "idx_jobs_salary_range",
                "columns": ["min_salary", "max_salary"],
                "type": IndexType.BTREE.value,
                "unique": False,
                "description": "薪资范围索引"
            },
            {
                "name": "idx_jobs_title_fulltext",
                "columns": ["title"],
                "type": IndexType.FULLTEXT.value,
                "unique": False,
                "description": "岗位标题全文索引"
            },
            {
                "name": "idx_jobs_description_fulltext",
                "columns": ["description"],
                "type": IndexType.FULLTEXT.value,
                "unique": False,
                "description": "岗位描述全文索引"
            }
        ],
        "users": [
            {
                "name": "idx_users_email",
                "columns": ["email"],
                "type": IndexType.BTREE.value,
                "unique": True,
                "description": "用户邮箱唯一索引"
            },
            {
                "name": "idx_users_phone",
                "columns": ["phone"],
                "type": IndexType.BTREE.value,
                "unique": True,
                "description": "用户手机号唯一索引"
            },
            {
                "name": "idx_users_status_created",
                "columns": ["status", "created_at"],
                "type": IndexType.BTREE.value,
                "unique": False,
                "description": "用户状态和创建时间复合索引"
            }
        ],
        "applications": [
            {
                "name": "idx_applications_user_job",
                "columns": ["user_id", "job_id"],
                "type": IndexType.BTREE.value,
                "unique": True,
                "description": "用户岗位申请唯一索引"
            },
            {
                "name": "idx_applications_job_status",
                "columns": ["job_id", "status"],
                "type": IndexType.BTREE.value,
                "unique": False,
                "description": "岗位申请状态索引"
            },
            {
                "name": "idx_applications_user_status",
                "columns": ["user_id", "status"],
                "type": IndexType.BTREE.value,
                "unique": False,
                "description": "用户申请状态索引"
            },
            {
                "name": "idx_applications_created",
                "columns": ["created_at"],
                "type": IndexType.BTREE.value,
                "unique": False,
                "description": "申请创建时间索引"
            }
        ],
        "companies": [
            {
                "name": "idx_companies_name",
                "columns": ["name"],
                "type": IndexType.BTREE.value,
                "unique": True,
                "description": "公司名称唯一索引"
            },
            {
                "name": "idx_companies_status",
                "columns": ["status"],
                "type": IndexType.BTREE.value,
                "unique": False,
                "description": "公司状态索引"
            }
        ]
    },
    
    # 查询优化规则
    "query_optimization_rules": [
        {
            "name": "job_search_optimization",
            "pattern": "SELECT.*FROM jobs WHERE.*title.*LIKE.*",
            "suggestion": "使用全文索引替代LIKE查询",
            "optimized_query": "SELECT * FROM jobs WHERE MATCH(title) AGAINST(? IN BOOLEAN MODE)"
        },
        {
            "name": "pagination_optimization",
            "pattern": "SELECT.*LIMIT.*OFFSET.*",
            "suggestion": "使用游标分页替代OFFSET分页",
            "optimized_query": "SELECT * FROM table WHERE id > ? ORDER BY id LIMIT ?"
        },
        {
            "name": "count_optimization",
            "pattern": "SELECT COUNT\\(\\*\\) FROM.*",
            "suggestion": "对于大表使用近似计数或缓存",
            "optimized_query": "使用Redis缓存计数结果"
        }
    ],
    
    # 分区配置
    "partitioning": {
        "enabled": True,
        "strategies": {
            "jobs": {
                "type": "RANGE",
                "column": "created_at",
                "interval": "MONTH",
                "retention": 24  # 保留24个月
            },
            "applications": {
                "type": "RANGE", 
                "column": "created_at",
                "interval": "MONTH",
                "retention": 12  # 保留12个月
            },
            "user_activities": {
                "type": "RANGE",
                "column": "created_at", 
                "interval": "WEEK",
                "retention": 52  # 保留52周
            }
        }
    }
}

def get_database_optimization_config(section: str = None) -> Dict[str, Any]:
    """
    获取数据库优化配置
    
    Args:
        section: 配置节名称
        
    Returns:
        Dict[str, Any]: 配置字典
    """
    if section:
        return DATABASE_OPTIMIZATION_CONFIG.get(section, {})
    return DATABASE_OPTIMIZATION_CONFIG

def update_database_optimization_config(section: str, config: Dict[str, Any]) -> bool:
    """
    更新数据库优化配置
    
    Args:
        section: 配置节名称
        config: 新配置
        
    Returns:
        bool: 是否更新成功
    """
    try:
        if section in DATABASE_OPTIMIZATION_CONFIG:
            DATABASE_OPTIMIZATION_CONFIG[section].update(config)
            return True
        return False
    except Exception:
        return False

def get_table_indexes(table_name: str) -> List[Dict[str, Any]]:
    """
    获取表的索引定义
    
    Args:
        table_name: 表名
        
    Returns:
        List[Dict[str, Any]]: 索引定义列表
    """
    return DATABASE_OPTIMIZATION_CONFIG.get("indexes", {}).get(table_name, [])

def get_optimization_rules() -> List[Dict[str, Any]]:
    """
    获取查询优化规则
    
    Returns:
        List[Dict[str, Any]]: 优化规则列表
    """
    return DATABASE_OPTIMIZATION_CONFIG.get("query_optimization_rules", [])
