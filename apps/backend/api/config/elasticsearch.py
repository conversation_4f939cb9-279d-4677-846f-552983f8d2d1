#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Elasticsearch配置
"""

import os
from typing import Optional
from elasticsearch import Elasticsearch
from apps.backend.utils.logger import setup_logger

logger = setup_logger("elasticsearch_config")

# Elasticsearch配置
ELASTICSEARCH_CONFIG = {
    "host": os.getenv("ELASTICSEARCH_HOST", "localhost"),
    "port": int(os.getenv("ELASTICSEARCH_PORT", "9200")),
    "username": os.getenv("ELASTICSEARCH_USERNAME"),
    "password": os.getenv("ELASTICSEARCH_PASSWORD"),
    "use_ssl": os.getenv("ELASTICSEARCH_USE_SSL", "false").lower() == "true",
    "verify_certs": os.getenv("ELASTICSEARCH_VERIFY_CERTS", "false").lower() == "true",
    "timeout": int(os.getenv("ELASTICSEARCH_TIMEOUT", "30")),
    "max_retries": int(os.getenv("ELASTICSEARCH_MAX_RETRIES", "3")),
    "retry_on_timeout": True,
}

# 索引配置
INDEX_SETTINGS = {
    "jobs": {
        "index": "recruitment_jobs",
        "settings": {
            "number_of_shards": 1,
            "number_of_replicas": 0,
            "analysis": {
                "analyzer": {
                    "chinese_analyzer": {
                        "type": "standard"  # 使用标准分析器，避免依赖IK插件
                    },
                    "chinese_search_analyzer": {
                        "type": "standard"
                    }
                }
            }
        },
        "mappings": {
            "properties": {
                "id": {"type": "keyword"},
                "title": {
                    "type": "text",
                    "analyzer": "chinese_analyzer",
                    "search_analyzer": "chinese_search_analyzer",
                    "fields": {
                        "keyword": {"type": "keyword"}
                    }
                },
                "company": {
                    "type": "text",
                    "analyzer": "chinese_analyzer",
                    "search_analyzer": "chinese_search_analyzer",
                    "fields": {
                        "keyword": {"type": "keyword"}
                    }
                },
                "description": {
                    "type": "text",
                    "analyzer": "chinese_analyzer",
                    "search_analyzer": "chinese_search_analyzer"
                },
                "requirements": {
                    "type": "text",
                    "analyzer": "chinese_analyzer",
                    "search_analyzer": "chinese_search_analyzer"
                },
                "location": {
                    "type": "text",
                    "analyzer": "chinese_analyzer",
                    "search_analyzer": "chinese_search_analyzer",
                    "fields": {
                        "keyword": {"type": "keyword"}
                    }
                },
                "salary": {"type": "keyword"},
                "education": {"type": "keyword"},
                "experience": {"type": "keyword"},
                "job_type": {"type": "keyword"},
                "major": {
                    "type": "text",
                    "analyzer": "chinese_analyzer",
                    "search_analyzer": "chinese_search_analyzer",
                    "fields": {
                        "keyword": {"type": "keyword"}
                    }
                },
                "skills": {
                    "type": "text",
                    "analyzer": "chinese_analyzer",
                    "search_analyzer": "chinese_search_analyzer"
                },
                "publish_date": {"type": "date"},
                "deadline": {"type": "date"},
                "created_at": {"type": "date"},
                "updated_at": {"type": "date"},
                "status": {"type": "keyword"},
                "source": {"type": "keyword"},
                "url": {"type": "keyword"},
                "tags": {"type": "keyword"},
                "view_count": {"type": "integer"},
                "apply_count": {"type": "integer"},
                "favorite_count": {"type": "integer"}
            }
        }
    }
}


class ElasticsearchClient:
    """Elasticsearch客户端"""

    def __init__(self):
        """初始化Elasticsearch客户端"""
        self._client: Optional[Elasticsearch] = None
        self._connected = False

    def connect(self) -> bool:
        """
        连接到Elasticsearch

        Returns:
            bool: 是否连接成功
        """
        try:
            # 构建连接URL
            scheme = "https" if ELASTICSEARCH_CONFIG["use_ssl"] else "http"
            host_url = f"{scheme}://{ELASTICSEARCH_CONFIG['host']}:{ELASTICSEARCH_CONFIG['port']}"

            # 构建连接参数
            client_params = {
                "hosts": [host_url],
                "verify_certs": ELASTICSEARCH_CONFIG["verify_certs"],
                "request_timeout": ELASTICSEARCH_CONFIG["timeout"],
                "max_retries": ELASTICSEARCH_CONFIG["max_retries"],
                "retry_on_timeout": ELASTICSEARCH_CONFIG["retry_on_timeout"]
            }

            # 添加认证信息
            if ELASTICSEARCH_CONFIG["username"] and ELASTICSEARCH_CONFIG["password"]:
                client_params["basic_auth"] = (ELASTICSEARCH_CONFIG["username"], ELASTICSEARCH_CONFIG["password"])

            # 创建客户端
            self._client = Elasticsearch(**client_params)

            # 测试连接
            info = self._client.info()
            self._connected = True

            logger.info(f"Connected to Elasticsearch: {info['version']['number']}")
            return True

        except Exception as e:
            logger.warning(f"Elasticsearch not available: {e}")
            logger.info("Search functionality will fall back to database queries")
            self._connected = False
            return False

    def is_connected(self) -> bool:
        """
        检查是否已连接

        Returns:
            bool: 是否已连接
        """
        if not self._client or not self._connected:
            return False

        try:
            self._client.ping()
            return True
        except Exception:
            self._connected = False
            return False

    def get_client(self) -> Optional[Elasticsearch]:
        """
        获取Elasticsearch客户端

        Returns:
            Optional[Elasticsearch]: 客户端实例
        """
        if not self.is_connected():
            if not self.connect():
                return None

        return self._client


# 全局Elasticsearch客户端实例
es_client = ElasticsearchClient()


def get_elasticsearch() -> Optional[Elasticsearch]:
    """
    获取Elasticsearch客户端实例

    Returns:
        Optional[Elasticsearch]: 客户端实例
    """
    return es_client.get_client()


def init_elasticsearch() -> bool:
    """
    初始化Elasticsearch

    Returns:
        bool: 是否初始化成功
    """
    try:
        # 连接到Elasticsearch
        if not es_client.connect():
            logger.warning("Elasticsearch not available, search features will be disabled")
            return False

        logger.info("Elasticsearch initialized successfully")
        return True

    except Exception as e:
        logger.error(f"Failed to initialize Elasticsearch: {e}")
        return False
