#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
高可用部署配置
"""

import os
from typing import Dict, Any, List, Optional
from enum import Enum

# 部署模式
class DeploymentMode(Enum):
    """部署模式枚举"""
    STANDALONE = "standalone"
    CLUSTER = "cluster"
    MICROSERVICE = "microservice"

# 负载均衡策略
class LoadBalancingStrategy(Enum):
    """负载均衡策略枚举"""
    ROUND_ROBIN = "round_robin"
    LEAST_CONNECTIONS = "least_connections"
    IP_HASH = "ip_hash"
    WEIGHTED_ROUND_ROBIN = "weighted_round_robin"

# 健康检查类型
class HealthCheckType(Enum):
    """健康检查类型枚举"""
    HTTP = "http"
    TCP = "tcp"
    SCRIPT = "script"

# 高可用部署配置
HIGH_AVAILABILITY_CONFIG = {
    # 部署配置
    "deployment": {
        "mode": DeploymentMode.CLUSTER.value,
        "environment": os.getenv("DEPLOYMENT_ENV", "production"),
        "region": os.getenv("DEPLOYMENT_REGION", "us-west-1"),
        "availability_zones": ["us-west-1a", "us-west-1b", "us-west-1c"],
        "min_instances": int(os.getenv("MIN_INSTANCES", "2")),
        "max_instances": int(os.getenv("MAX_INSTANCES", "10")),
        "desired_instances": int(os.getenv("DESIRED_INSTANCES", "3")),
        "auto_scaling_enabled": True,
        "rolling_update_enabled": True,
        "blue_green_deployment": False
    },
    
    # 负载均衡配置
    "load_balancer": {
        "enabled": True,
        "type": "application",  # application, network, classic
        "strategy": LoadBalancingStrategy.ROUND_ROBIN.value,
        "health_check_enabled": True,
        "health_check_path": "/health",
        "health_check_interval": 30,
        "health_check_timeout": 5,
        "health_check_retries": 3,
        "sticky_sessions": False,
        "ssl_termination": True,
        "cross_zone_enabled": True
    },
    
    # 服务发现配置
    "service_discovery": {
        "enabled": True,
        "provider": "consul",  # consul, etcd, kubernetes
        "consul": {
            "host": os.getenv("CONSUL_HOST", "localhost"),
            "port": int(os.getenv("CONSUL_PORT", "8500")),
            "datacenter": "dc1",
            "token": os.getenv("CONSUL_TOKEN", ""),
            "health_check_interval": "10s",
            "health_check_timeout": "3s",
            "deregister_critical_after": "30s"
        },
        "service_name": "recruitment-api",
        "service_tags": ["api", "recruitment", "v1"],
        "service_meta": {
            "version": "1.0.0",
            "environment": os.getenv("DEPLOYMENT_ENV", "production")
        }
    },
    
    # 健康检查配置
    "health_check": {
        "enabled": True,
        "endpoint": "/health",
        "detailed_endpoint": "/health/detailed",
        "checks": [
            {
                "name": "database",
                "type": HealthCheckType.HTTP.value,
                "url": "http://localhost:8000/health/database",
                "timeout": 5,
                "interval": 30,
                "retries": 3
            },
            {
                "name": "redis",
                "type": HealthCheckType.HTTP.value,
                "url": "http://localhost:8000/health/redis",
                "timeout": 3,
                "interval": 30,
                "retries": 3
            },
            {
                "name": "elasticsearch",
                "type": HealthCheckType.HTTP.value,
                "url": "http://localhost:8000/health/elasticsearch",
                "timeout": 5,
                "interval": 60,
                "retries": 2
            }
        ]
    },
    
    # 故障转移配置
    "failover": {
        "enabled": True,
        "strategy": "automatic",  # automatic, manual
        "detection_interval": 10,
        "failure_threshold": 3,
        "recovery_threshold": 2,
        "circuit_breaker_enabled": True,
        "circuit_breaker_threshold": 5,
        "circuit_breaker_timeout": 60,
        "backup_instances": 1,
        "cross_region_failover": False
    },
    
    # 自动扩缩容配置
    "auto_scaling": {
        "enabled": True,
        "metrics": [
            {
                "name": "cpu_utilization",
                "target_value": 70,
                "scale_up_threshold": 80,
                "scale_down_threshold": 30,
                "scale_up_cooldown": 300,
                "scale_down_cooldown": 600
            },
            {
                "name": "memory_utilization",
                "target_value": 75,
                "scale_up_threshold": 85,
                "scale_down_threshold": 40,
                "scale_up_cooldown": 300,
                "scale_down_cooldown": 600
            },
            {
                "name": "request_count",
                "target_value": 1000,
                "scale_up_threshold": 1500,
                "scale_down_threshold": 500,
                "scale_up_cooldown": 180,
                "scale_down_cooldown": 300
            }
        ],
        "scale_up_step": 1,
        "scale_down_step": 1,
        "min_instances": 2,
        "max_instances": 10
    },
    
    # 容器化配置
    "containerization": {
        "enabled": True,
        "platform": "docker",  # docker, kubernetes, ecs
        "image_registry": "docker.io",
        "image_name": "recruitment-api",
        "image_tag": "latest",
        "resource_limits": {
            "cpu": "1000m",
            "memory": "1Gi",
            "storage": "10Gi"
        },
        "resource_requests": {
            "cpu": "500m",
            "memory": "512Mi",
            "storage": "5Gi"
        },
        "environment_variables": {
            "NODE_ENV": "production",
            "LOG_LEVEL": "info",
            "METRICS_ENABLED": "true"
        }
    },
    
    # 数据库高可用配置
    "database_ha": {
        "enabled": True,
        "replication_enabled": True,
        "master_slave_setup": True,
        "read_replicas": 2,
        "backup_enabled": True,
        "backup_schedule": "0 2 * * *",  # 每天凌晨2点
        "backup_retention_days": 30,
        "failover_timeout": 30,
        "connection_pooling": True,
        "connection_pool_size": 20
    },
    
    # 缓存高可用配置
    "cache_ha": {
        "enabled": True,
        "redis_cluster": True,
        "redis_sentinel": False,
        "cluster_nodes": 6,
        "replication_factor": 2,
        "failover_timeout": 5,
        "backup_enabled": True,
        "backup_schedule": "0 3 * * *"
    },
    
    # 监控和告警配置
    "monitoring": {
        "enabled": True,
        "prometheus_enabled": True,
        "grafana_enabled": True,
        "alertmanager_enabled": True,
        "log_aggregation": True,
        "distributed_tracing": True,
        "metrics_retention_days": 30,
        "alerts": [
            {
                "name": "high_cpu_usage",
                "condition": "cpu_usage > 80",
                "duration": "5m",
                "severity": "warning"
            },
            {
                "name": "high_memory_usage",
                "condition": "memory_usage > 85",
                "duration": "5m",
                "severity": "warning"
            },
            {
                "name": "service_down",
                "condition": "up == 0",
                "duration": "1m",
                "severity": "critical"
            },
            {
                "name": "high_error_rate",
                "condition": "error_rate > 5",
                "duration": "2m",
                "severity": "critical"
            }
        ]
    },
    
    # 安全配置
    "security": {
        "tls_enabled": True,
        "tls_version": "1.2",
        "certificate_auto_renewal": True,
        "firewall_enabled": True,
        "vpc_enabled": True,
        "private_subnets": True,
        "security_groups": [
            {
                "name": "api-sg",
                "rules": [
                    {"port": 80, "protocol": "tcp", "source": "0.0.0.0/0"},
                    {"port": 443, "protocol": "tcp", "source": "0.0.0.0/0"},
                    {"port": 8000, "protocol": "tcp", "source": "10.0.0.0/8"}
                ]
            }
        ]
    },
    
    # 备份和恢复配置
    "backup_recovery": {
        "enabled": True,
        "backup_strategy": "incremental",  # full, incremental, differential
        "backup_frequency": "daily",
        "backup_retention": {
            "daily": 7,
            "weekly": 4,
            "monthly": 12
        },
        "cross_region_backup": True,
        "backup_encryption": True,
        "recovery_testing": True,
        "rto_target": 15,  # Recovery Time Objective (minutes)
        "rpo_target": 60   # Recovery Point Objective (minutes)
    }
}

def get_ha_config(section: str = None) -> Dict[str, Any]:
    """
    获取高可用配置
    
    Args:
        section: 配置节名称
        
    Returns:
        Dict[str, Any]: 配置字典
    """
    if section:
        return HIGH_AVAILABILITY_CONFIG.get(section, {})
    return HIGH_AVAILABILITY_CONFIG

def update_ha_config(section: str, config: Dict[str, Any]) -> bool:
    """
    更新高可用配置
    
    Args:
        section: 配置节名称
        config: 新配置
        
    Returns:
        bool: 是否更新成功
    """
    try:
        if section in HIGH_AVAILABILITY_CONFIG:
            HIGH_AVAILABILITY_CONFIG[section].update(config)
            return True
        return False
    except Exception:
        return False

def get_deployment_config() -> Dict[str, Any]:
    """获取部署配置"""
    return get_ha_config("deployment")

def get_load_balancer_config() -> Dict[str, Any]:
    """获取负载均衡配置"""
    return get_ha_config("load_balancer")

def get_service_discovery_config() -> Dict[str, Any]:
    """获取服务发现配置"""
    return get_ha_config("service_discovery")

def get_health_check_config() -> Dict[str, Any]:
    """获取健康检查配置"""
    return get_ha_config("health_check")

def get_auto_scaling_config() -> Dict[str, Any]:
    """获取自动扩缩容配置"""
    return get_ha_config("auto_scaling")

def is_ha_enabled() -> bool:
    """检查是否启用高可用"""
    deployment_config = get_deployment_config()
    return deployment_config.get("mode") != DeploymentMode.STANDALONE.value
