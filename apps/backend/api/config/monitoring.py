#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
监控系统配置
"""

import os
from typing import Dict, Any, List, Optional
from enum import Enum

# 监控级别
class MonitoringLevel(Enum):
    """监控级别枚举"""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

# 指标类型
class MetricType(Enum):
    """指标类型枚举"""
    COUNTER = "counter"        # 计数器
    GAUGE = "gauge"           # 仪表盘
    HISTOGRAM = "histogram"   # 直方图
    SUMMARY = "summary"       # 摘要

# 监控配置
MONITORING_CONFIG = {
    # 基础配置
    "enabled": os.getenv("MONITORING_ENABLED", "true").lower() == "true",
    "environment": os.getenv("ENVIRONMENT", "development"),
    "service_name": "recruitment-api",
    "service_version": "1.0.0",
    
    # Prometheus配置
    "prometheus": {
        "enabled": True,
        "host": os.getenv("PROMETHEUS_HOST", "localhost"),
        "port": int(os.getenv("PROMETHEUS_PORT", "9090")),
        "metrics_path": "/metrics",
        "scrape_interval": "15s",
        "evaluation_interval": "15s",
        "retention": "15d",
        "storage_path": "./prometheus_data"
    },
    
    # 应用指标配置
    "metrics": {
        "enabled": True,
        "port": int(os.getenv("METRICS_PORT", "8001")),
        "path": "/metrics",
        "include_default": True,
        "custom_labels": {
            "service": "recruitment-api",
            "environment": os.getenv("ENVIRONMENT", "development"),
            "version": "1.0.0"
        }
    },
    
    # 系统指标
    "system_metrics": {
        "enabled": True,
        "collect_interval": 15,  # 秒
        "metrics": [
            "cpu_usage",
            "memory_usage", 
            "disk_usage",
            "network_io",
            "process_count",
            "file_descriptors"
        ]
    },
    
    # 应用指标
    "application_metrics": {
        "enabled": True,
        "metrics": {
            # HTTP请求指标
            "http_requests_total": {
                "type": "counter",
                "description": "Total HTTP requests",
                "labels": ["method", "endpoint", "status_code"]
            },
            "http_request_duration_seconds": {
                "type": "histogram",
                "description": "HTTP request duration in seconds",
                "labels": ["method", "endpoint"],
                "buckets": [0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0]
            },
            "http_request_size_bytes": {
                "type": "histogram",
                "description": "HTTP request size in bytes",
                "labels": ["method", "endpoint"],
                "buckets": [100, 1000, 10000, 100000, 1000000]
            },
            "http_response_size_bytes": {
                "type": "histogram",
                "description": "HTTP response size in bytes",
                "labels": ["method", "endpoint"],
                "buckets": [100, 1000, 10000, 100000, 1000000]
            },
            
            # 数据库指标
            "database_connections_active": {
                "type": "gauge",
                "description": "Active database connections",
                "labels": ["database"]
            },
            "database_query_duration_seconds": {
                "type": "histogram",
                "description": "Database query duration in seconds",
                "labels": ["operation", "table"],
                "buckets": [0.01, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5]
            },
            "database_queries_total": {
                "type": "counter",
                "description": "Total database queries",
                "labels": ["operation", "table", "status"]
            },
            
            # Redis指标
            "redis_connections_active": {
                "type": "gauge",
                "description": "Active Redis connections",
                "labels": ["instance"]
            },
            "redis_operations_total": {
                "type": "counter",
                "description": "Total Redis operations",
                "labels": ["operation", "status"]
            },
            "redis_operation_duration_seconds": {
                "type": "histogram",
                "description": "Redis operation duration in seconds",
                "labels": ["operation"],
                "buckets": [0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25]
            },
            
            # 业务指标
            "jobs_total": {
                "type": "gauge",
                "description": "Total number of jobs",
                "labels": ["status"]
            },
            "users_total": {
                "type": "gauge",
                "description": "Total number of users",
                "labels": ["type"]
            },
            "searches_total": {
                "type": "counter",
                "description": "Total number of searches",
                "labels": ["type", "status"]
            },
            "rate_limit_hits_total": {
                "type": "counter",
                "description": "Total rate limit hits",
                "labels": ["level", "endpoint"]
            },
            
            # 错误指标
            "errors_total": {
                "type": "counter",
                "description": "Total number of errors",
                "labels": ["type", "severity"]
            },
            "exceptions_total": {
                "type": "counter",
                "description": "Total number of exceptions",
                "labels": ["type", "module"]
            }
        }
    },
    
    # 告警配置
    "alerting": {
        "enabled": True,
        "rules": {
            # 系统告警
            "high_cpu_usage": {
                "condition": "cpu_usage > 80",
                "duration": "5m",
                "severity": "warning",
                "message": "CPU usage is above 80%"
            },
            "high_memory_usage": {
                "condition": "memory_usage > 85",
                "duration": "5m", 
                "severity": "warning",
                "message": "Memory usage is above 85%"
            },
            "disk_space_low": {
                "condition": "disk_usage > 90",
                "duration": "1m",
                "severity": "critical",
                "message": "Disk space is above 90%"
            },
            
            # 应用告警
            "high_error_rate": {
                "condition": "rate(http_requests_total{status_code=~'5..'}[5m]) / rate(http_requests_total[5m]) > 0.05",
                "duration": "2m",
                "severity": "critical",
                "message": "Error rate is above 5%"
            },
            "high_response_time": {
                "condition": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2",
                "duration": "5m",
                "severity": "warning",
                "message": "95th percentile response time is above 2 seconds"
            },
            "database_connection_high": {
                "condition": "database_connections_active > 80",
                "duration": "2m",
                "severity": "warning",
                "message": "Database connections are above 80"
            },
            
            # 业务告警
            "rate_limit_high": {
                "condition": "rate(rate_limit_hits_total[5m]) > 10",
                "duration": "1m",
                "severity": "warning",
                "message": "Rate limit hits are high"
            }
        }
    },
    
    # 日志配置
    "logging": {
        "enabled": True,
        "level": os.getenv("LOG_LEVEL", "INFO"),
        "format": "json",
        "file": {
            "enabled": True,
            "path": "./logs/monitoring.log",
            "max_size": "100MB",
            "backup_count": 10
        },
        "console": {
            "enabled": True,
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        }
    },
    
    # 健康检查配置
    "health_checks": {
        "enabled": True,
        "endpoint": "/health",
        "checks": {
            "database": {
                "enabled": True,
                "timeout": 5,
                "critical": True
            },
            "redis": {
                "enabled": True,
                "timeout": 3,
                "critical": False
            },
            "elasticsearch": {
                "enabled": True,
                "timeout": 5,
                "critical": False
            },
            "external_apis": {
                "enabled": True,
                "timeout": 10,
                "critical": False
            }
        }
    },
    
    # 性能监控
    "performance": {
        "enabled": True,
        "profiling": {
            "enabled": False,  # 生产环境建议关闭
            "sample_rate": 0.01,
            "output_dir": "./profiles"
        },
        "tracing": {
            "enabled": True,
            "sample_rate": 0.1,
            "jaeger": {
                "enabled": False,
                "host": "localhost",
                "port": 14268
            }
        }
    },
    
    # 通知配置
    "notifications": {
        "enabled": True,
        "channels": {
            "email": {
                "enabled": False,
                "smtp_host": "",
                "smtp_port": 587,
                "username": "",
                "password": "",
                "recipients": []
            },
            "webhook": {
                "enabled": True,
                "url": os.getenv("WEBHOOK_URL", ""),
                "timeout": 10
            },
            "slack": {
                "enabled": False,
                "webhook_url": "",
                "channel": "#alerts"
            }
        }
    }
}

def get_monitoring_config(section: str = None) -> Dict[str, Any]:
    """
    获取监控配置
    
    Args:
        section: 配置节名称
        
    Returns:
        Dict[str, Any]: 配置字典
    """
    if section:
        return MONITORING_CONFIG.get(section, {})
    return MONITORING_CONFIG

def update_monitoring_config(section: str, config: Dict[str, Any]) -> bool:
    """
    更新监控配置
    
    Args:
        section: 配置节名称
        config: 新配置
        
    Returns:
        bool: 是否更新成功
    """
    try:
        if section in MONITORING_CONFIG:
            MONITORING_CONFIG[section].update(config)
            return True
        return False
    except Exception:
        return False

def is_monitoring_enabled() -> bool:
    """
    检查监控是否启用
    
    Returns:
        bool: 是否启用监控
    """
    return MONITORING_CONFIG.get("enabled", False)
