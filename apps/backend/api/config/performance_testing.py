#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
性能测试配置
"""

import os
from typing import Dict, Any, List, Optional
from enum import Enum

# 测试类型
class TestType(Enum):
    """测试类型枚举"""
    LOAD_TEST = "load_test"
    STRESS_TEST = "stress_test"
    SPIKE_TEST = "spike_test"
    VOLUME_TEST = "volume_test"
    ENDURANCE_TEST = "endurance_test"

# 测试工具
class TestTool(Enum):
    """测试工具枚举"""
    LOCUST = "locust"
    APACHE_BENCH = "apache_bench"
    WRK = "wrk"
    JMETER = "jmeter"

# 性能测试配置
PERFORMANCE_TESTING_CONFIG = {
    # 测试环境配置
    "test_environment": {
        "base_url": os.getenv("TEST_BASE_URL", "http://localhost:8000"),
        "api_prefix": "/api/v1",
        "timeout": int(os.getenv("TEST_TIMEOUT", "30")),
        "verify_ssl": False,
        "max_retries": 3,
        "retry_delay": 1.0
    },
    
    # 负载测试配置
    "load_test": {
        "enabled": True,
        "tool": TestTool.LOCUST.value,
        "test_duration": 300,  # 5分钟
        "ramp_up_time": 60,    # 1分钟
        "user_scenarios": [
            {"users": 10, "spawn_rate": 2},
            {"users": 50, "spawn_rate": 5},
            {"users": 100, "spawn_rate": 10},
            {"users": 500, "spawn_rate": 20}
        ],
        "think_time": {"min": 1, "max": 3},  # 用户思考时间
        "data_file": "test_data/performance_test_data.json"
    },
    
    # 压力测试配置
    "stress_test": {
        "enabled": True,
        "tool": TestTool.LOCUST.value,
        "test_duration": 600,  # 10分钟
        "max_users": 1000,
        "step_users": 100,
        "step_duration": 60,
        "break_point_detection": True
    },
    
    # 峰值测试配置
    "spike_test": {
        "enabled": True,
        "tool": TestTool.LOCUST.value,
        "normal_load": 50,
        "spike_load": 500,
        "spike_duration": 60,
        "recovery_time": 120
    },
    
    # 容量测试配置
    "volume_test": {
        "enabled": True,
        "data_volumes": [1000, 10000, 100000, 1000000],
        "test_operations": ["search", "filter", "pagination"],
        "concurrent_users": 50
    },
    
    # 耐久性测试配置
    "endurance_test": {
        "enabled": False,
        "duration": 7200,  # 2小时
        "users": 100,
        "memory_leak_detection": True,
        "resource_monitoring": True
    },
    
    # 测试场景配置
    "test_scenarios": {
        "user_authentication": {
            "weight": 20,
            "endpoints": [
                {"method": "POST", "path": "/auth/login", "weight": 60},
                {"method": "POST", "path": "/auth/logout", "weight": 20},
                {"method": "GET", "path": "/auth/profile", "weight": 20}
            ]
        },
        "job_search": {
            "weight": 40,
            "endpoints": [
                {"method": "GET", "path": "/jobs", "weight": 50},
                {"method": "GET", "path": "/jobs/search", "weight": 30},
                {"method": "GET", "path": "/jobs/{id}", "weight": 20}
            ]
        },
        "data_query": {
            "weight": 30,
            "endpoints": [
                {"method": "GET", "path": "/companies", "weight": 30},
                {"method": "GET", "path": "/categories", "weight": 20},
                {"method": "GET", "path": "/locations", "weight": 20},
                {"method": "GET", "path": "/statistics", "weight": 30}
            ]
        },
        "admin_operations": {
            "weight": 10,
            "endpoints": [
                {"method": "GET", "path": "/admin/dashboard", "weight": 40},
                {"method": "GET", "path": "/admin/users", "weight": 30},
                {"method": "GET", "path": "/admin/jobs", "weight": 30}
            ]
        }
    },
    
    # 性能指标配置
    "performance_metrics": {
        "response_time": {
            "target_avg": 200,      # 平均响应时间目标 (ms)
            "target_p95": 500,      # P95响应时间目标 (ms)
            "target_p99": 1000,     # P99响应时间目标 (ms)
            "max_acceptable": 5000   # 最大可接受响应时间 (ms)
        },
        "throughput": {
            "target_qps": 100,      # 目标QPS
            "min_acceptable": 50    # 最小可接受QPS
        },
        "error_rate": {
            "max_acceptable": 1.0   # 最大可接受错误率 (%)
        },
        "resource_usage": {
            "cpu_threshold": 80,    # CPU使用率阈值 (%)
            "memory_threshold": 85, # 内存使用率阈值 (%)
            "disk_io_threshold": 80 # 磁盘I/O阈值 (%)
        }
    },
    
    # 监控配置
    "monitoring": {
        "enabled": True,
        "interval": 5,  # 监控间隔 (秒)
        "metrics": [
            "cpu_usage",
            "memory_usage",
            "disk_io",
            "network_io",
            "database_connections",
            "redis_connections",
            "response_times",
            "error_rates"
        ],
        "alerts": {
            "high_cpu": {"threshold": 90, "duration": 30},
            "high_memory": {"threshold": 95, "duration": 30},
            "high_error_rate": {"threshold": 5, "duration": 10}
        }
    },
    
    # 数据库性能测试配置
    "database_performance": {
        "enabled": True,
        "connection_pool_test": True,
        "query_performance_test": True,
        "slow_query_threshold": 1.0,  # 慢查询阈值 (秒)
        "test_queries": [
            {
                "name": "job_search",
                "query": "SELECT * FROM jobs WHERE status = 'active' ORDER BY created_at DESC LIMIT 20",
                "expected_time": 0.1
            },
            {
                "name": "user_lookup",
                "query": "SELECT * FROM users WHERE email = %s",
                "expected_time": 0.05
            },
            {
                "name": "job_count",
                "query": "SELECT COUNT(*) FROM jobs WHERE status = 'active'",
                "expected_time": 0.2
            }
        ]
    },
    
    # Redis性能测试配置
    "redis_performance": {
        "enabled": True,
        "operations": [
            {"type": "GET", "weight": 70},
            {"type": "SET", "weight": 20},
            {"type": "DEL", "weight": 10}
        ],
        "key_patterns": [
            "user:session:{id}",
            "job:cache:{id}",
            "search:result:{hash}"
        ],
        "target_hit_rate": 90,  # 目标缓存命中率 (%)
        "target_response_time": 1  # 目标响应时间 (ms)
    },
    
    # 报告配置
    "reporting": {
        "enabled": True,
        "output_dir": "performance_reports",
        "formats": ["html", "json", "csv"],
        "include_charts": True,
        "include_recommendations": True,
        "auto_upload": False
    },
    
    # 测试数据配置
    "test_data": {
        "users": {
            "count": 1000,
            "username_pattern": "testuser_{id}",
            "email_pattern": "testuser_{id}@example.com",
            "password": "testpass123"
        },
        "jobs": {
            "count": 10000,
            "title_patterns": [
                "Python开发工程师",
                "Java后端开发",
                "前端工程师",
                "数据分析师",
                "产品经理"
            ],
            "companies": ["阿里巴巴", "腾讯", "百度", "字节跳动", "美团"]
        },
        "search_keywords": [
            "Python", "Java", "前端", "后端", "数据分析",
            "产品经理", "UI设计", "测试工程师", "运维工程师"
        ]
    }
}

def get_performance_config(section: str = None) -> Dict[str, Any]:
    """
    获取性能测试配置
    
    Args:
        section: 配置节名称
        
    Returns:
        Dict[str, Any]: 配置字典
    """
    if section:
        return PERFORMANCE_TESTING_CONFIG.get(section, {})
    return PERFORMANCE_TESTING_CONFIG

def update_performance_config(section: str, config: Dict[str, Any]) -> bool:
    """
    更新性能测试配置
    
    Args:
        section: 配置节名称
        config: 新配置
        
    Returns:
        bool: 是否更新成功
    """
    try:
        if section in PERFORMANCE_TESTING_CONFIG:
            PERFORMANCE_TESTING_CONFIG[section].update(config)
            return True
        return False
    except Exception:
        return False

def get_test_scenarios() -> Dict[str, Any]:
    """获取测试场景配置"""
    return get_performance_config("test_scenarios")

def get_performance_targets() -> Dict[str, Any]:
    """获取性能目标配置"""
    return get_performance_config("performance_metrics")

def get_monitoring_config() -> Dict[str, Any]:
    """获取监控配置"""
    return get_performance_config("monitoring")

def is_performance_testing_enabled() -> bool:
    """检查是否启用性能测试"""
    load_test_config = get_performance_config("load_test")
    return load_test_config.get("enabled", False)
