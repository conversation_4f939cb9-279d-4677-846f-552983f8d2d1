#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
API限流配置
"""

import os
from typing import Dict, Any, List, Optional
from enum import Enum

# 限流策略类型
class RateLimitStrategy(Enum):
    """限流策略枚举"""
    FIXED_WINDOW = "fixed_window"      # 固定窗口
    SLIDING_WINDOW = "sliding_window"  # 滑动窗口
    TOKEN_BUCKET = "token_bucket"      # 令牌桶
    LEAKY_BUCKET = "leaky_bucket"      # 漏桶

# 限流级别
class RateLimitLevel(Enum):
    """限流级别枚举"""
    GLOBAL = "global"        # 全局限流
    IP = "ip"               # IP限流
    USER = "user"           # 用户限流
    API_KEY = "api_key"     # API密钥限流
    ENDPOINT = "endpoint"   # 端点限流

# 限流配置
RATE_LIMIT_CONFIG = {
    # 全局配置
    "enabled": os.getenv("RATE_LIMIT_ENABLED", "true").lower() == "true",
    "strategy": RateLimitStrategy.SLIDING_WINDOW.value,
    "storage": "redis",  # redis, memory
    "key_prefix": "rate_limit:",
    
    # 默认限制
    "default_limits": {
        "requests_per_minute": 60,
        "requests_per_hour": 1000,
        "requests_per_day": 10000,
        "burst_size": 10,  # 突发请求数
    },
    
    # IP级别限流
    "ip_limits": {
        "enabled": True,
        "requests_per_minute": 100,
        "requests_per_hour": 2000,
        "requests_per_day": 20000,
        "burst_size": 20,
        "whitelist": [
            "127.0.0.1",
            "::1",
            "localhost"
        ],
        "blacklist": []
    },
    
    # 用户级别限流
    "user_limits": {
        "enabled": True,
        "authenticated": {
            "requests_per_minute": 200,
            "requests_per_hour": 5000,
            "requests_per_day": 50000,
            "burst_size": 50,
        },
        "anonymous": {
            "requests_per_minute": 30,
            "requests_per_hour": 500,
            "requests_per_day": 2000,
            "burst_size": 5,
        }
    },
    
    # API端点特定限流
    "endpoint_limits": {
        # 认证相关
        "/api/v1/auth/login": {
            "requests_per_minute": 5,
            "requests_per_hour": 20,
            "requests_per_day": 100,
            "burst_size": 2,
        },
        "/api/v1/auth/register": {
            "requests_per_minute": 3,
            "requests_per_hour": 10,
            "requests_per_day": 50,
            "burst_size": 1,
        },
        "/api/v1/auth/reset-password": {
            "requests_per_minute": 2,
            "requests_per_hour": 5,
            "requests_per_day": 20,
            "burst_size": 1,
        },
        
        # 搜索相关
        "/api/v1/jobs": {
            "requests_per_minute": 100,
            "requests_per_hour": 1000,
            "requests_per_day": 5000,
            "burst_size": 20,
        },
        "/api/v1/jobs/search": {
            "requests_per_minute": 50,
            "requests_per_hour": 500,
            "requests_per_day": 2000,
            "burst_size": 10,
        },
        
        # 管理员API
        "/api/v1/admin/*": {
            "requests_per_minute": 300,
            "requests_per_hour": 3000,
            "requests_per_day": 30000,
            "burst_size": 100,
        },
        
        # 文件上传
        "/api/v1/upload": {
            "requests_per_minute": 10,
            "requests_per_hour": 50,
            "requests_per_day": 200,
            "burst_size": 3,
        },
        
        # 数据导出
        "/api/v1/export/*": {
            "requests_per_minute": 2,
            "requests_per_hour": 10,
            "requests_per_day": 50,
            "burst_size": 1,
        }
    },
    
    # 特殊用户组限流
    "user_group_limits": {
        "admin": {
            "requests_per_minute": 500,
            "requests_per_hour": 10000,
            "requests_per_day": 100000,
            "burst_size": 200,
        },
        "vip": {
            "requests_per_minute": 300,
            "requests_per_hour": 8000,
            "requests_per_day": 80000,
            "burst_size": 100,
        },
        "premium": {
            "requests_per_minute": 200,
            "requests_per_hour": 6000,
            "requests_per_day": 60000,
            "burst_size": 80,
        }
    },
    
    # 限流响应配置
    "response_config": {
        "status_code": 429,
        "message": "请求过于频繁，请稍后再试",
        "headers": {
            "Retry-After": "60",
            "X-RateLimit-Limit": "",
            "X-RateLimit-Remaining": "",
            "X-RateLimit-Reset": ""
        }
    },
    
    # 限流算法参数
    "algorithm_config": {
        "sliding_window": {
            "window_size": 60,  # 窗口大小（秒）
            "sub_windows": 6,   # 子窗口数量
        },
        "token_bucket": {
            "capacity": 100,    # 桶容量
            "refill_rate": 10,  # 令牌补充速率（每秒）
        },
        "leaky_bucket": {
            "capacity": 100,    # 桶容量
            "leak_rate": 10,    # 漏出速率（每秒）
        }
    },
    
    # 监控配置
    "monitoring": {
        "enabled": True,
        "log_violations": True,
        "metrics_enabled": True,
        "alert_threshold": 0.8,  # 达到限制的80%时告警
    }
}

# 限流规则匹配器
class RateLimitMatcher:
    """限流规则匹配器"""
    
    @staticmethod
    def match_endpoint(path: str) -> Optional[Dict[str, Any]]:
        """
        匹配端点限流规则
        
        Args:
            path: 请求路径
            
        Returns:
            Optional[Dict[str, Any]]: 匹配的限流规则
        """
        endpoint_limits = RATE_LIMIT_CONFIG["endpoint_limits"]
        
        # 精确匹配
        if path in endpoint_limits:
            return endpoint_limits[path]
        
        # 通配符匹配
        for pattern, limits in endpoint_limits.items():
            if pattern.endswith("*"):
                prefix = pattern[:-1]
                if path.startswith(prefix):
                    return limits
        
        return None
    
    @staticmethod
    def get_user_group_limits(user_role: str) -> Optional[Dict[str, Any]]:
        """
        获取用户组限流规则
        
        Args:
            user_role: 用户角色
            
        Returns:
            Optional[Dict[str, Any]]: 用户组限流规则
        """
        user_group_limits = RATE_LIMIT_CONFIG["user_group_limits"]
        return user_group_limits.get(user_role)
    
    @staticmethod
    def is_ip_whitelisted(ip: str) -> bool:
        """
        检查IP是否在白名单中
        
        Args:
            ip: IP地址
            
        Returns:
            bool: 是否在白名单中
        """
        whitelist = RATE_LIMIT_CONFIG["ip_limits"]["whitelist"]
        return ip in whitelist
    
    @staticmethod
    def is_ip_blacklisted(ip: str) -> bool:
        """
        检查IP是否在黑名单中
        
        Args:
            ip: IP地址
            
        Returns:
            bool: 是否在黑名单中
        """
        blacklist = RATE_LIMIT_CONFIG["ip_limits"]["blacklist"]
        return ip in blacklist

def get_rate_limit_config(section: str = None) -> Dict[str, Any]:
    """
    获取限流配置
    
    Args:
        section: 配置节名称
        
    Returns:
        Dict[str, Any]: 配置字典
    """
    if section:
        return RATE_LIMIT_CONFIG.get(section, {})
    return RATE_LIMIT_CONFIG

def update_rate_limit_config(section: str, config: Dict[str, Any]) -> bool:
    """
    更新限流配置
    
    Args:
        section: 配置节名称
        config: 新配置
        
    Returns:
        bool: 是否更新成功
    """
    try:
        if section in RATE_LIMIT_CONFIG:
            RATE_LIMIT_CONFIG[section].update(config)
            return True
        return False
    except Exception:
        return False
