#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Redis缓存配置和管理
"""

import os
import json
import time
import hashlib
from typing import Any, Optional, Dict, List
from functools import wraps

from apps.backend.api.config.redis import get_redis
from apps.backend.utils.logger import setup_logger

logger = setup_logger("redis_cache")


class CacheManager:
    """缓存管理器"""
    
    def __init__(self):
        """初始化缓存管理器"""
        self.redis_client = get_redis()
        self.default_ttl = 300  # 5分钟默认过期时间
        self.key_prefix = "recruitment:"
        
    def _make_key(self, key: str, namespace: str = "default") -> str:
        """生成缓存键"""
        return f"{self.key_prefix}{namespace}:{key}"
    
    def get(self, key: str, namespace: str = "default") -> Optional[Any]:
        """获取缓存值"""
        try:
            if not self.redis_client:
                return None
                
            cache_key = self._make_key(key, namespace)
            value = self.redis_client.get(cache_key)
            
            if value is None:
                return None
                
            # 尝试解析JSON
            try:
                return json.loads(value)
            except json.JSONDecodeError:
                return value.decode('utf-8') if isinstance(value, bytes) else value
                
        except Exception as e:
            logger.error(f"Error getting cache key {key}: {e}")
            return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None, namespace: str = "default") -> bool:
        """设置缓存值"""
        try:
            if not self.redis_client:
                return False
                
            cache_key = self._make_key(key, namespace)
            ttl = ttl or self.default_ttl
            
            # 序列化值
            if isinstance(value, (dict, list)):
                cache_value = json.dumps(value, ensure_ascii=False)
            else:
                cache_value = str(value)
            
            # 设置缓存
            result = self.redis_client.setex(cache_key, ttl, cache_value)
            return bool(result)
            
        except Exception as e:
            logger.error(f"Error setting cache key {key}: {e}")
            return False
    
    def delete(self, key: str, namespace: str = "default") -> bool:
        """删除缓存值"""
        try:
            if not self.redis_client:
                return False
                
            cache_key = self._make_key(key, namespace)
            result = self.redis_client.delete(cache_key)
            return bool(result)
            
        except Exception as e:
            logger.error(f"Error deleting cache key {key}: {e}")
            return False
    
    def exists(self, key: str, namespace: str = "default") -> bool:
        """检查缓存键是否存在"""
        try:
            if not self.redis_client:
                return False
                
            cache_key = self._make_key(key, namespace)
            return bool(self.redis_client.exists(cache_key))
            
        except Exception as e:
            logger.error(f"Error checking cache key {key}: {e}")
            return False
    
    def clear_namespace(self, namespace: str) -> int:
        """清空指定命名空间的所有缓存"""
        try:
            if not self.redis_client:
                return 0
                
            pattern = self._make_key("*", namespace)
            keys = self.redis_client.keys(pattern)
            
            if keys:
                return self.redis_client.delete(*keys)
            return 0
            
        except Exception as e:
            logger.error(f"Error clearing namespace {namespace}: {e}")
            return 0
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            if not self.redis_client:
                return {"error": "Redis not available"}
                
            info = self.redis_client.info()
            
            return {
                "connected_clients": info.get("connected_clients", 0),
                "used_memory": info.get("used_memory", 0),
                "used_memory_human": info.get("used_memory_human", "0B"),
                "keyspace_hits": info.get("keyspace_hits", 0),
                "keyspace_misses": info.get("keyspace_misses", 0),
                "hit_rate": self._calculate_hit_rate(info),
                "total_commands_processed": info.get("total_commands_processed", 0)
            }
            
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return {"error": str(e)}
    
    def _calculate_hit_rate(self, info: Dict[str, Any]) -> float:
        """计算缓存命中率"""
        hits = info.get("keyspace_hits", 0)
        misses = info.get("keyspace_misses", 0)
        total = hits + misses
        
        if total == 0:
            return 0.0
        
        return round((hits / total) * 100, 2)


# 全局缓存管理器实例
cache_manager = CacheManager()


def cache_result(ttl: int = 300, namespace: str = "default", key_func: Optional[callable] = None):
    """
    缓存装饰器
    
    Args:
        ttl: 缓存过期时间（秒）
        namespace: 缓存命名空间
        key_func: 自定义键生成函数
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                # 默认键生成策略
                func_name = func.__name__
                args_str = str(args) + str(sorted(kwargs.items()))
                key_hash = hashlib.md5(args_str.encode()).hexdigest()[:8]
                cache_key = f"{func_name}:{key_hash}"
            
            # 尝试从缓存获取
            cached_result = cache_manager.get(cache_key, namespace)
            if cached_result is not None:
                logger.debug(f"Cache hit for key: {cache_key}")
                return cached_result
            
            # 执行函数
            result = await func(*args, **kwargs)
            
            # 缓存结果
            if result is not None:
                cache_manager.set(cache_key, result, ttl, namespace)
                logger.debug(f"Cached result for key: {cache_key}")
            
            return result
        
        return wrapper
    return decorator


def invalidate_cache(keys: List[str], namespace: str = "default"):
    """
    缓存失效装饰器
    
    Args:
        keys: 要失效的缓存键列表
        namespace: 缓存命名空间
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 执行函数
            result = await func(*args, **kwargs)
            
            # 失效缓存
            for key in keys:
                cache_manager.delete(key, namespace)
                logger.debug(f"Invalidated cache key: {key}")
            
            return result
        
        return wrapper
    return decorator


# 预定义的缓存配置
CACHE_CONFIGS = {
    "jobs": {
        "namespace": "jobs",
        "ttl": 300,  # 5分钟
        "description": "岗位相关缓存"
    },
    "categories": {
        "namespace": "categories", 
        "ttl": 3600,  # 1小时
        "description": "分类相关缓存"
    },
    "users": {
        "namespace": "users",
        "ttl": 1800,  # 30分钟
        "description": "用户相关缓存"
    },
    "search": {
        "namespace": "search",
        "ttl": 600,  # 10分钟
        "description": "搜索结果缓存"
    },
    "api_response": {
        "namespace": "api",
        "ttl": 300,  # 5分钟
        "description": "API响应缓存"
    }
}


def get_cache_config(cache_type: str) -> Dict[str, Any]:
    """获取缓存配置"""
    return CACHE_CONFIGS.get(cache_type, {
        "namespace": "default",
        "ttl": 300,
        "description": "默认缓存配置"
    })


# 便捷的缓存函数
def cache_jobs_list(key: str, data: Any, ttl: int = 300) -> bool:
    """缓存岗位列表"""
    return cache_manager.set(key, data, ttl, "jobs")


def get_cached_jobs_list(key: str) -> Optional[Any]:
    """获取缓存的岗位列表"""
    return cache_manager.get(key, "jobs")


def cache_job_detail(job_id: str, data: Any, ttl: int = 1800) -> bool:
    """缓存岗位详情"""
    return cache_manager.set(f"detail:{job_id}", data, ttl, "jobs")


def get_cached_job_detail(job_id: str) -> Optional[Any]:
    """获取缓存的岗位详情"""
    return cache_manager.get(f"detail:{job_id}", "jobs")


def cache_categories(data: Any, ttl: int = 3600) -> bool:
    """缓存分类数据"""
    return cache_manager.set("all_categories", data, ttl, "categories")


def get_cached_categories() -> Optional[Any]:
    """获取缓存的分类数据"""
    return cache_manager.get("all_categories", "categories")


def cache_search_result(query_hash: str, data: Any, ttl: int = 600) -> bool:
    """缓存搜索结果"""
    return cache_manager.set(f"result:{query_hash}", data, ttl, "search")


def get_cached_search_result(query_hash: str) -> Optional[Any]:
    """获取缓存的搜索结果"""
    return cache_manager.get(f"result:{query_hash}", "search")


def clear_jobs_cache() -> int:
    """清空岗位相关缓存"""
    return cache_manager.clear_namespace("jobs")


def clear_categories_cache() -> int:
    """清空分类相关缓存"""
    return cache_manager.clear_namespace("categories")


def clear_search_cache() -> int:
    """清空搜索相关缓存"""
    return cache_manager.clear_namespace("search")


def clear_all_cache() -> Dict[str, int]:
    """清空所有缓存"""
    results = {}
    for cache_type in CACHE_CONFIGS:
        namespace = CACHE_CONFIGS[cache_type]["namespace"]
        results[namespace] = cache_manager.clear_namespace(namespace)
    return results


def get_cache_health() -> Dict[str, Any]:
    """获取缓存健康状态"""
    try:
        if not cache_manager.redis_client:
            return {
                "status": "unavailable",
                "message": "Redis client not available"
            }
        
        # 测试连接
        cache_manager.redis_client.ping()
        
        # 获取统计信息
        stats = cache_manager.get_stats()
        
        return {
            "status": "healthy",
            "stats": stats,
            "message": "Cache is working properly"
        }
        
    except Exception as e:
        return {
            "status": "error",
            "message": f"Cache health check failed: {str(e)}"
        }
