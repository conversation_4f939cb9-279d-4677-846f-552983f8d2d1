#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
安全配置
"""

import os
import re
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta

# 安全配置
SECURITY_CONFIG = {
    # 密码策略
    "password": {
        "min_length": 8,
        "max_length": 128,
        "require_uppercase": True,
        "require_lowercase": True,
        "require_digit": True,
        "require_special": True,
        "special_chars": "!@#$%^&*()_+-=[]{}|;:,.<>?",
        "max_attempts": 5,
        "lockout_duration": 1800,  # 30分钟
    },
    
    # JWT配置
    "jwt": {
        "algorithm": "HS256",
        "access_token_expire": 3600,  # 1小时
        "refresh_token_expire": 86400 * 7,  # 7天
        "secret_key": os.getenv("JWT_SECRET_KEY", "your-secret-key-change-in-production"),
    },
    
    # 输入验证
    "input_validation": {
        "max_string_length": 10000,
        "max_list_length": 1000,
        "max_file_size": 10 * 1024 * 1024,  # 10MB
        "allowed_file_types": [".jpg", ".jpeg", ".png", ".gif", ".pdf", ".doc", ".docx"],
    },
    
    # XSS防护
    "xss_protection": {
        "enabled": True,
        "escape_html": True,
        "allowed_tags": ["b", "i", "u", "strong", "em", "p", "br", "ul", "ol", "li"],
        "allowed_attributes": ["class", "id"],
    },
    
    # CSRF防护
    "csrf_protection": {
        "enabled": True,
        "token_length": 32,
        "token_expire": 3600,  # 1小时
    },
    
    # 速率限制
    "rate_limiting": {
        "enabled": True,
        "default_limit": "100/hour",
        "login_limit": "10/minute",
        "api_limit": "1000/hour",
        "search_limit": "200/hour",
    },
    
    # IP白名单/黑名单
    "ip_filtering": {
        "enabled": False,
        "whitelist": [],
        "blacklist": [],
    },
    
    # 安全头
    "security_headers": {
        "x_content_type_options": "nosniff",
        "x_frame_options": "DENY",
        "x_xss_protection": "1; mode=block",
        "strict_transport_security": "max-age=31536000; includeSubDomains",
        "content_security_policy": "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'",
        "referrer_policy": "strict-origin-when-cross-origin",
    },
}

# 危险字符模式
DANGEROUS_PATTERNS = [
    # SQL注入模式
    r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)",
    r"(\b(OR|AND)\s+\d+\s*=\s*\d+)",
    r"(\b(OR|AND)\s+['\"]?\w+['\"]?\s*=\s*['\"]?\w+['\"]?)",
    r"(--|#|/\*|\*/)",
    r"(\bxp_cmdshell\b|\bsp_executesql\b)",
    
    # XSS模式
    r"(<script[^>]*>.*?</script>)",
    r"(<iframe[^>]*>.*?</iframe>)",
    r"(<object[^>]*>.*?</object>)",
    r"(<embed[^>]*>.*?</embed>)",
    r"(<link[^>]*>)",
    r"(<meta[^>]*>)",
    r"(javascript:|vbscript:|data:)",
    r"(on\w+\s*=)",
    
    # 路径遍历
    r"(\.\./|\.\.\\)",
    r"(/etc/passwd|/etc/shadow)",
    r"(\\windows\\system32)",
    
    # 命令注入
    r"(\b(cat|ls|dir|type|copy|del|rm|mv|cp)\b)",
    r"(\||&|;|`|\$\(|\$\{)",
    r"(\bnc\b|\bnetcat\b|\bwget\b|\bcurl\b)",
]

# 编译正则表达式
COMPILED_PATTERNS = [re.compile(pattern, re.IGNORECASE) for pattern in DANGEROUS_PATTERNS]

# 敏感字段列表
SENSITIVE_FIELDS = [
    "password", "passwd", "pwd", "secret", "token", "key", "api_key",
    "access_token", "refresh_token", "session_id", "cookie", "auth",
    "authorization", "credential", "private_key", "public_key"
]

# 日志脱敏配置
LOG_MASKING_CONFIG = {
    "enabled": True,
    "mask_char": "*",
    "mask_length": 8,
    "sensitive_fields": SENSITIVE_FIELDS,
    "patterns": [
        r"\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b",  # 信用卡号
        r"\b\d{3}-\d{2}-\d{4}\b",  # 社会保险号
        r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b",  # 邮箱
        r"\b1[3-9]\d{9}\b",  # 手机号
        r"\b\d{15,19}\b",  # 银行卡号
    ]
}

# 文件上传安全配置
FILE_UPLOAD_CONFIG = {
    "max_file_size": 10 * 1024 * 1024,  # 10MB
    "allowed_extensions": [".jpg", ".jpeg", ".png", ".gif", ".pdf", ".doc", ".docx", ".txt"],
    "forbidden_extensions": [".exe", ".bat", ".cmd", ".com", ".pif", ".scr", ".vbs", ".js"],
    "scan_content": True,
    "quarantine_suspicious": True,
    "upload_path": "uploads/",
    "temp_path": "temp/",
}

# 会话安全配置
SESSION_CONFIG = {
    "secure": True,  # HTTPS only
    "httponly": True,  # 防止XSS访问cookie
    "samesite": "strict",  # CSRF防护
    "max_age": 3600,  # 1小时
    "regenerate_on_login": True,  # 登录时重新生成session ID
    "invalidate_on_logout": True,  # 登出时销毁session
}

# 数据库安全配置
DATABASE_SECURITY_CONFIG = {
    "use_prepared_statements": True,
    "escape_special_chars": True,
    "validate_input": True,
    "log_queries": False,  # 生产环境建议关闭
    "connection_encryption": True,
    "connection_timeout": 30,
    "max_connections": 100,
}

# API安全配置
API_SECURITY_CONFIG = {
    "require_authentication": True,
    "validate_content_type": True,
    "max_request_size": 10 * 1024 * 1024,  # 10MB
    "timeout": 30,
    "cors_origins": ["http://localhost:3000", "https://yourdomain.com"],
    "cors_methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    "cors_headers": ["Content-Type", "Authorization", "X-Requested-With"],
}

# 审计日志配置
AUDIT_LOG_CONFIG = {
    "enabled": True,
    "log_level": "INFO",
    "log_file": "logs/audit.log",
    "max_file_size": 100 * 1024 * 1024,  # 100MB
    "backup_count": 10,
    "log_format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "events_to_log": [
        "user_login", "user_logout", "user_register", "password_change",
        "admin_login", "admin_action", "data_access", "data_modification",
        "security_violation", "error_occurred", "system_event"
    ]
}

# 加密配置
ENCRYPTION_CONFIG = {
    "algorithm": "AES-256-GCM",
    "key_derivation": "PBKDF2",
    "iterations": 100000,
    "salt_length": 32,
    "iv_length": 16,
    "tag_length": 16,
}

def get_security_config(section: str) -> Dict[str, Any]:
    """
    获取安全配置
    
    Args:
        section: 配置节名称
        
    Returns:
        Dict[str, Any]: 配置字典
    """
    return SECURITY_CONFIG.get(section, {})

def is_sensitive_field(field_name: str) -> bool:
    """
    检查字段是否为敏感字段
    
    Args:
        field_name: 字段名称
        
    Returns:
        bool: 是否为敏感字段
    """
    return field_name.lower() in SENSITIVE_FIELDS

def mask_sensitive_data(data: str, field_name: str = "") -> str:
    """
    脱敏敏感数据
    
    Args:
        data: 原始数据
        field_name: 字段名称
        
    Returns:
        str: 脱敏后的数据
    """
    if not LOG_MASKING_CONFIG["enabled"]:
        return data
    
    # 检查是否为敏感字段
    if field_name and is_sensitive_field(field_name):
        mask_char = LOG_MASKING_CONFIG["mask_char"]
        mask_length = LOG_MASKING_CONFIG["mask_length"]
        return mask_char * mask_length
    
    # 使用正则表达式脱敏
    masked_data = data
    for pattern in LOG_MASKING_CONFIG["patterns"]:
        masked_data = re.sub(pattern, lambda m: "*" * len(m.group()), masked_data)
    
    return masked_data
