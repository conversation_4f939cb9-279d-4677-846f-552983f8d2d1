#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
安全审计配置
"""

import os
from typing import Dict, Any, List, Optional
from enum import Enum

# 安全级别
class SecurityLevel(Enum):
    """安全级别枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

# 审计类型
class AuditType(Enum):
    """审计类型枚举"""
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    INPUT_VALIDATION = "input_validation"
    SQL_INJECTION = "sql_injection"
    XSS = "xss"
    CSRF = "csrf"
    SESSION_SECURITY = "session_security"
    PASSWORD_SECURITY = "password_security"
    API_SECURITY = "api_security"
    FILE_SECURITY = "file_security"
    NETWORK_SECURITY = "network_security"
    CONFIGURATION = "configuration"

# 安全审计配置
SECURITY_AUDIT_CONFIG = {
    # 审计环境配置
    "audit_environment": {
        "target_url": os.getenv("AUDIT_TARGET_URL", "http://localhost:8000"),
        "api_prefix": "/api/v1",
        "timeout": int(os.getenv("AUDIT_TIMEOUT", "30")),
        "max_retries": 3,
        "verify_ssl": True,
        "user_agent": "SecurityAudit/1.0"
    },
    
    # 认证安全审计
    "authentication_audit": {
        "enabled": True,
        "test_cases": [
            {
                "name": "weak_password_test",
                "description": "测试弱密码策略",
                "severity": SecurityLevel.HIGH.value,
                "test_passwords": ["123456", "password", "admin", "123", "qwerty"]
            },
            {
                "name": "brute_force_protection",
                "description": "测试暴力破解保护",
                "severity": SecurityLevel.CRITICAL.value,
                "max_attempts": 5,
                "lockout_time": 300
            },
            {
                "name": "session_management",
                "description": "测试会话管理安全",
                "severity": SecurityLevel.HIGH.value,
                "session_timeout": 3600,
                "secure_cookies": True
            },
            {
                "name": "password_encryption",
                "description": "测试密码加密存储",
                "severity": SecurityLevel.CRITICAL.value,
                "hash_algorithms": ["bcrypt", "scrypt", "argon2"]
            }
        ]
    },
    
    # 授权安全审计
    "authorization_audit": {
        "enabled": True,
        "test_cases": [
            {
                "name": "privilege_escalation",
                "description": "测试权限提升漏洞",
                "severity": SecurityLevel.CRITICAL.value,
                "test_roles": ["user", "admin", "guest"]
            },
            {
                "name": "access_control",
                "description": "测试访问控制机制",
                "severity": SecurityLevel.HIGH.value,
                "protected_endpoints": [
                    "/api/v1/admin/*",
                    "/api/v1/users/*/profile",
                    "/api/v1/jobs/*/edit"
                ]
            },
            {
                "name": "jwt_security",
                "description": "测试JWT令牌安全",
                "severity": SecurityLevel.HIGH.value,
                "token_expiry": 3600,
                "secret_strength": 256
            }
        ]
    },
    
    # 输入验证审计
    "input_validation_audit": {
        "enabled": True,
        "test_cases": [
            {
                "name": "sql_injection",
                "description": "测试SQL注入漏洞",
                "severity": SecurityLevel.CRITICAL.value,
                "payloads": [
                    "' OR '1'='1",
                    "'; DROP TABLE users; --",
                    "' UNION SELECT * FROM users --",
                    "1' AND (SELECT COUNT(*) FROM users) > 0 --"
                ]
            },
            {
                "name": "xss_injection",
                "description": "测试XSS注入漏洞",
                "severity": SecurityLevel.HIGH.value,
                "payloads": [
                    "<script>alert('XSS')</script>",
                    "javascript:alert('XSS')",
                    "<img src=x onerror=alert('XSS')>",
                    "';alert('XSS');//"
                ]
            },
            {
                "name": "command_injection",
                "description": "测试命令注入漏洞",
                "severity": SecurityLevel.CRITICAL.value,
                "payloads": [
                    "; ls -la",
                    "| cat /etc/passwd",
                    "&& whoami",
                    "`id`"
                ]
            },
            {
                "name": "path_traversal",
                "description": "测试路径遍历漏洞",
                "severity": SecurityLevel.HIGH.value,
                "payloads": [
                    "../../../etc/passwd",
                    "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
                    "....//....//....//etc/passwd"
                ]
            }
        ]
    },
    
    # API安全审计
    "api_security_audit": {
        "enabled": True,
        "test_cases": [
            {
                "name": "rate_limiting",
                "description": "测试API限流机制",
                "severity": SecurityLevel.MEDIUM.value,
                "requests_per_minute": 100,
                "burst_requests": 200
            },
            {
                "name": "cors_policy",
                "description": "测试CORS策略配置",
                "severity": SecurityLevel.MEDIUM.value,
                "allowed_origins": ["http://localhost:3000"],
                "dangerous_headers": ["*"]
            },
            {
                "name": "http_methods",
                "description": "测试HTTP方法安全",
                "severity": SecurityLevel.MEDIUM.value,
                "dangerous_methods": ["TRACE", "OPTIONS", "DELETE"]
            },
            {
                "name": "information_disclosure",
                "description": "测试信息泄露",
                "severity": SecurityLevel.HIGH.value,
                "sensitive_headers": ["Server", "X-Powered-By", "X-AspNet-Version"]
            }
        ]
    },
    
    # 文件安全审计
    "file_security_audit": {
        "enabled": True,
        "test_cases": [
            {
                "name": "file_upload_security",
                "description": "测试文件上传安全",
                "severity": SecurityLevel.HIGH.value,
                "dangerous_extensions": [".php", ".jsp", ".asp", ".exe", ".sh"],
                "max_file_size": 10485760,  # 10MB
                "allowed_types": ["image/jpeg", "image/png", "application/pdf"]
            },
            {
                "name": "directory_listing",
                "description": "测试目录遍历",
                "severity": SecurityLevel.MEDIUM.value,
                "test_paths": ["/", "/admin", "/backup", "/config"]
            },
            {
                "name": "sensitive_files",
                "description": "测试敏感文件暴露",
                "severity": SecurityLevel.HIGH.value,
                "sensitive_files": [
                    "/.env",
                    "/config.json",
                    "/backup.sql",
                    "/.git/config",
                    "/robots.txt"
                ]
            }
        ]
    },
    
    # 网络安全审计
    "network_security_audit": {
        "enabled": True,
        "test_cases": [
            {
                "name": "ssl_tls_security",
                "description": "测试SSL/TLS安全配置",
                "severity": SecurityLevel.HIGH.value,
                "min_tls_version": "1.2",
                "weak_ciphers": ["RC4", "DES", "3DES"],
                "hsts_enabled": True
            },
            {
                "name": "security_headers",
                "description": "测试安全响应头",
                "severity": SecurityLevel.MEDIUM.value,
                "required_headers": [
                    "X-Content-Type-Options",
                    "X-Frame-Options",
                    "X-XSS-Protection",
                    "Content-Security-Policy",
                    "Strict-Transport-Security"
                ]
            }
        ]
    },
    
    # 配置安全审计
    "configuration_audit": {
        "enabled": True,
        "test_cases": [
            {
                "name": "debug_mode",
                "description": "检查调试模式配置",
                "severity": SecurityLevel.HIGH.value,
                "production_debug": False
            },
            {
                "name": "default_credentials",
                "description": "检查默认凭据",
                "severity": SecurityLevel.CRITICAL.value,
                "default_users": [
                    {"username": "admin", "password": "admin"},
                    {"username": "root", "password": "root"},
                    {"username": "test", "password": "test"}
                ]
            },
            {
                "name": "error_handling",
                "description": "检查错误处理机制",
                "severity": SecurityLevel.MEDIUM.value,
                "expose_stack_trace": False,
                "generic_error_messages": True
            }
        ]
    },
    
    # 审计报告配置
    "reporting": {
        "enabled": True,
        "output_dir": "security_reports",
        "formats": ["json", "html", "pdf"],
        "include_recommendations": True,
        "severity_threshold": SecurityLevel.LOW.value,
        "auto_remediation": False
    },
    
    # 审计工具配置
    "audit_tools": {
        "sqlmap": {
            "enabled": False,
            "path": "/usr/local/bin/sqlmap",
            "options": ["--batch", "--random-agent"]
        },
        "nmap": {
            "enabled": False,
            "path": "/usr/local/bin/nmap",
            "options": ["-sV", "-sC", "--script=vuln"]
        },
        "nikto": {
            "enabled": False,
            "path": "/usr/local/bin/nikto",
            "options": ["-h"]
        }
    },
    
    # 合规性检查
    "compliance": {
        "gdpr": {
            "enabled": True,
            "data_protection": True,
            "consent_management": True,
            "data_retention": True
        },
        "owasp_top10": {
            "enabled": True,
            "version": "2021",
            "categories": [
                "A01_Broken_Access_Control",
                "A02_Cryptographic_Failures",
                "A03_Injection",
                "A04_Insecure_Design",
                "A05_Security_Misconfiguration"
            ]
        }
    }
}

def get_security_audit_config(section: str = None) -> Dict[str, Any]:
    """
    获取安全审计配置
    
    Args:
        section: 配置节名称
        
    Returns:
        Dict[str, Any]: 配置字典
    """
    if section:
        return SECURITY_AUDIT_CONFIG.get(section, {})
    return SECURITY_AUDIT_CONFIG

def update_security_audit_config(section: str, config: Dict[str, Any]) -> bool:
    """
    更新安全审计配置
    
    Args:
        section: 配置节名称
        config: 新配置
        
    Returns:
        bool: 是否更新成功
    """
    try:
        if section in SECURITY_AUDIT_CONFIG:
            SECURITY_AUDIT_CONFIG[section].update(config)
            return True
        return False
    except Exception:
        return False

def get_audit_test_cases(audit_type: str) -> List[Dict[str, Any]]:
    """获取指定类型的审计测试用例"""
    audit_config = get_security_audit_config(f"{audit_type}_audit")
    return audit_config.get("test_cases", [])

def get_security_level_priority(level: str) -> int:
    """获取安全级别优先级"""
    priority_map = {
        SecurityLevel.LOW.value: 1,
        SecurityLevel.MEDIUM.value: 2,
        SecurityLevel.HIGH.value: 3,
        SecurityLevel.CRITICAL.value: 4
    }
    return priority_map.get(level, 0)

def is_audit_enabled(audit_type: str) -> bool:
    """检查指定审计类型是否启用"""
    audit_config = get_security_audit_config(f"{audit_type}_audit")
    return audit_config.get("enabled", False)
