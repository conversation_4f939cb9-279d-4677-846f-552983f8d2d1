import os
from typing import Optional, Dict, Any, List

from pydantic import field_validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    # API设置
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "Recruitment API"
    DEBUG: bool = False

    # 安全设置
    SECRET_KEY: str = "your-secret-key-here"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8 days
    ACCESS_TOKEN_EXPIRE_DAYS: int = 30  # 30 days
    ALGORITHM: str = "HS256"

    # 数据库设置
    MYSQL_HOST: str = "localhost"
    MYSQL_PORT: int = 3306
    MYSQL_USER: str = "recruitment"
    MYSQL_PASSWORD: str = "recruitment"
    MYSQL_DATABASE: str = "recruitment"
    DATABASE_URI: Optional[str] = None

    @field_validator("DATABASE_URI", mode="before")
    @classmethod
    def assemble_db_connection(cls, v: Optional[str], info) -> str:
        if isinstance(v, str):
            return v
        values = info.data if hasattr(info, 'data') else {}
        return f"mysql+pymysql://{values.get('MYSQL_USER', 'recruitment')}:{values.get('MYSQL_PASSWORD', 'recruitment')}@{values.get('MYSQL_HOST', 'localhost')}:{values.get('MYSQL_PORT', 3306)}/{values.get('MYSQL_DATABASE', 'recruitment')}"

    # Redis设置
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: Optional[str] = None

    # Elasticsearch设置
    ELASTICSEARCH_HOST: str = "localhost"
    ELASTICSEARCH_PORT: int = 9200

    # CORS设置
    CORS_ORIGINS: List[str] = [
        "http://localhost:8000",
        "http://localhost:8001",
        "http://localhost:3000",
        "http://localhost:3001",  # 保留端口配置以备将来使用
        "http://localhost:8080",
        "http://127.0.0.1:8000",
        "http://127.0.0.1:8001",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:3001",  # 保留端口配置以备将来使用
        "http://127.0.0.1:8080",
        "https://example.com",
        "https://api.example.com"
    ]

    @field_validator("CORS_ORIGINS", mode="before")
    @classmethod
    def assemble_cors_origins(cls, v: Any) -> List[str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, list):
            return v
        elif isinstance(v, str):
            return [v]
        raise ValueError(v)



    # AI服务设置
    OPENAI_API_KEY: Optional[str] = None

    model_config = {
        "env_file": ".env",
        "case_sensitive": True,
        "extra": "allow"  # 允许额外的输入
    }


settings = Settings()
