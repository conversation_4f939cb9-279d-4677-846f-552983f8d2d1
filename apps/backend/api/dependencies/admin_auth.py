"""
管理员认证依赖模块

专门为管理员系统设计的独立认证和权限验证系统，
与普通用户系统完全分离。

作者: 优智帮工作室
日期: 2025-05-31
版本: v1.0
"""

from typing import Optional

from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from jose import JWTError, jwt
from apps.backend.api.dependencies.database import get_db
from apps.backend.storage.db_manager import DatabaseManager
from apps.backend.api.config.database import get_db as get_sqlalchemy_db
from sqlalchemy.orm import Session
from apps.backend.api.config.settings import settings
from apps.backend.api.models.admin import Admin, AdminRole, AdminPermission
from apps.backend.api.schemas.admin import TokenData


# 管理员专用OAuth2密码Bearer
admin_oauth2_scheme = OAuth2PasswordBearer(
    tokenUrl=f"{settings.API_V1_STR}/admin/auth/login",
    scheme_name="AdminAuth"
)


async def get_current_admin(
    db: DatabaseManager = Depends(get_db),
    token: str = Depends(admin_oauth2_scheme)
) -> Admin:
    """获取当前管理员"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="管理员身份验证失败",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        # 解码JWT token
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        admin_id: Optional[int] = payload.get("sub")
        if admin_id is None:
            raise credentials_exception
        token_data = TokenData(user_id=int(admin_id), exp=payload.get("exp"))
    except JWTError:
        raise credentials_exception

    # 查询管理员
    admin_result = db.execute_query(
        "SELECT id, username, real_name, email, phone, admin_role_id, status, last_login, created_at, updated_at FROM admins WHERE id = %s",
        (token_data.user_id,)
    )

    if not admin_result:
        raise credentials_exception

    # 创建Admin对象
    admin_data = admin_result[0]
    admin = Admin(
        id=admin_data['id'],
        username=admin_data['username'],
        real_name=admin_data.get('real_name'),
        email=admin_data.get('email'),
        phone=admin_data.get('phone'),
        admin_role_id=admin_data['admin_role_id'],
        status=admin_data['status'],
        last_login=admin_data.get('last_login'),
        created_at=admin_data.get('created_at'),
        updated_at=admin_data.get('updated_at')
    )

    return admin


async def get_current_active_admin(
    current_admin: Admin = Depends(get_current_admin),
) -> Admin:
    """获取当前活跃管理员"""
    if not current_admin.status:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, 
            detail="管理员账户已被禁用"
        )
    return current_admin


def check_admin_permission(permission_code: str):
    """检查管理员权限装饰器（使用DatabaseManager）"""

    async def check_permission(
        current_admin: Admin = Depends(get_current_active_admin),
        db: DatabaseManager = Depends(get_db)
    ) -> Admin:
        """检查管理员是否具有指定权限"""

        # 如果是admin用户，直接允许访问（临时解决方案）
        if hasattr(current_admin, 'username') and current_admin.username == 'admin':
            return current_admin

        try:
            # 查询管理员角色
            admin_role_result = db.execute_query(
                "SELECT id, name, is_active FROM admin_roles WHERE id = %s",
                (current_admin.admin_role_id,)
            )

            if not admin_role_result:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="管理员角色不存在"
                )

            admin_role = admin_role_result[0]

            # 检查角色是否激活
            if not admin_role['is_active']:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="管理员角色已被禁用"
                )

            # 超级管理员拥有所有权限
            if admin_role['name'] == 'super_admin':
                return current_admin
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="需要管理员权限"
            )

    return check_permission


def check_admin_permission_sqlalchemy(permission_code: str):
    """检查管理员权限装饰器（使用SQLAlchemy Session）"""

    async def check_permission(
        current_admin: Admin = Depends(get_current_active_admin),
        db: Session = Depends(get_sqlalchemy_db)
    ) -> Admin:
        """检查管理员是否具有指定权限"""

        # 如果是admin用户，直接允许访问（临时解决方案）
        if hasattr(current_admin, 'username') and current_admin.username == 'admin':
            return current_admin

        try:
            # 查询管理员角色
            admin_role = db.query(AdminRole).filter(
                AdminRole.id == current_admin.admin_role_id
            ).first()

            if not admin_role:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="管理员角色不存在"
                )

            # 检查角色是否激活
            if not admin_role.is_active:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="管理员角色已被禁用"
                )

            # 超级管理员拥有所有权限
            if admin_role.name == 'super_admin':
                return current_admin

            # 查询权限
            permission = db.query(AdminPermission).filter(
                AdminPermission.code == permission_code
            ).first()

            if not permission:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"权限不存在: {permission_code}"
                )

            # 检查角色是否具有该权限
            has_permission = db.query(AdminPermission).join(
                AdminRole.admin_permissions
            ).filter(
                AdminRole.id == current_admin.admin_role_id,
                AdminPermission.code == permission_code
            ).first()

            if not has_permission:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"缺少权限: {permission_code}"
                )

            return current_admin
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="需要管理员权限"
            )

    return check_permission


def require_admin_role(role_name: str):
    """要求特定管理员角色装饰器"""
    
    async def check_role(
        current_admin: Admin = Depends(get_current_active_admin),
        db: DatabaseManager = Depends(get_db)
    ) -> Admin:
        """检查管理员是否具有指定角色"""
        
        # 查询管理员角色
        admin_role_result = db.execute_query(
            "SELECT id, name, is_active FROM admin_roles WHERE id = %s",
            (current_admin.admin_role_id,)
        )

        if not admin_role_result or admin_role_result[0]['name'] != role_name:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"需要 {role_name} 角色权限"
            )
        
        return current_admin
    
    return check_role


def require_super_admin():
    """要求超级管理员权限"""
    return require_admin_role('super_admin')


def require_system_admin():
    """要求系统管理员权限"""
    
    async def check_system_admin(
        current_admin: Admin = Depends(get_current_active_admin),
        db: DatabaseManager = Depends(get_db)
    ) -> Admin:
        """检查是否为系统管理员或超级管理员"""
        
        # 查询管理员角色
        admin_role_result = db.execute_query(
            "SELECT id, name, is_active FROM admin_roles WHERE id = %s",
            (current_admin.admin_role_id,)
        )

        if not admin_role_result or admin_role_result[0]['name'] not in ['super_admin', 'admin']:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="需要系统管理员权限"
            )
        
        return current_admin
    
    return check_system_admin


# 常用权限检查快捷方式
def require_system_manage():
    """要求系统管理权限"""
    return check_admin_permission('system:manage')


def require_system_view():
    """要求系统查看权限"""
    return check_admin_permission('system:view')


def require_user_manage():
    """要求用户管理权限"""
    return check_admin_permission('user:manage')


def require_user_view():
    """要求用户查看权限"""
    return check_admin_permission('user:view')


def require_content_manage():
    """要求内容管理权限"""
    return check_admin_permission('content:manage')


def require_content_view():
    """要求内容查看权限"""
    return check_admin_permission('content:view')


def require_analytics_view():
    """要求数据分析权限"""
    return check_admin_permission('analytics:view')


def require_admin_manage():
    """要求管理员管理权限"""
    return check_admin_permission('admin:manage')


# 兼容性别名（用于现有代码）
get_current_admin_user = get_current_admin
