from typing import Optional

from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2<PERSON><PERSON>wordBearer
from jose import JW<PERSON>rror, jwt
from sqlalchemy.orm import Session

from apps.backend.api.config.database import get_db
from apps.backend.api.config.settings import settings
from apps.backend.api.models.user import User, Role, Permission
from apps.backend.api.schemas.user import TokenData

# OAuth2密码Bearer
oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_STR}/auth/token")
admin_oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_STR}/admin/auth/token")


async def get_current_user(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
) -> User:
    """获取当前用户"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        user_id: Optional[int] = payload.get("sub")
        if user_id is None:
            raise credentials_exception
        token_data = TokenData(user_id=user_id, exp=payload.get("exp"))
    except JWTError:
        raise credentials_exception

    user = db.query(User).filter(User.id == token_data.user_id).first()
    if user is None:
        raise credentials_exception
    return user


async def get_current_active_user(
    current_user: User = Depends(get_current_user),
) -> User:
    """获取当前活跃用户"""
    if not current_user.status:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user


async def get_current_admin_user(
    current_user: User = Depends(get_current_active_user),
) -> User:
    """获取当前管理员用户"""
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    return current_user


def check_user_permission(permission_name: str):
    """检查用户权限"""

    async def check_permission(
        current_user: User = Depends(get_current_admin_user),
    ) -> User:
        # 检查权限
        if not current_user.has_permission(permission_name):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"缺少权限: {permission_name}"
            )

        return current_user

    return check_permission


# 为了兼容性，添加别名函数
async def get_current_admin(
    current_user: User = Depends(get_current_admin_user),
) -> User:
    """获取当前管理员（别名函数）"""
    return current_user


async def get_current_active_admin(
    current_user: User = Depends(get_current_admin_user),
) -> User:
    """获取当前活跃管理员（别名函数）"""
    return current_user


def check_admin_permission(permission_name: str):
    """检查管理员权限（别名函数）"""
    return check_user_permission(permission_name)
