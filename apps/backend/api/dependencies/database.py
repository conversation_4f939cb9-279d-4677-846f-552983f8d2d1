#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库依赖注入模块
"""

from typing import Generator
from apps.backend.storage.db_manager import DatabaseManager
from apps.backend.utils.logger import setup_logger

logger = setup_logger("database_deps")

def get_db() -> Generator[DatabaseManager, None, None]:
    """
    获取数据库会话
    
    Returns:
        Generator[DatabaseManager, None, None]: 数据库管理器实例
    """
    db_manager = DatabaseManager()
    try:
        yield db_manager
    except Exception as e:
        logger.error(f"Database session error: {e}")
        raise
    finally:
        # 数据库管理器会自动处理连接关闭
        pass
