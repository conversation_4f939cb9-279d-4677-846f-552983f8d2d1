"""
高级速率限制器
使用滑动窗口算法和Redis实现精确的速率控制
支持多级限制、动态调整和智能防护
"""
import time
import json
import redis
import hashlib
from typing import Dict, Optional, Tuple, List
from fastapi import Request, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware
from enum import Enum
from dataclasses import dataclass
from datetime import datetime, timedelta

class RateLimitType(Enum):
    """速率限制类型"""
    PER_IP = "per_ip"
    PER_USER = "per_user"
    PER_ENDPOINT = "per_endpoint"
    GLOBAL = "global"

class RateLimitStrategy(Enum):
    """速率限制策略"""
    SLIDING_WINDOW = "sliding_window"
    TOKEN_BUCKET = "token_bucket"
    FIXED_WINDOW = "fixed_window"

@dataclass
class RateLimitRule:
    """速率限制规则"""
    requests: int
    window_seconds: int
    strategy: RateLimitStrategy = RateLimitStrategy.SLIDING_WINDOW
    burst_allowance: int = 0
    penalty_seconds: int = 0

class AdvancedRateLimiter:
    """高级速率限制器"""

    def __init__(self, redis_client: redis.Redis):
        self.redis = redis_client

        # 预定义的速率限制规则
        self.rate_limit_rules = {
            # 认证相关 - 严格限制
            '/api/v1/auth/login': RateLimitRule(
                requests=5, window_seconds=300,
                penalty_seconds=900  # 失败后15分钟惩罚
            ),
            '/api/v1/auth/register': RateLimitRule(
                requests=3, window_seconds=3600,
                penalty_seconds=3600  # 失败后1小时惩罚
            ),
            '/api/v1/auth/reset-password': RateLimitRule(
                requests=3, window_seconds=3600,
                penalty_seconds=1800
            ),

            # 搜索相关 - 中等限制
            '/api/v1/jobs/search': RateLimitRule(
                requests=100, window_seconds=60,
                burst_allowance=20  # 允许突发20个请求
            ),
            '/api/v1/jobs': RateLimitRule(
                requests=200, window_seconds=60,
                burst_allowance=50
            ),

            # 用户操作 - 宽松限制
            '/api/v1/users/profile': RateLimitRule(
                requests=50, window_seconds=60
            ),
            '/api/v1/users/favorites': RateLimitRule(
                requests=30, window_seconds=60
            ),

            # 管理员操作 - 严格限制
            '/api/v1/admin/': RateLimitRule(
                requests=20, window_seconds=60,
                penalty_seconds=300
            ),

            # 数据导出 - 非常严格
            '/api/v1/export/': RateLimitRule(
                requests=5, window_seconds=3600,
                penalty_seconds=1800
            ),

            # 默认规则
            'default': RateLimitRule(
                requests=1000, window_seconds=3600,
                burst_allowance=100
            )
        }

        # 可疑行为检测阈值
        self.suspicious_thresholds = {
            'rapid_requests': 50,  # 10秒内50个请求
            'failed_auth_attempts': 10,  # 连续失败认证次数
            'different_endpoints': 20,  # 短时间内访问不同端点数量
        }

    def is_rate_limited(self, key: str, rule: RateLimitRule) -> Tuple[bool, Dict]:
        """检查是否超过速率限制"""
        current_time = int(time.time())

        if rule.strategy == RateLimitStrategy.SLIDING_WINDOW:
            return self._sliding_window_check(key, rule, current_time)
        elif rule.strategy == RateLimitStrategy.TOKEN_BUCKET:
            return self._token_bucket_check(key, rule, current_time)
        else:  # FIXED_WINDOW
            return self._fixed_window_check(key, rule, current_time)

    def _sliding_window_check(self, key: str, rule: RateLimitRule, current_time: int) -> Tuple[bool, Dict]:
        """滑动窗口算法检查"""
        window_start = current_time - rule.window_seconds

        # 检查是否在惩罚期
        penalty_key = f"{key}:penalty"
        penalty_until = self.redis.get(penalty_key)
        if penalty_until and int(penalty_until) > current_time:
            return True, {
                'limited': True,
                'reason': 'penalty_period',
                'reset_time': int(penalty_until),
                'remaining_requests': 0
            }

        pipe = self.redis.pipeline()

        # 移除过期的请求记录
        pipe.zremrangebyscore(key, 0, window_start)

        # 添加当前请求
        pipe.zadd(key, {str(current_time): current_time})

        # 获取当前窗口内的请求数
        pipe.zcard(key)

        # 设置过期时间
        pipe.expire(key, rule.window_seconds)

        results = pipe.execute()
        request_count = results[2]

        # 考虑突发允许量
        effective_limit = rule.requests + rule.burst_allowance

        is_limited = request_count > effective_limit

        return is_limited, {
            'limited': is_limited,
            'reason': 'rate_limit_exceeded' if is_limited else 'allowed',
            'requests_made': request_count,
            'limit': rule.requests,
            'window_seconds': rule.window_seconds,
            'reset_time': current_time + rule.window_seconds,
            'remaining_requests': max(0, effective_limit - request_count)
        }

    def _token_bucket_check(self, key: str, rule: RateLimitRule, current_time: int) -> Tuple[bool, Dict]:
        """令牌桶算法检查"""
        bucket_key = f"{key}:bucket"

        # 获取桶状态
        bucket_data = self.redis.hgetall(bucket_key)

        if bucket_data:
            last_refill = int(bucket_data.get('last_refill', current_time))
            tokens = float(bucket_data.get('tokens', rule.requests))
        else:
            last_refill = current_time
            tokens = rule.requests

        # 计算需要添加的令牌
        time_passed = current_time - last_refill
        tokens_to_add = (time_passed / rule.window_seconds) * rule.requests
        tokens = min(rule.requests, tokens + tokens_to_add)

        # 检查是否有足够的令牌
        if tokens >= 1:
            tokens -= 1
            is_limited = False
        else:
            is_limited = True

        # 更新桶状态
        self.redis.hset(bucket_key, mapping={
            'tokens': tokens,
            'last_refill': current_time
        })
        self.redis.expire(bucket_key, rule.window_seconds * 2)

        return is_limited, {
            'limited': is_limited,
            'reason': 'token_bucket_empty' if is_limited else 'allowed',
            'tokens_remaining': int(tokens),
            'refill_rate': rule.requests / rule.window_seconds
        }

    def _fixed_window_check(self, key: str, rule: RateLimitRule, current_time: int) -> Tuple[bool, Dict]:
        """固定窗口算法检查"""
        window_key = f"{key}:{current_time // rule.window_seconds}"

        # 原子性增加计数
        pipe = self.redis.pipeline()
        pipe.incr(window_key)
        pipe.expire(window_key, rule.window_seconds)
        results = pipe.execute()

        request_count = results[0]
        is_limited = request_count > rule.requests

        window_start = (current_time // rule.window_seconds) * rule.window_seconds

        return is_limited, {
            'limited': is_limited,
            'reason': 'fixed_window_exceeded' if is_limited else 'allowed',
            'requests_made': request_count,
            'limit': rule.requests,
            'window_start': window_start,
            'window_end': window_start + rule.window_seconds,
            'remaining_requests': max(0, rule.requests - request_count)
        }

    def apply_penalty(self, key: str, rule: RateLimitRule):
        """应用惩罚"""
        if rule.penalty_seconds > 0:
            penalty_key = f"{key}:penalty"
            penalty_until = int(time.time()) + rule.penalty_seconds
            self.redis.set(penalty_key, penalty_until, ex=rule.penalty_seconds)

    def detect_suspicious_behavior(self, client_id: str) -> Dict[str, bool]:
        """检测可疑行为"""
        current_time = int(time.time())
        suspicious_flags = {}

        # 检测快速请求
        rapid_key = f"rapid:{client_id}"
        rapid_count = self.redis.zcount(rapid_key, current_time - 10, current_time)
        suspicious_flags['rapid_requests'] = rapid_count > self.suspicious_thresholds['rapid_requests']

        # 检测失败认证尝试
        auth_fail_key = f"auth_fail:{client_id}"
        auth_fail_count = self.redis.get(auth_fail_key) or 0
        suspicious_flags['failed_auth'] = int(auth_fail_count) > self.suspicious_thresholds['failed_auth_attempts']

        # 检测端点扫描
        endpoints_key = f"endpoints:{client_id}"
        endpoint_count = self.redis.scard(endpoints_key)
        suspicious_flags['endpoint_scanning'] = endpoint_count > self.suspicious_thresholds['different_endpoints']

        return suspicious_flags

    def get_rate_limit_for_path(self, path: str) -> RateLimitRule:
        """获取路径对应的速率限制规则"""
        # 精确匹配
        if path in self.rate_limit_rules:
            return self.rate_limit_rules[path]

        # 前缀匹配
        for pattern, rule in self.rate_limit_rules.items():
            if pattern != 'default' and path.startswith(pattern):
                return rule

        # 默认规则
        return self.rate_limit_rules['default']

class AdvancedRateLimitMiddleware(BaseHTTPMiddleware):
    """高级速率限制中间件"""

    def __init__(self, app, redis_client: redis.Redis):
        super().__init__(app)
        self.rate_limiter = AdvancedRateLimiter(redis_client)

        # 豁免路径
        self.exempt_paths = [
            '/health',
            '/docs',
            '/openapi.json',
            '/favicon.ico',
            '/static/'
        ]

    async def dispatch(self, request: Request, call_next):
        # 检查是否需要速率限制
        if self.should_apply_rate_limit(request):
            await self.apply_rate_limiting(request)

        # 记录请求用于行为分析
        self.record_request(request)

        response = await call_next(request)

        # 添加速率限制头部
        self.add_rate_limit_headers(request, response)

        return response

    def should_apply_rate_limit(self, request: Request) -> bool:
        """检查是否应该应用速率限制"""
        path = request.url.path

        # 检查豁免路径
        for exempt_path in self.exempt_paths:
            if path.startswith(exempt_path):
                return False

        return True

    async def apply_rate_limiting(self, request: Request):
        """应用速率限制"""
        client_id = self.get_client_id(request)
        path = request.url.path

        # 获取速率限制规则
        rule = self.rate_limiter.get_rate_limit_for_path(path)

        # 构造限制键
        rate_limit_key = f"rate_limit:{client_id}:{path}"

        # 检查速率限制
        is_limited, limit_info = self.rate_limiter.is_rate_limited(rate_limit_key, rule)

        if is_limited:
            # 应用惩罚（如果配置了）
            if rule.penalty_seconds > 0:
                self.rate_limiter.apply_penalty(rate_limit_key, rule)

            # 检测可疑行为
            suspicious_flags = self.rate_limiter.detect_suspicious_behavior(client_id)

            # 构造错误响应
            error_detail = {
                "error": "Rate limit exceeded",
                "message": "请求过于频繁，请稍后重试",
                "limit_info": limit_info,
                "retry_after": limit_info.get('reset_time', 0) - int(time.time())
            }

            # 如果检测到可疑行为，添加额外信息
            if any(suspicious_flags.values()):
                error_detail["warning"] = "检测到可疑行为，已加强监控"

            raise HTTPException(status_code=429, detail=error_detail)

        # 存储限制信息到请求状态
        request.state.rate_limit_info = limit_info

    def record_request(self, request: Request):
        """记录请求用于行为分析"""
        client_id = self.get_client_id(request)
        current_time = int(time.time())
        path = request.url.path

        # 记录快速请求检测
        rapid_key = f"rapid:{client_id}"
        self.rate_limiter.redis.zadd(rapid_key, {str(current_time): current_time})
        self.rate_limiter.redis.expire(rapid_key, 60)

        # 记录访问的端点
        endpoints_key = f"endpoints:{client_id}"
        self.rate_limiter.redis.sadd(endpoints_key, path)
        self.rate_limiter.redis.expire(endpoints_key, 300)

    def add_rate_limit_headers(self, request: Request, response):
        """添加速率限制头部"""
        if hasattr(request.state, 'rate_limit_info'):
            limit_info = request.state.rate_limit_info

            response.headers["X-RateLimit-Limit"] = str(limit_info.get('limit', 0))
            response.headers["X-RateLimit-Remaining"] = str(limit_info.get('remaining_requests', 0))
            response.headers["X-RateLimit-Reset"] = str(limit_info.get('reset_time', 0))
            response.headers["X-RateLimit-Window"] = str(limit_info.get('window_seconds', 0))

    def get_client_id(self, request: Request) -> str:
        """获取客户端标识"""
        # 优先使用用户ID
        user_id = getattr(request.state, 'user_id', None)
        if user_id:
            return f"user:{user_id}"

        # 使用IP地址
        forwarded_for = request.headers.get('X-Forwarded-For')
        if forwarded_for:
            return f"ip:{forwarded_for.split(',')[0].strip()}"

        client_host = request.client.host if request.client else 'unknown'
        return f"ip:{client_host}"
