"""
JWT认证中间件
处理请求的身份验证和授权
"""
import logging
from fastapi import Request, Response, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse
from typing import Optional, List
import redis

from ..auth.jwt_manager import J<PERSON>TManager
from ..auth.permission_manager import PermissionManager, Permission

logger = logging.getLogger(__name__)

class JWTAuthMiddleware(BaseHTTPMiddleware):
    """JWT认证中间件"""
    
    def __init__(self, app, secret_key: str, redis_client: Optional[redis.Redis] = None):
        super().__init__(app)
        self.jwt_manager = JWTManager(secret_key, redis_client)
        self.permission_manager = PermissionManager()
        
        # 不需要认证的路径
        self.public_paths = [
            '/',
            '/health',
            '/docs',
            '/redoc',
            '/openapi.json',
            '/api/v1/auth/login',
            '/api/v1/auth/register',
            '/api/v1/auth/refresh',
            '/api/v1/jobs',  # 岗位查看允许匿名访问
            '/static/',
        ]
        
        # 需要特定权限的路径
        self.protected_paths = {
            '/api/v1/jobs': {
                'GET': [],  # 查看岗位不需要特殊权限
                'POST': [Permission.JOB_CREATE.value],
                'PUT': [Permission.JOB_UPDATE.value],
                'DELETE': [Permission.JOB_DELETE.value]
            },
            '/api/v1/users': {
                'GET': [Permission.USER_VIEW.value],
                'POST': [Permission.USER_CREATE.value],
                'PUT': [Permission.USER_UPDATE.value],
                'DELETE': [Permission.USER_DELETE.value]
            },
            '/api/v1/applications': {
                'GET': [Permission.APPLICATION_VIEW.value],
                'POST': [Permission.APPLICATION_CREATE.value],
                'PUT': [Permission.APPLICATION_UPDATE.value],
                'DELETE': [Permission.APPLICATION_DELETE.value]
            },
            '/api/v1/admin': {
                'GET': [Permission.SYSTEM_MONITOR.value],
                'POST': [Permission.SYSTEM_CONFIG.value],
                'PUT': [Permission.SYSTEM_CONFIG.value],
                'DELETE': [Permission.SYSTEM_CONFIG.value]
            }
        }
    
    async def dispatch(self, request: Request, call_next):
        # 检查是否为公开路径
        if self._is_public_path(request.url.path):
            return await call_next(request)
        
        # 提取和验证JWT令牌
        token = self._extract_token(request)
        if not token:
            return self._create_auth_error_response("缺少认证令牌")
        
        # 验证令牌
        payload = self.jwt_manager.verify_token(token, self.jwt_manager.TOKEN_TYPE_ACCESS)
        if not payload:
            return self._create_auth_error_response("无效或过期的令牌")
        
        # 将用户信息添加到请求状态
        request.state.user = {
            'user_id': payload['user_id'],
            'username': payload['username'],
            'email': payload['email'],
            'role': payload['role'],
            'permissions': payload['permissions']
        }
        
        # 检查权限
        if not self._check_permissions(request):
            return self._create_permission_error_response("权限不足")
        
        # 记录访问日志
        logger.info(f"认证成功: user_id={payload['user_id']}, path={request.url.path}, method={request.method}")
        
        # 继续处理请求
        response = await call_next(request)
        
        # 添加安全头部
        self._add_security_headers(response)
        
        return response
    
    def _is_public_path(self, path: str) -> bool:
        """检查是否为公开路径"""
        for public_path in self.public_paths:
            if path.startswith(public_path):
                return True
        return False
    
    def _extract_token(self, request: Request) -> Optional[str]:
        """从请求中提取JWT令牌"""
        # 从Authorization头部提取
        authorization = request.headers.get('Authorization')
        if authorization and authorization.startswith('Bearer '):
            return authorization[7:]  # 移除 'Bearer ' 前缀
        
        # 从Cookie中提取（可选）
        token = request.cookies.get('access_token')
        if token:
            return token
        
        # 从查询参数提取（不推荐，仅用于特殊情况）
        token = request.query_params.get('token')
        if token:
            logger.warning(f"从查询参数获取令牌: {request.url.path}")
            return token
        
        return None
    
    def _check_permissions(self, request: Request) -> bool:
        """检查用户权限"""
        path = request.url.path
        method = request.method.upper()
        
        # 查找匹配的保护路径
        for protected_path, methods in self.protected_paths.items():
            if path.startswith(protected_path):
                required_permissions = methods.get(method, [])
                
                # 如果没有特定权限要求，允许访问
                if not required_permissions:
                    return True
                
                # 检查用户权限
                user_permissions = request.state.user.get('permissions', [])
                return self.permission_manager.has_any_permission(user_permissions, required_permissions)
        
        # 默认允许访问（对于未明确配置的路径）
        return True
    
    def _create_auth_error_response(self, message: str) -> JSONResponse:
        """创建认证错误响应"""
        return JSONResponse(
            status_code=401,
            content={
                'error': 'authentication_required',
                'message': message,
                'code': 'AUTH_001'
            },
            headers={'WWW-Authenticate': 'Bearer'}
        )
    
    def _create_permission_error_response(self, message: str) -> JSONResponse:
        """创建权限错误响应"""
        return JSONResponse(
            status_code=403,
            content={
                'error': 'permission_denied',
                'message': message,
                'code': 'AUTH_002'
            }
        )
    
    def _add_security_headers(self, response: Response):
        """添加安全头部"""
        # 防止缓存敏感信息
        response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        
        # 添加认证相关头部
        response.headers['X-Auth-Required'] = 'true'

class OptionalAuthMiddleware(BaseHTTPMiddleware):
    """可选认证中间件（用于可选认证的端点）"""
    
    def __init__(self, app, secret_key: str, redis_client: Optional[redis.Redis] = None):
        super().__init__(app)
        self.jwt_manager = JWTManager(secret_key, redis_client)
    
    async def dispatch(self, request: Request, call_next):
        # 尝试提取和验证令牌
        token = self._extract_token(request)
        
        if token:
            payload = self.jwt_manager.verify_token(token, self.jwt_manager.TOKEN_TYPE_ACCESS)
            if payload:
                # 如果令牌有效，添加用户信息
                request.state.user = {
                    'user_id': payload['user_id'],
                    'username': payload['username'],
                    'email': payload['email'],
                    'role': payload['role'],
                    'permissions': payload['permissions'],
                    'authenticated': True
                }
            else:
                # 令牌无效，设置为匿名用户
                request.state.user = {'authenticated': False}
        else:
            # 没有令牌，设置为匿名用户
            request.state.user = {'authenticated': False}
        
        return await call_next(request)
    
    def _extract_token(self, request: Request) -> Optional[str]:
        """从请求中提取JWT令牌"""
        authorization = request.headers.get('Authorization')
        if authorization and authorization.startswith('Bearer '):
            return authorization[7:]
        
        return request.cookies.get('access_token')

class RateLimitByUserMiddleware(BaseHTTPMiddleware):
    """基于用户的速率限制中间件"""
    
    def __init__(self, app, redis_client: redis.Redis, default_limit: int = 100):
        super().__init__(app)
        self.redis_client = redis_client
        self.default_limit = default_limit
        
        # 不同用户角色的速率限制
        self.role_limits = {
            'admin': 1000,    # 管理员更高限制
            'recruiter': 500, # 招聘者中等限制
            'user': 100,      # 普通用户基础限制
            'guest': 50       # 访客最低限制
        }
    
    async def dispatch(self, request: Request, call_next):
        # 获取用户信息
        user = getattr(request.state, 'user', None)
        
        if user and user.get('authenticated'):
            user_id = user['user_id']
            role = user.get('role', 'user')
            limit = self.role_limits.get(role, self.default_limit)
        else:
            # 匿名用户使用IP地址
            user_id = f"ip:{request.client.host}"
            limit = self.role_limits['guest']
        
        # 检查速率限制
        if not self._check_rate_limit(user_id, limit):
            return JSONResponse(
                status_code=429,
                content={
                    'error': 'rate_limit_exceeded',
                    'message': f'请求频率过高，限制: {limit} 请求/小时',
                    'code': 'RATE_001'
                }
            )
        
        return await call_next(request)
    
    def _check_rate_limit(self, user_id: str, limit: int) -> bool:
        """检查速率限制"""
        try:
            key = f"rate_limit:{user_id}:hour"
            current = self.redis_client.get(key)
            
            if current is None:
                # 第一次请求
                self.redis_client.setex(key, 3600, 1)  # 1小时过期
                return True
            
            current_count = int(current)
            if current_count >= limit:
                return False
            
            # 增加计数
            self.redis_client.incr(key)
            return True
            
        except Exception as e:
            logger.error(f"速率限制检查失败: {e}")
            return True  # 出错时允许通过

# 认证依赖函数（用于FastAPI依赖注入）
def get_current_user(request: Request) -> dict:
    """获取当前用户（FastAPI依赖）"""
    user = getattr(request.state, 'user', None)
    if not user or not user.get('authenticated', False):
        raise HTTPException(status_code=401, detail="未认证")
    return user

def get_current_user_optional(request: Request) -> Optional[dict]:
    """获取当前用户（可选，FastAPI依赖）"""
    user = getattr(request.state, 'user', None)
    if user and user.get('authenticated', False):
        return user
    return None

def require_permission(permission: str):
    """权限检查依赖"""
    def permission_checker(current_user: dict = get_current_user):
        user_permissions = current_user.get('permissions', [])
        permission_manager = PermissionManager()
        
        if not permission_manager.has_permission(user_permissions, permission):
            raise HTTPException(
                status_code=403, 
                detail=f"权限不足，需要权限: {permission}"
            )
        
        return current_user
    
    return permission_checker

def require_role(role: str):
    """角色检查依赖"""
    def role_checker(current_user: dict = get_current_user):
        user_role = current_user.get('role')
        permission_manager = PermissionManager()
        
        role_hierarchy = permission_manager.get_user_role_hierarchy(user_role)
        if role not in role_hierarchy:
            raise HTTPException(
                status_code=403, 
                detail=f"权限不足，需要角色: {role}"
            )
        
        return current_user
    
    return role_checker
