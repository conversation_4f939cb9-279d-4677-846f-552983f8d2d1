
"""
Redis缓存中间件
"""
import json
import redis
import hashlib
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from typing import Optional

class RedisCacheMiddleware(BaseHTTPMiddleware):
    """Redis缓存中间件"""
    
    def __init__(self, app, redis_url: str = "redis://localhost:6379/0"):
        super().__init__(app)
        self.redis_client = redis.from_url(redis_url, decode_responses=True)
        
        # 缓存配置
        self.cache_config = {
            '/api/v1/jobs': 300,  # 岗位搜索缓存5分钟
            '/api/v1/jobs/': 1800,  # 岗位详情缓存30分钟
            '/api/v1/categories': 86400,  # 分类缓存24小时
            '/api/v1/locations': 86400,  # 地区缓存24小时
        }
        
        # 不缓存的方法
        self.no_cache_methods = ['POST', 'PUT', 'DELETE', 'PATCH']
    
    async def dispatch(self, request: Request, call_next):
        # 检查是否需要缓存
        if not self._should_cache(request):
            return await call_next(request)
        
        # 生成缓存键
        cache_key = self._generate_cache_key(request)
        
        # 尝试从缓存获取
        cached_response = self._get_from_cache(cache_key)
        if cached_response:
            return self._create_response_from_cache(cached_response)
        
        # 执行请求
        response = await call_next(request)
        
        # 缓存响应
        if response.status_code == 200:
            self._save_to_cache(cache_key, response, request.url.path)
        
        return response
    
    def _should_cache(self, request: Request) -> bool:
        """检查是否应该缓存"""
        # 不缓存非GET请求
        if request.method in self.no_cache_methods:
            return False
        
        # 检查路径是否在缓存配置中
        path = request.url.path
        for cache_path in self.cache_config.keys():
            if path.startswith(cache_path):
                return True
        
        return False
    
    def _generate_cache_key(self, request: Request) -> str:
        """生成缓存键"""
        # 包含路径、查询参数和用户信息
        path = request.url.path
        query = str(request.query_params)
        user_id = getattr(request.state, 'user_id', 'anonymous')
        
        # 生成哈希键
        key_string = f"{path}:{query}:{user_id}"
        hash_key = hashlib.md5(key_string.encode()).hexdigest()
        
        return f"api_cache:{hash_key}"
    
    def _get_from_cache(self, cache_key: str) -> Optional[dict]:
        """从缓存获取数据"""
        try:
            cached_data = self.redis_client.get(cache_key)
            if cached_data:
                return json.loads(cached_data)
        except Exception:
            pass
        return None
    
    def _save_to_cache(self, cache_key: str, response: Response, path: str):
        """保存到缓存"""
        try:
            # 获取TTL
            ttl = self._get_ttl_for_path(path)
            
            # 准备缓存数据
            cache_data = {
                'status_code': response.status_code,
                'headers': dict(response.headers),
                'content': response.body.decode() if response.body else ''
            }
            
            # 保存到Redis
            self.redis_client.setex(
                cache_key, 
                ttl, 
                json.dumps(cache_data)
            )
            
        except Exception:
            pass  # 缓存失败不影响正常响应
    
    def _get_ttl_for_path(self, path: str) -> int:
        """获取路径对应的TTL"""
        for cache_path, ttl in self.cache_config.items():
            if path.startswith(cache_path):
                return ttl
        return 300  # 默认5分钟
    
    def _create_response_from_cache(self, cached_data: dict) -> Response:
        """从缓存数据创建响应"""
        response = Response(
            content=cached_data['content'],
            status_code=cached_data['status_code'],
            headers=cached_data['headers']
        )
        
        # 添加缓存标识
        response.headers['X-Cache'] = 'HIT'
        
        return response
