
"""
响应压缩中间件
"""
import gzip
import json
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

class CompressionMiddleware(BaseHTTPMiddleware):
    """响应压缩中间件"""
    
    def __init__(self, app, minimum_size: int = 1024):
        super().__init__(app)
        self.minimum_size = minimum_size
        
        # 支持压缩的内容类型
        self.compressible_types = [
            'application/json',
            'text/html',
            'text/css',
            'text/javascript',
            'application/javascript',
            'text/plain'
        ]
    
    async def dispatch(self, request: Request, call_next):
        response = await call_next(request)
        
        # 检查是否支持压缩
        if not self._should_compress(request, response):
            return response
        
        # 压缩响应
        compressed_response = self._compress_response(response)
        
        return compressed_response
    
    def _should_compress(self, request: Request, response: Response) -> bool:
        """检查是否应该压缩"""
        # 检查客户端是否支持gzip
        accept_encoding = request.headers.get('accept-encoding', '')
        if 'gzip' not in accept_encoding:
            return False
        
        # 检查响应大小
        content_length = len(response.body) if response.body else 0
        if content_length < self.minimum_size:
            return False
        
        # 检查内容类型
        content_type = response.headers.get('content-type', '')
        for compressible_type in self.compressible_types:
            if content_type.startswith(compressible_type):
                return True
        
        return False
    
    def _compress_response(self, response: Response) -> Response:
        """压缩响应"""
        if not response.body:
            return response
        
        # 压缩内容
        compressed_content = gzip.compress(response.body)
        
        # 创建新响应
        compressed_response = Response(
            content=compressed_content,
            status_code=response.status_code,
            headers=dict(response.headers)
        )
        
        # 设置压缩头部
        compressed_response.headers['content-encoding'] = 'gzip'
        compressed_response.headers['content-length'] = str(len(compressed_content))
        
        return compressed_response
