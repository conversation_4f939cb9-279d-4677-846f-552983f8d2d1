
"""
API性能监控中间件
"""
import time
import logging
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

logger = logging.getLogger(__name__)

class PerformanceMonitoringMiddleware(BaseHTTPMiddleware):
    """性能监控中间件"""
    
    def __init__(self, app):
        super().__init__(app)
        self.slow_request_threshold = 1.0  # 1秒
    
    async def dispatch(self, request: Request, call_next):
        start_time = time.time()
        
        # 执行请求
        response = await call_next(request)
        
        # 计算响应时间
        process_time = time.time() - start_time
        
        # 添加响应时间头部
        response.headers["X-Process-Time"] = str(process_time)
        
        # 记录慢请求
        if process_time > self.slow_request_threshold:
            logger.warning(
                f"Slow request: {request.method} {request.url.path} "
                f"took {process_time:.3f}s"
            )
        
        # 记录性能指标
        logger.info(
            f"API Request: {request.method} {request.url.path} "
            f"Status: {response.status_code} Time: {process_time:.3f}s"
        )
        
        return response
