"""
API速率限制中间件
防止API滥用和DDoS攻击
"""
import time
import redis
import logging
from typing import Dict, Optional, Tuple
from fastapi import Request, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware
import json
import hashlib

logger = logging.getLogger(__name__)

class RateLimiter:
    """速率限制器"""
    
    def __init__(self, redis_client: redis.Redis):
        self.redis = redis_client
        
        # 不同API端点的速率限制配置
        self.rate_limits = {
            # 认证相关 - 严格限制
            '/api/v1/auth/login': (5, 300),          # 登录: 5次/5分钟
            '/api/v1/auth/register': (3, 3600),      # 注册: 3次/小时
            '/api/v1/auth/reset-password': (3, 3600), # 重置密码: 3次/小时
            '/api/v1/auth/verify-email': (5, 300),   # 邮箱验证: 5次/5分钟
            
            # 搜索相关 - 中等限制
            '/api/v1/jobs/search': (100, 60),        # 搜索: 100次/分钟
            '/api/v1/jobs/advanced-search': (50, 60), # 高级搜索: 50次/分钟
            
            # 数据获取 - 宽松限制
            '/api/v1/jobs': (200, 60),               # 岗位列表: 200次/分钟
            '/api/v1/jobs/hot': (300, 60),           # 热门岗位: 300次/分钟
            '/api/v1/categories': (500, 60),         # 分类: 500次/分钟
            '/api/v1/locations': (500, 60),          # 地区: 500次/分钟
            
            # 用户操作 - 中等限制
            '/api/v1/users/profile': (30, 60),       # 用户资料: 30次/分钟
            '/api/v1/users/favorites': (50, 60),     # 收藏: 50次/分钟
            '/api/v1/users/applications': (20, 60),  # 申请: 20次/分钟
            
            # 管理员操作 - 严格限制
            '/api/v1/admin': (100, 60),              # 管理员: 100次/分钟
            '/api/v1/admin/users': (50, 60),         # 用户管理: 50次/分钟
            '/api/v1/admin/analytics': (20, 60),     # 分析: 20次/分钟
            
            # 文件上传 - 非常严格
            '/api/v1/upload': (10, 300),             # 上传: 10次/5分钟
            
            # 默认限制
            'default': (1000, 3600),                 # 默认: 1000次/小时
        }
        
        # IP黑名单
        self.ip_blacklist_key = "ip_blacklist"
        self.blacklist_duration = 3600  # 1小时
        
        # 可疑行为阈值
        self.suspicious_thresholds = {
            'rapid_requests': 50,      # 1分钟内超过50次请求
            'failed_auth': 10,         # 1小时内10次认证失败
            'invalid_requests': 20,    # 1小时内20次无效请求
        }
    
    def is_ip_blacklisted(self, ip: str) -> bool:
        """检查IP是否在黑名单中"""
        return self.redis.sismember(self.ip_blacklist_key, ip)
    
    def add_to_blacklist(self, ip: str, reason: str = "suspicious_activity"):
        """将IP添加到黑名单"""
        self.redis.sadd(self.ip_blacklist_key, ip)
        self.redis.expire(self.ip_blacklist_key, self.blacklist_duration)
        
        # 记录黑名单事件
        blacklist_event = {
            'ip': ip,
            'reason': reason,
            'timestamp': time.time(),
        }
        self.redis.lpush(f"blacklist_events:{ip}", json.dumps(blacklist_event))
        self.redis.expire(f"blacklist_events:{ip}", self.blacklist_duration)
        
        logger.warning(f"IP {ip} 已被加入黑名单，原因: {reason}")
    
    def is_rate_limited(self, key: str, limit: int, window: int) -> Tuple[bool, Dict[str, int]]:
        """检查是否超过速率限制"""
        current_time = int(time.time())
        window_start = current_time - window
        
        # 使用Redis滑动窗口算法
        pipe = self.redis.pipeline()
        
        # 移除过期的请求记录
        pipe.zremrangebyscore(key, 0, window_start)
        
        # 添加当前请求
        pipe.zadd(key, {str(current_time): current_time})
        
        # 获取当前窗口内的请求数
        pipe.zcard(key)
        
        # 设置过期时间
        pipe.expire(key, window)
        
        results = pipe.execute()
        request_count = results[2]
        
        # 获取限制信息
        limit_info = self._get_rate_limit_info(key, window, request_count, current_time)
        
        return request_count > limit, limit_info
    
    def _get_rate_limit_info(self, key: str, window: int, request_count: int, current_time: int) -> Dict[str, int]:
        """获取速率限制信息"""
        # 获取最早的请求时间
        earliest_requests = self.redis.zrange(key, 0, 0, withscores=True)
        
        if earliest_requests:
            earliest_time = int(earliest_requests[0][1])
            reset_time = earliest_time + window
        else:
            reset_time = current_time + window
        
        return {
            'requests_made': request_count,
            'reset_time': reset_time,
            'remaining_time': max(0, reset_time - current_time),
            'window': window
        }
    
    def check_suspicious_activity(self, client_id: str, request_path: str) -> bool:
        """检查可疑活动"""
        current_time = int(time.time())
        
        # 检查快速请求
        rapid_key = f"rapid_requests:{client_id}"
        rapid_count = self.redis.zcount(rapid_key, current_time - 60, current_time)
        
        if rapid_count > self.suspicious_thresholds['rapid_requests']:
            logger.warning(f"检测到快速请求: {client_id}, 1分钟内{rapid_count}次请求")
            return True
        
        # 检查认证失败
        if 'auth' in request_path:
            auth_fail_key = f"auth_failures:{client_id}"
            auth_fail_count = self.redis.zcount(auth_fail_key, current_time - 3600, current_time)
            
            if auth_fail_count > self.suspicious_thresholds['failed_auth']:
                logger.warning(f"检测到频繁认证失败: {client_id}, 1小时内{auth_fail_count}次失败")
                return True
        
        return False
    
    def record_request(self, client_id: str, request_path: str, status_code: int):
        """记录请求信息"""
        current_time = int(time.time())
        
        # 记录快速请求检测
        rapid_key = f"rapid_requests:{client_id}"
        self.redis.zadd(rapid_key, {str(current_time): current_time})
        self.redis.expire(rapid_key, 60)
        
        # 记录认证失败
        if 'auth' in request_path and status_code in [401, 403]:
            auth_fail_key = f"auth_failures:{client_id}"
            self.redis.zadd(auth_fail_key, {str(current_time): current_time})
            self.redis.expire(auth_fail_key, 3600)
        
        # 记录无效请求
        if status_code >= 400:
            invalid_key = f"invalid_requests:{client_id}"
            self.redis.zadd(invalid_key, {str(current_time): current_time})
            self.redis.expire(invalid_key, 3600)

class RateLimitMiddleware(BaseHTTPMiddleware):
    """速率限制中间件"""
    
    def __init__(self, app, redis_client: redis.Redis):
        super().__init__(app)
        self.rate_limiter = RateLimiter(redis_client)
        
        # 豁免路径（不进行速率限制）
        self.exempt_paths = [
            '/health',
            '/docs',
            '/openapi.json',
            '/favicon.ico',
            '/static/',
        ]
    
    async def dispatch(self, request: Request, call_next):
        # 检查是否为豁免路径
        if self._is_exempt_path(request.url.path):
            return await call_next(request)
        
        # 获取客户端标识
        client_id = self._get_client_id(request)
        client_ip = self._get_client_ip(request)
        
        # 检查IP黑名单
        if self.rate_limiter.is_ip_blacklisted(client_ip):
            logger.warning(f"黑名单IP访问被拒绝: {client_ip}")
            raise HTTPException(
                status_code=403,
                detail={
                    "error": "Access denied",
                    "message": "您的IP地址已被暂时封禁",
                    "code": "IP_BLACKLISTED"
                }
            )
        
        # 获取路径对应的速率限制
        path = request.url.path
        limit, window = self._get_rate_limit_for_path(path)
        
        # 构造Redis键
        rate_limit_key = f"rate_limit:{client_id}:{self._normalize_path(path)}"
        
        # 检查速率限制
        is_limited, limit_info = self.rate_limiter.is_rate_limited(rate_limit_key, limit, window)
        
        if is_limited:
            # 检查可疑活动
            if self.rate_limiter.check_suspicious_activity(client_id, path):
                self.rate_limiter.add_to_blacklist(client_ip, "rate_limit_exceeded")
            
            logger.warning(f"速率限制触发: {client_id}, 路径: {path}")
            
            raise HTTPException(
                status_code=429,
                detail={
                    "error": "Rate limit exceeded",
                    "message": f"请求过于频繁，请在 {limit_info['remaining_time']} 秒后重试",
                    "reset_time": limit_info['reset_time'],
                    "limit": limit,
                    "window": window
                }
            )
        
        # 执行请求
        response = await call_next(request)
        
        # 记录请求信息
        self.rate_limiter.record_request(client_id, path, response.status_code)
        
        # 添加速率限制头部
        response.headers["X-RateLimit-Limit"] = str(limit)
        response.headers["X-RateLimit-Remaining"] = str(max(0, limit - limit_info['requests_made']))
        response.headers["X-RateLimit-Reset"] = str(limit_info['reset_time'])
        response.headers["X-RateLimit-Window"] = str(window)
        
        return response
    
    def _is_exempt_path(self, path: str) -> bool:
        """检查是否为豁免路径"""
        return any(path.startswith(exempt) for exempt in self.exempt_paths)
    
    def _get_client_id(self, request: Request) -> str:
        """获取客户端标识"""
        # 优先使用用户ID
        user_id = getattr(request.state, 'user_id', None)
        if user_id:
            return f"user:{user_id}"
        
        # 使用IP地址
        client_ip = self._get_client_ip(request)
        return f"ip:{client_ip}"
    
    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        # 检查代理头部
        forwarded_for = request.headers.get('X-Forwarded-For')
        if forwarded_for:
            return forwarded_for.split(',')[0].strip()
        
        real_ip = request.headers.get('X-Real-IP')
        if real_ip:
            return real_ip
        
        # 使用直接连接IP
        return request.client.host if request.client else 'unknown'
    
    def _get_rate_limit_for_path(self, path: str) -> Tuple[int, int]:
        """获取路径对应的速率限制"""
        # 精确匹配
        if path in self.rate_limiter.rate_limits:
            return self.rate_limiter.rate_limits[path]
        
        # 前缀匹配
        for pattern, (limit, window) in self.rate_limiter.rate_limits.items():
            if pattern == 'default':
                continue
            if path.startswith(pattern):
                return limit, window
        
        # 返回默认限制
        return self.rate_limiter.rate_limits['default']
    
    def _normalize_path(self, path: str) -> str:
        """标准化路径用于缓存键"""
        # 移除查询参数
        if '?' in path:
            path = path.split('?')[0]
        
        # 移除尾部斜杠
        if path.endswith('/') and len(path) > 1:
            path = path[:-1]
        
        return path
