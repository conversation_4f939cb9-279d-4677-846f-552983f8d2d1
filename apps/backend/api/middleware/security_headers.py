#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
安全响应头中间件
"""

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from typing import Callable

from apps.backend.utils.logger import setup_logger

logger = setup_logger("security_headers")


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """安全响应头中间件"""
    
    def __init__(self, app, config: dict = None):
        """
        初始化安全响应头中间件
        
        Args:
            app: FastAPI应用实例
            config: 安全配置字典
        """
        super().__init__(app)
        self.config = config or {}
        
        # 默认安全响应头配置
        self.default_headers = {
            # 防止MIME类型嗅探
            "X-Content-Type-Options": "nosniff",

            # 防止点击劫持
            "X-Frame-Options": "DENY",

            # XSS保护
            "X-XSS-Protection": "1; mode=block",

            # 内容安全策略
            "Content-Security-Policy": (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net; "
                "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net; "
                "img-src 'self' data: https: blob:; "
                "font-src 'self' https://fonts.gstatic.com https://cdn.jsdelivr.net; "
                "connect-src 'self' https:; "
                "media-src 'self' https:; "
                "object-src 'none'; "
                "child-src 'self'; "
                "frame-src 'self'; "
                "worker-src 'self' blob:; "
                "frame-ancestors 'none'; "
                "form-action 'self'; "
                "base-uri 'self'; "
                "manifest-src 'self';"
            ),

            # 推荐人策略
            "Referrer-Policy": "strict-origin-when-cross-origin",

            # 权限策略（增强版）
            "Permissions-Policy": (
                "accelerometer=(), "
                "ambient-light-sensor=(), "
                "autoplay=(), "
                "battery=(), "
                "camera=(), "
                "cross-origin-isolated=(), "
                "display-capture=(), "
                "document-domain=(), "
                "encrypted-media=(), "
                "execution-while-not-rendered=(), "
                "execution-while-out-of-viewport=(), "
                "fullscreen=(), "
                "geolocation=(), "
                "gyroscope=(), "
                "keyboard-map=(), "
                "magnetometer=(), "
                "microphone=(), "
                "midi=(), "
                "navigation-override=(), "
                "payment=(), "
                "picture-in-picture=(), "
                "publickey-credentials-get=(), "
                "screen-wake-lock=(), "
                "sync-xhr=(), "
                "usb=(), "
                "web-share=(), "
                "xr-spatial-tracking=()"
            ),

            # 跨域嵌入器策略
            "Cross-Origin-Embedder-Policy": "require-corp",

            # 跨域开启器策略
            "Cross-Origin-Opener-Policy": "same-origin",

            # 跨域资源策略
            "Cross-Origin-Resource-Policy": "same-origin",
        }
        
        # 合并用户配置
        self.headers = {**self.default_headers, **self.config.get("headers", {})}
        
        # 是否隐藏服务器信息
        self.hide_server_info = self.config.get("hide_server_info", True)
        
        # HTTPS相关配置
        self.force_https = self.config.get("force_https", False)
        self.hsts_enabled = self.config.get("hsts_enabled", False)
        self.hsts_max_age = self.config.get("hsts_max_age", 31536000)  # 1年
        
        logger.info("Security headers middleware initialized")
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        处理请求并添加安全响应头
        
        Args:
            request: 请求对象
            call_next: 下一个中间件或路由处理器
            
        Returns:
            Response: 添加了安全响应头的响应
        """
        try:
            # 检查是否需要强制HTTPS
            if self.force_https and request.url.scheme != "https":
                # 重定向到HTTPS
                https_url = request.url.replace(scheme="https")
                return Response(
                    status_code=301,
                    headers={"Location": str(https_url)}
                )
            
            # 调用下一个处理器
            response = await call_next(request)
            
            # 添加安全响应头
            self._add_security_headers(request, response)
            
            # 隐藏服务器信息
            if self.hide_server_info:
                self._hide_server_info(response)
            
            return response
            
        except Exception as e:
            logger.error(f"Error in security headers middleware: {e}")
            # 即使出错也要继续处理请求
            response = await call_next(request)
            return response
    
    def _add_security_headers(self, request: Request, response: Response):
        """
        添加安全响应头
        
        Args:
            request: 请求对象
            response: 响应对象
        """
        try:
            # 添加基本安全响应头
            for header, value in self.headers.items():
                if header not in response.headers:
                    response.headers[header] = value
            
            # 根据请求协议添加HSTS头
            if self.hsts_enabled and request.url.scheme == "https":
                hsts_value = f"max-age={self.hsts_max_age}; includeSubDomains"
                response.headers["Strict-Transport-Security"] = hsts_value
            
            # 为API响应添加特定的安全头
            if request.url.path.startswith("/api/"):
                self._add_api_security_headers(response)
            
            # 为静态资源添加缓存控制
            if self._is_static_resource(request.url.path):
                self._add_static_resource_headers(response)
                
        except Exception as e:
            logger.error(f"Error adding security headers: {e}")
    
    def _add_api_security_headers(self, response: Response):
        """
        为API响应添加特定的安全头
        
        Args:
            response: 响应对象
        """
        # API响应不应该被缓存
        response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
        response.headers["Pragma"] = "no-cache"
        response.headers["Expires"] = "0"
        
        # API响应的内容类型
        if "content-type" not in response.headers:
            response.headers["Content-Type"] = "application/json; charset=utf-8"
    
    def _add_static_resource_headers(self, response: Response):
        """
        为静态资源添加缓存控制头
        
        Args:
            response: 响应对象
        """
        # 静态资源可以缓存较长时间
        response.headers["Cache-Control"] = "public, max-age=31536000"  # 1年
    
    def _hide_server_info(self, response: Response):
        """
        隐藏服务器信息
        
        Args:
            response: 响应对象
        """
        # 移除或替换可能泄露服务器信息的响应头
        headers_to_remove = ["server", "x-powered-by", "x-aspnet-version"]
        
        for header in headers_to_remove:
            if header in response.headers:
                del response.headers[header]
        
        # 设置通用的服务器标识
        response.headers["Server"] = "WebServer"
    
    def _is_static_resource(self, path: str) -> bool:
        """
        判断是否为静态资源
        
        Args:
            path: 请求路径
            
        Returns:
            bool: 是否为静态资源
        """
        static_extensions = [
            ".css", ".js", ".png", ".jpg", ".jpeg", ".gif", ".svg",
            ".ico", ".woff", ".woff2", ".ttf", ".eot", ".map"
        ]
        
        return any(path.lower().endswith(ext) for ext in static_extensions)


def create_security_headers_middleware(config: dict = None):
    """
    创建安全响应头中间件
    
    Args:
        config: 安全配置字典
        
    Returns:
        SecurityHeadersMiddleware: 安全响应头中间件实例
    """
    return SecurityHeadersMiddleware(None, config)


# 默认安全配置
DEFAULT_SECURITY_CONFIG = {
    "hide_server_info": True,
    "force_https": False,  # 在生产环境中应该设置为True
    "hsts_enabled": False,  # 在HTTPS环境中应该设置为True
    "hsts_max_age": 31536000,
    "headers": {
        "X-Content-Type-Options": "nosniff",
        "X-Frame-Options": "DENY",
        "X-XSS-Protection": "1; mode=block",
        "Content-Security-Policy": (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
            "style-src 'self' 'unsafe-inline'; "
            "img-src 'self' data: https:; "
            "font-src 'self' https:; "
            "connect-src 'self' https:; "
            "frame-ancestors 'none';"
        ),
        "Referrer-Policy": "strict-origin-when-cross-origin",
        "Permissions-Policy": (
            "geolocation=(), "
            "microphone=(), "
            "camera=(), "
            "payment=(), "
            "usb=(), "
            "magnetometer=(), "
            "gyroscope=(), "
            "speaker=()"
        )
    }
}


def get_security_config():
    """
    获取安全配置
    
    Returns:
        dict: 安全配置字典
    """
    import os
    
    # 从环境变量读取配置
    config = DEFAULT_SECURITY_CONFIG.copy()
    
    # 生产环境配置
    if os.getenv("ENVIRONMENT") == "production":
        config.update({
            "force_https": True,
            "hsts_enabled": True,
            "headers": {
                **config["headers"],
                "Content-Security-Policy": (
                    "default-src 'self'; "
                    "script-src 'self'; "
                    "style-src 'self'; "
                    "img-src 'self' data: https:; "
                    "font-src 'self' https:; "
                    "connect-src 'self' https:; "
                    "frame-ancestors 'none';"
                )
            }
        })
    
    return config
