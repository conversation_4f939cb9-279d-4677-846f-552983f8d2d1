#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
高级限流中间件
"""

import time
import json
from typing import Dict, Any, Optional, List
from fastapi import Request, Response
from fastapi.responses import J<PERSON>NResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from apps.backend.api.config.rate_limit import (
    get_rate_limit_config, RateLimitMatcher, RateLimitLevel
)
from apps.backend.api.utils.rate_limiter import rate_limiter_factory
from apps.backend.utils.logger import setup_logger

logger = setup_logger("advanced_rate_limit")


class AdvancedRateLimitMiddleware(BaseHTTPMiddleware):
    """高级限流中间件"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.config = get_rate_limit_config()
        self.matcher = RateLimitMatcher()
        self.metrics = {
            "total_requests": 0,
            "blocked_requests": 0,
            "rate_limit_hits": {}
        }
    
    async def dispatch(self, request: Request, call_next):
        """处理请求限流"""
        # 检查是否启用限流
        if not self.config.get("enabled", True):
            return await call_next(request)
        
        # 更新请求计数
        self.metrics["total_requests"] += 1
        
        # 获取客户端信息
        client_info = self._get_client_info(request)
        
        # 检查IP黑名单
        if self.matcher.is_ip_blacklisted(client_info["ip"]):
            logger.warning(f"Blocked request from blacklisted IP: {client_info['ip']}")
            return self._create_rate_limit_response("IP已被禁止访问")
        
        # 检查IP白名单
        if self.matcher.is_ip_whitelisted(client_info["ip"]):
            return await call_next(request)
        
        # 执行多级限流检查
        rate_limit_result = await self._check_rate_limits(request, client_info)
        
        if not rate_limit_result["allowed"]:
            # 记录限流事件
            self._record_rate_limit_event(client_info, rate_limit_result)
            
            # 返回限流响应
            return self._create_rate_limit_response(
                rate_limit_result.get("message", "请求过于频繁"),
                rate_limit_result.get("info", {})
            )
        
        # 执行请求
        response = await call_next(request)
        
        # 添加限流头信息
        self._add_rate_limit_headers(response, rate_limit_result.get("info", {}))
        
        return response
    
    def _get_client_info(self, request: Request) -> Dict[str, Any]:
        """获取客户端信息"""
        # 获取IP地址
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            ip = forwarded_for.split(",")[0].strip()
        else:
            real_ip = request.headers.get("X-Real-IP")
            ip = real_ip if real_ip else (request.client.host if request.client else "unknown")
        
        # 获取用户信息
        user_id = None
        user_role = "anonymous"
        
        # 从请求头或JWT中提取用户信息
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            try:
                # 这里应该解析JWT获取用户信息
                # 为了简化，使用模拟数据
                user_id = "user_123"
                user_role = "authenticated"
            except Exception:
                pass
        
        return {
            "ip": ip,
            "user_id": user_id,
            "user_role": user_role,
            "user_agent": request.headers.get("User-Agent", ""),
            "path": request.url.path,
            "method": request.method
        }
    
    async def _check_rate_limits(self, request: Request, client_info: Dict[str, Any]) -> Dict[str, Any]:
        """检查多级限流"""
        path = client_info["path"]
        ip = client_info["ip"]
        user_id = client_info["user_id"]
        user_role = client_info["user_role"]
        
        # 1. 检查端点特定限流
        endpoint_limits = self.matcher.match_endpoint(path)
        if endpoint_limits:
            result = await self._check_single_rate_limit(
                f"endpoint:{path}:{ip}",
                endpoint_limits,
                "endpoint"
            )
            if not result["allowed"]:
                result["message"] = f"端点 {path} 访问频率超限"
                return result
        
        # 2. 检查用户组限流
        user_group_limits = self.matcher.get_user_group_limits(user_role)
        if user_group_limits:
            key = f"user_group:{user_role}:{user_id or ip}"
            result = await self._check_single_rate_limit(
                key,
                user_group_limits,
                "user_group"
            )
            if not result["allowed"]:
                result["message"] = f"用户组 {user_role} 访问频率超限"
                return result
        
        # 3. 检查用户级限流
        user_limits = self.config["user_limits"]
        if user_limits["enabled"]:
            if user_id:
                limits = user_limits["authenticated"]
                key = f"user:{user_id}"
            else:
                limits = user_limits["anonymous"]
                key = f"anonymous:{ip}"
            
            result = await self._check_single_rate_limit(key, limits, "user")
            if not result["allowed"]:
                result["message"] = "用户访问频率超限"
                return result
        
        # 4. 检查IP级限流
        ip_limits = self.config["ip_limits"]
        if ip_limits["enabled"]:
            result = await self._check_single_rate_limit(
                f"ip:{ip}",
                ip_limits,
                "ip"
            )
            if not result["allowed"]:
                result["message"] = "IP访问频率超限"
                return result
        
        # 5. 检查全局限流
        default_limits = self.config["default_limits"]
        result = await self._check_single_rate_limit(
            "global",
            default_limits,
            "global"
        )
        if not result["allowed"]:
            result["message"] = "全局访问频率超限"
            return result
        
        return {"allowed": True, "info": result.get("info", {})}
    
    async def _check_single_rate_limit(
        self, 
        key: str, 
        limits: Dict[str, Any], 
        level: str
    ) -> Dict[str, Any]:
        """检查单个限流规则"""
        try:
            strategy = self.config.get("strategy", "sliding_window")
            
            # 检查每分钟限制
            if "requests_per_minute" in limits:
                limiter = rate_limiter_factory.create_limiter(
                    strategy=strategy,
                    key=f"{key}:minute",
                    limit=limits["requests_per_minute"],
                    window=60
                )
                allowed, info = limiter.is_allowed()
                if not allowed:
                    return {
                        "allowed": False,
                        "level": level,
                        "period": "minute",
                        "info": info
                    }
            
            # 检查每小时限制
            if "requests_per_hour" in limits:
                limiter = rate_limiter_factory.create_limiter(
                    strategy=strategy,
                    key=f"{key}:hour",
                    limit=limits["requests_per_hour"],
                    window=3600
                )
                allowed, info = limiter.is_allowed()
                if not allowed:
                    return {
                        "allowed": False,
                        "level": level,
                        "period": "hour",
                        "info": info
                    }
            
            # 检查每日限制
            if "requests_per_day" in limits:
                limiter = rate_limiter_factory.create_limiter(
                    strategy=strategy,
                    key=f"{key}:day",
                    limit=limits["requests_per_day"],
                    window=86400
                )
                allowed, info = limiter.is_allowed()
                if not allowed:
                    return {
                        "allowed": False,
                        "level": level,
                        "period": "day",
                        "info": info
                    }
            
            return {"allowed": True, "level": level, "info": info}
            
        except Exception as e:
            logger.error(f"Error checking rate limit for {key}: {e}")
            return {"allowed": True, "error": str(e)}
    
    def _create_rate_limit_response(
        self, 
        message: str, 
        info: Optional[Dict[str, Any]] = None
    ) -> JSONResponse:
        """创建限流响应"""
        response_config = self.config["response_config"]
        
        headers = {}
        if info:
            headers["X-RateLimit-Limit"] = str(info.get("limit", ""))
            headers["X-RateLimit-Remaining"] = str(info.get("remaining", ""))
            headers["X-RateLimit-Reset"] = str(int(info.get("reset", time.time())))
            
            # 计算重试时间
            reset_time = info.get("reset", time.time())
            retry_after = max(1, int(reset_time - time.time()))
            headers["Retry-After"] = str(retry_after)
        
        # 更新阻止请求计数
        self.metrics["blocked_requests"] += 1
        
        return JSONResponse(
            status_code=response_config["status_code"],
            content={
                "error": "Rate limit exceeded",
                "message": message,
                "code": response_config["status_code"]
            },
            headers=headers
        )
    
    def _add_rate_limit_headers(self, response: Response, info: Dict[str, Any]):
        """添加限流头信息"""
        if info:
            response.headers["X-RateLimit-Limit"] = str(info.get("limit", ""))
            response.headers["X-RateLimit-Remaining"] = str(info.get("remaining", ""))
            response.headers["X-RateLimit-Reset"] = str(int(info.get("reset", time.time())))
    
    def _record_rate_limit_event(self, client_info: Dict[str, Any], result: Dict[str, Any]):
        """记录限流事件"""
        event = {
            "timestamp": time.time(),
            "ip": client_info["ip"],
            "user_id": client_info.get("user_id"),
            "path": client_info["path"],
            "method": client_info["method"],
            "level": result.get("level"),
            "period": result.get("period"),
            "limit": result.get("info", {}).get("limit"),
            "current": result.get("info", {}).get("current")
        }
        
        # 记录到日志
        logger.warning(f"Rate limit exceeded: {json.dumps(event)}")
        
        # 更新指标
        level = result.get("level", "unknown")
        if level not in self.metrics["rate_limit_hits"]:
            self.metrics["rate_limit_hits"][level] = 0
        self.metrics["rate_limit_hits"][level] += 1
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取限流指标"""
        return {
            "total_requests": self.metrics["total_requests"],
            "blocked_requests": self.metrics["blocked_requests"],
            "block_rate": (
                self.metrics["blocked_requests"] / self.metrics["total_requests"] 
                if self.metrics["total_requests"] > 0 else 0
            ),
            "rate_limit_hits": self.metrics["rate_limit_hits"],
            "timestamp": time.time()
        }
