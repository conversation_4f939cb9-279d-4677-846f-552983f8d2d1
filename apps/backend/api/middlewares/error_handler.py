from typing import Callable

from fastapi import Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from apps.backend.api.utils.logger import setup_logger
from apps.backend.api.utils.response import error_response

# 设置日志
logger = setup_logger("api_error_handler")


class ErrorHandlerMiddleware(BaseHTTPMiddleware):
    """错误处理中间件"""

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        try:
            return await call_next(request)
        except Exception as e:
            # 记录异常日志
            logger.exception(f"Unhandled exception: {str(e)}")

            # 返回错误响应
            return JSONResponse(
                status_code=500,
                content=error_response(
                    message="Internal server error",
                    code=500,
                    data={"detail": str(e)} if request.app.debug else None,
                ),
            )
