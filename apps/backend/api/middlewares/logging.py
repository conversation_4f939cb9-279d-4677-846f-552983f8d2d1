import time
from typing import Callable

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from apps.backend.api.utils.logger import setup_logger

# 设置日志
logger = setup_logger("api_middleware")


class LoggingMiddleware(BaseHTTPMiddleware):
    """日志中间件"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 开始时间
        start_time = time.time()
        
        # 请求信息
        request_id = request.headers.get("X-Request-ID", "")
        method = request.method
        url = str(request.url)
        client_host = request.client.host if request.client else ""
        
        # 记录请求日志
        logger.info(f"Request: {method} {url} from {client_host} (Request ID: {request_id})")
        
        # 处理请求
        try:
            response = await call_next(request)
            
            # 结束时间
            process_time = time.time() - start_time
            
            # 记录响应日志
            logger.info(f"Response: {method} {url} - {response.status_code} in {process_time:.4f}s (Request ID: {request_id})")
            
            # 添加处理时间头
            response.headers["X-Process-Time"] = str(process_time)
            
            return response
        except Exception as e:
            # 结束时间
            process_time = time.time() - start_time
            
            # 记录异常日志
            logger.error(f"Error: {method} {url} - {str(e)} in {process_time:.4f}s (Request ID: {request_id})")
            
            # 重新抛出异常
            raise
