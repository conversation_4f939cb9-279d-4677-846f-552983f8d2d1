#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
监控中间件
"""

import time
import sys
from typing import Dict, Any
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from apps.backend.api.utils.metrics import business_metrics
from apps.backend.api.config.monitoring import get_monitoring_config
from apps.backend.utils.logger import setup_logger

logger = setup_logger("monitoring_middleware")


class MonitoringMiddleware(BaseHTTPMiddleware):
    """监控中间件"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.config = get_monitoring_config()
        self.enabled = self.config.get("enabled", False)
    
    async def dispatch(self, request: Request, call_next):
        """处理请求监控"""
        if not self.enabled:
            return await call_next(request)
        
        # 记录请求开始时间
        start_time = time.time()
        
        # 获取请求信息
        method = request.method
        path = request.url.path
        
        # 简化路径（移除动态参数）
        endpoint = self._normalize_endpoint(path)
        
        # 获取请求大小
        request_size = 0
        if hasattr(request, 'headers') and 'content-length' in request.headers:
            try:
                request_size = int(request.headers['content-length'])
            except (ValueError, TypeError):
                pass
        
        # 执行请求
        response = None
        status_code = 500
        exception_occurred = False
        
        try:
            response = await call_next(request)
            status_code = response.status_code
        except Exception as e:
            exception_occurred = True
            # 记录异常
            business_metrics.record_exception(
                exception_type=type(e).__name__,
                module=self.__class__.__module__
            )
            business_metrics.record_error(
                error_type="request_exception",
                severity="error"
            )
            logger.error(f"Request exception: {e}")
            raise
        finally:
            # 计算请求持续时间
            duration = time.time() - start_time
            
            # 获取响应大小
            response_size = 0
            if response and hasattr(response, 'headers') and 'content-length' in response.headers:
                try:
                    response_size = int(response.headers['content-length'])
                except (ValueError, TypeError):
                    pass
            
            # 记录HTTP请求指标
            business_metrics.record_http_request(
                method=method,
                endpoint=endpoint,
                status_code=status_code,
                duration=duration,
                request_size=request_size,
                response_size=response_size
            )
            
            # 记录错误
            if status_code >= 400:
                error_type = "client_error" if status_code < 500 else "server_error"
                severity = "warning" if status_code < 500 else "error"
                business_metrics.record_error(error_type, severity)
        
        return response
    
    def _normalize_endpoint(self, path: str) -> str:
        """标准化端点路径"""
        # 移除查询参数
        if '?' in path:
            path = path.split('?')[0]
        
        # 替换常见的动态参数
        path_parts = path.split('/')
        normalized_parts = []
        
        for part in path_parts:
            # 检查是否为数字ID
            if part.isdigit():
                normalized_parts.append('{id}')
            # 检查是否为UUID
            elif len(part) == 36 and part.count('-') == 4:
                normalized_parts.append('{uuid}')
            # 检查是否为其他动态参数
            elif part and not part.isalnum() and len(part) > 10:
                normalized_parts.append('{param}')
            else:
                normalized_parts.append(part)
        
        return '/'.join(normalized_parts)


class HealthCheckMiddleware(BaseHTTPMiddleware):
    """健康检查中间件"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.config = get_monitoring_config("health_checks")
        self.enabled = self.config.get("enabled", False)
        self.endpoint = self.config.get("endpoint", "/health")
    
    async def dispatch(self, request: Request, call_next):
        """处理健康检查"""
        if not self.enabled or request.url.path != self.endpoint:
            return await call_next(request)
        
        # 执行健康检查
        health_status = await self._perform_health_checks()
        
        # 返回健康检查结果
        from fastapi.responses import JSONResponse
        
        status_code = 200 if health_status["status"] == "healthy" else 503
        
        return JSONResponse(
            status_code=status_code,
            content=health_status
        )
    
    async def _perform_health_checks(self) -> Dict[str, Any]:
        """执行健康检查"""
        checks = self.config.get("checks", {})
        results = {}
        overall_status = "healthy"
        
        for check_name, check_config in checks.items():
            if not check_config.get("enabled", True):
                continue
            
            try:
                result = await self._check_component(check_name, check_config)
                results[check_name] = result
                
                # 如果是关键组件且不健康，则整体状态为不健康
                if check_config.get("critical", False) and not result["healthy"]:
                    overall_status = "unhealthy"
                elif not result["healthy"] and overall_status == "healthy":
                    overall_status = "degraded"
                    
            except Exception as e:
                results[check_name] = {
                    "healthy": False,
                    "error": str(e),
                    "timestamp": time.time()
                }
                
                if check_config.get("critical", False):
                    overall_status = "unhealthy"
                elif overall_status == "healthy":
                    overall_status = "degraded"
        
        return {
            "status": overall_status,
            "timestamp": time.time(),
            "checks": results
        }
    
    async def _check_component(self, component: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """检查单个组件"""
        timeout = config.get("timeout", 5)
        start_time = time.time()
        
        try:
            if component == "database":
                healthy = await self._check_database(timeout)
            elif component == "redis":
                healthy = await self._check_redis(timeout)
            elif component == "elasticsearch":
                healthy = await self._check_elasticsearch(timeout)
            elif component == "external_apis":
                healthy = await self._check_external_apis(timeout)
            else:
                healthy = True
            
            duration = time.time() - start_time
            
            return {
                "healthy": healthy,
                "response_time": duration,
                "timestamp": time.time()
            }
            
        except Exception as e:
            duration = time.time() - start_time
            return {
                "healthy": False,
                "error": str(e),
                "response_time": duration,
                "timestamp": time.time()
            }
    
    async def _check_database(self, timeout: int) -> bool:
        """检查数据库连接"""
        try:
            from apps.backend.storage.db_manager import get_db_manager
            db_manager = get_db_manager()
            
            # 执行简单查询
            with db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                return result is not None
        except Exception:
            return False
    
    async def _check_redis(self, timeout: int) -> bool:
        """检查Redis连接"""
        try:
            from apps.backend.api.config.redis import get_redis
            redis_client = get_redis()
            
            # 执行ping命令
            return redis_client.ping()
        except Exception:
            return False
    
    async def _check_elasticsearch(self, timeout: int) -> bool:
        """检查Elasticsearch连接"""
        try:
            from apps.backend.api.config.elasticsearch import get_elasticsearch
            es_client = get_elasticsearch()
            
            if es_client:
                return es_client.ping()
            return False
        except Exception:
            return False
    
    async def _check_external_apis(self, timeout: int) -> bool:
        """检查外部API"""
        # 这里可以检查依赖的外部API
        # 为了演示，直接返回True
        return True


class MetricsMiddleware(BaseHTTPMiddleware):
    """指标中间件"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.config = get_monitoring_config("metrics")
        self.enabled = self.config.get("enabled", False)
        self.metrics_path = self.config.get("path", "/metrics")
    
    async def dispatch(self, request: Request, call_next):
        """处理指标请求"""
        if not self.enabled or request.url.path != self.metrics_path:
            return await call_next(request)
        
        # 返回Prometheus指标
        from apps.backend.api.utils.metrics import metrics_collector
        from fastapi.responses import Response
        
        metrics_data = metrics_collector.get_metrics()
        
        return Response(
            content=metrics_data,
            media_type="text/plain; version=0.0.4; charset=utf-8"
        )


class PerformanceMiddleware(BaseHTTPMiddleware):
    """性能监控中间件"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.config = get_monitoring_config("performance")
        self.enabled = self.config.get("enabled", False)
        self.profiling_enabled = self.config.get("profiling", {}).get("enabled", False)
        self.sample_rate = self.config.get("profiling", {}).get("sample_rate", 0.01)
    
    async def dispatch(self, request: Request, call_next):
        """处理性能监控"""
        if not self.enabled:
            return await call_next(request)
        
        # 性能分析（仅在开发环境启用）
        if self.profiling_enabled and self._should_profile():
            return await self._profile_request(request, call_next)
        
        return await call_next(request)
    
    def _should_profile(self) -> bool:
        """判断是否应该进行性能分析"""
        import random
        return random.random() < self.sample_rate
    
    async def _profile_request(self, request: Request, call_next):
        """性能分析请求"""
        import cProfile
        import pstats
        import io
        from datetime import datetime
        
        # 创建性能分析器
        profiler = cProfile.Profile()
        
        try:
            profiler.enable()
            response = await call_next(request)
            profiler.disable()
            
            # 保存性能分析结果
            output_dir = self.config.get("profiling", {}).get("output_dir", "./profiles")
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{output_dir}/profile_{timestamp}_{request.url.path.replace('/', '_')}.prof"
            
            # 确保目录存在
            import os
            os.makedirs(output_dir, exist_ok=True)
            
            # 保存分析结果
            profiler.dump_stats(filename)
            
            # 记录性能分析信息
            s = io.StringIO()
            ps = pstats.Stats(profiler, stream=s)
            ps.sort_stats('cumulative')
            ps.print_stats(10)  # 只打印前10个函数
            
            logger.info(f"Performance profile saved: {filename}")
            logger.debug(f"Profile summary:\n{s.getvalue()}")
            
            return response
            
        except Exception as e:
            logger.error(f"Error during profiling: {e}")
            profiler.disable()
            return await call_next(request)
