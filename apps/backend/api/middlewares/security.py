#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
安全中间件
提供各种安全防护功能
"""

import time
import hashlib
import secrets
from typing import Dict, Any, Optional, List
from fastapi import Request, Response, HTTPException
from fastapi.responses import J<PERSON>NResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from apps.backend.api.config.security import get_security_config, SECURITY_CONFIG
from apps.backend.api.utils.validators import input_validator
from apps.backend.utils.logger import setup_logger

logger = setup_logger("security_middleware")


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """安全头中间件"""

    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.headers_config = get_security_config("security_headers")

    async def dispatch(self, request: Request, call_next):
        """处理请求并添加安全头"""
        response = await call_next(request)

        # 添加安全头
        for header_name, header_value in self.headers_config.items():
            # 转换下划线为连字符
            header_name = header_name.replace("_", "-")
            response.headers[header_name] = header_value

        return response


class RateLimitMiddleware(BaseHTTPMiddleware):
    """速率限制中间件"""

    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.rate_config = get_security_config("rate_limiting")
        self.requests = {}  # 存储请求计数
        self.cleanup_interval = 300  # 5分钟清理一次
        self.last_cleanup = time.time()

    async def dispatch(self, request: Request, call_next):
        """处理速率限制"""
        if not self.rate_config.get("enabled", True):
            return await call_next(request)

        # 获取客户端IP
        client_ip = self._get_client_ip(request)

        # 检查速率限制
        if self._is_rate_limited(client_ip, request.url.path):
            logger.warning(f"Rate limit exceeded for IP {client_ip} on path {request.url.path}")
            return JSONResponse(
                status_code=429,
                content={"error": "Rate limit exceeded", "message": "请求过于频繁，请稍后再试"}
            )

        # 记录请求
        self._record_request(client_ip, request.url.path)

        # 定期清理过期记录
        self._cleanup_expired_records()

        return await call_next(request)

    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        # 检查代理头
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()

        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip

        return request.client.host if request.client else "unknown"

    def _is_rate_limited(self, client_ip: str, path: str) -> bool:
        """检查是否超过速率限制"""
        current_time = time.time()

        # 确定限制规则
        limit_key = self._get_limit_key(path)
        limit_config = self._parse_limit(self.rate_config.get(limit_key, "100/hour"))

        # 检查请求记录
        key = f"{client_ip}:{limit_key}"
        if key not in self.requests:
            return False

        # 过滤时间窗口内的请求
        window_start = current_time - limit_config["window"]
        recent_requests = [
            req_time for req_time in self.requests[key]
            if req_time > window_start
        ]

        return len(recent_requests) >= limit_config["count"]

    def _record_request(self, client_ip: str, path: str):
        """记录请求"""
        current_time = time.time()
        limit_key = self._get_limit_key(path)
        key = f"{client_ip}:{limit_key}"

        if key not in self.requests:
            self.requests[key] = []

        self.requests[key].append(current_time)

    def _get_limit_key(self, path: str) -> str:
        """根据路径确定限制键"""
        if "/auth/login" in path:
            return "login_limit"
        elif "/search" in path:
            return "search_limit"
        elif "/api/" in path:
            return "api_limit"
        else:
            return "default_limit"

    def _parse_limit(self, limit_str: str) -> Dict[str, int]:
        """解析限制字符串"""
        # 格式: "100/hour", "10/minute"
        count, period = limit_str.split("/")
        count = int(count)

        if period == "minute":
            window = 60
        elif period == "hour":
            window = 3600
        elif period == "day":
            window = 86400
        else:
            window = 3600  # 默认1小时

        return {"count": count, "window": window}

    def _cleanup_expired_records(self):
        """清理过期记录"""
        current_time = time.time()

        if current_time - self.last_cleanup < self.cleanup_interval:
            return

        # 清理1小时前的记录
        cutoff_time = current_time - 3600

        for key in list(self.requests.keys()):
            self.requests[key] = [
                req_time for req_time in self.requests[key]
                if req_time > cutoff_time
            ]

            # 删除空列表
            if not self.requests[key]:
                del self.requests[key]

        self.last_cleanup = current_time


class IPFilteringMiddleware(BaseHTTPMiddleware):
    """IP过滤中间件"""

    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.ip_config = get_security_config("ip_filtering")

    async def dispatch(self, request: Request, call_next):
        """处理IP过滤"""
        if not self.ip_config.get("enabled", False):
            return await call_next(request)

        client_ip = self._get_client_ip(request)

        # 检查黑名单
        blacklist = self.ip_config.get("blacklist", [])
        if blacklist and client_ip in blacklist:
            logger.warning(f"Blocked request from blacklisted IP: {client_ip}")
            return JSONResponse(
                status_code=403,
                content={"error": "Access denied", "message": "访问被拒绝"}
            )

        # 检查白名单
        whitelist = self.ip_config.get("whitelist", [])
        if whitelist and client_ip not in whitelist:
            logger.warning(f"Blocked request from non-whitelisted IP: {client_ip}")
            return JSONResponse(
                status_code=403,
                content={"error": "Access denied", "message": "访问被拒绝"}
            )

        return await call_next(request)

    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()

        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip

        return request.client.host if request.client else "unknown"


class CSRFProtectionMiddleware(BaseHTTPMiddleware):
    """CSRF防护中间件"""

    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.csrf_config = get_security_config("csrf_protection")
        self.tokens = {}  # 存储CSRF令牌

    async def dispatch(self, request: Request, call_next):
        """处理CSRF防护"""
        if not self.csrf_config.get("enabled", True):
            return await call_next(request)

        # 对于安全方法（GET, HEAD, OPTIONS）不需要CSRF检查
        if request.method in ["GET", "HEAD", "OPTIONS"]:
            response = await call_next(request)
            # 为响应添加CSRF令牌
            csrf_token = self._generate_csrf_token()
            response.headers["X-CSRF-Token"] = csrf_token
            return response

        # 对于不安全方法，检查CSRF令牌
        csrf_token = request.headers.get("X-CSRF-Token")
        if not csrf_token or not self._validate_csrf_token(csrf_token):
            logger.warning(f"CSRF token validation failed for {request.url.path}")
            return JSONResponse(
                status_code=403,
                content={"error": "CSRF token invalid", "message": "CSRF令牌无效"}
            )

        return await call_next(request)

    def _generate_csrf_token(self) -> str:
        """生成CSRF令牌"""
        token = secrets.token_urlsafe(self.csrf_config.get("token_length", 32))
        expire_time = time.time() + self.csrf_config.get("token_expire", 3600)

        self.tokens[token] = expire_time
        return token

    def _validate_csrf_token(self, token: str) -> bool:
        """验证CSRF令牌"""
        if token not in self.tokens:
            return False

        # 检查是否过期
        if time.time() > self.tokens[token]:
            del self.tokens[token]
            return False

        return True


class InputValidationMiddleware(BaseHTTPMiddleware):
    """输入验证中间件"""

    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.max_request_size = get_security_config("input_validation").get("max_request_size", 10 * 1024 * 1024)

    async def dispatch(self, request: Request, call_next):
        """处理输入验证"""
        # 检查请求大小
        content_length = request.headers.get("content-length")
        if content_length and int(content_length) > self.max_request_size:
            logger.warning(f"Request too large: {content_length} bytes")
            return JSONResponse(
                status_code=413,
                content={"error": "Request too large", "message": "请求体过大"}
            )

        # 验证Content-Type
        if request.method in ["POST", "PUT", "PATCH"]:
            content_type = request.headers.get("content-type", "")
            if not content_type.startswith(("application/json", "application/x-www-form-urlencoded", "multipart/form-data")):
                logger.warning(f"Invalid content type: {content_type}")
                return JSONResponse(
                    status_code=400,
                    content={"error": "Invalid content type", "message": "不支持的内容类型"}
                )

        return await call_next(request)


class SecurityAuditMiddleware(BaseHTTPMiddleware):
    """安全审计中间件"""

    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.audit_config = get_security_config("audit_log")

    async def dispatch(self, request: Request, call_next):
        """处理安全审计"""
        start_time = time.time()
        client_ip = self._get_client_ip(request)

        # 记录请求开始
        logger.info(f"Request started: {request.method} {request.url.path} from {client_ip}")

        try:
            response = await call_next(request)

            # 记录请求完成
            duration = time.time() - start_time
            logger.info(f"Request completed: {request.method} {request.url.path} - {response.status_code} in {duration:.3f}s")

            # 记录安全事件
            if response.status_code >= 400:
                self._log_security_event("error_occurred", {
                    "method": request.method,
                    "path": request.url.path,
                    "status_code": response.status_code,
                    "client_ip": client_ip,
                    "duration": duration
                })

            return response

        except Exception as e:
            # 记录异常
            duration = time.time() - start_time
            logger.error(f"Request failed: {request.method} {request.url.path} - {str(e)} in {duration:.3f}s")

            self._log_security_event("error_occurred", {
                "method": request.method,
                "path": request.url.path,
                "error": str(e),
                "client_ip": client_ip,
                "duration": duration
            })

            raise

    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()

        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip

        return request.client.host if request.client else "unknown"

    def _log_security_event(self, event_type: str, details: Dict[str, Any]):
        """记录安全事件"""
        if not self.audit_config.get("enabled", True):
            return

        events_to_log = self.audit_config.get("events_to_log", [])
        if event_type not in events_to_log:
            return

        logger.info(f"Security event: {event_type} - {details}")


# 安全中间件工厂函数
def create_security_middlewares() -> List[BaseHTTPMiddleware]:
    """
    创建安全中间件列表

    Returns:
        List[BaseHTTPMiddleware]: 安全中间件列表
    """
    middlewares = []

    # 添加各种安全中间件
    middlewares.append(SecurityHeadersMiddleware)
    middlewares.append(RateLimitMiddleware)
    middlewares.append(IPFilteringMiddleware)
    middlewares.append(CSRFProtectionMiddleware)
    middlewares.append(InputValidationMiddleware)
    middlewares.append(SecurityAuditMiddleware)

    return middlewares
