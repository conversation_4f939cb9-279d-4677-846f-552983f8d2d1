from apps.backend.api.models.base import BaseModel
from apps.backend.api.models.user import User, UserProfile
from apps.backend.api.models.job import Job, JobDetail
from apps.backend.api.models.interaction import Favorite, BrowseHistory, Subscription
from apps.backend.api.models.community import CommunityPost, Comment
from apps.backend.api.models.content import Policy, Banner, SystemConfig
# 注释掉旧的admin模型导入，现在使用统一的用户模型
# from apps.backend.api.models.admin import Admin, Role, Permission, OperationLog, role_permissions
from apps.backend.api.models.crawler import CrawlerTask, CrawlerTaskLog

# 导出所有模型
__all__ = [
    "BaseModel",
    "User",
    "UserProfile",
    "Job",
    "JobDetail",
    "Favorite",
    "BrowseHistory",
    "Subscription",
    "CommunityPost",
    "Comment",
    "Policy",
    "Banner",
    "SystemConfig",
    # 管理员相关 - 现在使用统一的用户模型
    # "Admin",
    # "Role",
    # "Permission",
    # "OperationLog",
    # "role_permissions",
    "CrawlerTask",
    "CrawlerTaskLog",
]
