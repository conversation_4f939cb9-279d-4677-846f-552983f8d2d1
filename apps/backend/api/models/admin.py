from sqlalchemy import Column, String, Integer, <PERSON>olean, DateTime, Text, ForeignKey, Table
from sqlalchemy.orm import relationship

from apps.backend.api.models.base import BaseModel


# 管理员角色-权限关联表（独立的管理员权限系统）
admin_role_permissions = Table(
    "admin_role_permissions",
    BaseModel.metadata,
    Column("id", Integer, primary_key=True, autoincrement=True),
    Column("admin_role_id", Integer, ForeignKey("admin_roles.id", ondelete="CASCADE"), nullable=False),
    Column("admin_permission_id", Integer, ForeignKey("admin_permissions.id", ondelete="CASCADE"), nullable=False),
    Column("created_at", DateTime, nullable=False),
    extend_existing=True
)


class Admin(BaseModel):
    """管理员模型"""
    
    __tablename__ = "admins"
    
    username = Column(String(50), unique=True, index=True, nullable=False)
    password_hash = Column(String(128), nullable=False)
    real_name = Column(String(50), nullable=True)
    email = Column(String(100), nullable=True)
    phone = Column(String(20), nullable=True)
    admin_role_id = Column(Integer, ForeignKey("admin_roles.id"), nullable=False)
    last_login = Column(DateTime, nullable=True)
    status = Column(Boolean, default=True, nullable=False)

    # 关系
    admin_role = relationship("AdminRole", back_populates="admins")
    operation_logs = relationship("OperationLog", back_populates="admin", cascade="all, delete-orphan")
    
    __table_args__ = (
        {"mysql_charset": "utf8mb4", "mysql_collate": "utf8mb4_unicode_ci"},
    )


class AdminRole(BaseModel):
    """管理员角色模型 - 独立的管理员角色系统"""

    __tablename__ = "admin_roles"

    name = Column(String(50), unique=True, nullable=False)
    display_name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)

    # 关系
    admins = relationship("Admin", back_populates="admin_role")
    admin_permissions = relationship("AdminPermission", secondary=admin_role_permissions, back_populates="admin_roles")

    __table_args__ = (
        {"mysql_charset": "utf8mb4", "mysql_collate": "utf8mb4_unicode_ci", "extend_existing": True},
    )


class AdminPermission(BaseModel):
    """管理员权限模型 - 独立的管理员权限系统"""

    __tablename__ = "admin_permissions"

    name = Column(String(50), unique=True, nullable=False)
    code = Column(String(50), unique=True, nullable=False)
    description = Column(String(255), nullable=True)

    # 关系
    admin_roles = relationship("AdminRole", secondary=admin_role_permissions, back_populates="admin_permissions")

    __table_args__ = (
        {"mysql_charset": "utf8mb4", "mysql_collate": "utf8mb4_unicode_ci", "extend_existing": True},
    )


class OperationLog(BaseModel):
    """操作日志模型"""
    
    __tablename__ = "operation_logs"
    
    admin_id = Column(Integer, ForeignKey("admins.id", ondelete="CASCADE"), nullable=False)
    module = Column(String(50), index=True, nullable=False)
    operation = Column(String(50), index=True, nullable=False)
    content = Column(Text, nullable=True)
    ip = Column(String(50), nullable=True)
    user_agent = Column(String(255), nullable=True)
    
    # 关系
    admin = relationship("Admin", back_populates="operation_logs")
    
    __table_args__ = (
        {"mysql_charset": "utf8mb4", "mysql_collate": "utf8mb4_unicode_ci"},
    )
