#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI管理相关的数据模型
"""

import uuid
from datetime import datetime
from typing import Dict, Any, List
from sqlalchemy import Column, String, Text, DateTime, Float, Integer, Boolean, JSON, Enum as SQLEnum
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.dialects.mysql import CHAR

from apps.backend.api.models.base import BaseModel
from apps.backend.api.schemas.ai_management import ModelStatus, ModelType, MatchRuleType, RuleStatus, TagType, TagStatus

Base = declarative_base()


class AIModelModel(BaseModel):
    """AI模型数据模型"""
    __tablename__ = "ai_models"

    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String(255), nullable=False, comment="模型名称")
    type = Column(SQLEnum(ModelType), nullable=False, comment="模型类型")
    version = Column(String(50), nullable=False, comment="版本号")
    description = Column(Text, comment="模型描述")
    api_endpoint = Column(String(255), nullable=False, comment="API端点")
    parameters = Column(JSON, default={}, comment="模型参数")
    status = Column(SQLEnum(ModelStatus), default=ModelStatus.INACTIVE, comment="模型状态")
    
    # 性能指标
    accuracy = Column(Float, default=0.0, comment="准确率")
    response_time = Column(Float, default=0.0, comment="响应时间")
    call_count = Column(Integer, default=0, comment="调用次数")
    error_rate = Column(Float, default=0.0, comment="错误率")
    
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    def __repr__(self):
        return f"<AIModel(id={self.id}, name={self.name}, type={self.type})>"


class RecommendationConfigModel(BaseModel):
    """推荐配置数据模型"""
    __tablename__ = "recommendation_configs"

    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # 算法权重配置
    content_based_weight = Column(Float, default=40.0, comment="基于内容推荐权重")
    collaborative_filtering_weight = Column(Float, default=30.0, comment="协同过滤权重")
    behavioral_weight = Column(Float, default=20.0, comment="行为推荐权重")
    trending_weight = Column(Float, default=10.0, comment="热门推荐权重")
    
    # 特征权重配置
    major_match_weight = Column(Float, default=25.0, comment="专业匹配权重")
    skill_match_weight = Column(Float, default=20.0, comment="技能匹配权重")
    location_match_weight = Column(Float, default=15.0, comment="地点匹配权重")
    salary_match_weight = Column(Float, default=15.0, comment="薪资匹配权重")
    experience_match_weight = Column(Float, default=15.0, comment="经验匹配权重")
    education_match_weight = Column(Float, default=10.0, comment="学历匹配权重")
    
    # 推荐参数
    max_recommendations = Column(Integer, default=20, comment="最大推荐数量")
    min_match_score = Column(Float, default=60.0, comment="最小匹配分数")
    diversity_factor = Column(Float, default=0.3, comment="多样性因子")
    freshness_weight = Column(Float, default=0.2, comment="新鲜度权重")
    popularity_weight = Column(Float, default=0.1, comment="热门度权重")
    
    # 协同过滤参数
    user_similarity_threshold = Column(Float, default=0.5, comment="用户相似度阈值")
    item_similarity_threshold = Column(Float, default=0.6, comment="物品相似度阈值")
    min_interactions = Column(Integer, default=5, comment="最小交互次数")
    max_neighbors = Column(Integer, default=50, comment="最大邻居数")
    
    # 实验配置
    ab_test_enabled = Column(Boolean, default=False, comment="是否启用A/B测试")
    test_group_ratio = Column(Float, default=0.1, comment="测试组比例")
    experiment_duration = Column(Integer, default=7, comment="实验持续时间")
    
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    def __repr__(self):
        return f"<RecommendationConfig(id={self.id})>"


class MatchRuleModel(BaseModel):
    """匹配规则数据模型"""
    __tablename__ = "match_rules"

    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String(255), nullable=False, comment="规则名称")
    type = Column(SQLEnum(MatchRuleType), nullable=False, comment="规则类型")
    category = Column(String(100), nullable=False, comment="规则分类")
    conditions = Column(JSON, default=[], comment="匹配条件")
    threshold = Column(Float, nullable=False, comment="匹配阈值")
    priority = Column(Integer, nullable=False, comment="优先级")
    description = Column(Text, comment="规则描述")
    status = Column(SQLEnum(RuleStatus), default=RuleStatus.INACTIVE, comment="规则状态")
    
    # 使用统计
    match_count = Column(Integer, default=0, comment="匹配次数")
    success_rate = Column(Float, default=0.0, comment="成功率")
    avg_score = Column(Float, default=0.0, comment="平均分数")
    
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    def __repr__(self):
        return f"<MatchRule(id={self.id}, name={self.name}, type={self.type})>"


class SmartTagModel(BaseModel):
    """智能标签数据模型"""
    __tablename__ = "smart_tags"

    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String(255), nullable=False, comment="标签名称")
    type = Column(SQLEnum(TagType), nullable=False, comment="标签类型")
    category = Column(String(100), nullable=False, comment="标签分类")
    description = Column(Text, comment="标签描述")
    color = Column(String(20), nullable=False, comment="标签颜色")
    is_ai_generated = Column(Boolean, default=False, comment="是否AI生成")
    confidence = Column(Float, default=100.0, comment="置信度")
    usage_count = Column(Integer, default=0, comment="使用次数")
    status = Column(SQLEnum(TagStatus), default=TagStatus.ACTIVE, comment="标签状态")
    
    # 标签规则
    keywords = Column(JSON, default=[], comment="关键词")
    conditions = Column(JSON, default=[], comment="条件")
    weight = Column(Float, default=0.8, comment="权重")
    
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    def __repr__(self):
        return f"<SmartTag(id={self.id}, name={self.name}, type={self.type})>"


class ServiceMetricsModel(BaseModel):
    """服务指标数据模型"""
    __tablename__ = "service_metrics"

    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    timestamp = Column(DateTime, nullable=False, comment="时间戳")
    api_calls = Column(Integer, default=0, comment="API调用次数")
    success_rate = Column(Float, default=0.0, comment="成功率")
    avg_response_time = Column(Float, default=0.0, comment="平均响应时间")
    error_rate = Column(Float, default=0.0, comment="错误率")
    active_users = Column(Integer, default=0, comment="活跃用户数")
    
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")

    def __repr__(self):
        return f"<ServiceMetrics(id={self.id}, timestamp={self.timestamp})>"


class APIEndpointStatusModel(BaseModel):
    """API端点状态数据模型"""
    __tablename__ = "api_endpoint_status"

    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    endpoint = Column(String(255), nullable=False, comment="端点路径")
    method = Column(String(10), nullable=False, comment="HTTP方法")
    calls = Column(Integer, default=0, comment="调用次数")
    success_rate = Column(Float, default=0.0, comment="成功率")
    avg_response_time = Column(Float, default=0.0, comment="平均响应时间")
    status = Column(String(20), default="healthy", comment="状态")
    
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    def __repr__(self):
        return f"<APIEndpointStatus(id={self.id}, endpoint={self.endpoint})>"


class ErrorLogModel(BaseModel):
    """错误日志数据模型"""
    __tablename__ = "error_logs"

    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    timestamp = Column(DateTime, nullable=False, comment="时间戳")
    endpoint = Column(String(255), nullable=False, comment="端点")
    error_type = Column(String(100), nullable=False, comment="错误类型")
    message = Column(Text, nullable=False, comment="错误消息")
    severity = Column(String(20), default="medium", comment="严重程度")
    stack_trace = Column(Text, comment="堆栈跟踪")
    user_id = Column(CHAR(36), comment="用户ID")
    request_data = Column(JSON, comment="请求数据")
    
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")

    def __repr__(self):
        return f"<ErrorLog(id={self.id}, error_type={self.error_type})>"


class AIModelCallLogModel(BaseModel):
    """AI模型调用日志数据模型"""
    __tablename__ = "ai_model_call_logs"

    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    model_id = Column(CHAR(36), nullable=False, comment="模型ID")
    user_id = Column(CHAR(36), comment="用户ID")
    request_data = Column(JSON, comment="请求数据")
    response_data = Column(JSON, comment="响应数据")
    response_time = Column(Float, comment="响应时间")
    success = Column(Boolean, default=True, comment="是否成功")
    error_message = Column(Text, comment="错误消息")
    
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")

    def __repr__(self):
        return f"<AIModelCallLog(id={self.id}, model_id={self.model_id})>"
