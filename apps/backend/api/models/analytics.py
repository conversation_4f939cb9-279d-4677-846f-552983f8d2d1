#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据分析相关的数据模型
"""

import uuid
from datetime import datetime
from typing import Dict, Any, List
from sqlalchemy import Column, String, Text, DateTime, Float, Integer, Boolean, JSON, Enum as SQLEnum
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.dialects.mysql import CHAR

from apps.backend.api.models.base import BaseModel
from apps.backend.api.schemas.analytics import ReportType, ChartType, ReportStatus, Frequency

Base = declarative_base()


class UserActivityLogModel(BaseModel):
    """用户活动日志数据模型"""
    __tablename__ = "user_activity_logs"

    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(CHAR(36), comment="用户ID")
    session_id = Column(String(255), comment="会话ID")
    action_type = Column(String(50), nullable=False, comment="动作类型")
    page_url = Column(String(500), comment="页面URL")
    referrer = Column(String(500), comment="来源页面")
    user_agent = Column(Text, comment="用户代理")
    ip_address = Column(String(45), comment="IP地址")
    device_type = Column(String(20), comment="设备类型")
    browser = Column(String(50), comment="浏览器")
    os = Column(String(50), comment="操作系统")
    location = Column(String(100), comment="地理位置")
    duration = Column(Integer, comment="停留时间(秒)")
    extra_data = Column(JSON, comment="额外数据")
    
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")

    def __repr__(self):
        return f"<UserActivityLog(id={self.id}, action_type={self.action_type})>"


class SearchLogModel(BaseModel):
    """搜索日志数据模型"""
    __tablename__ = "search_logs"

    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(CHAR(36), comment="用户ID")
    session_id = Column(String(255), comment="会话ID")
    keyword = Column(String(255), nullable=False, comment="搜索关键词")
    category = Column(String(50), comment="搜索类别")
    filters = Column(JSON, comment="搜索过滤器")
    results_count = Column(Integer, comment="结果数量")
    clicked_result_id = Column(CHAR(36), comment="点击的结果ID")
    click_position = Column(Integer, comment="点击位置")
    search_duration = Column(Integer, comment="搜索耗时(毫秒)")
    
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")

    def __repr__(self):
        return f"<SearchLog(id={self.id}, keyword={self.keyword})>"


class JobApplicationLogModel(BaseModel):
    """岗位申请日志数据模型"""
    __tablename__ = "job_application_logs"

    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(CHAR(36), nullable=False, comment="用户ID")
    job_id = Column(CHAR(36), nullable=False, comment="岗位ID")
    company_id = Column(CHAR(36), comment="公司ID")
    application_status = Column(String(20), comment="申请状态")
    source = Column(String(50), comment="申请来源")
    match_score = Column(Float, comment="匹配分数")
    ai_recommendation = Column(Boolean, default=False, comment="是否AI推荐")
    application_data = Column(JSON, comment="申请数据")
    
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    def __repr__(self):
        return f"<JobApplicationLog(id={self.id}, user_id={self.user_id}, job_id={self.job_id})>"


class PageViewLogModel(BaseModel):
    """页面浏览日志数据模型"""
    __tablename__ = "page_view_logs"

    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(CHAR(36), comment="用户ID")
    session_id = Column(String(255), comment="会话ID")
    page_url = Column(String(500), nullable=False, comment="页面URL")
    page_title = Column(String(255), comment="页面标题")
    referrer = Column(String(500), comment="来源页面")
    load_time = Column(Integer, comment="加载时间(毫秒)")
    view_duration = Column(Integer, comment="浏览时长(秒)")
    scroll_depth = Column(Float, comment="滚动深度")
    bounce = Column(Boolean, default=False, comment="是否跳出")
    
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")

    def __repr__(self):
        return f"<PageViewLog(id={self.id}, page_url={self.page_url})>"


class UserSessionModel(BaseModel):
    """用户会话数据模型"""
    __tablename__ = "user_sessions"

    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    session_id = Column(String(255), unique=True, nullable=False, comment="会话ID")
    user_id = Column(CHAR(36), comment="用户ID")
    start_time = Column(DateTime, nullable=False, comment="开始时间")
    end_time = Column(DateTime, comment="结束时间")
    duration = Column(Integer, comment="会话时长(秒)")
    page_views = Column(Integer, default=0, comment="页面浏览数")
    actions = Column(Integer, default=0, comment="动作数")
    device_type = Column(String(20), comment="设备类型")
    browser = Column(String(50), comment="浏览器")
    os = Column(String(50), comment="操作系统")
    ip_address = Column(String(45), comment="IP地址")
    location = Column(String(100), comment="地理位置")
    
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    def __repr__(self):
        return f"<UserSession(id={self.id}, session_id={self.session_id})>"


class DailyStatsModel(BaseModel):
    """每日统计数据模型"""
    __tablename__ = "daily_stats"

    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    date = Column(DateTime, nullable=False, comment="日期")
    
    # 用户相关统计
    total_users = Column(Integer, default=0, comment="总用户数")
    new_users = Column(Integer, default=0, comment="新增用户数")
    active_users = Column(Integer, default=0, comment="活跃用户数")
    returning_users = Column(Integer, default=0, comment="回访用户数")
    
    # 页面浏览统计
    page_views = Column(Integer, default=0, comment="页面浏览量")
    unique_page_views = Column(Integer, default=0, comment="独立页面浏览量")
    sessions = Column(Integer, default=0, comment="会话数")
    avg_session_duration = Column(Float, default=0, comment="平均会话时长")
    bounce_rate = Column(Float, default=0, comment="跳出率")
    
    # 搜索相关统计
    searches = Column(Integer, default=0, comment="搜索次数")
    unique_searches = Column(Integer, default=0, comment="独立搜索次数")
    search_success_rate = Column(Float, default=0, comment="搜索成功率")
    
    # 岗位相关统计
    job_views = Column(Integer, default=0, comment="岗位浏览量")
    job_applications = Column(Integer, default=0, comment="岗位申请数")
    job_favorites = Column(Integer, default=0, comment="岗位收藏数")
    new_jobs = Column(Integer, default=0, comment="新增岗位数")
    
    # 转化相关统计
    view_to_apply_rate = Column(Float, default=0, comment="浏览到申请转化率")
    search_to_apply_rate = Column(Float, default=0, comment="搜索到申请转化率")
    
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    def __repr__(self):
        return f"<DailyStats(id={self.id}, date={self.date})>"


class CustomReportModel(BaseModel):
    """自定义报表数据模型"""
    __tablename__ = "custom_reports"

    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String(255), nullable=False, comment="报表名称")
    description = Column(Text, comment="报表描述")
    type = Column(SQLEnum(ReportType), nullable=False, comment="报表类型")
    chart_type = Column(SQLEnum(ChartType), comment="图表类型")
    data_source = Column(String(100), nullable=False, comment="数据源")
    filters = Column(JSON, default={}, comment="过滤器")
    columns = Column(JSON, default=[], comment="列")
    status = Column(SQLEnum(ReportStatus), default=ReportStatus.ACTIVE, comment="状态")
    
    # 调度配置
    schedule_enabled = Column(Boolean, default=False, comment="是否启用调度")
    schedule_frequency = Column(SQLEnum(Frequency), comment="调度频率")
    schedule_time = Column(String(10), comment="调度时间")
    schedule_recipients = Column(JSON, default=[], comment="接收者")
    
    # 运行信息
    last_run = Column(DateTime, comment="最后运行时间")
    run_count = Column(Integer, default=0, comment="运行次数")
    
    created_by = Column(CHAR(36), comment="创建者ID")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    def __repr__(self):
        return f"<CustomReport(id={self.id}, name={self.name})>"


class ReportExecutionLogModel(BaseModel):
    """报表执行日志数据模型"""
    __tablename__ = "report_execution_logs"

    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    report_id = Column(CHAR(36), nullable=False, comment="报表ID")
    execution_type = Column(String(20), nullable=False, comment="执行类型")  # manual, scheduled
    status = Column(String(20), nullable=False, comment="执行状态")  # running, success, failed
    start_time = Column(DateTime, nullable=False, comment="开始时间")
    end_time = Column(DateTime, comment="结束时间")
    duration = Column(Integer, comment="执行时长(秒)")
    result_count = Column(Integer, comment="结果数量")
    error_message = Column(Text, comment="错误消息")
    output_format = Column(String(20), comment="输出格式")
    output_path = Column(String(500), comment="输出路径")
    
    executed_by = Column(CHAR(36), comment="执行者ID")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")

    def __repr__(self):
        return f"<ReportExecutionLog(id={self.id}, report_id={self.report_id})>"


class RealTimeMetricsModel(BaseModel):
    """实时指标数据模型"""
    __tablename__ = "realtime_metrics"

    id = Column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    timestamp = Column(DateTime, nullable=False, comment="时间戳")
    
    # 用户指标
    online_users = Column(Integer, default=0, comment="在线用户数")
    active_sessions = Column(Integer, default=0, comment="活跃会话数")
    
    # 系统指标
    page_views_per_minute = Column(Integer, default=0, comment="每分钟页面浏览量")
    searches_per_minute = Column(Integer, default=0, comment="每分钟搜索次数")
    applications_per_minute = Column(Integer, default=0, comment="每分钟申请次数")
    
    # 性能指标
    avg_response_time = Column(Float, default=0, comment="平均响应时间")
    error_rate = Column(Float, default=0, comment="错误率")
    system_load = Column(Float, default=0, comment="系统负载")
    
    # 业务指标
    conversion_rate = Column(Float, default=0, comment="转化率")
    user_satisfaction = Column(Float, default=0, comment="用户满意度")
    
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")

    def __repr__(self):
        return f"<RealTimeMetrics(id={self.id}, timestamp={self.timestamp})>"
