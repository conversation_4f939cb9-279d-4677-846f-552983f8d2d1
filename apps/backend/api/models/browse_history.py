"""
浏览历史模型
"""

from sqlalchemy import Column, String, Integer, DateTime, ForeignKey
from sqlalchemy.orm import relationship

from apps.backend.api.models.base import BaseModel


class BrowseHistory(BaseModel):
    """浏览历史模型"""

    __tablename__ = "browse_histories"

    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    job_id = Column(String(100), ForeignKey("jobs.job_id", ondelete="CASCADE"), nullable=False)
    view_count = Column(Integer, default=1, nullable=False)
    last_view_time = Column(DateTime, nullable=False)

    # 关系
    user = relationship("User", back_populates="browse_histories")
    job = relationship("Job", back_populates="browse_histories")

    __table_args__ = (
        {"mysql_charset": "utf8mb4", "mysql_collate": "utf8mb4_unicode_ci"},
    )
