from sqlalchemy import Column, String, Integer, <PERSON><PERSON>an, DateTime, Text, ForeignKey, Table
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from apps.backend.api.models.base import BaseModel


class CommunityPost(BaseModel):
    """社区帖子模型"""

    __tablename__ = "community_posts"

    post_id = Column(String(100), unique=True, index=True, nullable=False)
    title = Column(String(200), nullable=False)
    content = Column(Text, nullable=False)
    category = Column(String(50), index=True, nullable=True)
    tags = Column(Text, nullable=True)
    author_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    view_count = Column(Integer, default=0, nullable=False)
    like_count = Column(Integer, default=0, nullable=False)
    comment_count = Column(Integer, default=0, nullable=False)
    status = Column(Integer, default=1, nullable=False)  # 1: 正常, 0: 禁用, 2: 审核中
    reviewer_id = Column(Integer, ForeignKey("users.id", ondelete="SET NULL"), nullable=True)
    review_time = Column(DateTime, nullable=True)
    is_pinned = Column(Boolean, default=False, nullable=False)
    is_essence = Column(Boolean, default=False, nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=True)

    # 关系
    author = relationship("User", back_populates="posts", foreign_keys=[author_id])
    comments = relationship("Comment", back_populates="post", cascade="all, delete-orphan")
    tags = relationship("PostTag", back_populates="post", cascade="all, delete-orphan")
    reviewer = relationship("User", foreign_keys=[reviewer_id])

    __table_args__ = (
        {"mysql_charset": "utf8mb4", "mysql_collate": "utf8mb4_unicode_ci"},
    )


class Comment(BaseModel):
    """评论模型"""

    __tablename__ = "comments"

    comment_id = Column(String(100), unique=True, index=True, nullable=False)
    post_id = Column(String(100), ForeignKey("community_posts.post_id", ondelete="CASCADE"), nullable=False)
    content = Column(Text, nullable=False)
    author_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    parent_id = Column(String(100), index=True, nullable=True)
    like_count = Column(Integer, default=0, nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=True)

    # 关系
    post = relationship("CommunityPost", back_populates="comments")
    author = relationship("User", back_populates="comments")

    __table_args__ = (
        {"mysql_charset": "utf8mb4", "mysql_collate": "utf8mb4_unicode_ci"},
    )


class Like(BaseModel):
    """点赞模型"""

    __tablename__ = "likes"

    post_id = Column(String(100), ForeignKey("community_posts.post_id", ondelete="CASCADE"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)

    # 关系
    post = relationship("CommunityPost")
    user = relationship("User")

    __table_args__ = (
        {"mysql_charset": "utf8mb4", "mysql_collate": "utf8mb4_unicode_ci"},
    )


class Tag(BaseModel):
    """标签模型"""

    __tablename__ = "tags"

    name = Column(String(50), unique=True, index=True, nullable=False)

    # 关系
    posts = relationship("PostTag", back_populates="tag")

    __table_args__ = (
        {"mysql_charset": "utf8mb4", "mysql_collate": "utf8mb4_unicode_ci"},
    )


class PostTag(BaseModel):
    """帖子标签关联模型"""

    __tablename__ = "post_tags"

    post_id = Column(String(100), ForeignKey("community_posts.post_id", ondelete="CASCADE"), nullable=False)
    tag_id = Column(Integer, ForeignKey("tags.id", ondelete="CASCADE"), nullable=False)

    # 关系
    post = relationship("CommunityPost", back_populates="tags")
    tag = relationship("Tag", back_populates="posts")

    __table_args__ = (
        {"mysql_charset": "utf8mb4", "mysql_collate": "utf8mb4_unicode_ci"},
    )