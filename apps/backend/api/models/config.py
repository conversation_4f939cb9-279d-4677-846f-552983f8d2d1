#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
系统配置数据模型
"""

from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from apps.backend.api.config.database import Base


class SystemConfig(Base):
    """
    系统配置表
    """
    __tablename__ = "system_config"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    key = Column(String(100), unique=True, index=True, nullable=False, comment="配置键")
    value = Column(Text, nullable=True, comment="配置值")
    description = Column(String(255), nullable=True, comment="配置描述")
    group = Column(String(50), nullable=True, comment="配置分组")
    type = Column(String(20), nullable=False, default="string", comment="配置类型：string, number, boolean, json")
    is_system = Column(Boolean, default=False, comment="是否系统配置，系统配置不可删除")
    created_at = Column(DateTime, server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now(), comment="更新时间")

    def __repr__(self):
        return f"<SystemConfig(key='{self.key}', group='{self.group}')>"
