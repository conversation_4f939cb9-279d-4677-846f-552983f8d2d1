from sqlalchemy import Column, String, Integer, Boolean, DateTime, Text

from apps.backend.api.models.base import BaseModel


class Policy(BaseModel):
    """政策模型"""

    __tablename__ = "policies"

    policy_id = Column(String(100), unique=True, index=True, nullable=False)
    title = Column(String(200), index=True, nullable=False)
    content = Column(Text, nullable=False)
    summary = Column(Text, nullable=True)
    key_points = Column(Text, nullable=True)
    special_notes = Column(Text, nullable=True)
    publish_date = Column(DateTime, index=True, nullable=True)
    source_url = Column(String(255), nullable=True)

    __table_args__ = (
        {"mysql_charset": "utf8mb4", "mysql_collate": "utf8mb4_unicode_ci"},
    )


class Banner(BaseModel):
    """轮播图模型"""

    __tablename__ = "banners"

    title = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    image_url = Column(String(255), nullable=False)
    link_url = Column(String(255), nullable=True)
    sort_order = Column(Integer, default=0, nullable=False)
    status = Column(Boolean, default=True, nullable=False)
    start_time = Column(DateTime, nullable=True)
    end_time = Column(DateTime, nullable=True)

    __table_args__ = (
        {"mysql_charset": "utf8mb4", "mysql_collate": "utf8mb4_unicode_ci"},
    )


class SystemConfig(BaseModel):
    """系统配置模型"""

    __tablename__ = "system_configs"

    config_key = Column(String(100), unique=True, index=True, nullable=False)
    config_value = Column(Text, nullable=False)
    description = Column(String(255), nullable=True)

    __table_args__ = (
        {"mysql_charset": "utf8mb4", "mysql_collate": "utf8mb4_unicode_ci"},
    )
