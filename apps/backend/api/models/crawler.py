from sqlalchemy import Column, String, Integer, DateTime, Text, Foreign<PERSON>ey, <PERSON>olean, JSON
from sqlalchemy.orm import relationship

from apps.backend.api.models.base import BaseModel


class Crawler(BaseModel):
    """爬虫模型"""

    __tablename__ = "crawlers"

    name = Column(String(100), nullable=False)
    crawler_type = Column(String(50), index=True, nullable=False)
    description = Column(Text, nullable=True)
    config = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    created_by = Column(Integer, ForeignKey("admins.id"), nullable=False)

    # 关系
    tasks = relationship("CrawlerTask", back_populates="crawler", cascade="all, delete-orphan")

    __table_args__ = (
        {"mysql_charset": "utf8mb4", "mysql_collate": "utf8mb4_unicode_ci"},
    )


class CrawlerTask(BaseModel):
    """爬虫任务模型"""

    __tablename__ = "crawler_tasks"

    task_name = Column(String(100), nullable=False)
    task_type = Column(String(50), index=True, nullable=False)
    target_url = Column(String(255), nullable=True)
    parameters = Column(Text, nullable=True)
    schedule = Column(String(100), nullable=True)
    status = Column(String(20), default="pending", index=True, nullable=False)
    last_run_time = Column(DateTime, nullable=True)
    next_run_time = Column(DateTime, index=True, nullable=True)
    created_by = Column(Integer, ForeignKey("admins.id"), nullable=False)
    crawler_id = Column(Integer, ForeignKey("crawlers.id"), nullable=True)

    # 关系
    logs = relationship("CrawlerTaskLog", back_populates="task", cascade="all, delete-orphan")
    crawler = relationship("Crawler", back_populates="tasks")

    __table_args__ = (
        {"mysql_charset": "utf8mb4", "mysql_collate": "utf8mb4_unicode_ci"},
    )


class CrawlerTaskLog(BaseModel):
    """爬虫任务日志模型"""

    __tablename__ = "crawler_task_logs"

    task_id = Column(Integer, ForeignKey("crawler_tasks.id", ondelete="CASCADE"), nullable=False)
    start_time = Column(DateTime, nullable=False)
    end_time = Column(DateTime, nullable=True)
    status = Column(String(20), index=True, nullable=False)
    result = Column(Text, nullable=True)
    error_message = Column(Text, nullable=True)

    # 关系
    task = relationship("CrawlerTask", back_populates="logs")

    __table_args__ = (
        {"mysql_charset": "utf8mb4", "mysql_collate": "utf8mb4_unicode_ci"},
    )
