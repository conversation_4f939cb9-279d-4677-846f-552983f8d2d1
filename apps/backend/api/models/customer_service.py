#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
客服系统数据模型
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, Float, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ey, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime
import enum

from apps.backend.api.models.base import Base


class ConversationStatusEnum(enum.Enum):
    """对话状态枚举"""
    ACTIVE = "active"
    WAITING = "waiting"
    RESOLVED = "resolved"
    ESCALATED = "escalated"
    CLOSED = "closed"


class MessageTypeEnum(enum.Enum):
    """消息类型枚举"""
    TEXT = "text"
    IMAGE = "image"
    FILE = "file"
    QUICK_REPLY = "quick_reply"
    CARD = "card"


class IntentTypeEnum(enum.Enum):
    """意图类型枚举"""
    JOB_SEARCH = "job_search"
    APPLICATION_STATUS = "application_status"
    ACCOUNT_ISSUE = "account_issue"
    TECHNICAL_SUPPORT = "technical_support"
    POLICY_INQUIRY = "policy_inquiry"
    COMPLAINT = "complaint"
    GENERAL_INQUIRY = "general_inquiry"
    GREETING = "greeting"
    GOODBYE = "goodbye"


class TicketStatusEnum(enum.Enum):
    """工单状态枚举"""
    OPEN = "open"
    IN_PROGRESS = "in_progress"
    PENDING = "pending"
    RESOLVED = "resolved"
    CLOSED = "closed"
    CANCELLED = "cancelled"


class TicketPriorityEnum(enum.Enum):
    """工单优先级枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"
    CRITICAL = "critical"


class TicketCategoryEnum(enum.Enum):
    """工单分类枚举"""
    TECHNICAL_ISSUE = "technical_issue"
    ACCOUNT_PROBLEM = "account_problem"
    JOB_SEARCH_HELP = "job_search_help"
    APPLICATION_ISSUE = "application_issue"
    POLICY_QUESTION = "policy_question"
    FEATURE_REQUEST = "feature_request"
    BUG_REPORT = "bug_report"
    COMPLAINT = "complaint"
    GENERAL_INQUIRY = "general_inquiry"


class Conversation(Base):
    """对话记录"""
    __tablename__ = "conversations"
    
    id = Column(Integer, primary_key=True, index=True)
    conversation_id = Column(String(100), unique=True, index=True, nullable=False)
    user_id = Column(String(50), index=True, nullable=False)
    
    # 对话状态
    status = Column(Enum(ConversationStatusEnum), default=ConversationStatusEnum.ACTIVE)
    current_intent = Column(Enum(IntentTypeEnum), nullable=True)
    
    # 对话信息
    entities = Column(JSON, default={})
    user_profile = Column(JSON, default={})
    
    # 状态信息
    waiting_for_input = Column(Boolean, default=False)
    escalation_reason = Column(Text, nullable=True)
    satisfaction_rating = Column(Integer, nullable=True)
    
    # 时间信息
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    ended_at = Column(DateTime, nullable=True)
    
    # 关联关系
    messages = relationship("ConversationMessage", back_populates="conversation", cascade="all, delete-orphan")


class ConversationMessage(Base):
    """对话消息"""
    __tablename__ = "conversation_messages"
    
    id = Column(Integer, primary_key=True, index=True)
    message_id = Column(String(100), unique=True, index=True, nullable=False)
    conversation_id = Column(String(100), ForeignKey("conversations.conversation_id"), nullable=False)
    
    # 消息信息
    sender_type = Column(String(20), nullable=False)  # user, bot, agent, system
    sender_id = Column(String(50), nullable=True)
    content = Column(Text, nullable=False)
    message_type = Column(Enum(MessageTypeEnum), default=MessageTypeEnum.TEXT)
    
    # AI相关信息
    intent = Column(Enum(IntentTypeEnum), nullable=True)
    confidence = Column(Float, nullable=True)
    entities = Column(JSON, default={})
    
    # 附加信息
    attachments = Column(JSON, default=[])
    quick_replies = Column(JSON, default=[])
    suggested_actions = Column(JSON, default=[])
    
    # 时间信息
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关联关系
    conversation = relationship("Conversation", back_populates="messages")


class Ticket(Base):
    """工单"""
    __tablename__ = "tickets"
    
    id = Column(Integer, primary_key=True, index=True)
    ticket_id = Column(String(100), unique=True, index=True, nullable=False)
    user_id = Column(String(50), index=True, nullable=False)
    
    # 工单基本信息
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=False)
    category = Column(Enum(TicketCategoryEnum), nullable=False)
    priority = Column(Enum(TicketPriorityEnum), default=TicketPriorityEnum.MEDIUM)
    status = Column(Enum(TicketStatusEnum), default=TicketStatusEnum.OPEN)
    
    # 分配信息
    assigned_agent_id = Column(String(50), nullable=True)
    assigned_at = Column(DateTime, nullable=True)
    
    # 时间信息
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    resolved_at = Column(DateTime, nullable=True)
    closed_at = Column(DateTime, nullable=True)
    
    # 附加信息
    tags = Column(JSON, default=[])
    
    # 统计信息
    response_time = Column(Integer, nullable=True)  # 首次响应时间（秒）
    resolution_time = Column(Integer, nullable=True)  # 解决时间（秒）
    satisfaction_rating = Column(Integer, nullable=True)  # 满意度评分（1-5）
    
    # 自动化信息
    auto_categorized = Column(Boolean, default=False)
    auto_priority_assigned = Column(Boolean, default=False)
    escalation_count = Column(Integer, default=0)
    
    # 关联关系
    comments = relationship("TicketComment", back_populates="ticket", cascade="all, delete-orphan")
    attachments = relationship("TicketAttachment", back_populates="ticket", cascade="all, delete-orphan")


class TicketComment(Base):
    """工单评论"""
    __tablename__ = "ticket_comments"
    
    id = Column(Integer, primary_key=True, index=True)
    comment_id = Column(String(100), unique=True, index=True, nullable=False)
    ticket_id = Column(String(100), ForeignKey("tickets.ticket_id"), nullable=False)
    
    # 评论信息
    author_id = Column(String(50), nullable=False)
    author_type = Column(String(20), nullable=False)  # user, agent, system
    content = Column(Text, nullable=False)
    is_internal = Column(Boolean, default=False)
    
    # 时间信息
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关联关系
    ticket = relationship("Ticket", back_populates="comments")


class TicketAttachment(Base):
    """工单附件"""
    __tablename__ = "ticket_attachments"
    
    id = Column(Integer, primary_key=True, index=True)
    attachment_id = Column(String(100), unique=True, index=True, nullable=False)
    ticket_id = Column(String(100), ForeignKey("tickets.ticket_id"), nullable=False)
    
    # 附件信息
    filename = Column(String(255), nullable=False)
    file_type = Column(String(50), nullable=False)
    file_size = Column(Integer, nullable=False)
    file_url = Column(String(500), nullable=False)
    
    # 时间信息
    uploaded_at = Column(DateTime, default=datetime.utcnow)
    
    # 关联关系
    ticket = relationship("Ticket", back_populates="attachments")


class CustomerServiceAgent(Base):
    """客服代理"""
    __tablename__ = "customer_service_agents"
    
    id = Column(Integer, primary_key=True, index=True)
    agent_id = Column(String(50), unique=True, index=True, nullable=False)
    
    # 基本信息
    name = Column(String(100), nullable=False)
    email = Column(String(100), nullable=False)
    phone = Column(String(20), nullable=True)
    
    # 工作信息
    department = Column(String(50), nullable=True)
    specialties = Column(JSON, default=[])  # 专业领域
    languages = Column(JSON, default=[])    # 支持语言
    
    # 状态信息
    is_active = Column(Boolean, default=True)
    is_online = Column(Boolean, default=False)
    current_workload = Column(Integer, default=0)  # 当前处理工单数
    max_workload = Column(Integer, default=10)     # 最大处理工单数
    
    # 统计信息
    total_tickets_handled = Column(Integer, default=0)
    avg_response_time = Column(Float, default=0.0)
    avg_resolution_time = Column(Float, default=0.0)
    customer_satisfaction_score = Column(Float, default=0.0)
    
    # 时间信息
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_active_at = Column(DateTime, nullable=True)


class AutomationWorkflow(Base):
    """自动化工作流"""
    __tablename__ = "automation_workflows"
    
    id = Column(Integer, primary_key=True, index=True)
    workflow_id = Column(String(100), unique=True, index=True, nullable=False)
    
    # 基本信息
    name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    
    # 工作流配置
    trigger_config = Column(JSON, nullable=False)  # 触发条件配置
    actions_config = Column(JSON, nullable=False)  # 动作配置
    
    # 状态信息
    status = Column(String(20), default="active")  # active, inactive, paused, error
    is_enabled = Column(Boolean, default=True)
    
    # 统计信息
    execution_count = Column(Integer, default=0)
    success_count = Column(Integer, default=0)
    failure_count = Column(Integer, default=0)
    
    # 时间信息
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_executed_at = Column(DateTime, nullable=True)
    
    # 关联关系
    executions = relationship("WorkflowExecution", back_populates="workflow", cascade="all, delete-orphan")


class WorkflowExecution(Base):
    """工作流执行记录"""
    __tablename__ = "workflow_executions"
    
    id = Column(Integer, primary_key=True, index=True)
    execution_id = Column(String(100), unique=True, index=True, nullable=False)
    workflow_id = Column(String(100), ForeignKey("automation_workflows.workflow_id"), nullable=False)
    
    # 执行信息
    trigger_context = Column(JSON, default={})  # 触发上下文
    execution_result = Column(JSON, default={}) # 执行结果
    
    # 状态信息
    status = Column(String(20), nullable=False)  # success, failure, partial
    error_message = Column(Text, nullable=True)
    
    # 时间信息
    started_at = Column(DateTime, default=datetime.utcnow)
    completed_at = Column(DateTime, nullable=True)
    duration_seconds = Column(Float, nullable=True)
    
    # 关联关系
    workflow = relationship("AutomationWorkflow", back_populates="executions")


class CustomerServiceMetrics(Base):
    """客服系统指标"""
    __tablename__ = "customer_service_metrics"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # 时间维度
    date = Column(DateTime, nullable=False, index=True)
    hour = Column(Integer, nullable=True)  # 小时级别统计
    
    # 对话指标
    total_conversations = Column(Integer, default=0)
    active_conversations = Column(Integer, default=0)
    resolved_conversations = Column(Integer, default=0)
    escalated_conversations = Column(Integer, default=0)
    avg_conversation_duration = Column(Float, default=0.0)
    
    # 工单指标
    total_tickets = Column(Integer, default=0)
    new_tickets = Column(Integer, default=0)
    resolved_tickets = Column(Integer, default=0)
    avg_response_time = Column(Float, default=0.0)
    avg_resolution_time = Column(Float, default=0.0)
    
    # 满意度指标
    total_ratings = Column(Integer, default=0)
    avg_satisfaction_rating = Column(Float, default=0.0)
    
    # AI指标
    ai_resolution_rate = Column(Float, default=0.0)  # AI解决率
    avg_intent_confidence = Column(Float, default=0.0)  # 平均意图识别置信度
    
    # 自动化指标
    workflow_executions = Column(Integer, default=0)
    workflow_success_rate = Column(Float, default=0.0)
    
    # 时间信息
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
