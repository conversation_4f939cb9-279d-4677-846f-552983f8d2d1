from sqlalchemy import Colum<PERSON>, <PERSON>, In<PERSON><PERSON>, <PERSON><PERSON><PERSON>, DateTime, Text, ForeignKey
from sqlalchemy.orm import relationship

from apps.backend.api.models.base import BaseModel
from apps.backend.api.models.job_stats import <PERSON>View, JobMatch


class Job(BaseModel):
    """岗位模型"""

    __tablename__ = "jobs"

    job_id = Column(String(100), unique=True, index=True, nullable=False)
    title = Column(String(200), index=True, nullable=False)
    company_name = Column(String(200), index=True, nullable=False)
    work_location = Column(String(100), index=True, nullable=True)
    salary_range = Column(String(100), nullable=True)
    job_description = Column(Text, nullable=True)
    education_requirement = Column(String(100), nullable=True)
    experience_requirement = Column(String(100), nullable=True)
    major_requirement = Column(String(200), nullable=True)
    publish_date = Column(DateTime, index=True, nullable=True)
    source_url = Column(String(255), nullable=True)
    is_graduate_friendly = Column(Boolean, default=False, index=True)
    job_type = Column(String(50), index=True, nullable=True)
    data_source = Column(String(50), nullable=True)
    status = Column(String(20), index=True, default='active', nullable=False)  # active, inactive, expired

    # 关系
    details = relationship("JobDetail", back_populates="job", uselist=False, cascade="all, delete-orphan")
    favorites = relationship("Favorite", back_populates="job", cascade="all, delete-orphan")
    browse_histories = relationship("BrowseHistory", back_populates="job", cascade="all, delete-orphan")
    subscriptions = relationship("Subscription", back_populates="job", cascade="all, delete-orphan")

    # 用户关系
    views = relationship("JobView", back_populates="job", cascade="all, delete-orphan")
    matches = relationship("JobMatch", back_populates="job", cascade="all, delete-orphan")


class JobDetail(BaseModel):
    """岗位详情模型"""

    __tablename__ = "job_details"

    job_id = Column(String(100), ForeignKey("jobs.job_id", ondelete="CASCADE"), unique=True, nullable=False)
    major_standardized = Column(String(100), nullable=True)
    similar_majors = Column(Text, nullable=True)
    interview_questions = Column(Text, nullable=True)
    professional_questions = Column(Text, nullable=True)
    interview_preparation_tips = Column(Text, nullable=True)
    policy_summary = Column(Text, nullable=True)
    policy_key_points = Column(Text, nullable=True)
    policy_special_notes = Column(Text, nullable=True)

    # 关系
    job = relationship("Job", back_populates="details")
