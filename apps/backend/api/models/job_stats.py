"""
岗位统计模型
"""

from sqlalchemy import Column, String, Integer, DateTime, ForeignKey, func
from sqlalchemy.orm import relationship

from apps.backend.api.models.base import BaseModel


class JobView(BaseModel):
    """岗位浏览统计模型"""

    __tablename__ = "job_views"

    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    job_id = Column(String(100), ForeignKey("jobs.job_id", ondelete="CASCADE"), nullable=False)
    view_count = Column(Integer, default=1, nullable=False)
    last_view_time = Column(DateTime, default=func.now(), nullable=False)

    # 关系
    user = relationship("User", backref="job_views")
    job = relationship("Job", back_populates="views")

    __table_args__ = (
        {"mysql_charset": "utf8mb4", "mysql_collate": "utf8mb4_unicode_ci"},
    )


class JobMatch(BaseModel):
    """岗位匹配统计模型"""

    __tablename__ = "job_matches"

    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    job_id = Column(String(100), ForeignKey("jobs.job_id", ondelete="CASCADE"), nullable=False)
    match_score = Column(Integer, default=0, nullable=False)
    match_details = Column(String(500), nullable=True)
    match_time = Column(DateTime, default=func.now(), nullable=False)

    # 关系
    user = relationship("User", backref="job_matches")
    job = relationship("Job", back_populates="matches")

    __table_args__ = (
        {"mysql_charset": "utf8mb4", "mysql_collate": "utf8mb4_unicode_ci"},
    )
