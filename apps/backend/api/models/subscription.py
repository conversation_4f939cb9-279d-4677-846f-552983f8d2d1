"""
订阅模型
"""

from sqlalchemy import Column, String, Integer, DateTime, ForeignKey, Boolean, func
from sqlalchemy.orm import relationship

from apps.backend.api.models.base import BaseModel


class Subscription(BaseModel):
    """订阅模型"""

    __tablename__ = "subscriptions"

    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    job_id = Column(String(100), ForeignKey("jobs.job_id", ondelete="CASCADE"), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    notification_frequency = Column(String(20), default="daily", nullable=False)  # daily, weekly, realtime
    last_notified_at = Column(DateTime, nullable=True)

    # 关系
    user = relationship("User", back_populates="subscriptions")
    job = relationship("Job", back_populates="subscriptions")

    __table_args__ = (
        {"mysql_charset": "utf8mb4", "mysql_collate": "utf8mb4_unicode_ci"},
    )
