from datetime import datetime
from sqlalchemy import Column, String, Integer, Boolean, DateTime, Text, ForeignKey, Table
from sqlalchemy.orm import relationship

from apps.backend.api.models.base import BaseModel

# 用户角色关联表
user_roles = Table(
    'user_roles',
    BaseModel.metadata,
    Column('user_id', Integer, Foreign<PERSON>ey('users.id', ondelete='CASCADE'), primary_key=True),
    Column('role_id', Integer, ForeignKey('roles.id', ondelete='CASCADE'), primary_key=True)
)

# 用户角色权限关联表
user_role_permissions = Table(
    'user_role_permissions',
    BaseModel.metadata,
    Column('id', Integer, primary_key=True, autoincrement=True),
    Column('role_id', Integer, ForeignKey('roles.id', ondelete='CASCADE'), nullable=False),
    Column('permission_id', Integer, ForeignKey('permissions.id', ondelete='CASCADE'), nullable=False),
    Column('created_at', DateTime, nullable=False),
    extend_existing=True
)


class User(BaseModel):
    """用户模型"""

    __tablename__ = "users"

    username = Column(String(50), unique=True, index=True, nullable=False)
    password_hash = Column(String(128), nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=True)
    phone = Column(String(20), unique=True, index=True, nullable=True)
    real_name = Column(String(50), nullable=True)
    education = Column(String(50), nullable=True)
    major = Column(String(100), nullable=True)
    graduation_year = Column(Integer, nullable=True)
    school = Column(String(100), nullable=True)
    last_login = Column(DateTime, nullable=True)
    status = Column(Boolean, default=True, nullable=False)
    openid = Column(String(100), unique=True, index=True, nullable=True)
    session_key = Column(String(100), nullable=True)
    avatar_url = Column(String(255), nullable=True)

    # 关系
    profile = relationship("UserProfile", back_populates="user", uselist=False, cascade="all, delete-orphan")
    favorites = relationship("Favorite", back_populates="user", cascade="all, delete-orphan")
    browse_histories = relationship("BrowseHistory", back_populates="user", cascade="all, delete-orphan")
    posts = relationship("CommunityPost", back_populates="author", foreign_keys="CommunityPost.author_id", cascade="all, delete-orphan")
    comments = relationship("Comment", back_populates="author", cascade="all, delete-orphan")
    subscriptions = relationship("Subscription", back_populates="user", cascade="all, delete-orphan")

    # 角色关系
    roles = relationship("Role", secondary=user_roles, back_populates="users")

    def has_permission(self, permission_name: str) -> bool:
        """检查用户是否有指定权限"""
        for role in self.roles:
            for permission in role.permissions:
                if permission.name == permission_name:
                    return True
        return False

    def has_role(self, role_name: str) -> bool:
        """检查用户是否有指定角色"""
        return any(role.name == role_name for role in self.roles)

    @property
    def is_admin(self) -> bool:
        """检查是否为管理员"""
        return self.has_role('admin') or self.has_role('super_admin')

    @property
    def is_active(self) -> bool:
        """检查用户是否激活"""
        return self.status


class UserProfile(BaseModel):
    """用户资料模型"""

    __tablename__ = "user_profiles"

    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    education = Column(String(50), nullable=True)
    major = Column(String(100), nullable=True)
    skills = Column(Text, nullable=True)
    experience = Column(Text, nullable=True)
    location_preference = Column(String(100), nullable=True)
    job_type_preference = Column(String(100), nullable=True)
    resume_url = Column(String(255), nullable=True)

    # 关系
    user = relationship("User", back_populates="profile")





class Role(BaseModel):
    """角色模型"""

    __tablename__ = "roles"

    name = Column(String(50), unique=True, index=True, nullable=False)
    display_name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)

    # 关系
    users = relationship("User", secondary=user_roles, back_populates="roles")
    permissions = relationship("Permission", secondary=user_role_permissions, back_populates="roles")

    __table_args__ = (
        {"extend_existing": True},
    )

    def __repr__(self):
        return f"<Role(name='{self.name}', display_name='{self.display_name}')>"


class Permission(BaseModel):
    """权限模型"""

    __tablename__ = "permissions"

    name = Column(String(100), unique=True, index=True, nullable=False)
    display_name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    resource = Column(String(50), nullable=False)  # 资源类型：user, job, community等
    action = Column(String(50), nullable=False)    # 操作类型：create, read, update, delete等
    is_active = Column(Boolean, default=True, nullable=False)

    # 关系
    roles = relationship("Role", secondary=user_role_permissions, back_populates="permissions")

    __table_args__ = (
        {"extend_existing": True},
    )

    def __repr__(self):
        return f"<Permission(name='{self.name}', resource='{self.resource}', action='{self.action}')>"


class UserLoginLog(BaseModel):
    """用户登录日志模型"""

    __tablename__ = "user_login_logs"

    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    login_time = Column(DateTime, default=datetime.now, nullable=False)
    ip_address = Column(String(45), nullable=True)  # 支持IPv6
    user_agent = Column(Text, nullable=True)
    login_type = Column(String(20), default='web', nullable=False)  # web, mobile, api等
    is_success = Column(Boolean, default=True, nullable=False)
    failure_reason = Column(String(255), nullable=True)

    # 关系
    user = relationship("User", backref="login_logs")

    def __repr__(self):
        return f"<UserLoginLog(user_id={self.user_id}, login_time='{self.login_time}', is_success={self.is_success})>"
