#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
监控面板和报告生成器
"""

import json
import time
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict

from apps.backend.api.monitoring.performance_monitor import performance_monitor
from apps.backend.api.monitoring.health_monitor import health_monitor
from apps.backend.utils.logger import setup_logger

logger = setup_logger(__name__)


@dataclass
class DashboardPanel:
    """面板配置"""
    id: str
    title: str
    type: str  # chart, stat, gauge, table
    metrics: List[str]
    time_range: str = "1h"
    refresh_interval: int = 30
    config: Dict[str, Any] = None


@dataclass
class MonitoringReport:
    """监控报告"""
    report_id: str
    title: str
    generated_at: float
    time_range: str
    summary: Dict[str, Any]
    sections: List[Dict[str, Any]]


class MonitoringDashboard:
    """监控面板管理器"""
    
    def __init__(self):
        self.logger = logger
        
        # 预定义面板配置
        self.default_panels = [
            DashboardPanel(
                id="system_overview",
                title="系统概览",
                type="stat",
                metrics=["cpu_usage", "memory_usage", "disk_usage"],
                config={
                    "thresholds": {
                        "cpu_usage": [70, 85],
                        "memory_usage": [75, 90],
                        "disk_usage": [80, 95]
                    }
                }
            ),
            
            DashboardPanel(
                id="api_performance",
                title="API性能",
                type="chart",
                metrics=["api_request_duration", "api_request_total", "api_error_rate"],
                config={
                    "chart_type": "line",
                    "y_axis": "response_time"
                }
            ),
            
            DashboardPanel(
                id="database_performance",
                title="数据库性能",
                type="chart",
                metrics=["db_query_duration", "db_connection_pool", "db_query_total"],
                config={
                    "chart_type": "line",
                    "y_axis": "query_time"
                }
            ),
            
            DashboardPanel(
                id="cache_performance",
                title="缓存性能",
                type="gauge",
                metrics=["cache_hit_rate", "cache_memory_usage"],
                config={
                    "gauge_type": "donut",
                    "max_value": 100
                }
            ),
            
            DashboardPanel(
                id="ai_performance",
                title="AI功能性能",
                type="chart",
                metrics=["ai_request_duration", "ai_accuracy_score", "ai_cache_hit_rate"],
                config={
                    "chart_type": "mixed",
                    "dual_axis": True
                }
            ),
            
            DashboardPanel(
                id="service_health",
                title="服务健康状态",
                type="table",
                metrics=["service_status", "response_time", "error_count"],
                config={
                    "columns": ["服务名称", "状态", "响应时间", "错误次数", "最后检查"]
                }
            )
        ]
    
    def get_dashboard_data(self, time_range: str = "1h") -> Dict[str, Any]:
        """获取面板数据"""
        # 解析时间范围
        hours = self._parse_time_range(time_range)
        
        # 获取性能数据
        performance_data = performance_monitor.get_metrics_summary(hours=hours)
        performance_trends = performance_monitor.get_performance_trends(hours=hours)
        
        # 获取健康数据
        health_data = health_monitor.get_overall_health()
        
        # 构建面板数据
        dashboard_data = {
            "timestamp": time.time(),
            "time_range": time_range,
            "panels": {}
        }
        
        for panel in self.default_panels:
            dashboard_data["panels"][panel.id] = self._generate_panel_data(
                panel, performance_data, performance_trends, health_data
            )
        
        return dashboard_data
    
    def get_real_time_metrics(self) -> Dict[str, Any]:
        """获取实时指标"""
        # 获取当前性能快照
        current_snapshot = performance_monitor.get_current_snapshot()
        
        # 获取健康状态
        health_status = health_monitor.get_overall_health()
        
        # 获取性能告警
        performance_alerts = performance_monitor.check_performance_alerts()
        
        return {
            "timestamp": time.time(),
            "system_metrics": current_snapshot.system_metrics,
            "api_metrics": current_snapshot.api_metrics,
            "database_metrics": current_snapshot.database_metrics,
            "cache_metrics": current_snapshot.cache_metrics,
            "ai_metrics": current_snapshot.ai_metrics,
            "health_status": health_status,
            "alerts": performance_alerts
        }
    
    def generate_performance_report(self, time_range: str = "24h") -> MonitoringReport:
        """生成性能报告"""
        hours = self._parse_time_range(time_range)
        report_id = f"perf_report_{int(time.time())}"
        
        # 获取数据
        performance_data = performance_monitor.get_metrics_summary(hours=hours)
        performance_trends = performance_monitor.get_performance_trends(hours=hours)
        health_data = health_monitor.get_overall_health()
        alerts = performance_monitor.check_performance_alerts()
        
        # 生成摘要
        summary = self._generate_performance_summary(performance_data, health_data, alerts)
        
        # 生成各个部分
        sections = [
            self._generate_system_section(performance_data, performance_trends),
            self._generate_api_section(performance_data, performance_trends),
            self._generate_database_section(performance_data, performance_trends),
            self._generate_cache_section(performance_data, performance_trends),
            self._generate_ai_section(performance_data, performance_trends),
            self._generate_health_section(health_data),
            self._generate_alerts_section(alerts)
        ]
        
        return MonitoringReport(
            report_id=report_id,
            title=f"系统性能报告 - {time_range}",
            generated_at=time.time(),
            time_range=time_range,
            summary=summary,
            sections=sections
        )
    
    def export_report_json(self, report: MonitoringReport) -> str:
        """导出报告为JSON"""
        return json.dumps(asdict(report), indent=2, ensure_ascii=False)
    
    def export_report_html(self, report: MonitoringReport) -> str:
        """导出报告为HTML"""
        html_template = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>{title}</title>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background: #f5f5f5; padding: 20px; border-radius: 5px; }}
                .section {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }}
                .metric {{ display: inline-block; margin: 10px; padding: 10px; background: #f9f9f9; border-radius: 3px; }}
                .alert {{ padding: 10px; margin: 5px 0; border-radius: 3px; }}
                .alert.warning {{ background: #fff3cd; border: 1px solid #ffeaa7; }}
                .alert.error {{ background: #f8d7da; border: 1px solid #f5c6cb; }}
                .alert.critical {{ background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }}
                table {{ width: 100%; border-collapse: collapse; }}
                th, td {{ padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>{title}</h1>
                <p>生成时间: {generated_at}</p>
                <p>时间范围: {time_range}</p>
            </div>
            
            <div class="section">
                <h2>系统摘要</h2>
                {summary_html}
            </div>
            
            {sections_html}
        </body>
        </html>
        """
        
        # 生成摘要HTML
        summary_html = self._generate_summary_html(report.summary)
        
        # 生成各部分HTML
        sections_html = ""
        for section in report.sections:
            sections_html += f"""
            <div class="section">
                <h2>{section['title']}</h2>
                {self._generate_section_html(section)}
            </div>
            """
        
        return html_template.format(
            title=report.title,
            generated_at=datetime.fromtimestamp(report.generated_at).strftime("%Y-%m-%d %H:%M:%S"),
            time_range=report.time_range,
            summary_html=summary_html,
            sections_html=sections_html
        )
    
    def _parse_time_range(self, time_range: str) -> int:
        """解析时间范围"""
        if time_range.endswith('h'):
            return int(time_range[:-1])
        elif time_range.endswith('d'):
            return int(time_range[:-1]) * 24
        else:
            return 1  # 默认1小时
    
    def _generate_panel_data(self, panel: DashboardPanel, performance_data: Dict, 
                           performance_trends: Dict, health_data: Dict) -> Dict[str, Any]:
        """生成面板数据"""
        panel_data = {
            "id": panel.id,
            "title": panel.title,
            "type": panel.type,
            "config": panel.config or {},
            "data": {}
        }
        
        if panel.type == "stat":
            # 统计面板
            for metric in panel.metrics:
                if metric in performance_data:
                    panel_data["data"][metric] = {
                        "value": performance_data[metric].get("avg", 0),
                        "change": 0,  # 可以计算变化率
                        "status": "normal"
                    }
        
        elif panel.type == "chart":
            # 图表面板
            for metric in panel.metrics:
                if metric in performance_trends:
                    panel_data["data"][metric] = performance_trends[metric]
        
        elif panel.type == "gauge":
            # 仪表盘
            for metric in panel.metrics:
                if metric in performance_data:
                    panel_data["data"][metric] = {
                        "value": performance_data[metric].get("avg", 0),
                        "max": panel.config.get("max_value", 100)
                    }
        
        elif panel.type == "table":
            # 表格面板
            if "service_status" in panel.metrics:
                panel_data["data"]["services"] = []
                for service_name, service_health in health_data.get("services", {}).items():
                    panel_data["data"]["services"].append({
                        "name": service_name,
                        "status": service_health["overall_status"],
                        "response_time": service_health.get("last_response_time", 0),
                        "error_count": service_health.get("error_count", 0),
                        "last_check": service_health.get("last_check", 0)
                    })
        
        return panel_data
    
    def _generate_performance_summary(self, performance_data: Dict, health_data: Dict, alerts: List) -> Dict[str, Any]:
        """生成性能摘要"""
        return {
            "overall_status": health_data.get("status", "unknown"),
            "total_services": health_data.get("summary", {}).get("total", 0),
            "healthy_services": health_data.get("summary", {}).get("healthy", 0),
            "warning_services": health_data.get("summary", {}).get("warning", 0),
            "critical_services": health_data.get("summary", {}).get("critical", 0),
            "active_alerts": len(alerts),
            "critical_alerts": len([a for a in alerts if a.get("severity") == "critical"]),
            "avg_api_response_time": performance_data.get("api_request_duration", {}).get("avg", 0),
            "avg_db_query_time": performance_data.get("db_query_duration", {}).get("avg", 0),
            "cache_hit_rate": performance_data.get("cache_hit_rate", {}).get("avg", 0)
        }
    
    def _generate_system_section(self, performance_data: Dict, performance_trends: Dict) -> Dict[str, Any]:
        """生成系统部分"""
        return {
            "title": "系统资源",
            "type": "metrics",
            "data": {
                "cpu_usage": performance_data.get("system_cpu_usage", {}),
                "memory_usage": performance_data.get("system_memory_usage", {}),
                "disk_usage": performance_data.get("system_disk_usage", {}),
                "trends": {
                    "cpu_usage": performance_trends.get("cpu_usage", []),
                    "memory_usage": performance_trends.get("memory_usage", [])
                }
            }
        }
    
    def _generate_api_section(self, performance_data: Dict, performance_trends: Dict) -> Dict[str, Any]:
        """生成API部分"""
        return {
            "title": "API性能",
            "type": "metrics",
            "data": {
                "request_duration": performance_data.get("api_request_duration", {}),
                "request_total": performance_data.get("api_request_total", {}),
                "error_rate": performance_data.get("api_error_rate", {}),
                "trends": {
                    "api_response_time": performance_trends.get("api_response_time", [])
                }
            }
        }
    
    def _generate_database_section(self, performance_data: Dict, performance_trends: Dict) -> Dict[str, Any]:
        """生成数据库部分"""
        return {
            "title": "数据库性能",
            "type": "metrics",
            "data": {
                "query_duration": performance_data.get("db_query_duration", {}),
                "connection_pool": performance_data.get("db_connection_pool", {}),
                "query_total": performance_data.get("db_query_total", {}),
                "trends": {
                    "database_query_time": performance_trends.get("database_query_time", [])
                }
            }
        }
    
    def _generate_cache_section(self, performance_data: Dict, performance_trends: Dict) -> Dict[str, Any]:
        """生成缓存部分"""
        return {
            "title": "缓存性能",
            "type": "metrics",
            "data": {
                "hit_rate": performance_data.get("cache_hit_rate", {}),
                "memory_usage": performance_data.get("cache_memory_usage", {}),
                "operation_duration": performance_data.get("cache_operation_duration", {}),
                "trends": {
                    "cache_hit_rate": performance_trends.get("cache_hit_rate", [])
                }
            }
        }
    
    def _generate_ai_section(self, performance_data: Dict, performance_trends: Dict) -> Dict[str, Any]:
        """生成AI部分"""
        return {
            "title": "AI功能性能",
            "type": "metrics",
            "data": {
                "request_duration": performance_data.get("ai_request_duration", {}),
                "accuracy_score": performance_data.get("ai_accuracy_score", {}),
                "cache_hit_rate": performance_data.get("ai_cache_hit_rate", {})
            }
        }
    
    def _generate_health_section(self, health_data: Dict) -> Dict[str, Any]:
        """生成健康部分"""
        return {
            "title": "服务健康状态",
            "type": "health",
            "data": health_data
        }
    
    def _generate_alerts_section(self, alerts: List) -> Dict[str, Any]:
        """生成告警部分"""
        return {
            "title": "性能告警",
            "type": "alerts",
            "data": {
                "alerts": alerts,
                "summary": {
                    "total": len(alerts),
                    "critical": len([a for a in alerts if a.get("severity") == "critical"]),
                    "warning": len([a for a in alerts if a.get("severity") == "warning"])
                }
            }
        }
    
    def _generate_summary_html(self, summary: Dict) -> str:
        """生成摘要HTML"""
        return f"""
        <div class="metric">
            <strong>整体状态:</strong> {summary.get('overall_status', 'unknown')}
        </div>
        <div class="metric">
            <strong>服务总数:</strong> {summary.get('total_services', 0)}
        </div>
        <div class="metric">
            <strong>健康服务:</strong> {summary.get('healthy_services', 0)}
        </div>
        <div class="metric">
            <strong>活跃告警:</strong> {summary.get('active_alerts', 0)}
        </div>
        <div class="metric">
            <strong>平均API响应时间:</strong> {summary.get('avg_api_response_time', 0):.3f}s
        </div>
        """
    
    def _generate_section_html(self, section: Dict) -> str:
        """生成部分HTML"""
        if section["type"] == "metrics":
            html = "<div>"
            for metric_name, metric_data in section["data"].items():
                if isinstance(metric_data, dict) and "avg" in metric_data:
                    html += f"""
                    <div class="metric">
                        <strong>{metric_name}:</strong> 
                        平均: {metric_data.get('avg', 0):.3f}, 
                        最小: {metric_data.get('min', 0):.3f}, 
                        最大: {metric_data.get('max', 0):.3f}
                    </div>
                    """
            html += "</div>"
            return html
        
        elif section["type"] == "alerts":
            html = "<div>"
            for alert in section["data"]["alerts"]:
                severity_class = alert.get("severity", "info")
                html += f"""
                <div class="alert {severity_class}">
                    <strong>{alert.get('metric', 'unknown')}:</strong> 
                    {alert.get('message', 'No message')}
                </div>
                """
            html += "</div>"
            return html
        
        return "<p>暂无数据</p>"


# 全局监控面板实例
monitoring_dashboard = MonitoringDashboard()
