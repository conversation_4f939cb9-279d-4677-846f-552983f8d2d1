#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
系统健康监控服务
"""

import time
import asyncio
import threading
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from enum import Enum

from apps.backend.api.monitoring.monitoring_architecture import MonitoringTarget
from apps.backend.utils.logger import setup_logger

logger = setup_logger(__name__)


class HealthStatus(Enum):
    """健康状态"""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"


@dataclass
class HealthCheck:
    """健康检查结果"""
    target: str
    status: HealthStatus
    response_time: float
    message: str
    timestamp: float
    details: Dict[str, Any] = None


@dataclass
class ServiceHealth:
    """服务健康状态"""
    service_name: str
    overall_status: HealthStatus
    checks: List[HealthCheck]
    last_check: float
    uptime: float
    error_count: int = 0


class HealthMonitor:
    """系统健康监控器"""
    
    def __init__(self, check_interval: int = 60):
        self.check_interval = check_interval
        self.logger = logger
        
        # 健康检查结果存储
        self.health_results = {}
        self.service_health = {}
        
        # 监控状态
        self.is_running = False
        self.monitor_thread = None
        self.lock = threading.RLock()
        
        # 健康检查器注册表
        self.health_checkers = {
            "api": self._check_api_health,
            "database": self._check_database_health,
            "cache": self._check_cache_health,
            "search": self._check_search_health,
            "ai": self._check_ai_health
        }
        
        # 服务启动时间
        self.start_time = time.time()
    
    def start(self):
        """启动健康监控"""
        if self.is_running:
            self.logger.warning("Health monitor is already running")
            return
        
        self.is_running = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        self.logger.info("Health monitor started")
    
    def stop(self):
        """停止健康监控"""
        self.is_running = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        self.logger.info("Health monitor stopped")
    
    def register_health_checker(self, service_type: str, checker: Callable):
        """注册健康检查器"""
        self.health_checkers[service_type] = checker
        self.logger.info(f"Registered health checker for {service_type}")
    
    def check_service_health(self, target: MonitoringTarget) -> HealthCheck:
        """检查单个服务健康状态"""
        start_time = time.time()
        
        try:
            checker = self.health_checkers.get(target.type)
            if not checker:
                return HealthCheck(
                    target=target.name,
                    status=HealthStatus.UNKNOWN,
                    response_time=0.0,
                    message=f"No health checker for type: {target.type}",
                    timestamp=time.time()
                )
            
            # 执行健康检查
            result = checker(target)
            result.response_time = time.time() - start_time
            result.timestamp = time.time()
            
            return result
            
        except Exception as e:
            return HealthCheck(
                target=target.name,
                status=HealthStatus.CRITICAL,
                response_time=time.time() - start_time,
                message=f"Health check failed: {str(e)}",
                timestamp=time.time()
            )
    
    def get_overall_health(self) -> Dict[str, Any]:
        """获取系统整体健康状态"""
        with self.lock:
            if not self.service_health:
                return {
                    "status": HealthStatus.UNKNOWN.value,
                    "message": "No health data available",
                    "services": {},
                    "summary": {
                        "total": 0,
                        "healthy": 0,
                        "warning": 0,
                        "critical": 0,
                        "unknown": 0
                    }
                }
            
            # 统计各状态的服务数量
            summary = {
                "total": len(self.service_health),
                "healthy": 0,
                "warning": 0,
                "critical": 0,
                "unknown": 0
            }
            
            overall_status = HealthStatus.HEALTHY
            
            for service_name, health in self.service_health.items():
                summary[health.overall_status.value] += 1
                
                # 确定整体状态（最严重的状态）
                if health.overall_status == HealthStatus.CRITICAL:
                    overall_status = HealthStatus.CRITICAL
                elif health.overall_status == HealthStatus.WARNING and overall_status != HealthStatus.CRITICAL:
                    overall_status = HealthStatus.WARNING
                elif health.overall_status == HealthStatus.UNKNOWN and overall_status == HealthStatus.HEALTHY:
                    overall_status = HealthStatus.UNKNOWN
            
            return {
                "status": overall_status.value,
                "message": self._get_status_message(overall_status, summary),
                "services": {name: asdict(health) for name, health in self.service_health.items()},
                "summary": summary,
                "uptime": time.time() - self.start_time
            }
    
    def get_service_health(self, service_name: str) -> Optional[ServiceHealth]:
        """获取特定服务的健康状态"""
        with self.lock:
            return self.service_health.get(service_name)
    
    def get_health_history(self, service_name: str, hours: int = 24) -> List[HealthCheck]:
        """获取服务健康历史"""
        cutoff_time = time.time() - (hours * 3600)
        
        with self.lock:
            history = self.health_results.get(service_name, [])
            return [check for check in history if check.timestamp >= cutoff_time]
    
    def _monitor_loop(self):
        """监控循环"""
        from apps.backend.api.monitoring.monitoring_architecture import MONITORING_TARGETS
        
        while self.is_running:
            try:
                # 检查所有监控目标
                for target in MONITORING_TARGETS:
                    if not target.enabled:
                        continue
                    
                    # 执行健康检查
                    health_check = self.check_service_health(target)
                    
                    # 存储结果
                    with self.lock:
                        if target.name not in self.health_results:
                            self.health_results[target.name] = []
                        
                        self.health_results[target.name].append(health_check)
                        
                        # 限制历史记录数量
                        if len(self.health_results[target.name]) > 1000:
                            self.health_results[target.name] = self.health_results[target.name][-500:]
                        
                        # 更新服务健康状态
                        self._update_service_health(target.name, health_check)
                
                # 等待下一次检查
                time.sleep(self.check_interval)
                
            except Exception as e:
                self.logger.error(f"Error in health monitor loop: {e}")
                time.sleep(5)
    
    def _update_service_health(self, service_name: str, health_check: HealthCheck):
        """更新服务健康状态"""
        if service_name not in self.service_health:
            self.service_health[service_name] = ServiceHealth(
                service_name=service_name,
                overall_status=HealthStatus.UNKNOWN,
                checks=[],
                last_check=0.0,
                uptime=0.0
            )
        
        service = self.service_health[service_name]
        service.checks.append(health_check)
        service.last_check = health_check.timestamp
        
        # 限制检查历史数量
        if len(service.checks) > 100:
            service.checks = service.checks[-50:]
        
        # 更新整体状态
        recent_checks = [check for check in service.checks if check.timestamp > time.time() - 300]  # 最近5分钟
        
        if not recent_checks:
            service.overall_status = HealthStatus.UNKNOWN
        else:
            # 如果有任何critical状态，整体为critical
            if any(check.status == HealthStatus.CRITICAL for check in recent_checks):
                service.overall_status = HealthStatus.CRITICAL
            # 如果有warning状态，整体为warning
            elif any(check.status == HealthStatus.WARNING for check in recent_checks):
                service.overall_status = HealthStatus.WARNING
            # 否则为healthy
            else:
                service.overall_status = HealthStatus.HEALTHY
        
        # 更新错误计数
        if health_check.status in [HealthStatus.CRITICAL, HealthStatus.WARNING]:
            service.error_count += 1
        
        # 计算正常运行时间
        healthy_checks = [check for check in service.checks if check.status == HealthStatus.HEALTHY]
        if healthy_checks:
            service.uptime = time.time() - healthy_checks[0].timestamp
    
    def _check_api_health(self, target: MonitoringTarget) -> HealthCheck:
        """检查API健康状态"""
        try:
            import requests
            
            url = f"http://localhost:8001{target.endpoint or '/health'}"
            response = requests.get(url, timeout=target.timeout)
            
            if response.status_code == 200:
                return HealthCheck(
                    target=target.name,
                    status=HealthStatus.HEALTHY,
                    response_time=0.0,
                    message="API is healthy",
                    details={"status_code": response.status_code}
                )
            else:
                return HealthCheck(
                    target=target.name,
                    status=HealthStatus.WARNING,
                    response_time=0.0,
                    message=f"API returned status {response.status_code}",
                    details={"status_code": response.status_code}
                )
                
        except Exception as e:
            return HealthCheck(
                target=target.name,
                status=HealthStatus.CRITICAL,
                response_time=0.0,
                message=f"API health check failed: {str(e)}"
            )
    
    def _check_database_health(self, target: MonitoringTarget) -> HealthCheck:
        """检查数据库健康状态"""
        try:
            from apps.backend.api.config.database import get_db
            
            # 尝试获取数据库连接
            db = next(get_db())
            
            # 执行简单查询
            result = db.execute("SELECT 1").fetchone()
            
            if result:
                return HealthCheck(
                    target=target.name,
                    status=HealthStatus.HEALTHY,
                    response_time=0.0,
                    message="Database is healthy"
                )
            else:
                return HealthCheck(
                    target=target.name,
                    status=HealthStatus.WARNING,
                    response_time=0.0,
                    message="Database query returned no result"
                )
                
        except Exception as e:
            return HealthCheck(
                target=target.name,
                status=HealthStatus.CRITICAL,
                response_time=0.0,
                message=f"Database health check failed: {str(e)}"
            )
    
    def _check_cache_health(self, target: MonitoringTarget) -> HealthCheck:
        """检查缓存健康状态"""
        try:
            from apps.backend.api.config.redis import get_redis
            
            redis_client = get_redis()
            
            # 执行ping命令
            result = redis_client.ping()
            
            if result:
                return HealthCheck(
                    target=target.name,
                    status=HealthStatus.HEALTHY,
                    response_time=0.0,
                    message="Redis cache is healthy"
                )
            else:
                return HealthCheck(
                    target=target.name,
                    status=HealthStatus.WARNING,
                    response_time=0.0,
                    message="Redis ping failed"
                )
                
        except Exception as e:
            return HealthCheck(
                target=target.name,
                status=HealthStatus.CRITICAL,
                response_time=0.0,
                message=f"Redis health check failed: {str(e)}"
            )
    
    def _check_search_health(self, target: MonitoringTarget) -> HealthCheck:
        """检查搜索服务健康状态"""
        try:
            from apps.backend.api.config.elasticsearch import get_elasticsearch
            
            es_client = get_elasticsearch()
            
            # 检查集群健康状态
            health = es_client.cluster.health()
            
            if health['status'] == 'green':
                status = HealthStatus.HEALTHY
                message = "Elasticsearch cluster is healthy"
            elif health['status'] == 'yellow':
                status = HealthStatus.WARNING
                message = "Elasticsearch cluster has warnings"
            else:
                status = HealthStatus.CRITICAL
                message = "Elasticsearch cluster is unhealthy"
            
            return HealthCheck(
                target=target.name,
                status=status,
                response_time=0.0,
                message=message,
                details=health
            )
            
        except Exception as e:
            return HealthCheck(
                target=target.name,
                status=HealthStatus.CRITICAL,
                response_time=0.0,
                message=f"Elasticsearch health check failed: {str(e)}"
            )
    
    def _check_ai_health(self, target: MonitoringTarget) -> HealthCheck:
        """检查AI服务健康状态"""
        try:
            from apps.backend.api.services.ai import AIService
            from apps.backend.api.schemas.ai import SkillMatchRequest
            
            # 执行简单的AI功能测试
            test_request = SkillMatchRequest(
                job_skills=["Python"],
                user_skills=["Python"]
            )
            
            result = AIService.analyze_skill_match(test_request)
            
            if result and result.match_score >= 0:
                return HealthCheck(
                    target=target.name,
                    status=HealthStatus.HEALTHY,
                    response_time=0.0,
                    message="AI service is healthy"
                )
            else:
                return HealthCheck(
                    target=target.name,
                    status=HealthStatus.WARNING,
                    response_time=0.0,
                    message="AI service returned invalid result"
                )
                
        except Exception as e:
            return HealthCheck(
                target=target.name,
                status=HealthStatus.CRITICAL,
                response_time=0.0,
                message=f"AI service health check failed: {str(e)}"
            )
    
    def _get_status_message(self, status: HealthStatus, summary: Dict[str, int]) -> str:
        """获取状态消息"""
        if status == HealthStatus.HEALTHY:
            return f"All {summary['total']} services are healthy"
        elif status == HealthStatus.WARNING:
            return f"{summary['warning']} services have warnings"
        elif status == HealthStatus.CRITICAL:
            return f"{summary['critical']} services are critical"
        else:
            return "System health status unknown"


# 全局健康监控器实例
health_monitor = HealthMonitor()
