#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
完整监控架构设计
"""

import time
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta

from apps.backend.utils.logger import setup_logger

logger = setup_logger(__name__)


class MetricType(Enum):
    """指标类型"""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    SUMMARY = "summary"


class AlertSeverity(Enum):
    """告警严重级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class MetricDefinition:
    """指标定义"""
    name: str
    type: MetricType
    description: str
    labels: List[str] = field(default_factory=list)
    unit: str = ""
    help_text: str = ""


@dataclass
class AlertRule:
    """告警规则"""
    name: str
    metric: str
    condition: str  # >, <, >=, <=, ==, !=
    threshold: float
    duration: int  # 持续时间（秒）
    severity: AlertSeverity
    description: str
    labels: Dict[str, str] = field(default_factory=dict)
    enabled: bool = True


@dataclass
class MonitoringTarget:
    """监控目标"""
    name: str
    type: str  # api, database, cache, ai, system
    endpoint: Optional[str] = None
    check_interval: int = 60  # 检查间隔（秒）
    timeout: int = 30  # 超时时间（秒）
    enabled: bool = True
    critical: bool = True  # 是否关键服务


# 核心监控指标定义
CORE_METRICS = {
    # API性能指标
    "api_request_duration": MetricDefinition(
        name="api_request_duration_seconds",
        type=MetricType.HISTOGRAM,
        description="API请求响应时间",
        labels=["method", "endpoint", "status_code"],
        unit="seconds"
    ),
    
    "api_request_total": MetricDefinition(
        name="api_requests_total",
        type=MetricType.COUNTER,
        description="API请求总数",
        labels=["method", "endpoint", "status_code"]
    ),
    
    "api_concurrent_requests": MetricDefinition(
        name="api_concurrent_requests",
        type=MetricType.GAUGE,
        description="并发API请求数",
        labels=["endpoint"]
    ),
    
    # 数据库性能指标
    "db_query_duration": MetricDefinition(
        name="db_query_duration_seconds",
        type=MetricType.HISTOGRAM,
        description="数据库查询响应时间",
        labels=["operation", "table", "status"],
        unit="seconds"
    ),
    
    "db_connection_pool": MetricDefinition(
        name="db_connection_pool_size",
        type=MetricType.GAUGE,
        description="数据库连接池大小",
        labels=["pool_name", "status"]
    ),
    
    "db_query_total": MetricDefinition(
        name="db_queries_total",
        type=MetricType.COUNTER,
        description="数据库查询总数",
        labels=["operation", "table", "status"]
    ),
    
    # 缓存性能指标
    "cache_hit_rate": MetricDefinition(
        name="cache_hit_rate",
        type=MetricType.GAUGE,
        description="缓存命中率",
        labels=["cache_type", "cache_level"]
    ),
    
    "cache_operation_duration": MetricDefinition(
        name="cache_operation_duration_seconds",
        type=MetricType.HISTOGRAM,
        description="缓存操作响应时间",
        labels=["operation", "cache_type", "cache_level"],
        unit="seconds"
    ),
    
    "cache_memory_usage": MetricDefinition(
        name="cache_memory_usage_bytes",
        type=MetricType.GAUGE,
        description="缓存内存使用量",
        labels=["cache_type"],
        unit="bytes"
    ),
    
    # AI功能指标
    "ai_request_duration": MetricDefinition(
        name="ai_request_duration_seconds",
        type=MetricType.HISTOGRAM,
        description="AI请求响应时间",
        labels=["ai_function", "source", "status"],
        unit="seconds"
    ),
    
    "ai_accuracy_score": MetricDefinition(
        name="ai_accuracy_score",
        type=MetricType.GAUGE,
        description="AI功能准确率",
        labels=["ai_function"]
    ),
    
    "ai_cache_hit_rate": MetricDefinition(
        name="ai_cache_hit_rate",
        type=MetricType.GAUGE,
        description="AI缓存命中率",
        labels=["ai_function"]
    ),
    
    # 系统资源指标
    "system_cpu_usage": MetricDefinition(
        name="system_cpu_usage_percent",
        type=MetricType.GAUGE,
        description="CPU使用率",
        labels=["cpu"],
        unit="percent"
    ),
    
    "system_memory_usage": MetricDefinition(
        name="system_memory_usage_bytes",
        type=MetricType.GAUGE,
        description="内存使用量",
        labels=["type"],
        unit="bytes"
    ),
    
    "system_disk_usage": MetricDefinition(
        name="system_disk_usage_percent",
        type=MetricType.GAUGE,
        description="磁盘使用率",
        labels=["device", "mountpoint"],
        unit="percent"
    ),
    
    # 业务指标
    "job_search_total": MetricDefinition(
        name="job_searches_total",
        type=MetricType.COUNTER,
        description="岗位搜索总数",
        labels=["search_type", "status"]
    ),
    
    "user_activity_total": MetricDefinition(
        name="user_activities_total",
        type=MetricType.COUNTER,
        description="用户活动总数",
        labels=["activity_type", "user_type"]
    ),
    
    "data_quality_score": MetricDefinition(
        name="data_quality_score",
        type=MetricType.GAUGE,
        description="数据质量评分",
        labels=["data_type"]
    )
}


# 告警规则定义
ALERT_RULES = [
    # 系统资源告警
    AlertRule(
        name="high_cpu_usage",
        metric="system_cpu_usage_percent",
        condition=">",
        threshold=80.0,
        duration=300,  # 5分钟
        severity=AlertSeverity.WARNING,
        description="CPU使用率过高"
    ),
    
    AlertRule(
        name="high_memory_usage",
        metric="system_memory_usage_percent",
        condition=">",
        threshold=85.0,
        duration=300,
        severity=AlertSeverity.WARNING,
        description="内存使用率过高"
    ),
    
    AlertRule(
        name="disk_space_low",
        metric="system_disk_usage_percent",
        condition=">",
        threshold=90.0,
        duration=60,
        severity=AlertSeverity.CRITICAL,
        description="磁盘空间不足"
    ),
    
    # API性能告警
    AlertRule(
        name="high_api_response_time",
        metric="api_request_duration_seconds_p95",
        condition=">",
        threshold=2.0,
        duration=300,
        severity=AlertSeverity.WARNING,
        description="API响应时间过长"
    ),
    
    AlertRule(
        name="high_api_error_rate",
        metric="api_error_rate",
        condition=">",
        threshold=0.05,  # 5%
        duration=120,
        severity=AlertSeverity.ERROR,
        description="API错误率过高"
    ),
    
    # 数据库性能告警
    AlertRule(
        name="high_db_query_time",
        metric="db_query_duration_seconds_p95",
        condition=">",
        threshold=1.0,
        duration=300,
        severity=AlertSeverity.WARNING,
        description="数据库查询时间过长"
    ),
    
    AlertRule(
        name="db_connection_pool_exhausted",
        metric="db_connection_pool_available",
        condition="<",
        threshold=5.0,
        duration=60,
        severity=AlertSeverity.CRITICAL,
        description="数据库连接池耗尽"
    ),
    
    # 缓存性能告警
    AlertRule(
        name="low_cache_hit_rate",
        metric="cache_hit_rate",
        condition="<",
        threshold=0.6,  # 60%
        duration=600,  # 10分钟
        severity=AlertSeverity.WARNING,
        description="缓存命中率过低"
    ),
    
    # AI功能告警
    AlertRule(
        name="high_ai_response_time",
        metric="ai_request_duration_seconds_p95",
        condition=">",
        threshold=5.0,
        duration=300,
        severity=AlertSeverity.WARNING,
        description="AI功能响应时间过长"
    ),
    
    AlertRule(
        name="low_ai_accuracy",
        metric="ai_accuracy_score",
        condition="<",
        threshold=0.7,  # 70%
        duration=1800,  # 30分钟
        severity=AlertSeverity.ERROR,
        description="AI功能准确率过低"
    )
]


# 监控目标定义
MONITORING_TARGETS = [
    MonitoringTarget(
        name="api_server",
        type="api",
        endpoint="/health",
        check_interval=30,
        timeout=10,
        critical=True
    ),
    
    MonitoringTarget(
        name="database",
        type="database",
        check_interval=60,
        timeout=15,
        critical=True
    ),
    
    MonitoringTarget(
        name="redis_cache",
        type="cache",
        check_interval=60,
        timeout=10,
        critical=False
    ),
    
    MonitoringTarget(
        name="elasticsearch",
        type="search",
        check_interval=120,
        timeout=20,
        critical=False
    ),
    
    MonitoringTarget(
        name="ai_service",
        type="ai",
        check_interval=300,  # 5分钟
        timeout=30,
        critical=False
    )
]


class MonitoringArchitecture:
    """监控架构管理器"""
    
    def __init__(self):
        self.metrics = CORE_METRICS
        self.alert_rules = ALERT_RULES
        self.targets = MONITORING_TARGETS
        self.logger = logger
    
    def get_metric_definition(self, name: str) -> Optional[MetricDefinition]:
        """获取指标定义"""
        return self.metrics.get(name)
    
    def get_alert_rules(self, severity: Optional[AlertSeverity] = None) -> List[AlertRule]:
        """获取告警规则"""
        if severity:
            return [rule for rule in self.alert_rules if rule.severity == severity]
        return self.alert_rules
    
    def get_monitoring_targets(self, target_type: Optional[str] = None) -> List[MonitoringTarget]:
        """获取监控目标"""
        if target_type:
            return [target for target in self.targets if target.type == target_type]
        return self.targets
    
    def add_custom_metric(self, name: str, metric_def: MetricDefinition):
        """添加自定义指标"""
        self.metrics[name] = metric_def
        self.logger.info(f"Added custom metric: {name}")
    
    def add_custom_alert_rule(self, rule: AlertRule):
        """添加自定义告警规则"""
        self.alert_rules.append(rule)
        self.logger.info(f"Added custom alert rule: {rule.name}")
    
    def validate_configuration(self) -> Dict[str, List[str]]:
        """验证监控配置"""
        issues = {
            "errors": [],
            "warnings": []
        }
        
        # 验证指标定义
        for name, metric in self.metrics.items():
            if not metric.name:
                issues["errors"].append(f"Metric {name} has no name")
            if not metric.description:
                issues["warnings"].append(f"Metric {name} has no description")
        
        # 验证告警规则
        for rule in self.alert_rules:
            if rule.metric not in self.metrics:
                issues["errors"].append(f"Alert rule {rule.name} references unknown metric {rule.metric}")
            if rule.threshold <= 0 and rule.condition in [">", ">="]:
                issues["warnings"].append(f"Alert rule {rule.name} has suspicious threshold")
        
        return issues
    
    def get_dashboard_config(self) -> Dict[str, Any]:
        """获取监控面板配置"""
        return {
            "panels": [
                {
                    "title": "API性能",
                    "metrics": ["api_request_duration", "api_request_total", "api_concurrent_requests"],
                    "type": "timeseries"
                },
                {
                    "title": "数据库性能",
                    "metrics": ["db_query_duration", "db_connection_pool", "db_query_total"],
                    "type": "timeseries"
                },
                {
                    "title": "缓存性能",
                    "metrics": ["cache_hit_rate", "cache_operation_duration", "cache_memory_usage"],
                    "type": "stat"
                },
                {
                    "title": "AI功能",
                    "metrics": ["ai_request_duration", "ai_accuracy_score", "ai_cache_hit_rate"],
                    "type": "timeseries"
                },
                {
                    "title": "系统资源",
                    "metrics": ["system_cpu_usage", "system_memory_usage", "system_disk_usage"],
                    "type": "gauge"
                }
            ]
        }


# 全局监控架构实例
monitoring_architecture = MonitoringArchitecture()
