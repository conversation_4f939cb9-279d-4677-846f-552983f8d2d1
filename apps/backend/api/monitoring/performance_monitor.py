#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
性能监控服务
"""

import time
import threading
import psutil
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from collections import defaultdict, deque

from apps.backend.api.monitoring.monitoring_architecture import monitoring_architecture
from apps.backend.utils.logger import setup_logger

logger = setup_logger(__name__)


@dataclass
class PerformanceMetric:
    """性能指标数据"""
    timestamp: float
    metric_name: str
    value: float
    labels: Dict[str, str]
    unit: str = ""


@dataclass
class PerformanceSnapshot:
    """性能快照"""
    timestamp: float
    api_metrics: Dict[str, Any]
    database_metrics: Dict[str, Any]
    cache_metrics: Dict[str, Any]
    ai_metrics: Dict[str, Any]
    system_metrics: Dict[str, Any]


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, retention_hours: int = 24, collection_interval: int = 30):
        self.retention_hours = retention_hours
        self.collection_interval = collection_interval
        self.logger = logger
        
        # 性能数据存储
        self.metrics_buffer = deque(maxlen=10000)  # 最多保存10000个数据点
        self.snapshots = deque(maxlen=2880)  # 24小时，每30秒一个快照
        
        # 统计数据
        self.stats = defaultdict(lambda: {
            "count": 0,
            "sum": 0.0,
            "min": float('inf'),
            "max": float('-inf'),
            "avg": 0.0
        })
        
        # 监控状态
        self.is_running = False
        self.monitor_thread = None
        self.lock = threading.RLock()
        
        # 性能阈值
        self.thresholds = {
            "api_response_time_p95": 2.0,  # 2秒
            "db_query_time_p95": 1.0,      # 1秒
            "cache_hit_rate_min": 0.6,     # 60%
            "cpu_usage_max": 80.0,         # 80%
            "memory_usage_max": 85.0,      # 85%
            "disk_usage_max": 90.0          # 90%
        }
    
    def start(self):
        """启动性能监控"""
        if self.is_running:
            self.logger.warning("Performance monitor is already running")
            return
        
        self.is_running = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        self.logger.info("Performance monitor started")
    
    def stop(self):
        """停止性能监控"""
        self.is_running = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        self.logger.info("Performance monitor stopped")
    
    def record_metric(self, metric_name: str, value: float, labels: Dict[str, str] = None, unit: str = ""):
        """记录性能指标"""
        with self.lock:
            metric = PerformanceMetric(
                timestamp=time.time(),
                metric_name=metric_name,
                value=value,
                labels=labels or {},
                unit=unit
            )
            
            self.metrics_buffer.append(metric)
            
            # 更新统计数据
            key = f"{metric_name}:{str(sorted((labels or {}).items()))}"
            stats = self.stats[key]
            stats["count"] += 1
            stats["sum"] += value
            stats["min"] = min(stats["min"], value)
            stats["max"] = max(stats["max"], value)
            stats["avg"] = stats["sum"] / stats["count"]
    
    def get_current_snapshot(self) -> PerformanceSnapshot:
        """获取当前性能快照"""
        return PerformanceSnapshot(
            timestamp=time.time(),
            api_metrics=self._collect_api_metrics(),
            database_metrics=self._collect_database_metrics(),
            cache_metrics=self._collect_cache_metrics(),
            ai_metrics=self._collect_ai_metrics(),
            system_metrics=self._collect_system_metrics()
        )
    
    def get_metrics_summary(self, hours: int = 1) -> Dict[str, Any]:
        """获取指标摘要"""
        cutoff_time = time.time() - (hours * 3600)
        
        with self.lock:
            recent_metrics = [
                metric for metric in self.metrics_buffer
                if metric.timestamp >= cutoff_time
            ]
        
        summary = defaultdict(lambda: {
            "count": 0,
            "values": [],
            "avg": 0.0,
            "min": float('inf'),
            "max": float('-inf'),
            "p50": 0.0,
            "p95": 0.0,
            "p99": 0.0
        })
        
        # 按指标名称分组
        for metric in recent_metrics:
            key = metric.metric_name
            summary[key]["count"] += 1
            summary[key]["values"].append(metric.value)
        
        # 计算统计值
        for key, data in summary.items():
            if data["values"]:
                values = sorted(data["values"])
                data["avg"] = sum(values) / len(values)
                data["min"] = min(values)
                data["max"] = max(values)
                data["p50"] = self._percentile(values, 50)
                data["p95"] = self._percentile(values, 95)
                data["p99"] = self._percentile(values, 99)
                del data["values"]  # 删除原始数据以节省内存
        
        return dict(summary)
    
    def get_performance_trends(self, hours: int = 24) -> Dict[str, List[Dict[str, Any]]]:
        """获取性能趋势"""
        cutoff_time = time.time() - (hours * 3600)
        
        with self.lock:
            recent_snapshots = [
                snapshot for snapshot in self.snapshots
                if snapshot.timestamp >= cutoff_time
            ]
        
        trends = {
            "api_response_time": [],
            "database_query_time": [],
            "cache_hit_rate": [],
            "cpu_usage": [],
            "memory_usage": []
        }
        
        for snapshot in recent_snapshots:
            timestamp = snapshot.timestamp
            
            # API响应时间趋势
            if "avg_response_time" in snapshot.api_metrics:
                trends["api_response_time"].append({
                    "timestamp": timestamp,
                    "value": snapshot.api_metrics["avg_response_time"]
                })
            
            # 数据库查询时间趋势
            if "avg_query_time" in snapshot.database_metrics:
                trends["database_query_time"].append({
                    "timestamp": timestamp,
                    "value": snapshot.database_metrics["avg_query_time"]
                })
            
            # 缓存命中率趋势
            if "hit_rate" in snapshot.cache_metrics:
                trends["cache_hit_rate"].append({
                    "timestamp": timestamp,
                    "value": snapshot.cache_metrics["hit_rate"]
                })
            
            # CPU使用率趋势
            if "cpu_percent" in snapshot.system_metrics:
                trends["cpu_usage"].append({
                    "timestamp": timestamp,
                    "value": snapshot.system_metrics["cpu_percent"]
                })
            
            # 内存使用率趋势
            if "memory_percent" in snapshot.system_metrics:
                trends["memory_usage"].append({
                    "timestamp": timestamp,
                    "value": snapshot.system_metrics["memory_percent"]
                })
        
        return trends
    
    def check_performance_alerts(self) -> List[Dict[str, Any]]:
        """检查性能告警"""
        alerts = []
        current_metrics = self.get_metrics_summary(hours=0.25)  # 最近15分钟
        
        # 检查API响应时间
        if "api_request_duration" in current_metrics:
            p95_time = current_metrics["api_request_duration"]["p95"]
            if p95_time > self.thresholds["api_response_time_p95"]:
                alerts.append({
                    "type": "performance",
                    "severity": "warning",
                    "metric": "api_response_time",
                    "value": p95_time,
                    "threshold": self.thresholds["api_response_time_p95"],
                    "message": f"API响应时间P95过高: {p95_time:.2f}s"
                })
        
        # 检查数据库查询时间
        if "db_query_duration" in current_metrics:
            p95_time = current_metrics["db_query_duration"]["p95"]
            if p95_time > self.thresholds["db_query_time_p95"]:
                alerts.append({
                    "type": "performance",
                    "severity": "warning",
                    "metric": "db_query_time",
                    "value": p95_time,
                    "threshold": self.thresholds["db_query_time_p95"],
                    "message": f"数据库查询时间P95过高: {p95_time:.2f}s"
                })
        
        # 检查系统资源
        system_metrics = self._collect_system_metrics()
        
        if system_metrics.get("cpu_percent", 0) > self.thresholds["cpu_usage_max"]:
            alerts.append({
                "type": "system",
                "severity": "warning",
                "metric": "cpu_usage",
                "value": system_metrics["cpu_percent"],
                "threshold": self.thresholds["cpu_usage_max"],
                "message": f"CPU使用率过高: {system_metrics['cpu_percent']:.1f}%"
            })
        
        if system_metrics.get("memory_percent", 0) > self.thresholds["memory_usage_max"]:
            alerts.append({
                "type": "system",
                "severity": "warning",
                "metric": "memory_usage",
                "value": system_metrics["memory_percent"],
                "threshold": self.thresholds["memory_usage_max"],
                "message": f"内存使用率过高: {system_metrics['memory_percent']:.1f}%"
            })
        
        return alerts
    
    def _monitor_loop(self):
        """监控循环"""
        while self.is_running:
            try:
                # 收集性能快照
                snapshot = self.get_current_snapshot()
                
                with self.lock:
                    self.snapshots.append(snapshot)
                
                # 清理过期数据
                self._cleanup_old_data()
                
                # 等待下一次收集
                time.sleep(self.collection_interval)
                
            except Exception as e:
                self.logger.error(f"Error in monitor loop: {e}")
                time.sleep(5)  # 出错后等待5秒
    
    def _collect_api_metrics(self) -> Dict[str, Any]:
        """收集API性能指标"""
        # 这里应该从实际的API监控中获取数据
        # 暂时返回模拟数据
        return {
            "request_count": 0,
            "avg_response_time": 0.0,
            "error_rate": 0.0,
            "concurrent_requests": 0
        }
    
    def _collect_database_metrics(self) -> Dict[str, Any]:
        """收集数据库性能指标"""
        # 这里应该从数据库监控中获取数据
        return {
            "connection_count": 0,
            "avg_query_time": 0.0,
            "slow_queries": 0,
            "deadlocks": 0
        }
    
    def _collect_cache_metrics(self) -> Dict[str, Any]:
        """收集缓存性能指标"""
        # 这里应该从缓存监控中获取数据
        return {
            "hit_rate": 0.0,
            "miss_rate": 0.0,
            "memory_usage": 0,
            "evictions": 0
        }
    
    def _collect_ai_metrics(self) -> Dict[str, Any]:
        """收集AI功能指标"""
        # 这里应该从AI服务监控中获取数据
        return {
            "request_count": 0,
            "avg_response_time": 0.0,
            "accuracy_score": 0.0,
            "cache_hit_rate": 0.0
        }
    
    def _collect_system_metrics(self) -> Dict[str, Any]:
        """收集系统资源指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用情况
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_used = memory.used
            memory_total = memory.total
            
            # 磁盘使用情况
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            
            # 网络IO
            network = psutil.net_io_counters()
            
            return {
                "cpu_percent": cpu_percent,
                "memory_percent": memory_percent,
                "memory_used": memory_used,
                "memory_total": memory_total,
                "disk_percent": disk_percent,
                "disk_used": disk.used,
                "disk_total": disk.total,
                "network_bytes_sent": network.bytes_sent,
                "network_bytes_recv": network.bytes_recv
            }
        except Exception as e:
            self.logger.error(f"Error collecting system metrics: {e}")
            return {}
    
    def _percentile(self, values: List[float], percentile: int) -> float:
        """计算百分位数"""
        if not values:
            return 0.0
        
        k = (len(values) - 1) * percentile / 100
        f = int(k)
        c = k - f
        
        if f == len(values) - 1:
            return values[f]
        
        return values[f] * (1 - c) + values[f + 1] * c
    
    def _cleanup_old_data(self):
        """清理过期数据"""
        cutoff_time = time.time() - (self.retention_hours * 3600)
        
        # 清理指标缓冲区
        while self.metrics_buffer and self.metrics_buffer[0].timestamp < cutoff_time:
            self.metrics_buffer.popleft()
        
        # 清理快照
        while self.snapshots and self.snapshots[0].timestamp < cutoff_time:
            self.snapshots.popleft()


# 全局性能监控器实例
performance_monitor = PerformanceMonitor()
