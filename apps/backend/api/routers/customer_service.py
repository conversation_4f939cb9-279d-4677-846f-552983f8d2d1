#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
智能客服系统API路由
"""

import time
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Body
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field
from datetime import datetime

from apps.backend.api.database import get_db
from apps.backend.ai.intelligent_customer_service import (
    intelligent_customer_service, UserMessage, MessageType, 
    ConversationStatus, IntentType
)
from apps.backend.ai.ticket_management import (
    ticket_manager, TicketStatus, TicketPriority, TicketCategory
)
from apps.backend.ai.automation_engine import automation_engine
from apps.backend.utils.logger import setup_logger

logger = setup_logger(__name__)

router = APIRouter(prefix="/customer-service", tags=["智能客服"])


class ChatMessageRequest(BaseModel):
    """聊天消息请求"""
    user_id: str = Field(..., description="用户ID")
    content: str = Field(..., description="消息内容")
    message_type: str = Field("text", description="消息类型")
    attachments: List[str] = Field(default=[], description="附件列表")


class ChatMessageResponse(BaseModel):
    """聊天消息响应"""
    response_id: str = Field(..., description="响应ID")
    content: str = Field(..., description="响应内容")
    message_type: str = Field(..., description="消息类型")
    intent: str = Field(..., description="识别的意图")
    confidence: float = Field(..., description="置信度")
    quick_replies: List[str] = Field(default=[], description="快速回复选项")
    suggested_actions: List[str] = Field(default=[], description="建议操作")
    conversation_id: str = Field(..., description="对话ID")


class TicketCreateRequest(BaseModel):
    """工单创建请求"""
    user_id: str = Field(..., description="用户ID")
    title: str = Field(..., description="工单标题")
    description: str = Field(..., description="工单描述")
    attachments: List[str] = Field(default=[], description="附件列表")


class TicketResponse(BaseModel):
    """工单响应"""
    ticket_id: str = Field(..., description="工单ID")
    user_id: str = Field(..., description="用户ID")
    title: str = Field(..., description="工单标题")
    description: str = Field(..., description="工单描述")
    category: str = Field(..., description="工单分类")
    priority: str = Field(..., description="优先级")
    status: str = Field(..., description="状态")
    assigned_agent_id: Optional[str] = Field(None, description="分配的客服ID")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")


class TicketCommentRequest(BaseModel):
    """工单评论请求"""
    ticket_id: str = Field(..., description="工单ID")
    content: str = Field(..., description="评论内容")
    is_internal: bool = Field(False, description="是否内部评论")
    attachments: List[str] = Field(default=[], description="附件列表")


@router.post("/chat", response_model=ChatMessageResponse)
async def chat_with_bot(
    request: ChatMessageRequest,
    db: Session = Depends(get_db)
):
    """
    与智能客服聊天
    
    发送消息给智能客服并获取响应
    """
    try:
        # 创建用户消息
        user_message = UserMessage(
            message_id=f"msg_{int(time.time())}",
            user_id=request.user_id,
            content=request.content,
            message_type=MessageType(request.message_type),
            timestamp=datetime.now(),
            attachments=request.attachments
        )
        
        # 处理消息
        bot_response = intelligent_customer_service.process_message(user_message, db)
        
        # 获取对话上下文
        context = intelligent_customer_service._get_or_create_context(request.user_id)
        
        return ChatMessageResponse(
            response_id=bot_response.response_id,
            content=bot_response.content,
            message_type=bot_response.message_type.value,
            intent=bot_response.intent.value,
            confidence=bot_response.confidence,
            quick_replies=bot_response.quick_replies,
            suggested_actions=bot_response.suggested_actions,
            conversation_id=context.conversation_id
        )
        
    except Exception as e:
        logger.error(f"Error in chat: {e}")
        raise HTTPException(status_code=500, detail=f"聊天服务异常: {str(e)}")


@router.post("/escalate")
async def escalate_to_human(
    user_id: str = Body(..., description="用户ID"),
    reason: str = Body("", description="转人工原因"),
    db: Session = Depends(get_db)
):
    """
    转接人工客服
    
    将当前对话转接给人工客服
    """
    try:
        success = intelligent_customer_service.escalate_to_human(user_id, reason)
        
        if success:
            return {"status": "success", "message": "已转接人工客服，请稍候"}
        else:
            return {"status": "error", "message": "转接失败，请重试"}
            
    except Exception as e:
        logger.error(f"Error escalating to human: {e}")
        raise HTTPException(status_code=500, detail=f"转接服务异常: {str(e)}")


@router.post("/end-conversation")
async def end_conversation(
    user_id: str = Body(..., description="用户ID"),
    satisfaction_rating: Optional[int] = Body(None, description="满意度评分(1-5)"),
    db: Session = Depends(get_db)
):
    """
    结束对话
    
    结束当前对话并可选择提供满意度评分
    """
    try:
        success = intelligent_customer_service.end_conversation(user_id, satisfaction_rating)
        
        if success:
            return {"status": "success", "message": "对话已结束，感谢您的使用"}
        else:
            return {"status": "error", "message": "结束对话失败"}
            
    except Exception as e:
        logger.error(f"Error ending conversation: {e}")
        raise HTTPException(status_code=500, detail=f"结束对话异常: {str(e)}")


@router.get("/conversation-stats")
async def get_conversation_stats(
    db: Session = Depends(get_db)
):
    """
    获取对话统计
    
    获取当前智能客服的对话统计信息
    """
    try:
        stats = intelligent_customer_service.get_conversation_stats()
        return stats
        
    except Exception as e:
        logger.error(f"Error getting conversation stats: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计信息异常: {str(e)}")


@router.post("/tickets", response_model=TicketResponse)
async def create_ticket(
    request: TicketCreateRequest,
    db: Session = Depends(get_db)
):
    """
    创建工单
    
    创建新的客服工单
    """
    try:
        # 创建工单
        ticket = ticket_manager.create_ticket(
            user_id=request.user_id,
            title=request.title,
            description=request.description,
            attachments=[]  # 简化处理，实际应该处理附件
        )
        
        # 触发工单创建事件
        event = {
            "type": "ticket_created",
            "ticket_id": ticket.ticket_id,
            "user_id": ticket.user_id,
            "priority": ticket.priority.value
        }
        
        # 如果是高优先级工单，触发特殊事件
        if ticket.priority in [TicketPriority.HIGH, TicketPriority.CRITICAL]:
            event["type"] = "high_priority_ticket_created"
        
        # 异步处理事件
        import asyncio
        asyncio.create_task(automation_engine.process_event(event))
        
        return TicketResponse(
            ticket_id=ticket.ticket_id,
            user_id=ticket.user_id,
            title=ticket.title,
            description=ticket.description,
            category=ticket.category.value,
            priority=ticket.priority.value,
            status=ticket.status.value,
            assigned_agent_id=ticket.assigned_agent_id,
            created_at=ticket.created_at.isoformat(),
            updated_at=ticket.updated_at.isoformat()
        )
        
    except Exception as e:
        logger.error(f"Error creating ticket: {e}")
        raise HTTPException(status_code=500, detail=f"创建工单异常: {str(e)}")


@router.get("/tickets/{ticket_id}", response_model=TicketResponse)
async def get_ticket(
    ticket_id: str,
    db: Session = Depends(get_db)
):
    """
    获取工单详情
    
    根据工单ID获取工单详细信息
    """
    try:
        ticket = ticket_manager.get_ticket(ticket_id)
        
        if not ticket:
            raise HTTPException(status_code=404, detail="工单不存在")
        
        return TicketResponse(
            ticket_id=ticket.ticket_id,
            user_id=ticket.user_id,
            title=ticket.title,
            description=ticket.description,
            category=ticket.category.value,
            priority=ticket.priority.value,
            status=ticket.status.value,
            assigned_agent_id=ticket.assigned_agent_id,
            created_at=ticket.created_at.isoformat(),
            updated_at=ticket.updated_at.isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting ticket: {e}")
        raise HTTPException(status_code=500, detail=f"获取工单异常: {str(e)}")


@router.get("/tickets/user/{user_id}")
async def get_user_tickets(
    user_id: str,
    status: Optional[str] = Query(None, description="工单状态筛选"),
    db: Session = Depends(get_db)
):
    """
    获取用户工单列表
    
    获取指定用户的所有工单
    """
    try:
        status_filter = None
        if status:
            try:
                status_filter = TicketStatus(status)
            except ValueError:
                raise HTTPException(status_code=400, detail="无效的工单状态")
        
        tickets = ticket_manager.get_user_tickets(user_id, status_filter)
        
        ticket_list = []
        for ticket in tickets:
            ticket_list.append({
                "ticket_id": ticket.ticket_id,
                "title": ticket.title,
                "category": ticket.category.value,
                "priority": ticket.priority.value,
                "status": ticket.status.value,
                "created_at": ticket.created_at.isoformat(),
                "updated_at": ticket.updated_at.isoformat()
            })
        
        return {
            "user_id": user_id,
            "total": len(ticket_list),
            "tickets": ticket_list
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting user tickets: {e}")
        raise HTTPException(status_code=500, detail=f"获取用户工单异常: {str(e)}")


@router.post("/tickets/{ticket_id}/comments")
async def add_ticket_comment(
    ticket_id: str,
    request: TicketCommentRequest,
    user_id: str = Body(..., description="评论者ID"),
    db: Session = Depends(get_db)
):
    """
    添加工单评论
    
    为指定工单添加评论
    """
    try:
        success = ticket_manager.add_comment(
            ticket_id=ticket_id,
            author_id=user_id,
            author_type="user",
            content=request.content,
            is_internal=request.is_internal,
            attachments=[]  # 简化处理
        )
        
        if success:
            return {"status": "success", "message": "评论添加成功"}
        else:
            raise HTTPException(status_code=404, detail="工单不存在")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error adding ticket comment: {e}")
        raise HTTPException(status_code=500, detail=f"添加评论异常: {str(e)}")


@router.put("/tickets/{ticket_id}/status")
async def update_ticket_status(
    ticket_id: str,
    status: str = Body(..., description="新状态"),
    comment: Optional[str] = Body(None, description="备注"),
    agent_id: Optional[str] = Body(None, description="操作员ID"),
    db: Session = Depends(get_db)
):
    """
    更新工单状态
    
    更新指定工单的状态
    """
    try:
        try:
            status_enum = TicketStatus(status)
        except ValueError:
            raise HTTPException(status_code=400, detail="无效的工单状态")
        
        success = ticket_manager.update_ticket_status(
            ticket_id=ticket_id,
            status=status_enum,
            agent_id=agent_id,
            comment=comment
        )
        
        if success:
            # 触发工单状态变更事件
            event = {
                "type": "ticket_status_changed",
                "ticket_id": ticket_id,
                "new_status": status,
                "agent_id": agent_id
            }
            
            # 如果工单已解决，触发解决事件
            if status_enum == TicketStatus.RESOLVED:
                event["type"] = "ticket_resolved"
            
            # 异步处理事件
            import asyncio
            asyncio.create_task(automation_engine.process_event(event))
            
            return {"status": "success", "message": "工单状态更新成功"}
        else:
            raise HTTPException(status_code=404, detail="工单不存在")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating ticket status: {e}")
        raise HTTPException(status_code=500, detail=f"更新工单状态异常: {str(e)}")


@router.get("/tickets/stats")
async def get_ticket_statistics(
    db: Session = Depends(get_db)
):
    """
    获取工单统计
    
    获取工单系统的统计信息
    """
    try:
        stats = ticket_manager.get_ticket_statistics()
        return stats
        
    except Exception as e:
        logger.error(f"Error getting ticket statistics: {e}")
        raise HTTPException(status_code=500, detail=f"获取工单统计异常: {str(e)}")


@router.get("/automation/workflows")
async def get_workflows(
    db: Session = Depends(get_db)
):
    """
    获取自动化工作流列表
    
    获取所有配置的自动化工作流
    """
    try:
        workflows = []
        for workflow in automation_engine.workflows.values():
            workflows.append({
                "workflow_id": workflow.workflow_id,
                "name": workflow.name,
                "description": workflow.description,
                "status": workflow.status.value,
                "execution_count": workflow.execution_count,
                "last_executed_at": workflow.last_executed_at.isoformat() if workflow.last_executed_at else None,
                "created_at": workflow.created_at.isoformat()
            })
        
        return {
            "total": len(workflows),
            "workflows": workflows
        }
        
    except Exception as e:
        logger.error(f"Error getting workflows: {e}")
        raise HTTPException(status_code=500, detail=f"获取工作流异常: {str(e)}")


@router.get("/automation/stats")
async def get_automation_statistics(
    db: Session = Depends(get_db)
):
    """
    获取自动化统计
    
    获取自动化引擎的统计信息
    """
    try:
        stats = automation_engine.get_workflow_statistics()
        return stats
        
    except Exception as e:
        logger.error(f"Error getting automation statistics: {e}")
        raise HTTPException(status_code=500, detail=f"获取自动化统计异常: {str(e)}")


@router.post("/automation/workflows/{workflow_id}/trigger")
async def trigger_workflow(
    workflow_id: str,
    context: Dict[str, Any] = Body({}, description="执行上下文"),
    db: Session = Depends(get_db)
):
    """
    手动触发工作流
    
    手动执行指定的自动化工作流
    """
    try:
        result = await automation_engine.trigger_workflow(workflow_id, context)
        return result
        
    except Exception as e:
        logger.error(f"Error triggering workflow: {e}")
        raise HTTPException(status_code=500, detail=f"触发工作流异常: {str(e)}")


@router.put("/automation/workflows/{workflow_id}/status")
async def update_workflow_status(
    workflow_id: str,
    status: str = Body(..., description="新状态"),
    db: Session = Depends(get_db)
):
    """
    更新工作流状态
    
    启用、禁用或暂停指定的工作流
    """
    try:
        from apps.backend.ai.automation_engine import WorkflowStatus
        
        try:
            status_enum = WorkflowStatus(status)
        except ValueError:
            raise HTTPException(status_code=400, detail="无效的工作流状态")
        
        success = automation_engine.update_workflow_status(workflow_id, status_enum)
        
        if success:
            return {"status": "success", "message": "工作流状态更新成功"}
        else:
            raise HTTPException(status_code=404, detail="工作流不存在")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating workflow status: {e}")
        raise HTTPException(status_code=500, detail=f"更新工作流状态异常: {str(e)}")
