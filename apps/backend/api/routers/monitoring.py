#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
监控API路由
"""

from fastapi import APIRouter, HTTPException, Query, Depends
from typing import Dict, List, Any, Optional
from datetime import datetime

from apps.backend.api.monitoring.performance_monitor import performance_monitor
from apps.backend.api.monitoring.health_monitor import health_monitor
from apps.backend.api.monitoring.dashboard import monitoring_dashboard
from apps.backend.api.monitoring.monitoring_architecture import monitoring_architecture
from apps.backend.monitoring.comprehensive_monitor import comprehensive_monitor
from apps.backend.monitoring.health_checker import health_checker
from apps.backend.optimization.performance_optimizer import performance_optimizer
from apps.backend.utils.logger import setup_logger

logger = setup_logger(__name__)

router = APIRouter(prefix="/monitoring", tags=["监控"])


@router.get("/health", summary="系统健康检查")
async def get_system_health():
    """获取系统整体健康状态"""
    try:
        health_data = health_monitor.get_overall_health()
        return {
            "success": True,
            "data": health_data,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Error getting system health: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health/{service_name}", summary="服务健康检查")
async def get_service_health(service_name: str):
    """获取特定服务的健康状态"""
    try:
        service_health = health_monitor.get_service_health(service_name)

        if not service_health:
            raise HTTPException(status_code=404, detail=f"Service {service_name} not found")

        return {
            "success": True,
            "data": service_health,
            "timestamp": datetime.now().isoformat()
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting service health for {service_name}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/metrics", summary="性能指标")
async def get_performance_metrics(
    hours: int = Query(1, ge=1, le=168, description="时间范围（小时）")
):
    """获取性能指标摘要"""
    try:
        metrics_summary = performance_monitor.get_metrics_summary(hours=hours)

        return {
            "success": True,
            "data": {
                "summary": metrics_summary,
                "time_range": f"{hours}h",
                "timestamp": datetime.now().isoformat()
            }
        }
    except Exception as e:
        logger.error(f"Error getting performance metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/metrics/trends", summary="性能趋势")
async def get_performance_trends(
    hours: int = Query(24, ge=1, le=168, description="时间范围（小时）")
):
    """获取性能趋势数据"""
    try:
        trends = performance_monitor.get_performance_trends(hours=hours)

        return {
            "success": True,
            "data": {
                "trends": trends,
                "time_range": f"{hours}h",
                "timestamp": datetime.now().isoformat()
            }
        }
    except Exception as e:
        logger.error(f"Error getting performance trends: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/metrics/realtime", summary="实时指标")
async def get_realtime_metrics():
    """获取实时性能指标"""
    try:
        realtime_data = monitoring_dashboard.get_real_time_metrics()

        return {
            "success": True,
            "data": realtime_data
        }
    except Exception as e:
        logger.error(f"Error getting realtime metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/alerts", summary="性能告警")
async def get_performance_alerts():
    """获取当前性能告警"""
    try:
        alerts = performance_monitor.check_performance_alerts()

        return {
            "success": True,
            "data": {
                "alerts": alerts,
                "count": len(alerts),
                "timestamp": datetime.now().isoformat()
            }
        }
    except Exception as e:
        logger.error(f"Error getting performance alerts: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/dashboard", summary="监控面板")
async def get_dashboard_data(
    time_range: str = Query("1h", description="时间范围（1h, 6h, 24h, 7d）")
):
    """获取监控面板数据"""
    try:
        dashboard_data = monitoring_dashboard.get_dashboard_data(time_range=time_range)

        return {
            "success": True,
            "data": dashboard_data
        }
    except Exception as e:
        logger.error(f"Error getting dashboard data: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/reports/performance", summary="性能报告")
async def get_performance_report(
    time_range: str = Query("24h", description="时间范围（1h, 6h, 24h, 7d）"),
    format: str = Query("json", description="报告格式（json, html）")
):
    """生成性能报告"""
    try:
        report = monitoring_dashboard.generate_performance_report(time_range=time_range)

        if format.lower() == "html":
            html_content = monitoring_dashboard.export_report_html(report)
            return {
                "success": True,
                "data": {
                    "format": "html",
                    "content": html_content,
                    "report_id": report.report_id
                }
            }
        else:
            return {
                "success": True,
                "data": {
                    "format": "json",
                    "report": report,
                    "report_id": report.report_id
                }
            }
    except Exception as e:
        logger.error(f"Error generating performance report: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/config/metrics", summary="指标配置")
async def get_metrics_config():
    """获取监控指标配置"""
    try:
        return {
            "success": True,
            "data": {
                "metrics": {name: metric.__dict__ for name, metric in monitoring_architecture.metrics.items()},
                "alert_rules": [rule.__dict__ for rule in monitoring_architecture.alert_rules],
                "targets": [target.__dict__ for target in monitoring_architecture.targets]
            }
        }
    except Exception as e:
        logger.error(f"Error getting metrics config: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/config/dashboard", summary="面板配置")
async def get_dashboard_config():
    """获取监控面板配置"""
    try:
        config = monitoring_architecture.get_dashboard_config()

        return {
            "success": True,
            "data": config
        }
    except Exception as e:
        logger.error(f"Error getting dashboard config: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/metrics/record", summary="记录指标")
async def record_metric(
    metric_name: str,
    value: float,
    labels: Optional[Dict[str, str]] = None,
    unit: str = ""
):
    """手动记录性能指标"""
    try:
        performance_monitor.record_metric(
            metric_name=metric_name,
            value=value,
            labels=labels or {},
            unit=unit
        )

        return {
            "success": True,
            "message": f"Metric {metric_name} recorded successfully",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Error recording metric: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/status", summary="监控服务状态")
async def get_monitoring_status():
    """获取监控服务状态"""
    try:
        return {
            "success": True,
            "data": {
                "performance_monitor": {
                    "running": performance_monitor.is_running,
                    "metrics_count": len(performance_monitor.metrics_buffer),
                    "snapshots_count": len(performance_monitor.snapshots)
                },
                "health_monitor": {
                    "running": health_monitor.is_running,
                    "services_count": len(health_monitor.service_health),
                    "check_interval": health_monitor.check_interval
                },
                "uptime": datetime.now().isoformat()
            }
        }
    except Exception as e:
        logger.error(f"Error getting monitoring status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/start", summary="启动监控")
async def start_monitoring():
    """启动监控服务"""
    try:
        if not performance_monitor.is_running:
            performance_monitor.start()

        if not health_monitor.is_running:
            health_monitor.start()

        return {
            "success": True,
            "message": "Monitoring services started",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Error starting monitoring: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/stop", summary="停止监控")
async def stop_monitoring():
    """停止监控服务"""
    try:
        if performance_monitor.is_running:
            performance_monitor.stop()

        if health_monitor.is_running:
            health_monitor.stop()

        return {
            "success": True,
            "message": "Monitoring services stopped",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Error stopping monitoring: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/history/{service_name}", summary="服务历史")
async def get_service_history(
    service_name: str,
    hours: int = Query(24, ge=1, le=168, description="时间范围（小时）")
):
    """获取服务健康历史"""
    try:
        history = health_monitor.get_health_history(service_name, hours=hours)

        return {
            "success": True,
            "data": {
                "service_name": service_name,
                "history": [check.__dict__ for check in history],
                "count": len(history),
                "time_range": f"{hours}h"
            }
        }
    except Exception as e:
        logger.error(f"Error getting service history for {service_name}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 简化的健康检查端点（用于负载均衡器等）
@router.get("/ping", summary="简单健康检查")
async def ping():
    """简单的健康检查端点"""
    return {"status": "ok", "timestamp": datetime.now().isoformat()}


# Prometheus指标端点
@router.get("/metrics/prometheus", summary="Prometheus指标")
async def get_prometheus_metrics():
    """获取Prometheus格式的指标"""
    try:
        # 这里应该返回Prometheus格式的指标
        # 暂时返回简单的文本格式
        metrics_text = """
# HELP recruitment_api_requests_total Total number of API requests
# TYPE recruitment_api_requests_total counter
recruitment_api_requests_total{method="GET",endpoint="/api/v1/jobs"} 1234

# HELP recruitment_api_request_duration_seconds API request duration
# TYPE recruitment_api_request_duration_seconds histogram
recruitment_api_request_duration_seconds_bucket{le="0.1"} 100
recruitment_api_request_duration_seconds_bucket{le="0.5"} 200
recruitment_api_request_duration_seconds_bucket{le="1.0"} 300
recruitment_api_request_duration_seconds_bucket{le="+Inf"} 350
recruitment_api_request_duration_seconds_sum 87.5
recruitment_api_request_duration_seconds_count 350
        """

        return metrics_text.strip()
    except Exception as e:
        logger.error(f"Error getting Prometheus metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 新增的综合监控端点
@router.get("/comprehensive/status", summary="综合监控状态")
async def get_comprehensive_status():
    """获取综合监控系统状态"""
    try:
        status = comprehensive_monitor.get_current_status()
        return {
            "success": True,
            "data": status
        }
    except Exception as e:
        logger.error(f"Error getting comprehensive status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/comprehensive/health", summary="系统健康检查")
async def get_comprehensive_health():
    """运行完整的系统健康检查"""
    try:
        health_report = await health_checker.run_all_checks()

        return {
            "success": True,
            "data": {
                "overall_status": health_report.overall_status.value,
                "timestamp": health_report.timestamp.isoformat(),
                "summary": health_report.summary,
                "check_results": [
                    {
                        "check_name": result.check_name,
                        "check_type": result.check_type.value,
                        "status": result.status.value,
                        "message": result.message,
                        "response_time": result.response_time,
                        "details": result.details
                    }
                    for result in health_report.check_results
                ],
                "recommendations": health_report.recommendations
            }
        }
    except Exception as e:
        logger.error(f"Error getting comprehensive health: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/optimization/report", summary="性能优化报告")
async def get_optimization_report():
    """获取性能优化建议报告"""
    try:
        report = performance_optimizer.get_optimization_report()
        return {
            "success": True,
            "data": report
        }
    except Exception as e:
        logger.error(f"Error getting optimization report: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/optimization/implement/{recommendation_id}", summary="实施优化建议")
async def implement_optimization(recommendation_id: str):
    """实施指定的性能优化建议"""
    try:
        result = performance_optimizer.implement_recommendation(recommendation_id)

        if result["success"]:
            return {
                "success": True,
                "message": "优化建议实施成功",
                "data": result
            }
        else:
            raise HTTPException(status_code=400, detail=result.get("error", "实施失败"))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error implementing optimization: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/comprehensive/start", summary="启动综合监控")
async def start_comprehensive_monitoring():
    """启动综合监控系统"""
    try:
        # 启动综合监控
        comprehensive_monitor.start()

        # 启动性能优化器
        performance_optimizer.start()

        return {
            "success": True,
            "message": "综合监控系统启动成功",
            "components": ["comprehensive_monitor", "performance_optimizer"]
        }
    except Exception as e:
        logger.error(f"Error starting comprehensive monitoring: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/comprehensive/stop", summary="停止综合监控")
async def stop_comprehensive_monitoring():
    """停止综合监控系统"""
    try:
        # 停止综合监控
        comprehensive_monitor.stop()

        # 停止性能优化器
        performance_optimizer.stop()

        return {
            "success": True,
            "message": "综合监控系统停止成功"
        }
    except Exception as e:
        logger.error(f"Error stopping comprehensive monitoring: {e}")
        raise HTTPException(status_code=500, detail=str(e))
