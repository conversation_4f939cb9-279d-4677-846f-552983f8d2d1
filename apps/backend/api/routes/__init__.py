"""
API路由模块
定义API路由和处理函数
"""

from fastapi import APIRouter

# 创建API路由器
api_router = APIRouter()

# 安全导入子路由
try:
    from apps.backend.api.routes import auth
    api_router.include_router(auth.router, prefix="/auth", tags=["auth"])
except ImportError as e:
    print(f"Warning: Failed to import auth routes: {e}")

try:
    from apps.backend.api.routes import users
    api_router.include_router(users.router, prefix="/users", tags=["users"])
except ImportError as e:
    print(f"Warning: Failed to import users routes: {e}")

try:
    from apps.backend.api.routes import jobs
    api_router.include_router(jobs.router, prefix="/jobs", tags=["jobs"])
except ImportError as e:
    print(f"Warning: Failed to import jobs routes: {e}")

try:
    from apps.backend.api.routes import community
    api_router.include_router(community.router, prefix="/community", tags=["community"])
except ImportError as e:
    print(f"Warning: Failed to import community routes: {e}")

try:
    from apps.backend.api.routes import admin
    api_router.include_router(admin.router, prefix="/admin", tags=["admin"])
except ImportError as e:
    print(f"Warning: Failed to import admin routes: {e}")

try:
    from apps.backend.api.routes import admin_auth
    api_router.include_router(admin_auth.router, prefix="/admin/auth", tags=["admin_auth"])
except ImportError as e:
    print(f"Warning: Failed to import admin_auth routes: {e}")

try:
    from apps.backend.api.routes import admin_manage
    api_router.include_router(admin_manage.router, prefix="/admin/manage", tags=["admin_manage"])
except ImportError as e:
    print(f"Warning: Failed to import admin_manage routes: {e}")

try:
    from apps.backend.api.routes import admin_content
    api_router.include_router(admin_content.router, prefix="/admin/content", tags=["admin_content"])
except ImportError as e:
    print(f"Warning: Failed to import admin_content routes: {e}")

try:
    from apps.backend.api.routes import admin_user
    api_router.include_router(admin_user.router, prefix="/admin/user", tags=["admin_user"])
except ImportError as e:
    print(f"Warning: Failed to import admin_user routes: {e}")

try:
    from apps.backend.api.routes import admin_job
    api_router.include_router(admin_job.router, prefix="/admin", tags=["admin_job"])
except ImportError as e:
    print(f"Warning: Failed to import admin_job routes: {e}")

try:
    from apps.backend.api.routes import admin_community
    api_router.include_router(admin_community.router, prefix="/admin/community", tags=["admin_community"])
except ImportError as e:
    print(f"Warning: Failed to import admin_community routes: {e}")

try:
    from apps.backend.api.routes import admin_analysis
    api_router.include_router(admin_analysis.router, prefix="/admin/analysis", tags=["admin_analysis"])
except ImportError as e:
    print(f"Warning: Failed to import admin_analysis routes: {e}")

try:
    from apps.backend.api.routes import admin_crawler
    api_router.include_router(admin_crawler.router, prefix="/admin/crawler", tags=["admin_crawler"])
except ImportError as e:
    print(f"Warning: Failed to import admin_crawler routes: {e}")

try:
    from apps.backend.api.routes import admin_config
    api_router.include_router(admin_config.router, prefix="/admin/config", tags=["admin_config"])
except ImportError as e:
    print(f"Warning: Failed to import admin_config routes: {e}")

try:
    from apps.backend.api.routes import admin_cache
    api_router.include_router(admin_cache.router, prefix="/admin", tags=["admin_cache"])
except ImportError as e:
    print(f"Warning: Failed to import admin_cache routes: {e}")

try:
    from apps.backend.api.routes import admin_elasticsearch
    api_router.include_router(admin_elasticsearch.router, prefix="/admin", tags=["admin_elasticsearch"])
except ImportError as e:
    print(f"Warning: Failed to import admin_elasticsearch routes: {e}")

try:
    from apps.backend.api.routes import admin_security
    api_router.include_router(admin_security.router, prefix="/admin", tags=["admin_security"])
except ImportError as e:
    print(f"Warning: Failed to import admin_security routes: {e}")

try:
    from apps.backend.api.routes import admin_rate_limit
    api_router.include_router(admin_rate_limit.router, prefix="/admin", tags=["admin_rate_limit"])
except ImportError as e:
    print(f"Warning: Failed to import admin_rate_limit routes: {e}")

try:
    from apps.backend.api.routes import admin_monitoring
    api_router.include_router(admin_monitoring.router, prefix="/admin", tags=["admin_monitoring"])
except ImportError as e:
    print(f"Warning: Failed to import admin_monitoring routes: {e}")

try:
    from apps.backend.api.routes import admin_database
    api_router.include_router(admin_database.router, prefix="/admin", tags=["admin_database"])
except ImportError as e:
    print(f"Warning: Failed to import admin_database routes: {e}")

try:
    from apps.backend.api.routes import admin_ha
    api_router.include_router(admin_ha.router, prefix="/admin", tags=["admin_ha"])
except ImportError as e:
    print(f"Warning: Failed to import admin_ha routes: {e}")

try:
    from apps.backend.api.routes import admin_performance
    api_router.include_router(admin_performance.router, prefix="/admin", tags=["admin_performance"])
except ImportError as e:
    print(f"Warning: Failed to import admin_performance routes: {e}")

try:
    from apps.backend.api.routes import admin_security_audit
    api_router.include_router(admin_security_audit.router, prefix="/admin", tags=["admin_security_audit"])
except ImportError as e:
    print(f"Warning: Failed to import admin_security_audit routes: {e}")

try:
    from apps.backend.api.routes import crawler
    api_router.include_router(crawler.router, prefix="/crawler", tags=["crawler"])
except ImportError as e:
    print(f"Warning: Failed to import crawler routes: {e}")

try:
    from apps.backend.api.routes import ai
    api_router.include_router(ai.router, prefix="/ai", tags=["ai"])
except ImportError as e:
    print(f"Warning: Failed to import ai routes: {e}")



try:
    from apps.backend.api.routes import admin_user_management
    api_router.include_router(admin_user_management.router, tags=["admin_user_management"])
except ImportError as e:
    print(f"Warning: Failed to import admin_user_management routes: {e}")

try:
    from apps.backend.api.routes import analytics
    api_router.include_router(analytics.router, prefix="/admin", tags=["analytics"])
except ImportError as e:
    print(f"Warning: Failed to import analytics routes: {e}")

try:
    from apps.backend.api.routes import admin_ai
    api_router.include_router(admin_ai.router, prefix="/admin", tags=["admin_ai"])
except ImportError as e:
    print(f"Warning: Failed to import admin_ai routes: {e}")

try:
    from apps.backend.api.routes import admin_analytics
    api_router.include_router(admin_analytics.router, prefix="/admin", tags=["admin_analytics"])
except ImportError as e:
    print(f"Warning: Failed to import admin_analytics routes: {e}")

try:
    from apps.backend.api.routes import admin_system
    api_router.include_router(admin_system.router, prefix="/admin", tags=["admin_system"])
except ImportError as e:
    print(f"Warning: Failed to import admin_system routes: {e}")

# 添加管理员认证路由
try:
    from apps.backend.api.routes import admin_auth
    api_router.include_router(admin_auth.router, prefix="/admin/auth", tags=["admin_auth"])
except ImportError as e:
    print(f"Warning: Failed to import admin_auth routes: {e}")

# 添加Mock API支持
try:
    from apps.backend.api.routes import mock_api
    api_router.include_router(mock_api.router, prefix="/mock", tags=["mock"])
except ImportError as e:
    print(f"Warning: Failed to import mock_api routes: {e}")
