from datetime import <PERSON><PERSON><PERSON>
from typing import Any, List

from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session

from apps.backend.api.config.database import get_db
from apps.backend.api.config.settings import settings
from apps.backend.api.dependencies.admin_auth import (
    get_current_active_admin, require_system_view
)
from apps.backend.api.models.admin import Admin, AdminRole, AdminPermission
from apps.backend.api.schemas.admin import (
    AdminLogin, Token, Admin as AdminSchema,
    Role as RoleSchema, Permission as PermissionSchema
)
from apps.backend.api.utils.security import (
    verify_password, create_access_token, create_refresh_token
)
from apps.backend.api.utils.response import success_response, error_response

router = APIRouter(tags=["admin"])


@router.post("/auth/token", response_model=Token)
async def admin_login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
) -> Any:
    """
    管理员登录
    """
    # 查询管理员
    admin = db.query(Admin).filter(Admin.username == form_data.username).first()
    if not admin or not verify_password(form_data.password, admin.password_hash):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 检查管理员状态
    if not admin.status:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive admin",
        )
    
    # 创建访问令牌
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": str(admin.id)},
        expires_delta=access_token_expires
    )
    
    # 创建刷新令牌
    refresh_token = create_refresh_token(data={"sub": str(admin.id)})
    
    # 更新最后登录时间
    admin.last_login = timedelta(minutes=0)
    db.commit()
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        "refresh_token": refresh_token,
    }


@router.get("/me", response_model=AdminSchema)
async def get_admin_me(
    current_admin: Admin = Depends(get_current_active_admin),
    db: Session = Depends(get_db)
) -> Any:
    """
    获取当前管理员信息
    """
    # 查询角色
    role = db.query(AdminRole).filter(AdminRole.id == current_admin.admin_role_id).first()
    
    # 构建管理员信息
    admin_info = AdminSchema.from_orm(current_admin)
    admin_info.role = RoleSchema.from_orm(role) if role else None
    
    return admin_info


@router.get("/roles", response_model=List[RoleSchema])
async def get_roles(
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(require_system_view())
) -> Any:
    """
    获取角色列表
    """
    roles = db.query(AdminRole).all()
    return roles


@router.get("/permissions", response_model=List[PermissionSchema])
async def get_permissions(
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(require_system_view())
) -> Any:
    """
    获取权限列表
    """
    permissions = db.query(AdminPermission).all()
    return permissions
