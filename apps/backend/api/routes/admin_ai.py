#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI管理相关的API路由
"""

import uuid
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.orm import Session
from sqlalchemy import desc, asc, func

from apps.backend.api.deps import get_db, get_current_admin_user
from apps.backend.api.schemas.ai_management import (
    AIModel, AIModelCreate, AIModelUpdate,
    RecommendationConfig, RecommendationConfigBase,
    MatchRule, MatchRuleCreate, MatchRuleUpdate,
    SmartTag, SmartTagCreate, SmartTagUpdate, GenerateTagsRequest, TagStatistics,
    ServiceMetrics, APIEndpointStatus, ErrorLog,
    ModelStatus, RuleStatus, TagStatus
)
from apps.backend.api.models.ai_management import (
    AIModelModel, RecommendationConfigModel, MatchRuleModel, SmartTagModel,
    ServiceMetricsModel, APIEndpointStatusModel, ErrorLogModel, AIModelCallLogModel
)
from apps.backend.api.schemas.common import PaginatedResponse, SuccessResponse
from apps.backend.api.utils.ai_service import AIServiceManager
from apps.backend.api.utils.monitoring import MetricsCollector

router = APIRouter()

# AI模型管理相关路由

@router.get("/ai/models", response_model=PaginatedResponse[AIModel])
async def get_ai_models(
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100),
    type: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin_user)
):
    """获取AI模型列表"""
    query = db.query(AIModelModel)
    
    if type:
        query = query.filter(AIModelModel.type == type)
    if status:
        query = query.filter(AIModelModel.status == status)
    
    total = query.count()
    models = query.order_by(desc(AIModelModel.created_at)).offset((page - 1) * page_size).limit(page_size).all()
    
    # 转换为响应格式
    model_list = []
    for model in models:
        model_dict = {
            "id": model.id,
            "name": model.name,
            "type": model.type,
            "version": model.version,
            "description": model.description,
            "api_endpoint": model.api_endpoint,
            "parameters": model.parameters,
            "status": model.status,
            "performance": {
                "accuracy": model.accuracy,
                "response_time": model.response_time,
                "call_count": model.call_count,
                "error_rate": model.error_rate
            },
            "created_at": model.created_at,
            "updated_at": model.updated_at
        }
        model_list.append(model_dict)
    
    return PaginatedResponse(
        data=model_list,
        total=total,
        page=page,
        page_size=page_size
    )


@router.get("/ai/models/{model_id}", response_model=AIModel)
async def get_ai_model(
    model_id: str,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin_user)
):
    """获取AI模型详情"""
    model = db.query(AIModelModel).filter(AIModelModel.id == model_id).first()
    if not model:
        raise HTTPException(status_code=404, detail="AI模型不存在")
    
    return {
        "id": model.id,
        "name": model.name,
        "type": model.type,
        "version": model.version,
        "description": model.description,
        "api_endpoint": model.api_endpoint,
        "parameters": model.parameters,
        "status": model.status,
        "performance": {
            "accuracy": model.accuracy,
            "response_time": model.response_time,
            "call_count": model.call_count,
            "error_rate": model.error_rate
        },
        "created_at": model.created_at,
        "updated_at": model.updated_at
    }


@router.post("/ai/models", response_model=AIModel)
async def create_ai_model(
    model_data: AIModelCreate,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin_user)
):
    """创建AI模型"""
    # 检查模型名称是否已存在
    existing_model = db.query(AIModelModel).filter(AIModelModel.name == model_data.name).first()
    if existing_model:
        raise HTTPException(status_code=400, detail="模型名称已存在")
    
    # 创建新模型
    new_model = AIModelModel(
        id=str(uuid.uuid4()),
        name=model_data.name,
        type=model_data.type,
        version=model_data.version,
        description=model_data.description,
        api_endpoint=model_data.api_endpoint,
        parameters=model_data.parameters,
        status=ModelStatus.INACTIVE
    )
    
    db.add(new_model)
    db.commit()
    db.refresh(new_model)
    
    return {
        "id": new_model.id,
        "name": new_model.name,
        "type": new_model.type,
        "version": new_model.version,
        "description": new_model.description,
        "api_endpoint": new_model.api_endpoint,
        "parameters": new_model.parameters,
        "status": new_model.status,
        "performance": {
            "accuracy": new_model.accuracy,
            "response_time": new_model.response_time,
            "call_count": new_model.call_count,
            "error_rate": new_model.error_rate
        },
        "created_at": new_model.created_at,
        "updated_at": new_model.updated_at
    }


@router.put("/ai/models/{model_id}", response_model=AIModel)
async def update_ai_model(
    model_id: str,
    model_data: AIModelUpdate,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin_user)
):
    """更新AI模型"""
    model = db.query(AIModelModel).filter(AIModelModel.id == model_id).first()
    if not model:
        raise HTTPException(status_code=404, detail="AI模型不存在")
    
    # 更新模型信息
    update_data = model_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(model, field, value)
    
    model.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(model)
    
    return {
        "id": model.id,
        "name": model.name,
        "type": model.type,
        "version": model.version,
        "description": model.description,
        "api_endpoint": model.api_endpoint,
        "parameters": model.parameters,
        "status": model.status,
        "performance": {
            "accuracy": model.accuracy,
            "response_time": model.response_time,
            "call_count": model.call_count,
            "error_rate": model.error_rate
        },
        "created_at": model.created_at,
        "updated_at": model.updated_at
    }


@router.delete("/ai/models/{model_id}", response_model=SuccessResponse)
async def delete_ai_model(
    model_id: str,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin_user)
):
    """删除AI模型"""
    model = db.query(AIModelModel).filter(AIModelModel.id == model_id).first()
    if not model:
        raise HTTPException(status_code=404, detail="AI模型不存在")
    
    db.delete(model)
    db.commit()
    
    return SuccessResponse(message="AI模型删除成功")


@router.patch("/ai/models/{model_id}/status", response_model=SuccessResponse)
async def toggle_model_status(
    model_id: str,
    status: ModelStatus,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin_user)
):
    """切换模型状态"""
    model = db.query(AIModelModel).filter(AIModelModel.id == model_id).first()
    if not model:
        raise HTTPException(status_code=404, detail="AI模型不存在")
    
    model.status = status
    model.updated_at = datetime.utcnow()
    db.commit()
    
    return SuccessResponse(message=f"模型状态已更新为{status.value}")


# 推荐算法配置相关路由

@router.get("/ai/recommendation/config", response_model=RecommendationConfig)
async def get_recommendation_config(
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin_user)
):
    """获取推荐配置"""
    config = db.query(RecommendationConfigModel).first()
    if not config:
        # 创建默认配置
        config = RecommendationConfigModel(id=str(uuid.uuid4()))
        db.add(config)
        db.commit()
        db.refresh(config)
    
    return {
        "id": config.id,
        "algorithm_weights": {
            "content_based": config.content_based_weight,
            "collaborative_filtering": config.collaborative_filtering_weight,
            "behavioral": config.behavioral_weight,
            "trending": config.trending_weight
        },
        "feature_weights": {
            "major_match": config.major_match_weight,
            "skill_match": config.skill_match_weight,
            "location_match": config.location_match_weight,
            "salary_match": config.salary_match_weight,
            "experience_match": config.experience_match_weight,
            "education_match": config.education_match_weight
        },
        "recommendation_params": {
            "max_recommendations": config.max_recommendations,
            "min_match_score": config.min_match_score,
            "diversity_factor": config.diversity_factor,
            "freshness_weight": config.freshness_weight,
            "popularity_weight": config.popularity_weight
        },
        "collaborative_params": {
            "user_similarity_threshold": config.user_similarity_threshold,
            "item_similarity_threshold": config.item_similarity_threshold,
            "min_interactions": config.min_interactions,
            "max_neighbors": config.max_neighbors
        },
        "experiment_config": {
            "ab_test_enabled": config.ab_test_enabled,
            "test_group_ratio": config.test_group_ratio,
            "experiment_duration": config.experiment_duration
        },
        "created_at": config.created_at,
        "updated_at": config.updated_at
    }


@router.put("/ai/recommendation/config", response_model=RecommendationConfig)
async def update_recommendation_config(
    config_data: RecommendationConfigBase,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin_user)
):
    """更新推荐配置"""
    config = db.query(RecommendationConfigModel).first()
    if not config:
        config = RecommendationConfigModel(id=str(uuid.uuid4()))
        db.add(config)
    
    # 更新算法权重
    config.content_based_weight = config_data.algorithm_weights.content_based
    config.collaborative_filtering_weight = config_data.algorithm_weights.collaborative_filtering
    config.behavioral_weight = config_data.algorithm_weights.behavioral
    config.trending_weight = config_data.algorithm_weights.trending
    
    # 更新特征权重
    config.major_match_weight = config_data.feature_weights.major_match
    config.skill_match_weight = config_data.feature_weights.skill_match
    config.location_match_weight = config_data.feature_weights.location_match
    config.salary_match_weight = config_data.feature_weights.salary_match
    config.experience_match_weight = config_data.feature_weights.experience_match
    config.education_match_weight = config_data.feature_weights.education_match
    
    # 更新推荐参数
    config.max_recommendations = config_data.recommendation_params.max_recommendations
    config.min_match_score = config_data.recommendation_params.min_match_score
    config.diversity_factor = config_data.recommendation_params.diversity_factor
    config.freshness_weight = config_data.recommendation_params.freshness_weight
    config.popularity_weight = config_data.recommendation_params.popularity_weight
    
    # 更新协同过滤参数
    config.user_similarity_threshold = config_data.collaborative_params.user_similarity_threshold
    config.item_similarity_threshold = config_data.collaborative_params.item_similarity_threshold
    config.min_interactions = config_data.collaborative_params.min_interactions
    config.max_neighbors = config_data.collaborative_params.max_neighbors
    
    # 更新实验配置
    config.ab_test_enabled = config_data.experiment_config.ab_test_enabled
    config.test_group_ratio = config_data.experiment_config.test_group_ratio
    config.experiment_duration = config_data.experiment_config.experiment_duration
    
    config.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(config)
    
    return await get_recommendation_config(db, current_admin)


@router.post("/ai/recommendation/config/reset", response_model=SuccessResponse)
async def reset_recommendation_config(
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin_user)
):
    """重置推荐配置为默认值"""
    config = db.query(RecommendationConfigModel).first()
    if config:
        db.delete(config)
        db.commit()

    return SuccessResponse(message="推荐配置已重置为默认值")


# 匹配规则管理相关路由

@router.get("/ai/match-rules", response_model=PaginatedResponse[MatchRule])
async def get_match_rules(
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100),
    type: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin_user)
):
    """获取匹配规则列表"""
    query = db.query(MatchRuleModel)

    if type:
        query = query.filter(MatchRuleModel.type == type)
    if status:
        query = query.filter(MatchRuleModel.status == status)

    total = query.count()
    rules = query.order_by(asc(MatchRuleModel.priority), desc(MatchRuleModel.created_at)).offset((page - 1) * page_size).limit(page_size).all()

    # 转换为响应格式
    rule_list = []
    for rule in rules:
        rule_dict = {
            "id": rule.id,
            "name": rule.name,
            "type": rule.type,
            "category": rule.category,
            "conditions": rule.conditions,
            "threshold": rule.threshold,
            "priority": rule.priority,
            "description": rule.description,
            "status": rule.status,
            "usage": {
                "match_count": rule.match_count,
                "success_rate": rule.success_rate,
                "avg_score": rule.avg_score
            },
            "created_at": rule.created_at,
            "updated_at": rule.updated_at
        }
        rule_list.append(rule_dict)

    return PaginatedResponse(
        data=rule_list,
        total=total,
        page=page,
        page_size=page_size
    )


@router.get("/ai/match-rules/{rule_id}", response_model=MatchRule)
async def get_match_rule(
    rule_id: str,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin_user)
):
    """获取匹配规则详情"""
    rule = db.query(MatchRuleModel).filter(MatchRuleModel.id == rule_id).first()
    if not rule:
        raise HTTPException(status_code=404, detail="匹配规则不存在")

    return {
        "id": rule.id,
        "name": rule.name,
        "type": rule.type,
        "category": rule.category,
        "conditions": rule.conditions,
        "threshold": rule.threshold,
        "priority": rule.priority,
        "description": rule.description,
        "status": rule.status,
        "usage": {
            "match_count": rule.match_count,
            "success_rate": rule.success_rate,
            "avg_score": rule.avg_score
        },
        "created_at": rule.created_at,
        "updated_at": rule.updated_at
    }


@router.post("/ai/match-rules", response_model=MatchRule)
async def create_match_rule(
    rule_data: MatchRuleCreate,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin_user)
):
    """创建匹配规则"""
    # 检查规则名称是否已存在
    existing_rule = db.query(MatchRuleModel).filter(MatchRuleModel.name == rule_data.name).first()
    if existing_rule:
        raise HTTPException(status_code=400, detail="规则名称已存在")

    # 创建新规则
    new_rule = MatchRuleModel(
        id=str(uuid.uuid4()),
        name=rule_data.name,
        type=rule_data.type,
        category=rule_data.category,
        conditions=[condition.dict() for condition in rule_data.conditions],
        threshold=rule_data.threshold,
        priority=rule_data.priority,
        description=rule_data.description,
        status=RuleStatus.INACTIVE
    )

    db.add(new_rule)
    db.commit()
    db.refresh(new_rule)

    return {
        "id": new_rule.id,
        "name": new_rule.name,
        "type": new_rule.type,
        "category": new_rule.category,
        "conditions": new_rule.conditions,
        "threshold": new_rule.threshold,
        "priority": new_rule.priority,
        "description": new_rule.description,
        "status": new_rule.status,
        "usage": {
            "match_count": new_rule.match_count,
            "success_rate": new_rule.success_rate,
            "avg_score": new_rule.avg_score
        },
        "created_at": new_rule.created_at,
        "updated_at": new_rule.updated_at
    }


@router.put("/ai/match-rules/{rule_id}", response_model=MatchRule)
async def update_match_rule(
    rule_id: str,
    rule_data: MatchRuleUpdate,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin_user)
):
    """更新匹配规则"""
    rule = db.query(MatchRuleModel).filter(MatchRuleModel.id == rule_id).first()
    if not rule:
        raise HTTPException(status_code=404, detail="匹配规则不存在")

    # 更新规则信息
    update_data = rule_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        if field == "conditions" and value is not None:
            setattr(rule, field, [condition.dict() for condition in value])
        else:
            setattr(rule, field, value)

    rule.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(rule)

    return await get_match_rule(rule_id, db, current_admin)


@router.delete("/ai/match-rules/{rule_id}", response_model=SuccessResponse)
async def delete_match_rule(
    rule_id: str,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin_user)
):
    """删除匹配规则"""
    rule = db.query(MatchRuleModel).filter(MatchRuleModel.id == rule_id).first()
    if not rule:
        raise HTTPException(status_code=404, detail="匹配规则不存在")

    db.delete(rule)
    db.commit()

    return SuccessResponse(message="匹配规则删除成功")


@router.patch("/ai/match-rules/{rule_id}/status", response_model=SuccessResponse)
async def toggle_rule_status(
    rule_id: str,
    status: RuleStatus,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin_user)
):
    """切换规则状态"""
    rule = db.query(MatchRuleModel).filter(MatchRuleModel.id == rule_id).first()
    if not rule:
        raise HTTPException(status_code=404, detail="匹配规则不存在")

    rule.status = status
    rule.updated_at = datetime.utcnow()
    db.commit()

    return SuccessResponse(message=f"规则状态已更新为{status.value}")


# 智能标签管理相关路由

@router.get("/ai/smart-tags", response_model=PaginatedResponse[SmartTag])
async def get_smart_tags(
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100),
    type: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    is_ai_generated: Optional[bool] = Query(None),
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin_user)
):
    """获取智能标签列表"""
    query = db.query(SmartTagModel)

    if type:
        query = query.filter(SmartTagModel.type == type)
    if status:
        query = query.filter(SmartTagModel.status == status)
    if is_ai_generated is not None:
        query = query.filter(SmartTagModel.is_ai_generated == is_ai_generated)

    total = query.count()
    tags = query.order_by(desc(SmartTagModel.usage_count), desc(SmartTagModel.created_at)).offset((page - 1) * page_size).limit(page_size).all()

    # 转换为响应格式
    tag_list = []
    for tag in tags:
        tag_dict = {
            "id": tag.id,
            "name": tag.name,
            "type": tag.type,
            "category": tag.category,
            "description": tag.description,
            "color": tag.color,
            "is_ai_generated": tag.is_ai_generated,
            "confidence": tag.confidence,
            "usage_count": tag.usage_count,
            "status": tag.status,
            "rules": {
                "keywords": tag.keywords,
                "conditions": tag.conditions,
                "weight": tag.weight
            },
            "created_at": tag.created_at,
            "updated_at": tag.updated_at
        }
        tag_list.append(tag_dict)

    return PaginatedResponse(
        data=tag_list,
        total=total,
        page=page,
        page_size=page_size
    )


@router.get("/ai/smart-tags/{tag_id}", response_model=SmartTag)
async def get_smart_tag(
    tag_id: str,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin_user)
):
    """获取智能标签详情"""
    tag = db.query(SmartTagModel).filter(SmartTagModel.id == tag_id).first()
    if not tag:
        raise HTTPException(status_code=404, detail="智能标签不存在")

    return {
        "id": tag.id,
        "name": tag.name,
        "type": tag.type,
        "category": tag.category,
        "description": tag.description,
        "color": tag.color,
        "is_ai_generated": tag.is_ai_generated,
        "confidence": tag.confidence,
        "usage_count": tag.usage_count,
        "status": tag.status,
        "rules": {
            "keywords": tag.keywords,
            "conditions": tag.conditions,
            "weight": tag.weight
        },
        "created_at": tag.created_at,
        "updated_at": tag.updated_at
    }


@router.post("/ai/smart-tags", response_model=SmartTag)
async def create_smart_tag(
    tag_data: SmartTagCreate,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin_user)
):
    """创建智能标签"""
    # 检查标签名称是否已存在
    existing_tag = db.query(SmartTagModel).filter(SmartTagModel.name == tag_data.name).first()
    if existing_tag:
        raise HTTPException(status_code=400, detail="标签名称已存在")

    # 创建新标签
    new_tag = SmartTagModel(
        id=str(uuid.uuid4()),
        name=tag_data.name,
        type=tag_data.type,
        category=tag_data.category,
        description=tag_data.description,
        color=tag_data.color,
        is_ai_generated=False,
        confidence=100.0,
        keywords=tag_data.rules.keywords,
        conditions=tag_data.rules.conditions,
        weight=tag_data.rules.weight,
        status=TagStatus.ACTIVE
    )

    db.add(new_tag)
    db.commit()
    db.refresh(new_tag)

    return await get_smart_tag(new_tag.id, db, current_admin)


@router.put("/ai/smart-tags/{tag_id}", response_model=SmartTag)
async def update_smart_tag(
    tag_id: str,
    tag_data: SmartTagUpdate,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin_user)
):
    """更新智能标签"""
    tag = db.query(SmartTagModel).filter(SmartTagModel.id == tag_id).first()
    if not tag:
        raise HTTPException(status_code=404, detail="智能标签不存在")

    # 更新标签信息
    update_data = tag_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        if field == "rules" and value is not None:
            tag.keywords = value.keywords
            tag.conditions = value.conditions
            tag.weight = value.weight
        else:
            setattr(tag, field, value)

    tag.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(tag)

    return await get_smart_tag(tag_id, db, current_admin)


@router.delete("/ai/smart-tags/{tag_id}", response_model=SuccessResponse)
async def delete_smart_tag(
    tag_id: str,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin_user)
):
    """删除智能标签"""
    tag = db.query(SmartTagModel).filter(SmartTagModel.id == tag_id).first()
    if not tag:
        raise HTTPException(status_code=404, detail="智能标签不存在")

    db.delete(tag)
    db.commit()

    return SuccessResponse(message="智能标签删除成功")


@router.patch("/ai/smart-tags/{tag_id}/status", response_model=SuccessResponse)
async def toggle_tag_status(
    tag_id: str,
    status: TagStatus,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin_user)
):
    """切换标签状态"""
    tag = db.query(SmartTagModel).filter(SmartTagModel.id == tag_id).first()
    if not tag:
        raise HTTPException(status_code=404, detail="智能标签不存在")

    tag.status = status
    tag.updated_at = datetime.utcnow()
    db.commit()

    return SuccessResponse(message=f"标签状态已更新为{status.value}")


@router.post("/ai/smart-tags/generate", response_model=List[SmartTag])
async def generate_ai_tags(
    request: GenerateTagsRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin_user)
):
    """AI生成标签"""
    # 这里应该调用AI服务生成标签
    # 为了演示，我们创建一些模拟的AI生成标签
    generated_tags = []

    for i in range(min(request.count, 5)):  # 限制生成数量
        tag_name = f"AI生成标签_{i+1}"
        new_tag = SmartTagModel(
            id=str(uuid.uuid4()),
            name=tag_name,
            type=request.type or TagType.JOB,
            category="AI生成",
            description=f"由AI自动生成的{tag_name}",
            color="blue",
            is_ai_generated=True,
            confidence=85.0 + (i * 2),  # 模拟不同的置信度
            keywords=[f"关键词{i+1}", f"keyword{i+1}"],
            conditions=[f"condition_{i+1}"],
            weight=0.8,
            status=TagStatus.ACTIVE
        )

        db.add(new_tag)
        generated_tags.append(new_tag)

    db.commit()

    # 转换为响应格式
    result = []
    for tag in generated_tags:
        db.refresh(tag)
        result.append(await get_smart_tag(tag.id, db, current_admin))

    return result


@router.get("/ai/smart-tags/statistics", response_model=TagStatistics)
async def get_tag_statistics(
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin_user)
):
    """获取标签统计"""
    total_tags = db.query(SmartTagModel).count()
    ai_generated_tags = db.query(SmartTagModel).filter(SmartTagModel.is_ai_generated == True).count()
    manual_tags = total_tags - ai_generated_tags

    # 计算平均置信度
    avg_confidence_result = db.query(func.avg(SmartTagModel.confidence)).scalar()
    average_confidence = float(avg_confidence_result) if avg_confidence_result else 0.0

    # 获取热门标签
    top_tags = db.query(SmartTagModel).order_by(desc(SmartTagModel.usage_count)).limit(3).all()
    top_used_tags = []
    for tag in top_tags:
        top_used_tags.append(await get_smart_tag(tag.id, db, current_admin))

    return {
        "total_tags": total_tags,
        "ai_generated_tags": ai_generated_tags,
        "manual_tags": manual_tags,
        "average_confidence": average_confidence,
        "top_used_tags": top_used_tags
    }


# 服务监控相关路由

@router.get("/ai/monitoring/realtime")
async def get_realtime_metrics(
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin_user)
):
    """获取实时指标"""
    # 模拟实时指标数据
    return {
        "online_users": 1256,
        "page_views": 15680,
        "searches": 890,
        "applications": 156,
        "system_load": 65.2,
        "response_time": 120,
        "error_rate": 0.8,
        "success_rate": 99.2
    }


@router.get("/ai/monitoring/metrics", response_model=List[ServiceMetrics])
async def get_metrics_history(
    time_range: Optional[str] = Query("24h"),
    start_time: Optional[str] = Query(None),
    end_time: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin_user)
):
    """获取历史指标数据"""
    # 计算时间范围
    end_dt = datetime.utcnow()
    if time_range == "1h":
        start_dt = end_dt - timedelta(hours=1)
    elif time_range == "24h":
        start_dt = end_dt - timedelta(hours=24)
    elif time_range == "7d":
        start_dt = end_dt - timedelta(days=7)
    elif time_range == "30d":
        start_dt = end_dt - timedelta(days=30)
    else:
        start_dt = end_dt - timedelta(hours=24)

    # 查询历史数据
    metrics = db.query(ServiceMetricsModel).filter(
        ServiceMetricsModel.timestamp >= start_dt,
        ServiceMetricsModel.timestamp <= end_dt
    ).order_by(ServiceMetricsModel.timestamp).all()

    # 如果没有数据，生成模拟数据
    if not metrics:
        metrics = []
        current_time = start_dt
        while current_time <= end_dt:
            metric = ServiceMetricsModel(
                id=str(uuid.uuid4()),
                timestamp=current_time,
                api_calls=120 + int((current_time.hour % 12) * 20),
                success_rate=99.0 + (current_time.minute % 10) * 0.1,
                avg_response_time=1.0 + (current_time.hour % 6) * 0.2,
                error_rate=0.5 + (current_time.minute % 5) * 0.1,
                active_users=45 + int((current_time.hour % 8) * 15)
            )
            metrics.append(metric)
            current_time += timedelta(hours=2)

    return [
        {
            "timestamp": metric.timestamp,
            "api_calls": metric.api_calls,
            "success_rate": metric.success_rate,
            "avg_response_time": metric.avg_response_time,
            "error_rate": metric.error_rate,
            "active_users": metric.active_users
        }
        for metric in metrics
    ]


@router.get("/ai/monitoring/endpoints", response_model=List[APIEndpointStatus])
async def get_api_endpoints(
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin_user)
):
    """获取API端点监控数据"""
    endpoints = db.query(APIEndpointStatusModel).all()

    # 如果没有数据，返回模拟数据
    if not endpoints:
        mock_endpoints = [
            {
                "endpoint": "/ai/major-match",
                "method": "POST",
                "calls": 3420,
                "success_rate": 99.1,
                "avg_response_time": 1.2,
                "status": "healthy"
            },
            {
                "endpoint": "/ai/skill-match",
                "method": "POST",
                "calls": 2890,
                "success_rate": 98.8,
                "avg_response_time": 0.9,
                "status": "healthy"
            },
            {
                "endpoint": "/ai/job-recommend",
                "method": "POST",
                "calls": 1560,
                "success_rate": 97.5,
                "avg_response_time": 2.1,
                "status": "warning"
            },
            {
                "endpoint": "/ai/interview-prep",
                "method": "POST",
                "calls": 890,
                "success_rate": 99.3,
                "avg_response_time": 1.8,
                "status": "healthy"
            },
            {
                "endpoint": "/ai/resume-parse",
                "method": "POST",
                "calls": 456,
                "success_rate": 96.2,
                "avg_response_time": 3.2,
                "status": "error"
            }
        ]
        return mock_endpoints

    return [
        {
            "endpoint": endpoint.endpoint,
            "method": endpoint.method,
            "calls": endpoint.calls,
            "success_rate": endpoint.success_rate,
            "avg_response_time": endpoint.avg_response_time,
            "status": endpoint.status
        }
        for endpoint in endpoints
    ]


@router.get("/ai/monitoring/errors", response_model=PaginatedResponse[ErrorLog])
async def get_error_logs(
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100),
    severity: Optional[str] = Query(None),
    endpoint: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin_user)
):
    """获取错误日志"""
    query = db.query(ErrorLogModel)

    if severity:
        query = query.filter(ErrorLogModel.severity == severity)
    if endpoint:
        query = query.filter(ErrorLogModel.endpoint.contains(endpoint))

    total = query.count()
    logs = query.order_by(desc(ErrorLogModel.timestamp)).offset((page - 1) * page_size).limit(page_size).all()

    # 如果没有数据，返回模拟数据
    if not logs:
        mock_logs = [
            {
                "id": "1",
                "timestamp": datetime.utcnow() - timedelta(minutes=5),
                "endpoint": "/ai/resume-parse",
                "error_type": "TimeoutError",
                "message": "AI服务响应超时",
                "severity": "high"
            },
            {
                "id": "2",
                "timestamp": datetime.utcnow() - timedelta(minutes=15),
                "endpoint": "/ai/job-recommend",
                "error_type": "ValidationError",
                "message": "用户画像数据格式错误",
                "severity": "medium"
            },
            {
                "id": "3",
                "timestamp": datetime.utcnow() - timedelta(minutes=30),
                "endpoint": "/ai/skill-match",
                "error_type": "RateLimitError",
                "message": "API调用频率超限",
                "severity": "low"
            }
        ]
        return PaginatedResponse(
            data=mock_logs,
            total=len(mock_logs),
            page=page,
            page_size=page_size
        )

    log_list = [
        {
            "id": log.id,
            "timestamp": log.timestamp,
            "endpoint": log.endpoint,
            "error_type": log.error_type,
            "message": log.message,
            "severity": log.severity
        }
        for log in logs
    ]

    return PaginatedResponse(
        data=log_list,
        total=total,
        page=page,
        page_size=page_size
    )


@router.get("/ai/monitoring/health")
async def get_health_status(
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin_user)
):
    """获取服务健康状态"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow(),
        "services": {
            "ai_models": "healthy",
            "recommendation_engine": "healthy",
            "match_rules": "healthy",
            "smart_tags": "healthy",
            "database": "healthy",
            "cache": "healthy"
        },
        "metrics": {
            "uptime": "99.9%",
            "response_time": "120ms",
            "error_rate": "0.8%",
            "throughput": "1200 req/min"
        }
    }
