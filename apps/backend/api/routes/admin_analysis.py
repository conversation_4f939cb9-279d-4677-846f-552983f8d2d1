#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据分析API路由
包括用户分析、岗位分析、行为分析等功能
"""

from typing import Any, Dict, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.orm import Session
from sqlalchemy import desc, asc, func, text
from datetime import datetime, timedelta

from apps.backend.api.config.database import get_db
from apps.backend.api.dependencies.auth import get_current_active_admin, check_admin_permission
from apps.backend.api.models.admin import Admin
from apps.backend.api.models.user import User
from apps.backend.api.models.job import Job
from apps.backend.api.models.interaction import Favorite, BrowseHistory
from apps.backend.api.models.community import CommunityPost, Comment
from apps.backend.api.utils.response import success_response, error_response
from apps.backend.utils.logger import setup_logger

# 创建路由
router = APIRouter(tags=["admin_analysis"])

# 设置日志
logger = setup_logger("api_admin_analysis")


@router.get("/overview", response_model=Dict[str, Any])
async def get_overview(
    days: int = Query(30, ge=1, le=365, description="统计天数"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("analysis:view"))
) -> Any:
    """
    获取数据概览

    - **days**: 统计天数，默认30天
    """
    try:
        # 计算开始日期
        start_date = datetime.now() - timedelta(days=days)

        # 用户统计
        total_users = db.query(func.count(User.id)).scalar() or 0
        new_users = db.query(func.count(User.id)).filter(User.created_at >= start_date).scalar() or 0
        active_users = db.query(func.count(User.id.distinct())).join(
            BrowseHistory, User.id == BrowseHistory.user_id
        ).filter(BrowseHistory.created_at >= start_date).scalar() or 0

        # 岗位统计
        total_jobs = db.query(func.count(Job.id)).scalar() or 0
        new_jobs = db.query(func.count(Job.id)).filter(Job.created_at >= start_date).scalar() or 0

        # 收藏统计
        total_favorites = db.query(func.count(Favorite.id)).scalar() or 0
        new_favorites = db.query(func.count(Favorite.id)).filter(Favorite.created_at >= start_date).scalar() or 0

        # 浏览统计
        total_views = db.query(func.count(BrowseHistory.id)).scalar() or 0
        new_views = db.query(func.count(BrowseHistory.id)).filter(BrowseHistory.created_at >= start_date).scalar() or 0

        # 社区统计
        total_posts = db.query(func.count(CommunityPost.id)).scalar() or 0
        new_posts = db.query(func.count(CommunityPost.id)).filter(CommunityPost.created_at >= start_date).scalar() or 0
        total_comments = db.query(func.count(Comment.id)).scalar() or 0
        new_comments = db.query(func.count(Comment.id)).filter(Comment.created_at >= start_date).scalar() or 0

        # 用户增长趋势
        user_trend = []
        for i in range(days):
            date = datetime.now() - timedelta(days=days-i-1)
            date_start = datetime(date.year, date.month, date.day, 0, 0, 0)
            date_end = datetime(date.year, date.month, date.day, 23, 59, 59)

            count = db.query(func.count(User.id)).filter(
                User.created_at >= date_start,
                User.created_at <= date_end
            ).scalar() or 0

            user_trend.append({
                "date": date_start.strftime("%Y-%m-%d"),
                "count": count
            })

        # 岗位增长趋势
        job_trend = []
        for i in range(days):
            date = datetime.now() - timedelta(days=days-i-1)
            date_start = datetime(date.year, date.month, date.day, 0, 0, 0)
            date_end = datetime(date.year, date.month, date.day, 23, 59, 59)

            count = db.query(func.count(Job.id)).filter(
                Job.created_at >= date_start,
                Job.created_at <= date_end
            ).scalar() or 0

            job_trend.append({
                "date": date_start.strftime("%Y-%m-%d"),
                "count": count
            })

        return success_response(data={
            "user": {
                "total": total_users,
                "new": new_users,
                "active": active_users,
                "trend": user_trend
            },
            "job": {
                "total": total_jobs,
                "new": new_jobs,
                "trend": job_trend
            },
            "interaction": {
                "favorites": {
                    "total": total_favorites,
                    "new": new_favorites
                },
                "views": {
                    "total": total_views,
                    "new": new_views
                }
            },
            "community": {
                "posts": {
                    "total": total_posts,
                    "new": new_posts
                },
                "comments": {
                    "total": total_comments,
                    "new": new_comments
                }
            }
        })
    except Exception as e:
        logger.error(f"获取数据概览异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get("/users", response_model=Dict[str, Any])
async def get_user_analysis(
    days: int = Query(30, ge=1, le=365, description="统计天数"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("analysis:view"))
) -> Any:
    """
    获取用户分析数据

    - **days**: 统计天数，默认30天
    """
    try:
        # 计算开始日期
        start_date = datetime.now() - timedelta(days=days)

        # 用户增长趋势
        user_trend = []
        for i in range(days):
            date = datetime.now() - timedelta(days=days-i-1)
            date_start = datetime(date.year, date.month, date.day, 0, 0, 0)
            date_end = datetime(date.year, date.month, date.day, 23, 59, 59)

            count = db.query(func.count(User.id)).filter(
                User.created_at >= date_start,
                User.created_at <= date_end
            ).scalar() or 0

            user_trend.append({
                "date": date_start.strftime("%Y-%m-%d"),
                "count": count
            })

        # 用户活跃度
        active_users_by_day = []
        for i in range(7):  # 最近7天
            date = datetime.now() - timedelta(days=i)
            date_start = datetime(date.year, date.month, date.day, 0, 0, 0)
            date_end = datetime(date.year, date.month, date.day, 23, 59, 59)

            count = db.query(func.count(User.id.distinct())).join(
                BrowseHistory, User.id == BrowseHistory.user_id
            ).filter(
                BrowseHistory.created_at >= date_start,
                BrowseHistory.created_at <= date_end
            ).scalar() or 0

            active_users_by_day.append({
                "date": date_start.strftime("%Y-%m-%d"),
                "count": count
            })

        # 用户地域分布
        user_regions = db.query(
            User.region, func.count(User.id).label("count")
        ).group_by(User.region).order_by(desc("count")).limit(10).all()

        region_distribution = [
            {"region": region or "未知", "count": count}
            for region, count in user_regions
        ]

        # 用户年龄分布
        age_distribution = [
            {"range": "18岁以下", "count": db.query(func.count(User.id)).filter(User.age < 18).scalar() or 0},
            {"range": "18-24岁", "count": db.query(func.count(User.id)).filter(User.age >= 18, User.age <= 24).scalar() or 0},
            {"range": "25-34岁", "count": db.query(func.count(User.id)).filter(User.age >= 25, User.age <= 34).scalar() or 0},
            {"range": "35-44岁", "count": db.query(func.count(User.id)).filter(User.age >= 35, User.age <= 44).scalar() or 0},
            {"range": "45岁以上", "count": db.query(func.count(User.id)).filter(User.age >= 45).scalar() or 0},
            {"range": "未知", "count": db.query(func.count(User.id)).filter(User.age == None).scalar() or 0}
        ]

        # 用户性别分布
        gender_distribution = [
            {"gender": "男", "count": db.query(func.count(User.id)).filter(User.gender == "male").scalar() or 0},
            {"gender": "女", "count": db.query(func.count(User.id)).filter(User.gender == "female").scalar() or 0},
            {"gender": "未知", "count": db.query(func.count(User.id)).filter(User.gender == None).scalar() or 0}
        ]

        return success_response(data={
            "trend": user_trend,
            "active": active_users_by_day,
            "region": region_distribution,
            "age": age_distribution,
            "gender": gender_distribution
        })
    except Exception as e:
        logger.error(f"获取用户分析数据异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get("/jobs", response_model=Dict[str, Any])
async def get_job_analysis(
    days: int = Query(30, ge=1, le=365, description="统计天数"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("analysis:view"))
) -> Any:
    """
    获取岗位分析数据

    - **days**: 统计天数，默认30天
    """
    try:
        # 计算开始日期
        start_date = datetime.now() - timedelta(days=days)

        # 岗位增长趋势
        job_trend = []
        for i in range(days):
            date = datetime.now() - timedelta(days=days-i-1)
            date_start = datetime(date.year, date.month, date.day, 0, 0, 0)
            date_end = datetime(date.year, date.month, date.day, 23, 59, 59)

            count = db.query(func.count(Job.id)).filter(
                Job.created_at >= date_start,
                Job.created_at <= date_end
            ).scalar() or 0

            job_trend.append({
                "date": date_start.strftime("%Y-%m-%d"),
                "count": count
            })

        # 热门岗位
        popular_jobs = db.query(
            Job.title, func.count(BrowseHistory.id).label("view_count")
        ).join(
            BrowseHistory, Job.id == BrowseHistory.job_id
        ).filter(
            BrowseHistory.created_at >= start_date
        ).group_by(Job.title).order_by(desc("view_count")).limit(10).all()

        popular_job_list = [
            {"title": title, "count": count}
            for title, count in popular_jobs
        ]

        # 行业分布
        industry_distribution = db.query(
            Job.industry, func.count(Job.id).label("count")
        ).group_by(Job.industry).order_by(desc("count")).limit(10).all()

        industry_list = [
            {"industry": industry or "未知", "count": count}
            for industry, count in industry_distribution
        ]

        # 地区分布
        region_distribution = db.query(
            Job.location, func.count(Job.id).label("count")
        ).group_by(Job.location).order_by(desc("count")).limit(10).all()

        region_list = [
            {"region": region or "未知", "count": count}
            for region, count in region_distribution
        ]

        # 薪资分布
        salary_distribution = [
            {"range": "3k以下", "count": db.query(func.count(Job.id)).filter(Job.salary_min < 3000).scalar() or 0},
            {"range": "3k-5k", "count": db.query(func.count(Job.id)).filter(Job.salary_min >= 3000, Job.salary_max <= 5000).scalar() or 0},
            {"range": "5k-10k", "count": db.query(func.count(Job.id)).filter(Job.salary_min >= 5000, Job.salary_max <= 10000).scalar() or 0},
            {"range": "10k-15k", "count": db.query(func.count(Job.id)).filter(Job.salary_min >= 10000, Job.salary_max <= 15000).scalar() or 0},
            {"range": "15k-20k", "count": db.query(func.count(Job.id)).filter(Job.salary_min >= 15000, Job.salary_max <= 20000).scalar() or 0},
            {"range": "20k-30k", "count": db.query(func.count(Job.id)).filter(Job.salary_min >= 20000, Job.salary_max <= 30000).scalar() or 0},
            {"range": "30k以上", "count": db.query(func.count(Job.id)).filter(Job.salary_min >= 30000).scalar() or 0},
            {"range": "未知", "count": db.query(func.count(Job.id)).filter(Job.salary_min == None).scalar() or 0}
        ]

        return success_response(data={
            "trend": job_trend,
            "popular": popular_job_list,
            "industry": industry_list,
            "region": region_list,
            "salary": salary_distribution
        })
    except Exception as e:
        logger.error(f"获取岗位分析数据异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get("/behavior", response_model=Dict[str, Any])
async def get_behavior_analysis(
    days: int = Query(30, ge=1, le=365, description="统计天数"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("analysis:view"))
) -> Any:
    """
    获取用户行为分析数据

    - **days**: 统计天数，默认30天
    """
    try:
        # 计算开始日期
        start_date = datetime.now() - timedelta(days=days)

        # 浏览趋势
        view_trend = []
        for i in range(days):
            date = datetime.now() - timedelta(days=days-i-1)
            date_start = datetime(date.year, date.month, date.day, 0, 0, 0)
            date_end = datetime(date.year, date.month, date.day, 23, 59, 59)

            count = db.query(func.count(BrowseHistory.id)).filter(
                BrowseHistory.created_at >= date_start,
                BrowseHistory.created_at <= date_end
            ).scalar() or 0

            view_trend.append({
                "date": date_start.strftime("%Y-%m-%d"),
                "count": count
            })

        # 收藏趋势
        favorite_trend = []
        for i in range(days):
            date = datetime.now() - timedelta(days=days-i-1)
            date_start = datetime(date.year, date.month, date.day, 0, 0, 0)
            date_end = datetime(date.year, date.month, date.day, 23, 59, 59)

            count = db.query(func.count(Favorite.id)).filter(
                Favorite.created_at >= date_start,
                Favorite.created_at <= date_end
            ).scalar() or 0

            favorite_trend.append({
                "date": date_start.strftime("%Y-%m-%d"),
                "count": count
            })

        # 热门搜索关键词
        # 注意：这里假设有一个search_log表记录搜索日志
        # 如果没有这个表，可以根据实际情况调整或移除这部分代码
        try:
            search_keywords = db.query(
                text("keyword"), func.count(text("id")).label("count")
            ).from_statement(
                text("SELECT keyword, COUNT(id) as count FROM search_log WHERE created_at >= :start_date GROUP BY keyword ORDER BY count DESC LIMIT 10")
            ).params(start_date=start_date).all()

            keyword_list = [
                {"keyword": keyword, "count": count}
                for keyword, count in search_keywords
            ]
        except:
            # 如果没有search_log表或查询出错，返回空列表
            keyword_list = []

        # 用户活跃时段
        hour_distribution = []
        for hour in range(24):
            count = db.query(func.count(BrowseHistory.id)).filter(
                func.extract('hour', BrowseHistory.created_at) == hour,
                BrowseHistory.created_at >= start_date
            ).scalar() or 0

            hour_distribution.append({
                "hour": f"{hour:02d}:00",
                "count": count
            })

        # 用户设备分布
        # 注意：这里假设User表有device_type字段
        # 如果没有这个字段，可以根据实际情况调整或移除这部分代码
        try:
            device_distribution = db.query(
                User.device_type, func.count(User.id).label("count")
            ).group_by(User.device_type).order_by(desc("count")).all()

            device_list = [
                {"device": device_type or "未知", "count": count}
                for device_type, count in device_distribution
            ]
        except:
            # 如果没有device_type字段或查询出错，返回空列表
            device_list = []

        return success_response(data={
            "view_trend": view_trend,
            "favorite_trend": favorite_trend,
            "keywords": keyword_list,
            "hour_distribution": hour_distribution,
            "device_distribution": device_list
        })
    except Exception as e:
        logger.error(f"获取用户行为分析数据异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
