#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据分析相关的API路由
"""

import uuid
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.orm import Session
from sqlalchemy import desc, asc, func, and_, or_

from apps.backend.api.dependencies import get_db
from apps.backend.api.dependencies.admin_auth import get_current_active_admin
from apps.backend.api.schemas.analytics import (
    UserActivityData, UserSourceData, PopularKeyword, HourlyActivityData, FunnelData,
    JobTrendData, IndustryData, SalaryData, RegionData, PopularJob, CompanySizeData,
    RealTimeMetrics, RecentActivity, SystemStatus, HotSearch,
    CustomReport, CustomReportCreate, CustomReportUpdate, ReportRunRequest, ExportRequest,
    DataSource, DataSourceField, ReportPreviewConfig,
    OverviewData, KeyMetric, TrendAnalysis, ComparisonAnalysis, TimeRange
)
from apps.backend.api.models.analytics import (
    UserActivityLogModel, SearchLogModel, JobApplicationLogModel, PageViewLogModel,
    UserSessionModel, DailyStatsModel, CustomReportModel, ReportExecutionLogModel,
    RealTimeMetricsModel
)
from apps.backend.api.schemas.base import PageResponse, ResponseBase
from apps.backend.api.utils.analytics import AnalyticsService
from apps.backend.api.utils.report_generator import ReportGenerator

router = APIRouter()

# 统计概览相关路由

@router.get("/statistics")
async def get_admin_statistics(
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_active_admin)
):
    """获取管理后台统计数据"""
    try:
        # 模拟统计数据
        statistics = {
            "overview": {
                "total_users": 12580,
                "total_jobs": 3420,
                "total_applications": 8960,
                "total_companies": 1280
            },
            "today": {
                "new_users": 156,
                "new_jobs": 23,
                "new_applications": 89,
                "page_views": 15680
            },
            "trends": {
                "user_growth": 12.5,
                "job_growth": 8.9,
                "application_growth": 15.2,
                "engagement_rate": 68.5
            },
            "real_time": {
                "online_users": 1256,
                "active_sessions": 890,
                "current_searches": 45,
                "system_load": 65.2
            }
        }

        return {
            "code": 200,
            "message": "获取统计数据成功",
            "data": statistics
        }
    except Exception as e:
        return {
            "code": 500,
            "message": f"获取统计数据失败: {str(e)}",
            "data": None
        }

# 用户行为分析相关路由

@router.get("/analytics/user-activity", response_model=List[UserActivityData])
async def get_user_activity(
    time_range: Optional[str] = Query("7d"),
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_active_admin)
):
    """获取用户活动数据"""
    # 计算时间范围
    end_dt = datetime.utcnow()
    if time_range == "1d":
        start_dt = end_dt - timedelta(days=1)
    elif time_range == "7d":
        start_dt = end_dt - timedelta(days=7)
    elif time_range == "30d":
        start_dt = end_dt - timedelta(days=30)
    elif time_range == "90d":
        start_dt = end_dt - timedelta(days=90)
    else:
        start_dt = end_dt - timedelta(days=7)
    
    # 查询每日统计数据
    daily_stats = db.query(DailyStatsModel).filter(
        DailyStatsModel.date >= start_dt,
        DailyStatsModel.date <= end_dt
    ).order_by(DailyStatsModel.date).all()
    
    # 如果没有数据，生成模拟数据
    if not daily_stats:
        mock_data = []
        current_date = start_dt
        while current_date <= end_dt:
            date_str = current_date.strftime("%m-%d")
            mock_data.append({
                "date": date_str,
                "visits": 1200 + int((current_date.day % 7) * 100),
                "searches": 890 + int((current_date.day % 5) * 50),
                "applications": 156 + int((current_date.day % 3) * 20),
                "favorites": 234 + int((current_date.day % 4) * 30)
            })
            current_date += timedelta(days=1)
        return mock_data
    
    # 转换为响应格式
    result = []
    for stat in daily_stats:
        result.append({
            "date": stat.date.strftime("%m-%d"),
            "visits": stat.page_views,
            "searches": stat.searches,
            "applications": stat.job_applications,
            "favorites": stat.job_favorites
        })
    
    return result


@router.get("/analytics/user-sources", response_model=List[UserSourceData])
async def get_user_sources(
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_active_admin)
):
    """获取用户来源分布"""
    # 模拟用户来源数据
    mock_data = [
        {"name": "直接访问", "value": 4500, "color": "#8884d8"},
        {"name": "搜索引擎", "value": 3200, "color": "#82ca9d"},
        {"name": "社交媒体", "value": 1800, "color": "#ffc658"},
        {"name": "推荐链接", "value": 1200, "color": "#ff7300"},
        {"name": "其他", "value": 800, "color": "#00ff00"}
    ]
    return mock_data


@router.get("/analytics/popular-keywords", response_model=List[PopularKeyword])
async def get_popular_keywords(
    limit: int = Query(10, ge=1, le=50),
    time_range: Optional[str] = Query("7d"),
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_active_admin)
):
    """获取热门搜索关键词"""
    # 计算时间范围
    end_dt = datetime.utcnow()
    if time_range == "1d":
        start_dt = end_dt - timedelta(days=1)
    elif time_range == "7d":
        start_dt = end_dt - timedelta(days=7)
    elif time_range == "30d":
        start_dt = end_dt - timedelta(days=30)
    else:
        start_dt = end_dt - timedelta(days=7)
    
    # 查询搜索日志统计
    keyword_stats = db.query(
        SearchLogModel.keyword,
        func.count(SearchLogModel.id).label('count')
    ).filter(
        SearchLogModel.created_at >= start_dt,
        SearchLogModel.created_at <= end_dt
    ).group_by(SearchLogModel.keyword).order_by(desc('count')).limit(limit).all()
    
    # 如果没有数据，返回模拟数据
    if not keyword_stats:
        mock_data = [
            {"keyword": "计算机", "count": 2580, "trend": "up"},
            {"keyword": "教师", "count": 1890, "trend": "up"},
            {"keyword": "医生", "count": 1650, "trend": "down"},
            {"keyword": "工程师", "count": 1420, "trend": "up"},
            {"keyword": "会计", "count": 1280, "trend": "stable"},
            {"keyword": "护士", "count": 980, "trend": "up"},
            {"keyword": "设计师", "count": 780, "trend": "down"},
            {"keyword": "销售", "count": 650, "trend": "stable"}
        ]
        return mock_data[:limit]
    
    # 转换为响应格式
    result = []
    for keyword, count in keyword_stats:
        # 简单的趋势计算（实际应该对比历史数据）
        trend = "up" if count > 100 else "stable" if count > 50 else "down"
        result.append({
            "keyword": keyword,
            "count": count,
            "trend": trend
        })
    
    return result


@router.get("/analytics/hourly-activity", response_model=List[HourlyActivityData])
async def get_hourly_activity(
    date: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_active_admin)
):
    """获取用户活跃时段数据"""
    # 模拟小时活动数据
    mock_data = [
        {"hour": "00:00", "users": 120},
        {"hour": "02:00", "users": 89},
        {"hour": "04:00", "users": 67},
        {"hour": "06:00", "users": 145},
        {"hour": "08:00", "users": 234},
        {"hour": "10:00", "users": 312},
        {"hour": "12:00", "users": 456},
        {"hour": "14:00", "users": 389},
        {"hour": "16:00", "users": 298},
        {"hour": "18:00", "users": 267},
        {"hour": "20:00", "users": 198},
        {"hour": "22:00", "users": 156}
    ]
    return mock_data


@router.get("/analytics/funnel", response_model=List[FunnelData])
async def get_funnel_data(
    time_range: Optional[str] = Query("7d"),
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_active_admin)
):
    """获取转化漏斗数据"""
    # 模拟漏斗数据
    mock_data = [
        {"name": "访问用户", "value": 10000, "fill": "#8884d8"},
        {"name": "搜索用户", "value": 7500, "fill": "#83a6ed"},
        {"name": "查看详情", "value": 5200, "fill": "#8dd1e1"},
        {"name": "收藏岗位", "value": 2800, "fill": "#82ca9d"},
        {"name": "申请岗位", "value": 1200, "fill": "#a4de6c"}
    ]
    return mock_data


@router.post("/analytics/user-behavior/export")
async def export_user_behavior_report(
    request: ExportRequest,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_active_admin)
):
    """导出用户行为报表"""
    # 这里应该生成实际的报表文件
    return {"message": "报表导出任务已提交", "task_id": str(uuid.uuid4())}


# 业务报表相关路由

@router.get("/analytics/job-trends", response_model=List[JobTrendData])
async def get_job_trends(
    time_range: Optional[str] = Query("30d"),
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_active_admin)
):
    """获取岗位趋势数据"""
    # 模拟岗位趋势数据
    mock_data = [
        {"date": "01-01", "published": 156, "applications": 890, "matches": 234},
        {"date": "01-08", "published": 189, "applications": 1050, "matches": 278},
        {"date": "01-15", "published": 234, "applications": 1180, "matches": 312},
        {"date": "01-22", "published": 267, "applications": 1320, "matches": 356},
        {"date": "01-29", "published": 289, "applications": 1420, "matches": 389}
    ]
    return mock_data


@router.get("/analytics/industry-distribution", response_model=List[IndustryData])
async def get_industry_data(
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_active_admin)
):
    """获取行业分布数据"""
    # 模拟行业分布数据
    mock_data = [
        {"name": "互联网/IT", "jobs": 3200, "applications": 15600, "color": "#8884d8"},
        {"name": "教育培训", "jobs": 2800, "applications": 12400, "color": "#82ca9d"},
        {"name": "医疗健康", "jobs": 1900, "applications": 8900, "color": "#ffc658"},
        {"name": "金融保险", "jobs": 1600, "applications": 7200, "color": "#ff7300"},
        {"name": "制造业", "jobs": 1400, "applications": 6800, "color": "#00ff00"},
        {"name": "其他", "jobs": 1200, "applications": 5400, "color": "#ff69b4"}
    ]
    return mock_data


@router.get("/analytics/salary-distribution", response_model=List[SalaryData])
async def get_salary_data(
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_active_admin)
):
    """获取薪资分布数据"""
    # 模拟薪资分布数据
    mock_data = [
        {"range": "3K以下", "count": 580, "percentage": 4.8},
        {"range": "3K-5K", "count": 1890, "percentage": 15.6},
        {"range": "5K-8K", "count": 3200, "percentage": 26.4},
        {"range": "8K-12K", "count": 2800, "percentage": 23.1},
        {"range": "12K-20K", "count": 2100, "percentage": 17.3},
        {"range": "20K-30K", "count": 980, "percentage": 8.1},
        {"range": "30K以上", "count": 580, "percentage": 4.8}
    ]
    return mock_data


@router.get("/analytics/region-distribution", response_model=List[RegionData])
async def get_region_data(
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_active_admin)
):
    """获取地区分布数据"""
    # 模拟地区分布数据
    mock_data = [
        {"region": "北京", "jobs": 2800, "growth": 12.5},
        {"region": "上海", "jobs": 2400, "growth": 8.9},
        {"region": "深圳", "jobs": 2100, "growth": 15.2},
        {"region": "广州", "jobs": 1800, "growth": 6.7},
        {"region": "杭州", "jobs": 1500, "growth": 18.3},
        {"region": "成都", "jobs": 1200, "growth": 22.1},
        {"region": "武汉", "jobs": 980, "growth": 14.6},
        {"region": "西安", "jobs": 780, "growth": 19.8}
    ]
    return mock_data


@router.get("/analytics/popular-jobs", response_model=List[PopularJob])
async def get_popular_jobs(
    limit: int = Query(10, ge=1, le=50),
    time_range: Optional[str] = Query("30d"),
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_active_admin)
):
    """获取热门岗位排行"""
    # 模拟热门岗位数据
    mock_data = [
        {"position": "软件工程师", "count": 1580, "avg_salary": 15000, "trend": "up"},
        {"position": "产品经理", "count": 890, "avg_salary": 18000, "trend": "up"},
        {"position": "数据分析师", "count": 650, "avg_salary": 12000, "trend": "up"},
        {"position": "UI设计师", "count": 580, "avg_salary": 10000, "trend": "down"},
        {"position": "运营专员", "count": 520, "avg_salary": 8000, "trend": "stable"},
        {"position": "销售经理", "count": 480, "avg_salary": 12000, "trend": "up"},
        {"position": "会计", "count": 420, "avg_salary": 6000, "trend": "stable"},
        {"position": "客服专员", "count": 380, "avg_salary": 5000, "trend": "down"}
    ]
    return mock_data[:limit]


@router.get("/analytics/company-size-distribution", response_model=List[CompanySizeData])
async def get_company_size_data(
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_active_admin)
):
    """获取企业规模分布"""
    # 模拟企业规模数据
    mock_data = [
        {"size": "1-20人", "count": 3200, "percentage": 35.2},
        {"size": "21-100人", "count": 2800, "percentage": 30.8},
        {"size": "101-500人", "count": 1900, "percentage": 20.9},
        {"size": "501-1000人", "count": 780, "percentage": 8.6},
        {"size": "1000人以上", "count": 420, "percentage": 4.6}
    ]
    return mock_data


@router.post("/analytics/business-reports/export")
async def export_business_report(
    request: ExportRequest,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_active_admin)
):
    """导出业务报表"""
    # 这里应该生成实际的报表文件
    return {"message": "业务报表导出任务已提交", "task_id": str(uuid.uuid4())}


# 实时监控相关路由

@router.get("/analytics/real-time/metrics", response_model=RealTimeMetrics)
async def get_realtime_metrics(
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_active_admin)
):
    """获取实时指标"""
    # 查询最新的实时指标
    latest_metrics = db.query(RealTimeMetricsModel).order_by(desc(RealTimeMetricsModel.timestamp)).first()

    if not latest_metrics:
        # 返回模拟数据
        return {
            "online_users": 1256,
            "page_views": 15680,
            "searches": 890,
            "applications": 156,
            "system_load": 65.2,
            "response_time": 120,
            "error_rate": 0.8,
            "success_rate": 99.2
        }

    return {
        "online_users": latest_metrics.online_users,
        "page_views": latest_metrics.page_views_per_minute * 60,  # 转换为小时数据
        "searches": latest_metrics.searches_per_minute * 60,
        "applications": latest_metrics.applications_per_minute * 60,
        "system_load": latest_metrics.system_load,
        "response_time": latest_metrics.avg_response_time,
        "error_rate": latest_metrics.error_rate,
        "success_rate": 100 - latest_metrics.error_rate
    }


@router.get("/analytics/real-time/data")
async def get_realtime_data(
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_active_admin)
):
    """获取实时数据流"""
    # 查询最近的实时数据
    metrics = db.query(RealTimeMetricsModel).order_by(desc(RealTimeMetricsModel.timestamp)).limit(limit).all()

    if not metrics:
        # 生成模拟数据
        mock_data = []
        current_time = datetime.utcnow()
        for i in range(limit):
            time_point = current_time - timedelta(minutes=i*5)
            mock_data.append({
                "time": time_point.strftime("%H:%M"),
                "users": 1200 + int((time_point.hour % 12) * 20),
                "pageViews": 800 + int((time_point.minute % 10) * 50),
                "searches": 50 + int((time_point.hour % 6) * 10),
                "applications": 10 + int((time_point.minute % 5) * 3)
            })
        return list(reversed(mock_data))

    # 转换为响应格式
    result = []
    for metric in reversed(metrics):
        result.append({
            "time": metric.timestamp.strftime("%H:%M"),
            "users": metric.online_users,
            "pageViews": metric.page_views_per_minute,
            "searches": metric.searches_per_minute,
            "applications": metric.applications_per_minute
        })

    return result


@router.get("/analytics/real-time/activities", response_model=List[RecentActivity])
async def get_recent_activities(
    limit: int = Query(10, ge=1, le=50),
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_active_admin)
):
    """获取最近活动"""
    # 模拟最近活动数据
    mock_data = [
        {"id": 1, "user": "张三", "action": "申请了软件工程师岗位", "time": "刚刚", "type": "application"},
        {"id": 2, "user": "李四", "action": "收藏了产品经理岗位", "time": "1分钟前", "type": "favorite"},
        {"id": 3, "user": "王五", "action": "搜索了计算机相关岗位", "time": "2分钟前", "type": "search"},
        {"id": 4, "user": "赵六", "action": "查看了数据分析师岗位详情", "time": "3分钟前", "type": "view"},
        {"id": 5, "user": "钱七", "action": "注册了新账户", "time": "5分钟前", "type": "register"},
        {"id": 6, "user": "孙八", "action": "更新了个人简历", "time": "7分钟前", "type": "update"},
        {"id": 7, "user": "周九", "action": "申请了UI设计师岗位", "time": "8分钟前", "type": "application"},
        {"id": 8, "user": "吴十", "action": "分享了岗位信息", "time": "10分钟前", "type": "share"}
    ]
    return mock_data[:limit]


@router.get("/analytics/real-time/system-status", response_model=List[SystemStatus])
async def get_system_status(
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_active_admin)
):
    """获取系统状态"""
    # 模拟系统状态数据
    mock_data = [
        {"service": "Web服务器", "status": "healthy", "response_time": 45, "uptime": 99.9},
        {"service": "API服务", "status": "healthy", "response_time": 120, "uptime": 99.8},
        {"service": "数据库", "status": "healthy", "response_time": 25, "uptime": 99.9},
        {"service": "Redis缓存", "status": "warning", "response_time": 15, "uptime": 98.5},
        {"service": "AI服务", "status": "healthy", "response_time": 180, "uptime": 99.2},
        {"service": "搜索引擎", "status": "healthy", "response_time": 80, "uptime": 99.7}
    ]
    return mock_data


@router.get("/analytics/real-time/hot-searches", response_model=List[HotSearch])
async def get_hot_searches(
    limit: int = Query(5, ge=1, le=20),
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_active_admin)
):
    """获取热门搜索"""
    # 模拟热门搜索数据
    mock_data = [
        {"keyword": "计算机", "count": 156, "trend": "up"},
        {"keyword": "教师", "count": 89, "trend": "up"},
        {"keyword": "医生", "count": 67, "trend": "down"},
        {"keyword": "工程师", "count": 45, "trend": "up"},
        {"keyword": "会计", "count": 34, "trend": "stable"}
    ]
    return mock_data[:limit]


# 自定义报表相关路由

@router.get("/analytics/custom-reports", response_model=PageResponse)
async def get_custom_reports(
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100),
    type: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_active_admin)
):
    """获取自定义报表列表"""
    query = db.query(CustomReportModel)

    if type:
        query = query.filter(CustomReportModel.type == type)
    if status:
        query = query.filter(CustomReportModel.status == status)

    total = query.count()
    reports = query.order_by(desc(CustomReportModel.created_at)).offset((page - 1) * page_size).limit(page_size).all()

    # 转换为响应格式
    report_list = []
    for report in reports:
        report_dict = {
            "id": report.id,
            "name": report.name,
            "description": report.description,
            "type": report.type,
            "chart_type": report.chart_type,
            "data_source": report.data_source,
            "filters": report.filters,
            "columns": report.columns,
            "schedule": {
                "enabled": report.schedule_enabled,
                "frequency": report.schedule_frequency,
                "time": report.schedule_time,
                "recipients": report.schedule_recipients
            } if report.schedule_enabled else None,
            "status": report.status,
            "created_at": report.created_at,
            "updated_at": report.updated_at,
            "last_run": report.last_run
        }
        report_list.append(report_dict)

    return PageResponse(
        items=report_list,
        total=total,
        page=page,
        page_size=page_size,
        pages=(total + page_size - 1) // page_size
    )


@router.get("/analytics/custom-reports/{report_id}", response_model=CustomReport)
async def get_custom_report(
    report_id: str,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_active_admin)
):
    """获取自定义报表详情"""
    report = db.query(CustomReportModel).filter(CustomReportModel.id == report_id).first()
    if not report:
        raise HTTPException(status_code=404, detail="报表不存在")

    return {
        "id": report.id,
        "name": report.name,
        "description": report.description,
        "type": report.type,
        "chart_type": report.chart_type,
        "data_source": report.data_source,
        "filters": report.filters,
        "columns": report.columns,
        "schedule": {
            "enabled": report.schedule_enabled,
            "frequency": report.schedule_frequency,
            "time": report.schedule_time,
            "recipients": report.schedule_recipients
        } if report.schedule_enabled else None,
        "status": report.status,
        "created_at": report.created_at,
        "updated_at": report.updated_at,
        "last_run": report.last_run
    }


@router.post("/analytics/custom-reports", response_model=CustomReport)
async def create_custom_report(
    report_data: CustomReportCreate,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_active_admin)
):
    """创建自定义报表"""
    # 检查报表名称是否已存在
    existing_report = db.query(CustomReportModel).filter(CustomReportModel.name == report_data.name).first()
    if existing_report:
        raise HTTPException(status_code=400, detail="报表名称已存在")

    # 创建新报表
    new_report = CustomReportModel(
        id=str(uuid.uuid4()),
        name=report_data.name,
        description=report_data.description,
        type=report_data.type,
        chart_type=report_data.chart_type,
        data_source=report_data.data_source,
        filters=report_data.filters.dict() if report_data.filters else {},
        columns=report_data.columns,
        schedule_enabled=report_data.schedule.enabled if report_data.schedule else False,
        schedule_frequency=report_data.schedule.frequency if report_data.schedule else None,
        schedule_time=report_data.schedule.time if report_data.schedule else None,
        schedule_recipients=report_data.schedule.recipients if report_data.schedule else [],
        created_by=current_admin.id
    )

    db.add(new_report)
    db.commit()
    db.refresh(new_report)

    return await get_custom_report(new_report.id, db, current_admin)


@router.put("/analytics/custom-reports/{report_id}", response_model=CustomReport)
async def update_custom_report(
    report_id: str,
    report_data: CustomReportUpdate,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_active_admin)
):
    """更新自定义报表"""
    report = db.query(CustomReportModel).filter(CustomReportModel.id == report_id).first()
    if not report:
        raise HTTPException(status_code=404, detail="报表不存在")

    # 更新报表信息
    update_data = report_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        if field == "filters" and value is not None:
            setattr(report, field, value.dict())
        elif field == "schedule" and value is not None:
            report.schedule_enabled = value.enabled
            report.schedule_frequency = value.frequency
            report.schedule_time = value.time
            report.schedule_recipients = value.recipients
        else:
            setattr(report, field, value)

    report.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(report)

    return await get_custom_report(report_id, db, current_admin)


@router.delete("/analytics/custom-reports/{report_id}", response_model=ResponseBase)
async def delete_custom_report(
    report_id: str,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_active_admin)
):
    """删除自定义报表"""
    report = db.query(CustomReportModel).filter(CustomReportModel.id == report_id).first()
    if not report:
        raise HTTPException(status_code=404, detail="报表不存在")

    db.delete(report)
    db.commit()

    return ResponseBase(message="报表删除成功")


@router.post("/analytics/custom-reports/{report_id}/run")
async def run_custom_report(
    report_id: str,
    request: ReportRunRequest,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_active_admin)
):
    """运行自定义报表"""
    report = db.query(CustomReportModel).filter(CustomReportModel.id == report_id).first()
    if not report:
        raise HTTPException(status_code=404, detail="报表不存在")

    # 记录执行日志
    execution_log = ReportExecutionLogModel(
        id=str(uuid.uuid4()),
        report_id=report_id,
        execution_type="manual",
        status="running",
        start_time=datetime.utcnow(),
        output_format=request.format,
        executed_by=current_admin.id
    )
    db.add(execution_log)

    # 更新报表运行信息
    report.last_run = datetime.utcnow()
    report.run_count += 1
    db.commit()

    # 这里应该实际执行报表生成逻辑
    # 为了演示，我们返回模拟结果
    mock_result = {
        "report_id": report_id,
        "execution_id": execution_log.id,
        "status": "success",
        "data": [
            {"column1": "value1", "column2": "value2"},
            {"column1": "value3", "column2": "value4"}
        ],
        "total_rows": 2,
        "execution_time": "1.2s"
    }

    # 更新执行状态
    execution_log.status = "success"
    execution_log.end_time = datetime.utcnow()
    execution_log.duration = 1
    execution_log.result_count = 2
    db.commit()

    return mock_result


@router.get("/analytics/data-sources", response_model=List[DataSource])
async def get_data_sources(
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_active_admin)
):
    """获取可用数据源"""
    data_sources = [
        {"value": "users", "label": "用户数据"},
        {"value": "jobs", "label": "岗位数据"},
        {"value": "job_applications", "label": "申请数据"},
        {"value": "companies", "label": "企业数据"},
        {"value": "ai_analytics", "label": "AI分析数据"}
    ]
    return data_sources


@router.get("/analytics/data-sources/{data_source}/fields", response_model=List[DataSourceField])
async def get_data_source_fields(
    data_source: str,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_active_admin)
):
    """获取数据源字段"""
    fields_map = {
        "users": [
            {"name": "id", "type": "string", "description": "用户ID"},
            {"name": "username", "type": "string", "description": "用户名"},
            {"name": "email", "type": "string", "description": "邮箱"},
            {"name": "created_at", "type": "datetime", "description": "创建时间"},
            {"name": "status", "type": "string", "description": "状态"},
            {"name": "major", "type": "string", "description": "专业"},
            {"name": "location", "type": "string", "description": "地点"}
        ],
        "jobs": [
            {"name": "id", "type": "string", "description": "岗位ID"},
            {"name": "title", "type": "string", "description": "岗位标题"},
            {"name": "company", "type": "string", "description": "公司"},
            {"name": "industry", "type": "string", "description": "行业"},
            {"name": "salary", "type": "number", "description": "薪资"},
            {"name": "location", "type": "string", "description": "地点"},
            {"name": "created_at", "type": "datetime", "description": "创建时间"},
            {"name": "status", "type": "string", "description": "状态"}
        ]
    }

    return fields_map.get(data_source, [])


@router.post("/analytics/custom-reports/preview")
async def preview_report_data(
    config: ReportPreviewConfig,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_active_admin)
):
    """预览报表数据"""
    # 这里应该根据配置查询实际数据
    # 为了演示，返回模拟数据
    mock_data = [
        {"id": "1", "name": "示例数据1", "value": 100, "date": "2024-01-27"},
        {"id": "2", "name": "示例数据2", "value": 200, "date": "2024-01-26"},
        {"id": "3", "name": "示例数据3", "value": 150, "date": "2024-01-25"}
    ]

    return {
        "data": mock_data[:config.limit],
        "total": len(mock_data),
        "columns": config.columns or ["id", "name", "value", "date"]
    }


# 通用分析API

@router.get("/analytics/overview", response_model=OverviewData)
async def get_analytics_overview(
    time_range: Optional[str] = Query("30d"),
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_active_admin)
):
    """获取分析概览"""
    # 模拟概览数据
    return {
        "total_users": 12580,
        "active_users": 1128,
        "total_jobs": 8956,
        "new_jobs": 156,
        "total_applications": 45680,
        "success_matches": 8956
    }


@router.get("/analytics/key-metrics", response_model=List[KeyMetric])
async def get_key_metrics(
    time_range: Optional[str] = Query("30d"),
    metrics: Optional[List[str]] = Query(None),
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_active_admin)
):
    """获取关键指标"""
    # 模拟关键指标数据
    mock_metrics = [
        {"name": "用户增长率", "value": 15.2, "unit": "%", "trend": "up", "change": 2.3},
        {"name": "岗位申请率", "value": 68.5, "unit": "%", "trend": "up", "change": 5.1},
        {"name": "匹配成功率", "value": 19.6, "unit": "%", "trend": "down", "change": -1.2},
        {"name": "平均响应时间", "value": 1.2, "unit": "s", "trend": "stable", "change": 0.1}
    ]
    return mock_metrics
