#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
管理员认证API路由
"""

from datetime import datetime, timedelta
from typing import Any, Dict

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2<PERSON><PERSON><PERSON>RequestForm
from jose import JWTError, jwt
from sqlalchemy.orm import Session

from apps.backend.api.config.database import get_db
from apps.backend.api.config.settings import settings
from apps.backend.api.dependencies.admin_auth import get_current_active_admin
from apps.backend.api.models.admin import Admin, AdminRole, OperationLog
from apps.backend.api.schemas.admin import AdminLogin
from apps.backend.api.schemas.token import RefreshToken
from apps.backend.api.utils.security import verify_password, create_access_token, create_refresh_token
from apps.backend.api.utils.response import success_response, error_response
from apps.backend.utils.logger import setup_logger

# 创建路由
router = APIRouter(tags=["admin_auth"])

# 设置日志
logger = setup_logger("api_admin_auth")


@router.post("/login", response_model=Dict[str, Any])
async def admin_login(
    form_data: AdminLogin,
    db: Session = Depends(get_db)
) -> Any:
    """
    管理员登录

    - **username**: 用户名
    - **password**: 密码
    """
    try:
        # 查询管理员
        admin = db.query(Admin).filter(Admin.username == form_data.username).first()
        if not admin:
            logger.warning(f"管理员登录失败: 用户名不存在 - {form_data.username}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误"
            )

        # 验证密码
        password_hash = admin.password_hash
        if isinstance(password_hash, str) and not verify_password(form_data.password, password_hash):
            logger.warning(f"管理员登录失败: 密码错误 - {form_data.username}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误"
            )

        # 检查管理员状态
        status_value = admin.status
        if isinstance(status_value, bool) and not status_value:
            logger.warning(f"管理员登录失败: 账号已禁用 - {form_data.username}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="账号已禁用"
            )

        # 创建访问令牌
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": str(admin.id)},
            expires_delta=access_token_expires
        )

        # 创建刷新令牌
        refresh_token = create_refresh_token(data={"sub": str(admin.id)})

        # 更新最后登录时间
        setattr(admin, "last_login", datetime.now())

        # 记录登录日志 (暂时注释掉用于调试)
        # log = OperationLog(
        #     admin_id=admin.id,
        #     module="认证",
        #     operation="登录",
        #     content=f"管理员 {admin.username} 登录系统",
        #     ip="127.0.0.1",  # 实际应用中应获取真实IP
        #     user_agent="Unknown"  # 实际应用中应获取真实User-Agent
        # )
        # db.add(log)
        db.commit()

        # 查询角色
        role = db.query(AdminRole).filter(AdminRole.id == admin.admin_role_id).first()

        logger.info(f"管理员登录成功: {admin.username}")

        return success_response(data={
            "token": access_token,
            "token_type": "bearer",
            "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            "refresh_token": refresh_token,
            "admin_id": admin.id,
            "username": admin.username,
            "role": role.name if role else None
        })
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"管理员登录异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="服务器内部错误"
        )


@router.post("/refresh-token", response_model=Dict[str, Any])
async def refresh_access_token(
    refresh_token_data: RefreshToken,
    db: Session = Depends(get_db)
) -> Any:
    """
    刷新访问令牌

    - **refresh_token**: 刷新令牌
    """
    try:
        # 验证刷新令牌
        try:
            payload = jwt.decode(
                refresh_token_data.refresh_token,
                settings.SECRET_KEY,
                algorithms=[settings.ALGORITHM]
            )
            admin_id = payload.get("sub")
            if admin_id is None:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="无效的刷新令牌"
                )
        except JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的刷新令牌"
            )

        # 查询管理员
        admin = db.query(Admin).filter(Admin.id == admin_id).first()
        if not admin:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="管理员不存在"
            )

        # 检查管理员状态
        status_value = admin.status
        if isinstance(status_value, bool) and not status_value:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="管理员已禁用"
            )

        # 创建新的访问令牌
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": str(admin.id)},
            expires_delta=access_token_expires
        )

        # 创建新的刷新令牌
        new_refresh_token = create_refresh_token(data={"sub": str(admin.id)})

        logger.info(f"管理员刷新令牌成功: {admin.username}")

        return success_response(data={
            "token": access_token,
            "token_type": "bearer",
            "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            "refresh_token": new_refresh_token
        })
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"刷新令牌异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="服务器内部错误"
        )


@router.get("/current", response_model=Dict[str, Any])
async def get_current_admin(
    current_admin: Admin = Depends(get_current_active_admin),
    db: Session = Depends(get_db)
) -> Any:
    """
    获取当前管理员信息
    """
    try:
        # 查询角色
        role = db.query(AdminRole).filter(AdminRole.id == current_admin.admin_role_id).first()

        # 构建管理员信息
        admin_info = {
            "id": current_admin.id,
            "username": current_admin.username,
            "real_name": current_admin.real_name,
            "email": current_admin.email,
            "phone": current_admin.phone,
            "role": {
                "id": role.id,
                "name": role.name,
                "description": role.description
            } if role else None,
            "last_login": current_admin.last_login,
            "status": current_admin.status,
            "created_at": current_admin.created_at,
            "updated_at": current_admin.updated_at
        }

        return success_response(data=admin_info)
    except Exception as e:
        logger.error(f"获取当前管理员信息异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.post("/refresh", response_model=Dict[str, Any])
async def refresh_token_alias(
    refresh_token_data: RefreshToken,
    db: Session = Depends(get_db)
) -> Any:
    """
    刷新访问令牌 (别名路由)
    """
    return await refresh_access_token(refresh_token_data, db)


@router.post("/logout", response_model=Dict[str, Any])
async def admin_logout(
    current_admin: Admin = Depends(get_current_active_admin),
    db: Session = Depends(get_db)
) -> Any:
    """
    管理员登出
    """
    try:
        # 记录登出日志
        log = OperationLog(
            admin_id=current_admin.id,
            module="认证",
            operation="登出",
            content=f"管理员 {current_admin.username} 登出系统",
            ip="127.0.0.1",  # 实际应用中应获取真实IP
            user_agent="Unknown"  # 实际应用中应获取真实User-Agent
        )
        db.add(log)
        db.commit()

        logger.info(f"管理员登出成功: {current_admin.username}")

        return success_response(message="登出成功")
    except Exception as e:
        logger.error(f"管理员登出异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get("/me", response_model=Dict[str, Any])
async def get_current_admin_info(
    current_admin: Admin = Depends(get_current_active_admin),
    db: Session = Depends(get_db)
) -> Any:
    """
    获取当前管理员信息 (别名路由)
    """
    return await get_current_admin(current_admin, db)
