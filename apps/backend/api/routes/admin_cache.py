#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
管理员缓存管理API路由
"""

from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, Query

from apps.backend.api.dependencies.auth import get_current_active_admin
from apps.backend.api.models.admin import Admin
from apps.backend.api.services.cache import cache_service
from apps.backend.api.config.redis_cache import get_cache_health
from apps.backend.api.utils.response import success_response, error_response
from apps.backend.utils.logger import setup_logger

# 创建路由器
router = APIRouter(
    prefix="/cache",
    tags=["admin-cache"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        500: {"description": "Internal server error"},
    },
)

# 设置日志
logger = setup_logger("admin_cache")


@router.get("/stats", response_model=Dict[str, Any])
async def get_cache_stats(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取缓存统计信息

    获取Redis缓存的统计信息，包括命中率、内存使用等
    """
    try:
        stats = cache_service.get_cache_stats()
        return success_response(data=stats, message="获取缓存统计信息成功")
    except Exception as e:
        logger.error(f"Error getting cache stats: {e}")
        return error_response(message=str(e), code=500)


@router.delete("/clear", response_model=Dict[str, Any])
async def clear_cache(
    cache_type: Optional[str] = Query(None, description="缓存类型，不指定则清除所有缓存"),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    清除缓存

    清除指定类型的缓存或所有缓存
    """
    try:
        if cache_type:
            # 清除指定类型的缓存
            deleted_count = cache_service.clear_pattern(cache_type)
            message = f"已清除 {cache_type} 类型的 {deleted_count} 个缓存项"
        else:
            # 清除所有缓存
            total_deleted = 0
            for cache_type in cache_service.CACHE_PREFIXES.keys():
                deleted_count = cache_service.clear_pattern(cache_type)
                total_deleted += deleted_count
            message = f"已清除所有缓存，共 {total_deleted} 个缓存项"

        logger.info(f"Cache cleared by admin {current_admin.username}: {message}")
        return success_response(message=message)
    except Exception as e:
        logger.error(f"Error clearing cache: {e}")
        return error_response(message=str(e), code=500)


@router.post("/warm-up", response_model=Dict[str, Any])
async def warm_up_cache(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    缓存预热

    预加载热点数据到缓存中
    """
    try:
        result = cache_service.warm_up_cache()
        logger.info(f"Cache warm-up initiated by admin {current_admin.username}")
        return success_response(data=result, message="缓存预热完成")
    except Exception as e:
        logger.error(f"Error warming up cache: {e}")
        return error_response(message=str(e), code=500)


@router.get("/types", response_model=Dict[str, Any])
async def get_cache_types(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取缓存类型列表

    获取所有可用的缓存类型及其配置
    """
    try:
        cache_types = []
        for cache_type, prefix in cache_service.CACHE_PREFIXES.items():
            ttl = cache_service.CACHE_TTL.get(cache_type, 3600)
            cache_types.append({
                "type": cache_type,
                "prefix": prefix,
                "ttl": ttl,
                "ttl_human": f"{ttl // 60}分钟" if ttl >= 60 else f"{ttl}秒"
            })

        return success_response(data=cache_types, message="获取缓存类型列表成功")
    except Exception as e:
        logger.error(f"Error getting cache types: {e}")
        return error_response(message=str(e), code=500)


@router.get("/keys", response_model=Dict[str, Any])
async def get_cache_keys(
    cache_type: str = Query(..., description="缓存类型"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取缓存键列表

    获取指定类型的缓存键列表
    """
    try:
        prefix = cache_service.CACHE_PREFIXES.get(cache_type)
        if not prefix:
            return error_response(message=f"未知的缓存类型: {cache_type}", code=400)

        # 获取所有键
        pattern = f"{prefix}:*"
        all_keys = cache_service.redis.keys(pattern)

        # 分页
        total = len(all_keys)
        start = (page - 1) * page_size
        end = start + page_size
        keys = all_keys[start:end]

        # 获取键的详细信息
        key_details = []
        for key in keys:
            try:
                ttl = cache_service.redis.ttl(key)
                size = len(cache_service.redis.get(key) or "")
                key_details.append({
                    "key": key.decode() if isinstance(key, bytes) else key,
                    "ttl": ttl,
                    "size": size,
                    "size_human": f"{size}B" if size < 1024 else f"{size//1024}KB"
                })
            except Exception as e:
                logger.warning(f"Error getting details for key {key}: {e}")
                key_details.append({
                    "key": key.decode() if isinstance(key, bytes) else key,
                    "ttl": -1,
                    "size": 0,
                    "size_human": "0B"
                })

        result = {
            "total": total,
            "page": page,
            "page_size": page_size,
            "pages": (total + page_size - 1) // page_size,
            "has_next": page * page_size < total,
            "has_prev": page > 1,
            "items": key_details
        }

        return success_response(data=result, message="获取缓存键列表成功")
    except Exception as e:
        logger.error(f"Error getting cache keys: {e}")
        return error_response(message=str(e), code=500)


@router.delete("/keys/{key_name}", response_model=Dict[str, Any])
async def delete_cache_key(
    key_name: str,
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    删除指定缓存键

    删除指定的缓存键
    """
    try:
        result = cache_service.redis.delete(key_name)
        if result:
            message = f"缓存键 {key_name} 删除成功"
            logger.info(f"Cache key deleted by admin {current_admin.username}: {key_name}")
        else:
            message = f"缓存键 {key_name} 不存在"

        return success_response(message=message)
    except Exception as e:
        logger.error(f"Error deleting cache key {key_name}: {e}")
        return error_response(message=str(e), code=500)


@router.get("/health", response_model=Dict[str, Any])
async def check_cache_health(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    检查缓存健康状态

    检查Redis连接和基本功能
    """
    try:
        # 测试Redis连接
        cache_service.redis.ping()

        # 测试基本操作
        test_key = "health_check_test"
        test_value = "test_value"

        # 设置测试键
        cache_service.redis.setex(test_key, 10, test_value)

        # 读取测试键
        retrieved_value = cache_service.redis.get(test_key)

        # 删除测试键
        cache_service.redis.delete(test_key)

        if retrieved_value and retrieved_value == test_value:
            health_status = "healthy"
            message = "缓存系统运行正常"
        else:
            health_status = "unhealthy"
            message = "缓存系统读写测试失败"

        result = {
            "status": health_status,
            "message": message,
            "redis_connected": True,
            "read_write_test": retrieved_value is not None
        }

        return success_response(data=result, message="缓存健康检查完成")
    except Exception as e:
        logger.error(f"Error checking cache health: {e}")
        result = {
            "status": "unhealthy",
            "message": str(e),
            "redis_connected": False,
            "read_write_test": False
        }
        return success_response(data=result, message="缓存健康检查完成")  # 即使有错误也返回200，因为这是健康检查的结果
