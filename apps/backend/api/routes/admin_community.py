#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
社区管理API路由
包括帖子审核、置顶、删除等功能
"""

from typing import Any, Dict, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.orm import Session
from sqlalchemy import desc, asc
from datetime import datetime

from apps.backend.api.config.database import get_db
from apps.backend.api.dependencies.auth import get_current_active_admin, check_admin_permission
from apps.backend.api.models.admin import Admin, OperationLog
from apps.backend.api.models.community import CommunityPost, Comment
from apps.backend.api.models.user import User
from apps.backend.api.schemas.community import PostQuery
from apps.backend.api.utils.response import success_response, error_response
from apps.backend.utils.logger import setup_logger

# 创建路由
router = APIRouter(tags=["admin_community"])

# 设置日志
logger = setup_logger("api_admin_community")


@router.get("/posts", response_model=Dict[str, Any])
async def get_posts(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    keywords: Optional[str] = Query(None, description="关键词"),
    category: Optional[str] = Query(None, description="分类"),
    is_pinned: Optional[bool] = Query(None, description="是否置顶"),
    is_essence: Optional[bool] = Query(None, description="是否精华"),
    sort: Optional[str] = Query("created_at", description="排序字段"),
    order: Optional[str] = Query("desc", description="排序方向"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("content:community:view"))
) -> Any:
    """
    获取帖子列表

    - **page**: 页码
    - **page_size**: 每页数量
    - **keywords**: 关键词（可选）
    - **category**: 分类（可选）
    - **is_pinned**: 是否置顶（可选）
    - **is_essence**: 是否精华（可选）
    - **sort**: 排序字段（可选）
    - **order**: 排序方向（可选）
    """
    try:
        # 构建查询
        query = db.query(CommunityPost).join(User, CommunityPost.author_id == User.id)

        # 应用过滤条件
        if keywords:
            query = query.filter(
                CommunityPost.title.like(f"%{keywords}%") |
                CommunityPost.content.like(f"%{keywords}%")
            )
        
        if category:
            query = query.filter(CommunityPost.category == category)
        
        if is_pinned is not None:
            query = query.filter(CommunityPost.is_pinned == is_pinned)
        
        if is_essence is not None:
            query = query.filter(CommunityPost.is_essence == is_essence)

        # 计算总数
        total = query.count()

        # 应用排序
        if sort and hasattr(CommunityPost, sort):
            sort_column = getattr(CommunityPost, sort)
            if order and order.lower() == "desc":
                query = query.order_by(desc(sort_column))
            else:
                query = query.order_by(asc(sort_column))
        else:
            # 默认按创建时间降序
            query = query.order_by(desc(CommunityPost.created_at))

        # 分页
        query = query.offset((page - 1) * page_size).limit(page_size)

        # 执行查询
        posts = query.all()

        # 构建结果
        post_list = []
        for post in posts:
            author = db.query(User).filter(User.id == post.author_id).first()
            author_name = author.username if author else "未知用户"
            
            post_list.append({
                "id": post.id,
                "post_id": post.post_id,
                "title": post.title,
                "content": post.content[:200] + "..." if len(post.content) > 200 else post.content,
                "category": post.category,
                "tags": post.tags,
                "author_id": post.author_id,
                "author_name": author_name,
                "view_count": post.view_count,
                "like_count": post.like_count,
                "comment_count": post.comment_count,
                "is_pinned": post.is_pinned,
                "is_essence": post.is_essence,
                "created_at": post.created_at,
                "updated_at": post.updated_at
            })

        return success_response(data={
            "items": post_list,
            "total": total,
            "page": page,
            "page_size": page_size,
            "pages": (total + page_size - 1) // page_size,
            "has_next": page * page_size < total,
            "has_prev": page > 1
        })
    except Exception as e:
        logger.error(f"获取帖子列表异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get("/posts/{post_id}", response_model=Dict[str, Any])
async def get_post(
    post_id: str = Path(..., description="帖子ID"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("content:community:view"))
) -> Any:
    """
    获取帖子详情

    - **post_id**: 帖子ID
    """
    try:
        # 查询帖子
        post = db.query(CommunityPost).filter(CommunityPost.post_id == post_id).first()
        if not post:
            return error_response(
                message="帖子不存在",
                code=status.HTTP_404_NOT_FOUND
            )

        # 获取作者信息
        author = db.query(User).filter(User.id == post.author_id).first()
        author_name = author.username if author else "未知用户"

        # 获取评论列表
        comments = db.query(Comment).filter(Comment.post_id == post.post_id).all()
        comment_list = []
        for comment in comments:
            comment_author = db.query(User).filter(User.id == comment.author_id).first()
            comment_author_name = comment_author.username if comment_author else "未知用户"
            
            comment_list.append({
                "id": comment.id,
                "comment_id": comment.comment_id,
                "content": comment.content,
                "author_id": comment.author_id,
                "author_name": comment_author_name,
                "parent_id": comment.parent_id,
                "like_count": comment.like_count,
                "created_at": comment.created_at,
                "updated_at": comment.updated_at
            })

        return success_response(data={
            "post": {
                "id": post.id,
                "post_id": post.post_id,
                "title": post.title,
                "content": post.content,
                "category": post.category,
                "tags": post.tags,
                "author_id": post.author_id,
                "author_name": author_name,
                "view_count": post.view_count,
                "like_count": post.like_count,
                "comment_count": post.comment_count,
                "is_pinned": post.is_pinned,
                "is_essence": post.is_essence,
                "created_at": post.created_at,
                "updated_at": post.updated_at
            },
            "comments": comment_list
        })
    except Exception as e:
        logger.error(f"获取帖子详情异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.put("/posts/{post_id}/pin", response_model=Dict[str, Any])
async def pin_post(
    post_id: str = Path(..., description="帖子ID"),
    is_pinned: bool = Query(..., description="是否置顶"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("content:community:edit"))
) -> Any:
    """
    置顶/取消置顶帖子

    - **post_id**: 帖子ID
    - **is_pinned**: 是否置顶
    """
    try:
        # 查询帖子
        post = db.query(CommunityPost).filter(CommunityPost.post_id == post_id).first()
        if not post:
            return error_response(
                message="帖子不存在",
                code=status.HTTP_404_NOT_FOUND
            )

        # 更新置顶状态
        post.is_pinned = is_pinned
        db.commit()
        db.refresh(post)

        # 记录操作日志
        log = OperationLog(
            admin_id=current_admin.id,
            module="社区管理",
            operation="置顶帖子" if is_pinned else "取消置顶帖子",
            content=f"{'置顶' if is_pinned else '取消置顶'}帖子 {post.title}",
            ip="127.0.0.1",  # 实际应用中应获取真实IP
            user_agent="Unknown"  # 实际应用中应获取真实User-Agent
        )
        db.add(log)
        db.commit()

        return success_response(
            message=f"帖子已{'置顶' if is_pinned else '取消置顶'}",
            data={
                "id": post.id,
                "post_id": post.post_id,
                "is_pinned": post.is_pinned
            }
        )
    except Exception as e:
        db.rollback()
        logger.error(f"置顶/取消置顶帖子异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.put("/posts/{post_id}/essence", response_model=Dict[str, Any])
async def set_essence(
    post_id: str = Path(..., description="帖子ID"),
    is_essence: bool = Query(..., description="是否精华"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("content:community:edit"))
) -> Any:
    """
    设置/取消精华帖子

    - **post_id**: 帖子ID
    - **is_essence**: 是否精华
    """
    try:
        # 查询帖子
        post = db.query(CommunityPost).filter(CommunityPost.post_id == post_id).first()
        if not post:
            return error_response(
                message="帖子不存在",
                code=status.HTTP_404_NOT_FOUND
            )

        # 更新精华状态
        post.is_essence = is_essence
        db.commit()
        db.refresh(post)

        # 记录操作日志
        log = OperationLog(
            admin_id=current_admin.id,
            module="社区管理",
            operation="设为精华" if is_essence else "取消精华",
            content=f"{'设为精华' if is_essence else '取消精华'}帖子 {post.title}",
            ip="127.0.0.1",  # 实际应用中应获取真实IP
            user_agent="Unknown"  # 实际应用中应获取真实User-Agent
        )
        db.add(log)
        db.commit()

        return success_response(
            message=f"帖子已{'设为精华' if is_essence else '取消精华'}",
            data={
                "id": post.id,
                "post_id": post.post_id,
                "is_essence": post.is_essence
            }
        )
    except Exception as e:
        db.rollback()
        logger.error(f"设置/取消精华帖子异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.delete("/posts/{post_id}", response_model=Dict[str, Any])
async def delete_post(
    post_id: str = Path(..., description="帖子ID"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("content:community:delete"))
) -> Any:
    """
    删除帖子

    - **post_id**: 帖子ID
    """
    try:
        # 查询帖子
        post = db.query(CommunityPost).filter(CommunityPost.post_id == post_id).first()
        if not post:
            return error_response(
                message="帖子不存在",
                code=status.HTTP_404_NOT_FOUND
            )

        # 记录操作日志
        log = OperationLog(
            admin_id=current_admin.id,
            module="社区管理",
            operation="删除帖子",
            content=f"删除帖子 {post.title}",
            ip="127.0.0.1",  # 实际应用中应获取真实IP
            user_agent="Unknown"  # 实际应用中应获取真实User-Agent
        )
        db.add(log)

        # 删除帖子
        db.delete(post)
        db.commit()

        return success_response(message="删除帖子成功")
    except Exception as e:
        db.rollback()
        logger.error(f"删除帖子异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.delete("/comments/{comment_id}", response_model=Dict[str, Any])
async def delete_comment(
    comment_id: str = Path(..., description="评论ID"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("content:community:delete"))
) -> Any:
    """
    删除评论

    - **comment_id**: 评论ID
    """
    try:
        # 查询评论
        comment = db.query(Comment).filter(Comment.comment_id == comment_id).first()
        if not comment:
            return error_response(
                message="评论不存在",
                code=status.HTTP_404_NOT_FOUND
            )

        # 获取帖子
        post = db.query(CommunityPost).filter(CommunityPost.post_id == comment.post_id).first()
        
        # 记录操作日志
        log = OperationLog(
            admin_id=current_admin.id,
            module="社区管理",
            operation="删除评论",
            content=f"删除评论 ID: {comment.comment_id}",
            ip="127.0.0.1",  # 实际应用中应获取真实IP
            user_agent="Unknown"  # 实际应用中应获取真实User-Agent
        )
        db.add(log)

        # 删除评论
        db.delete(comment)
        
        # 更新帖子评论数
        if post and post.comment_count:
            post.comment_count = post.comment_count - 1
            
        db.commit()

        return success_response(message="删除评论成功")
    except Exception as e:
        db.rollback()
        logger.error(f"删除评论异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
