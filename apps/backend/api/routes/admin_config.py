#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
系统配置API路由
包括系统配置管理功能
"""

from typing import Any, Dict, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body, status
from sqlalchemy.orm import Session
from sqlalchemy import desc, asc, func
from datetime import datetime
import json

from apps.backend.api.config.database import get_db
from apps.backend.api.dependencies.auth import get_current_active_admin, check_admin_permission
from apps.backend.api.models.admin import Admin, OperationLog
from apps.backend.api.models.config import SystemConfig
from apps.backend.api.utils.response import success_response, error_response
from apps.backend.utils.logger import setup_logger

# 创建路由
router = APIRouter(tags=["admin_config"])

# 设置日志
logger = setup_logger("api_admin_config")


@router.get("/configs", response_model=Dict[str, Any])
async def get_configs(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    keywords: Optional[str] = Query(None, description="关键词"),
    group: Optional[str] = Query(None, description="配置分组"),
    sort: Optional[str] = Query("key", description="排序字段"),
    order: Optional[str] = Query("asc", description="排序方向"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("config:view"))
) -> Any:
    """
    获取系统配置列表

    - **page**: 页码
    - **page_size**: 每页数量
    - **keywords**: 关键词（可选）
    - **group**: 配置分组（可选）
    - **sort**: 排序字段（可选）
    - **order**: 排序方向（可选）
    """
    try:
        # 构建查询
        query = db.query(SystemConfig)

        # 应用过滤条件
        if keywords:
            query = query.filter(
                SystemConfig.key.like(f"%{keywords}%") |
                SystemConfig.description.like(f"%{keywords}%")
            )
        
        if group:
            query = query.filter(SystemConfig.group == group)

        # 计算总数
        total = query.count()

        # 应用排序
        if sort and hasattr(SystemConfig, sort):
            sort_column = getattr(SystemConfig, sort)
            if order and order.lower() == "desc":
                query = query.order_by(desc(sort_column))
            else:
                query = query.order_by(asc(sort_column))
        else:
            # 默认按key升序
            query = query.order_by(asc(SystemConfig.key))

        # 分页
        query = query.offset((page - 1) * page_size).limit(page_size)

        # 执行查询
        configs = query.all()

        # 构建结果
        config_list = []
        for config in configs:
            # 根据类型处理值
            value = config.value
            if config.type == "json" and value:
                try:
                    value = json.loads(value)
                except:
                    value = config.value
            elif config.type == "boolean" and value:
                value = value.lower() == "true"
            elif config.type == "number" and value:
                try:
                    value = float(value)
                    if value.is_integer():
                        value = int(value)
                except:
                    value = config.value
            
            config_list.append({
                "id": config.id,
                "key": config.key,
                "value": value,
                "description": config.description,
                "group": config.group,
                "type": config.type,
                "is_system": config.is_system,
                "created_at": config.created_at,
                "updated_at": config.updated_at
            })

        return success_response(data={
            "items": config_list,
            "total": total,
            "page": page,
            "page_size": page_size,
            "pages": (total + page_size - 1) // page_size,
            "has_next": page * page_size < total,
            "has_prev": page > 1
        })
    except Exception as e:
        logger.error(f"获取系统配置列表异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get("/configs/{config_id}", response_model=Dict[str, Any])
async def get_config(
    config_id: int = Path(..., ge=1, description="配置ID"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("config:view"))
) -> Any:
    """
    获取系统配置详情

    - **config_id**: 配置ID
    """
    try:
        # 查询配置
        config = db.query(SystemConfig).filter(SystemConfig.id == config_id).first()
        if not config:
            return error_response(
                message="配置不存在",
                code=status.HTTP_404_NOT_FOUND
            )

        # 根据类型处理值
        value = config.value
        if config.type == "json" and value:
            try:
                value = json.loads(value)
            except:
                value = config.value
        elif config.type == "boolean" and value:
            value = value.lower() == "true"
        elif config.type == "number" and value:
            try:
                value = float(value)
                if value.is_integer():
                    value = int(value)
            except:
                value = config.value

        return success_response(data={
            "config": {
                "id": config.id,
                "key": config.key,
                "value": value,
                "description": config.description,
                "group": config.group,
                "type": config.type,
                "is_system": config.is_system,
                "created_at": config.created_at,
                "updated_at": config.updated_at
            }
        })
    except Exception as e:
        logger.error(f"获取系统配置详情异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.post("/configs", response_model=Dict[str, Any])
async def create_config(
    config_data: Dict[str, Any] = Body(...),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("config:add"))
) -> Any:
    """
    创建系统配置
    """
    try:
        # 检查key是否已存在
        existing_config = db.query(SystemConfig).filter(SystemConfig.key == config_data.get("key")).first()
        if existing_config:
            return error_response(
                message=f"配置键 '{config_data.get('key')}' 已存在",
                code=status.HTTP_400_BAD_REQUEST
            )
        
        # 处理值
        value = config_data.get("value")
        if config_data.get("type") == "json" and value:
            if isinstance(value, (dict, list)):
                value = json.dumps(value)
        elif config_data.get("type") == "boolean" and value is not None:
            value = str(value).lower()
        elif config_data.get("type") == "number" and value is not None:
            value = str(value)
        
        # 创建配置
        new_config = SystemConfig(
            key=config_data.get("key"),
            value=value,
            description=config_data.get("description"),
            group=config_data.get("group"),
            type=config_data.get("type", "string"),
            is_system=config_data.get("is_system", False)
        )
        db.add(new_config)
        db.commit()
        db.refresh(new_config)

        # 记录操作日志
        log = OperationLog(
            admin_id=current_admin.id,
            module="系统配置",
            operation="创建配置",
            content=f"创建配置 {new_config.key}",
            ip="127.0.0.1",  # 实际应用中应获取真实IP
            user_agent="Unknown"  # 实际应用中应获取真实User-Agent
        )
        db.add(log)
        db.commit()

        return success_response(
            message="创建配置成功",
            data={
                "id": new_config.id,
                "key": new_config.key
            }
        )
    except Exception as e:
        db.rollback()
        logger.error(f"创建系统配置异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.put("/configs/{config_id}", response_model=Dict[str, Any])
async def update_config(
    config_id: int = Path(..., ge=1, description="配置ID"),
    config_data: Dict[str, Any] = Body(...),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("config:edit"))
) -> Any:
    """
    更新系统配置

    - **config_id**: 配置ID
    """
    try:
        # 查询配置
        config = db.query(SystemConfig).filter(SystemConfig.id == config_id).first()
        if not config:
            return error_response(
                message="配置不存在",
                code=status.HTTP_404_NOT_FOUND
            )

        # 如果修改了key，检查新key是否已存在
        if "key" in config_data and config_data["key"] != config.key:
            existing_config = db.query(SystemConfig).filter(SystemConfig.key == config_data["key"]).first()
            if existing_config:
                return error_response(
                    message=f"配置键 '{config_data['key']}' 已存在",
                    code=status.HTTP_400_BAD_REQUEST
                )
            config.key = config_data["key"]

        # 处理值
        if "value" in config_data:
            value = config_data["value"]
            if config.type == "json":
                if isinstance(value, (dict, list)):
                    config.value = json.dumps(value)
                else:
                    config.value = value
            elif config.type == "boolean" and value is not None:
                config.value = str(value).lower()
            elif config.type == "number" and value is not None:
                config.value = str(value)
            else:
                config.value = value

        # 更新其他字段
        if "description" in config_data:
            config.description = config_data["description"]
        if "group" in config_data:
            config.group = config_data["group"]
        if "type" in config_data:
            config.type = config_data["type"]
        if "is_system" in config_data and not config.is_system:
            config.is_system = config_data["is_system"]

        db.commit()
        db.refresh(config)

        # 记录操作日志
        log = OperationLog(
            admin_id=current_admin.id,
            module="系统配置",
            operation="更新配置",
            content=f"更新配置 {config.key}",
            ip="127.0.0.1",  # 实际应用中应获取真实IP
            user_agent="Unknown"  # 实际应用中应获取真实User-Agent
        )
        db.add(log)
        db.commit()

        return success_response(
            message="更新配置成功",
            data={
                "id": config.id,
                "key": config.key
            }
        )
    except Exception as e:
        db.rollback()
        logger.error(f"更新系统配置异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.delete("/configs/{config_id}", response_model=Dict[str, Any])
async def delete_config(
    config_id: int = Path(..., ge=1, description="配置ID"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("config:delete"))
) -> Any:
    """
    删除系统配置

    - **config_id**: 配置ID
    """
    try:
        # 查询配置
        config = db.query(SystemConfig).filter(SystemConfig.id == config_id).first()
        if not config:
            return error_response(
                message="配置不存在",
                code=status.HTTP_404_NOT_FOUND
            )

        # 系统配置不可删除
        if config.is_system:
            return error_response(
                message="系统配置不可删除",
                code=status.HTTP_400_BAD_REQUEST
            )

        # 记录操作日志
        log = OperationLog(
            admin_id=current_admin.id,
            module="系统配置",
            operation="删除配置",
            content=f"删除配置 {config.key}",
            ip="127.0.0.1",  # 实际应用中应获取真实IP
            user_agent="Unknown"  # 实际应用中应获取真实User-Agent
        )
        db.add(log)

        # 删除配置
        db.delete(config)
        db.commit()

        return success_response(message="删除配置成功")
    except Exception as e:
        db.rollback()
        logger.error(f"删除系统配置异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get("/config-groups", response_model=Dict[str, Any])
async def get_config_groups(
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("config:view"))
) -> Any:
    """
    获取配置分组列表
    """
    try:
        # 查询所有不同的分组
        groups = db.query(SystemConfig.group).distinct().all()
        group_list = [group[0] for group in groups if group[0]]

        return success_response(data={
            "groups": group_list
        })
    except Exception as e:
        logger.error(f"获取配置分组列表异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
