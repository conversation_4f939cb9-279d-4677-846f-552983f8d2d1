#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
内容管理API路由
包括轮播图、政策等内容管理
"""

from typing import Any, Dict, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path, UploadFile, File, Form, status
from sqlalchemy.orm import Session
from sqlalchemy import desc, asc
from datetime import datetime

from apps.backend.api.config.database import get_db
from apps.backend.api.dependencies.auth import get_current_active_admin, check_admin_permission
from apps.backend.api.models.admin import Admin, OperationLog
from apps.backend.api.models.content import Banner, Policy
from apps.backend.api.schemas.content import (
    BannerCreate, BannerUpdate, Banner as BannerSchema,
    PolicyCreate, PolicyUpdate, Policy as PolicySchema, PolicyQuery
)
from apps.backend.api.utils.response import success_response, error_response, custom_error_response
from apps.backend.utils.logger import setup_logger

# 创建路由
router = APIRouter(tags=["admin_content"])

# 设置日志
logger = setup_logger("api_admin_content")


@router.get("/banners", response_model=Dict[str, Any])
async def get_banners(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    status: Optional[bool] = Query(None, description="状态"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("content:view"))
) -> Any:
    """
    获取轮播图列表

    - **page**: 页码
    - **page_size**: 每页数量
    - **status**: 状态（可选）
    """
    try:
        # 构建查询
        query = db.query(Banner)

        # 应用过滤条件
        if status is not None:
            query = query.filter(Banner.status == status)

        # 计算总数
        total = query.count()

        # 分页
        query = query.order_by(asc(Banner.sort_order), desc(Banner.id)).offset((page - 1) * page_size).limit(page_size)

        # 执行查询
        banners = query.all()

        # 构建结果
        banner_list = []
        for banner in banners:
            banner_list.append({
                "id": banner.id,
                "title": banner.title,
                "description": banner.description,
                "image_url": banner.image_url,
                "link_url": banner.link_url,
                "sort_order": banner.sort_order,
                "status": banner.status,
                "start_time": banner.start_time,
                "end_time": banner.end_time,
                "created_at": banner.created_at,
                "updated_at": banner.updated_at
            })

        return success_response(data={
            "items": banner_list,
            "total": total,
            "page": page,
            "page_size": page_size,
            "pages": (total + page_size - 1) // page_size,
            "has_next": page * page_size < total,
            "has_prev": page > 1
        })
    except Exception as e:
        logger.error(f"获取轮播图列表异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.post("/banners", response_model=Dict[str, Any])
async def create_banner(
    banner_data: BannerCreate,
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("content:edit"))
) -> Any:
    """
    创建轮播图
    """
    try:
        # 创建轮播图
        new_banner = Banner(
            title=banner_data.title,
            description=banner_data.description,
            image_url=banner_data.image_url,
            link_url=banner_data.link_url,
            sort_order=banner_data.sort_order,
            status=banner_data.status,
            start_time=banner_data.start_time,
            end_time=banner_data.end_time
        )
        db.add(new_banner)
        db.commit()
        db.refresh(new_banner)

        # 记录操作日志
        log = OperationLog(
            admin_id=current_admin.id,
            module="内容管理",
            operation="创建轮播图",
            content=f"创建轮播图 {new_banner.title}",
            ip="127.0.0.1",  # 实际应用中应获取真实IP
            user_agent="Unknown"  # 实际应用中应获取真实User-Agent
        )
        db.add(log)
        db.commit()

        return success_response(
            message="创建轮播图成功",
            data={
                "id": new_banner.id,
                "title": new_banner.title,
                "description": new_banner.description,
                "image_url": new_banner.image_url,
                "link_url": new_banner.link_url,
                "sort_order": new_banner.sort_order,
                "status": new_banner.status,
                "start_time": new_banner.start_time,
                "end_time": new_banner.end_time,
                "created_at": new_banner.created_at,
                "updated_at": new_banner.updated_at
            }
        )
    except Exception as e:
        db.rollback()
        logger.error(f"创建轮播图异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get("/banners/{banner_id}", response_model=Dict[str, Any])
async def get_banner(
    banner_id: int = Path(..., ge=1, description="轮播图ID"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("content:view"))
) -> Any:
    """
    获取轮播图详情

    - **banner_id**: 轮播图ID
    """
    try:
        # 查询轮播图
        banner = db.query(Banner).filter(Banner.id == banner_id).first()
        if not banner:
            return custom_error_response("轮播图不存在", 404)

        return success_response(data={
            "id": banner.id,
            "title": banner.title,
            "description": banner.description,
            "image_url": banner.image_url,
            "link_url": banner.link_url,
            "sort_order": banner.sort_order,
            "status": banner.status,
            "start_time": banner.start_time,
            "end_time": banner.end_time,
            "created_at": banner.created_at,
            "updated_at": banner.updated_at
        })
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取轮播图详情异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.put("/banners/{banner_id}", response_model=Dict[str, Any])
async def update_banner(
    banner_data: BannerUpdate,
    banner_id: int = Path(..., ge=1, description="轮播图ID"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("content:edit"))
) -> Any:
    """
    更新轮播图

    - **banner_id**: 轮播图ID
    """
    try:
        # 查询轮播图
        banner = db.query(Banner).filter(Banner.id == banner_id).first()
        if not banner:
            return custom_error_response("轮播图不存在", 404)

        # 更新轮播图信息
        if banner_data.title is not None:
            setattr(banner, "title", banner_data.title)
        if banner_data.description is not None:
            setattr(banner, "description", banner_data.description)
        if banner_data.image_url is not None:
            setattr(banner, "image_url", banner_data.image_url)
        if banner_data.link_url is not None:
            setattr(banner, "link_url", banner_data.link_url)
        if banner_data.sort_order is not None:
            setattr(banner, "sort_order", banner_data.sort_order)
        if banner_data.status is not None:
            setattr(banner, "status", banner_data.status)
        if banner_data.start_time is not None:
            setattr(banner, "start_time", banner_data.start_time)
        if banner_data.end_time is not None:
            setattr(banner, "end_time", banner_data.end_time)

        db.commit()
        db.refresh(banner)

        # 记录操作日志
        log = OperationLog(
            admin_id=current_admin.id,
            module="内容管理",
            operation="更新轮播图",
            content=f"更新轮播图 {banner.title}",
            ip="127.0.0.1",  # 实际应用中应获取真实IP
            user_agent="Unknown"  # 实际应用中应获取真实User-Agent
        )
        db.add(log)
        db.commit()

        return success_response(
            message="更新轮播图成功",
            data={
                "id": banner.id,
                "title": banner.title,
                "description": banner.description,
                "image_url": banner.image_url,
                "link_url": banner.link_url,
                "sort_order": banner.sort_order,
                "status": banner.status,
                "start_time": banner.start_time,
                "end_time": banner.end_time,
                "created_at": banner.created_at,
                "updated_at": banner.updated_at
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"更新轮播图异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.delete("/banners/{banner_id}", response_model=Dict[str, Any])
async def delete_banner(
    banner_id: int = Path(..., ge=1, description="轮播图ID"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("content:delete"))
) -> Any:
    """
    删除轮播图

    - **banner_id**: 轮播图ID
    """
    try:
        # 查询轮播图
        banner = db.query(Banner).filter(Banner.id == banner_id).first()
        if not banner:
            return custom_error_response("轮播图不存在", 404)

        # 记录操作日志
        log = OperationLog(
            admin_id=current_admin.id,
            module="内容管理",
            operation="删除轮播图",
            content=f"删除轮播图 {banner.title}",
            ip="127.0.0.1",  # 实际应用中应获取真实IP
            user_agent="Unknown"  # 实际应用中应获取真实User-Agent
        )
        db.add(log)

        # 删除轮播图
        db.delete(banner)
        db.commit()

        return success_response(message="删除成功")
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"删除轮播图异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.put("/banners/{banner_id}/status", response_model=Dict[str, Any])
@router.patch("/banners/{banner_id}/toggle", response_model=Dict[str, Any])
async def update_banner_status(
    banner_id: int = Path(..., ge=1, description="轮播图ID"),
    status: Optional[bool] = Query(None, description="状态"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("content:edit"))
) -> Any:
    """
    更新轮播图状态

    - **banner_id**: 轮播图ID
    - **status**: 状态
    """
    try:
        # 查询轮播图
        banner = db.query(Banner).filter(Banner.id == banner_id).first()
        if not banner:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="轮播图不存在"
            )

        # 更新状态 - 如果没有提供status参数，则切换状态
        if status is None:
            banner.status = not banner.status
        else:
            banner.status = status
        db.commit()
        db.refresh(banner)

        # 记录操作日志
        log = OperationLog(
            admin_id=current_admin.id,
            module="内容管理",
            operation="更新轮播图状态",
            content=f"更新轮播图 {banner.title} 状态为 {'启用' if status else '禁用'}",
            ip="127.0.0.1",  # 实际应用中应获取真实IP
            user_agent="Unknown"  # 实际应用中应获取真实User-Agent
        )
        db.add(log)
        db.commit()

        return success_response(
            message=f"轮播图状态已{'启用' if status else '禁用'}",
            data={
                "id": banner.id,
                "status": banner.status
            }
        )
    except Exception as e:
        db.rollback()
        logger.error(f"更新轮播图状态异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.post("/banners/sort", response_model=Dict[str, Any])
async def sort_banners(
    banner_ids: List[int],
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("content:banner:edit"))
) -> Any:
    """
    排序轮播图

    - **banner_ids**: 轮播图ID列表，按排序顺序
    """
    try:
        # 检查所有ID是否存在
        for index, banner_id in enumerate(banner_ids):
            banner = db.query(Banner).filter(Banner.id == banner_id).first()
            if not banner:
                return error_response(
                    message=f"ID为{banner_id}的轮播图不存在",
                    code=status.HTTP_404_NOT_FOUND
                )

            # 更新排序
            banner.sort_order = index + 1
            db.commit()

        # 记录操作日志
        log = OperationLog(
            admin_id=current_admin.id,
            module="内容管理",
            operation="排序轮播图",
            content=f"更新轮播图排序",
            ip="127.0.0.1",  # 实际应用中应获取真实IP
            user_agent="Unknown"  # 实际应用中应获取真实User-Agent
        )
        db.add(log)
        db.commit()

        return success_response(message="轮播图排序更新成功")
    except Exception as e:
        db.rollback()
        logger.error(f"排序轮播图异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.post("/upload/image", response_model=Dict[str, Any])
async def upload_image(
    file: UploadFile = File(...),
    current_admin: Admin = Depends(check_admin_permission("content:upload"))
) -> Any:
    """
    上传图片

    - **file**: 图片文件
    """
    try:
        # 检查文件类型
        if not file.content_type.startswith("image/"):
            return error_response(
                message="只允许上传图片文件",
                code=status.HTTP_400_BAD_REQUEST
            )

        # 读取文件内容
        contents = await file.read()

        # 生成文件名
        import os
        import uuid
        from datetime import datetime

        # 创建上传目录
        upload_dir = os.path.join("static", "uploads", "images", datetime.now().strftime("%Y%m"))
        os.makedirs(upload_dir, exist_ok=True)

        # 生成文件名
        filename = f"{uuid.uuid4().hex}{os.path.splitext(file.filename)[1]}"
        file_path = os.path.join(upload_dir, filename)

        # 保存文件
        with open(file_path, "wb") as f:
            f.write(contents)

        # 生成访问URL
        url = f"/static/uploads/images/{datetime.now().strftime('%Y%m')}/{filename}"

        # 记录操作日志
        log = OperationLog(
            admin_id=current_admin.id,
            module="内容管理",
            operation="上传图片",
            content=f"上传图片 {file.filename}",
            ip="127.0.0.1",  # 实际应用中应获取真实IP
            user_agent="Unknown"  # 实际应用中应获取真实User-Agent
        )
        db = next(get_db())
        db.add(log)
        db.commit()

        return success_response(
            message="图片上传成功",
            data={
                "url": url,
                "filename": filename,
                "original_filename": file.filename
            }
        )
    except Exception as e:
        logger.error(f"上传图片异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


# 政策管理API
@router.get("/policies", response_model=Dict[str, Any])
async def get_policies(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    keywords: Optional[str] = Query(None, description="关键词"),
    sort: Optional[str] = Query("publish_date", description="排序字段"),
    order: Optional[str] = Query("desc", description="排序方向"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("content:view"))
) -> Any:
    """
    获取政策列表

    - **page**: 页码
    - **page_size**: 每页数量
    - **keywords**: 关键词（可选）
    - **sort**: 排序字段（可选）
    - **order**: 排序方向（可选）
    """
    try:
        # 构建查询
        query = db.query(Policy)

        # 应用过滤条件
        if keywords:
            query = query.filter(
                Policy.title.like(f"%{keywords}%") |
                Policy.content.like(f"%{keywords}%") |
                Policy.summary.like(f"%{keywords}%")
            )

        # 计算总数
        total = query.count()

        # 应用排序
        if sort and hasattr(Policy, sort):
            sort_column = getattr(Policy, sort)
            if order and order.lower() == "desc":
                query = query.order_by(desc(sort_column))
            else:
                query = query.order_by(asc(sort_column))
        else:
            # 默认按发布日期降序
            query = query.order_by(desc(Policy.publish_date), desc(Policy.id))

        # 分页
        query = query.offset((page - 1) * page_size).limit(page_size)

        # 执行查询
        policies = query.all()

        # 构建结果
        policy_list = []
        for policy in policies:
            policy_list.append({
                "id": policy.id,
                "policy_id": policy.policy_id,
                "title": policy.title,
                "content": policy.content,
                "summary": policy.summary,
                "key_points": policy.key_points,
                "special_notes": policy.special_notes,
                "publish_date": policy.publish_date,
                "source_url": policy.source_url,
                "created_at": policy.created_at,
                "updated_at": policy.updated_at
            })

        return success_response(data={
            "items": policy_list,
            "total": total,
            "page": page,
            "page_size": page_size,
            "pages": (total + page_size - 1) // page_size,
            "has_next": page * page_size < total,
            "has_prev": page > 1
        })
    except Exception as e:
        logger.error(f"获取政策列表异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get("/policies/{policy_id}", response_model=Dict[str, Any])
async def get_policy(
    policy_id: int = Path(..., ge=1, description="政策ID"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("content:view"))
) -> Any:
    """
    获取政策详情

    - **policy_id**: 政策ID
    """
    try:
        # 查询政策
        policy = db.query(Policy).filter(Policy.id == policy_id).first()
        if not policy:
            return error_response(
                message="政策不存在",
                code=status.HTTP_404_NOT_FOUND
            )

        return success_response(data={
            "id": policy.id,
            "policy_id": policy.policy_id,
            "title": policy.title,
            "content": policy.content,
            "summary": policy.summary,
            "key_points": policy.key_points,
            "special_notes": policy.special_notes,
            "publish_date": policy.publish_date,
            "source_url": policy.source_url,
            "created_at": policy.created_at,
            "updated_at": policy.updated_at
        })
    except Exception as e:
        logger.error(f"获取政策详情异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.post("/policies", response_model=Dict[str, Any])
async def create_policy(
    policy_data: PolicyCreate,
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("content:edit"))
) -> Any:
    """
    创建政策
    """
    try:
        # 检查政策ID是否已存在
        existing_policy = db.query(Policy).filter(Policy.policy_id == policy_data.policy_id).first()
        if existing_policy:
            return error_response(
                message="政策ID已存在",
                code=status.HTTP_400_BAD_REQUEST
            )

        # 创建政策
        new_policy = Policy(
            policy_id=policy_data.policy_id,
            title=policy_data.title,
            content=policy_data.content,
            summary=policy_data.summary,
            key_points=policy_data.key_points,
            special_notes=policy_data.special_notes,
            publish_date=policy_data.publish_date,
            source_url=policy_data.source_url
        )
        db.add(new_policy)
        db.commit()
        db.refresh(new_policy)

        # 记录操作日志
        log = OperationLog(
            admin_id=current_admin.id,
            module="内容管理",
            operation="创建政策",
            content=f"创建政策 {new_policy.title}",
            ip="127.0.0.1",  # 实际应用中应获取真实IP
            user_agent="Unknown"  # 实际应用中应获取真实User-Agent
        )
        db.add(log)
        db.commit()

        return success_response(
            message="创建政策成功",
            data={
                "id": new_policy.id,
                "policy_id": new_policy.policy_id,
                "title": new_policy.title,
                "content": new_policy.content,
                "summary": new_policy.summary,
                "key_points": new_policy.key_points,
                "special_notes": new_policy.special_notes,
                "publish_date": new_policy.publish_date,
                "source_url": new_policy.source_url,
                "created_at": new_policy.created_at,
                "updated_at": new_policy.updated_at
            }
        )
    except Exception as e:
        db.rollback()
        logger.error(f"创建政策异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.put("/policies/{policy_id}", response_model=Dict[str, Any])
async def update_policy(
    policy_id: int = Path(..., ge=1, description="政策ID"),
    policy_data: PolicyUpdate = Depends(),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("content:edit"))
) -> Any:
    """
    更新政策

    - **policy_id**: 政策ID
    """
    try:
        # 查询政策
        policy = db.query(Policy).filter(Policy.id == policy_id).first()
        if not policy:
            return error_response(
                message="政策不存在",
                code=status.HTTP_404_NOT_FOUND
            )

        # 更新政策信息
        if policy_data.title is not None:
            setattr(policy, "title", policy_data.title)
        if policy_data.content is not None:
            setattr(policy, "content", policy_data.content)
        if policy_data.summary is not None:
            setattr(policy, "summary", policy_data.summary)
        if policy_data.key_points is not None:
            setattr(policy, "key_points", policy_data.key_points)
        if policy_data.special_notes is not None:
            setattr(policy, "special_notes", policy_data.special_notes)
        if policy_data.publish_date is not None:
            setattr(policy, "publish_date", policy_data.publish_date)
        if policy_data.source_url is not None:
            setattr(policy, "source_url", policy_data.source_url)

        db.commit()
        db.refresh(policy)

        # 记录操作日志
        log = OperationLog(
            admin_id=current_admin.id,
            module="内容管理",
            operation="更新政策",
            content=f"更新政策 {policy.title}",
            ip="127.0.0.1",  # 实际应用中应获取真实IP
            user_agent="Unknown"  # 实际应用中应获取真实User-Agent
        )
        db.add(log)
        db.commit()

        return success_response(
            message="更新政策成功",
            data={
                "id": policy.id,
                "policy_id": policy.policy_id,
                "title": policy.title,
                "content": policy.content,
                "summary": policy.summary,
                "key_points": policy.key_points,
                "special_notes": policy.special_notes,
                "publish_date": policy.publish_date,
                "source_url": policy.source_url,
                "created_at": policy.created_at,
                "updated_at": policy.updated_at
            }
        )
    except Exception as e:
        db.rollback()
        logger.error(f"更新政策异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.delete("/policies/{policy_id}", response_model=Dict[str, Any])
async def delete_policy(
    policy_id: int = Path(..., ge=1, description="政策ID"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("content:delete"))
) -> Any:
    """
    删除政策

    - **policy_id**: 政策ID
    """
    try:
        # 查询政策
        policy = db.query(Policy).filter(Policy.id == policy_id).first()
        if not policy:
            return error_response(
                message="政策不存在",
                code=status.HTTP_404_NOT_FOUND
            )

        # 记录操作日志
        log = OperationLog(
            admin_id=current_admin.id,
            module="内容管理",
            operation="删除政策",
            content=f"删除政策 {policy.title}",
            ip="127.0.0.1",  # 实际应用中应获取真实IP
            user_agent="Unknown"  # 实际应用中应获取真实User-Agent
        )
        db.add(log)

        # 删除政策
        db.delete(policy)
        db.commit()

        return success_response(message="删除政策成功")
    except Exception as e:
        db.rollback()
        logger.error(f"删除政策异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )