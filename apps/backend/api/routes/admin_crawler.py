#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
爬虫管理API路由
包括爬虫任务管理、监控等功能
"""

from typing import Any, Dict, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body, status
from sqlalchemy.orm import Session
from sqlalchemy import desc, asc, func
from datetime import datetime, timedelta
import json
import os

from apps.backend.api.config.database import get_db
from apps.backend.api.dependencies.auth import get_current_active_admin, check_admin_permission
from apps.backend.api.models.admin import Admin, OperationLog
from apps.backend.api.models.crawler import CrawlerTask, CrawlerTaskLog as CrawlerLog
from apps.backend.api.schemas.crawler import (
    CrawlerTaskCreate, CrawlerTaskUpdate, CrawlerTaskQuery,
    CrawlerTask as CrawlerTaskSchema
)
from apps.backend.api.utils.response import success_response, error_response
from apps.backend.utils.logger import setup_logger
from apps.backend.crawler.crawler_manager import CrawlerManager

# 创建路由
router = APIRouter(tags=["admin_crawler"])

# 设置日志
logger = setup_logger("api_admin_crawler")

# 爬虫管理器实例
crawler_manager = CrawlerManager()


@router.get("/tasks", response_model=Dict[str, Any])
async def get_tasks(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    keywords: Optional[str] = Query(None, description="关键词"),
    status: Optional[str] = Query(None, description="状态"),
    crawler_type: Optional[str] = Query(None, description="爬虫类型"),
    sort: Optional[str] = Query("created_at", description="排序字段"),
    order: Optional[str] = Query("desc", description="排序方向"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("crawler:task:view"))
) -> Any:
    """
    获取爬虫任务列表

    - **page**: 页码
    - **page_size**: 每页数量
    - **keywords**: 关键词（可选）
    - **status**: 状态（可选）
    - **crawler_type**: 爬虫类型（可选）
    - **sort**: 排序字段（可选）
    - **order**: 排序方向（可选）
    """
    try:
        # 构建查询
        query = db.query(CrawlerTask)

        # 应用过滤条件
        if keywords:
            query = query.filter(
                CrawlerTask.name.like(f"%{keywords}%") |
                CrawlerTask.description.like(f"%{keywords}%")
            )

        if status:
            query = query.filter(CrawlerTask.status == status)

        if crawler_type:
            query = query.filter(CrawlerTask.crawler_type == crawler_type)

        # 计算总数
        total = query.count()

        # 应用排序
        if sort and hasattr(CrawlerTask, sort):
            sort_column = getattr(CrawlerTask, sort)
            if order and order.lower() == "desc":
                query = query.order_by(desc(sort_column))
            else:
                query = query.order_by(asc(sort_column))
        else:
            # 默认按创建时间降序
            query = query.order_by(desc(CrawlerTask.created_at))

        # 分页
        query = query.offset((page - 1) * page_size).limit(page_size)

        # 执行查询
        tasks = query.all()

        # 构建结果
        task_list = []
        for task in tasks:
            # 获取最近一次日志
            last_log = db.query(CrawlerLog).filter(
                CrawlerLog.task_id == task.id
            ).order_by(desc(CrawlerLog.created_at)).first()

            task_list.append({
                "id": task.id,
                "name": task.name,
                "description": task.description,
                "crawler_type": task.crawler_type,
                "config": json.loads(task.config) if task.config else {},
                "schedule": task.schedule,
                "status": task.status,
                "last_run": task.last_run,
                "next_run": task.next_run,
                "created_at": task.created_at,
                "updated_at": task.updated_at,
                "last_log": {
                    "status": last_log.status if last_log else None,
                    "message": last_log.message if last_log else None,
                    "created_at": last_log.created_at if last_log else None
                }
            })

        return success_response(data={
            "items": task_list,
            "total": total,
            "page": page,
            "page_size": page_size,
            "pages": (total + page_size - 1) // page_size,
            "has_next": page * page_size < total,
            "has_prev": page > 1
        })
    except Exception as e:
        logger.error(f"获取爬虫任务列表异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get("/tasks/{task_id}", response_model=Dict[str, Any])
async def get_task(
    task_id: int = Path(..., ge=1, description="爬虫任务ID"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("crawler:task:view"))
) -> Any:
    """
    获取爬虫任务详情

    - **task_id**: 爬虫任务ID
    """
    try:
        # 查询爬虫任务
        task = db.query(CrawlerTask).filter(CrawlerTask.id == task_id).first()
        if not task:
            return error_response(
                message="爬虫任务不存在",
                code=status.HTTP_404_NOT_FOUND
            )

        # 获取最近的日志
        logs = db.query(CrawlerLog).filter(
            CrawlerLog.task_id == task.id
        ).order_by(desc(CrawlerLog.created_at)).limit(10).all()

        log_list = []
        for log in logs:
            log_list.append({
                "id": log.id,
                "task_id": log.task_id,
                "status": log.status,
                "message": log.message,
                "details": log.details,
                "created_at": log.created_at
            })

        return success_response(data={
            "task": {
                "id": task.id,
                "name": task.name,
                "description": task.description,
                "crawler_type": task.crawler_type,
                "config": json.loads(task.config) if task.config else {},
                "schedule": task.schedule,
                "status": task.status,
                "last_run": task.last_run,
                "next_run": task.next_run,
                "created_at": task.created_at,
                "updated_at": task.updated_at
            },
            "logs": log_list
        })
    except Exception as e:
        logger.error(f"获取爬虫任务详情异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.post("/tasks", response_model=Dict[str, Any])
async def create_task(
    task_data: CrawlerTaskCreate,
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("crawler:task:add"))
) -> Any:
    """
    创建爬虫任务
    """
    try:
        # 检查爬虫类型是否有效
        if task_data.crawler_type not in crawler_manager.get_available_crawlers():
            return error_response(
                message=f"无效的爬虫类型: {task_data.crawler_type}",
                code=status.HTTP_400_BAD_REQUEST
            )

        # 创建爬虫任务
        new_task = CrawlerTask(
            name=task_data.name,
            description=task_data.description,
            crawler_type=task_data.crawler_type,
            config=json.dumps(task_data.config) if task_data.config else None,
            schedule=task_data.schedule,
            status="inactive"
        )
        db.add(new_task)
        db.commit()
        db.refresh(new_task)

        # 记录操作日志
        log = OperationLog(
            admin_id=current_admin.id,
            module="爬虫管理",
            operation="创建爬虫任务",
            content=f"创建爬虫任务 {new_task.name}",
            ip="127.0.0.1",  # 实际应用中应获取真实IP
            user_agent="Unknown"  # 实际应用中应获取真实User-Agent
        )
        db.add(log)
        db.commit()

        return success_response(
            message="创建爬虫任务成功",
            data={
                "id": new_task.id,
                "name": new_task.name,
                "description": new_task.description,
                "crawler_type": new_task.crawler_type,
                "config": json.loads(new_task.config) if new_task.config else {},
                "schedule": new_task.schedule,
                "status": new_task.status,
                "created_at": new_task.created_at,
                "updated_at": new_task.updated_at
            }
        )
    except Exception as e:
        db.rollback()
        logger.error(f"创建爬虫任务异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.put("/tasks/{task_id}", response_model=Dict[str, Any])
async def update_task(
    task_id: int = Path(..., ge=1, description="爬虫任务ID"),
    task_data: CrawlerTaskUpdate = Body(...),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("crawler:task:edit"))
) -> Any:
    """
    更新爬虫任务

    - **task_id**: 爬虫任务ID
    """
    try:
        # 查询爬虫任务
        task = db.query(CrawlerTask).filter(CrawlerTask.id == task_id).first()
        if not task:
            return error_response(
                message="爬虫任务不存在",
                code=status.HTTP_404_NOT_FOUND
            )

        # 检查爬虫类型是否有效
        if task_data.crawler_type and task_data.crawler_type not in crawler_manager.get_available_crawlers():
            return error_response(
                message=f"无效的爬虫类型: {task_data.crawler_type}",
                code=status.HTTP_400_BAD_REQUEST
            )

        # 更新爬虫任务信息
        if task_data.name is not None:
            task.name = task_data.name
        if task_data.description is not None:
            task.description = task_data.description
        if task_data.crawler_type is not None:
            task.crawler_type = task_data.crawler_type
        if task_data.config is not None:
            task.config = json.dumps(task_data.config)
        if task_data.schedule is not None:
            task.schedule = task_data.schedule
        if task_data.status is not None:
            task.status = task_data.status

        db.commit()
        db.refresh(task)

        # 记录操作日志
        log = OperationLog(
            admin_id=current_admin.id,
            module="爬虫管理",
            operation="更新爬虫任务",
            content=f"更新爬虫任务 {task.name}",
            ip="127.0.0.1",  # 实际应用中应获取真实IP
            user_agent="Unknown"  # 实际应用中应获取真实User-Agent
        )
        db.add(log)
        db.commit()

        return success_response(
            message="更新爬虫任务成功",
            data={
                "id": task.id,
                "name": task.name,
                "description": task.description,
                "crawler_type": task.crawler_type,
                "config": json.loads(task.config) if task.config else {},
                "schedule": task.schedule,
                "status": task.status,
                "last_run": task.last_run,
                "next_run": task.next_run,
                "created_at": task.created_at,
                "updated_at": task.updated_at
            }
        )
    except Exception as e:
        db.rollback()
        logger.error(f"更新爬虫任务异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.delete("/tasks/{task_id}", response_model=Dict[str, Any])
async def delete_task(
    task_id: int = Path(..., ge=1, description="爬虫任务ID"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("crawler:task:delete"))
) -> Any:
    """
    删除爬虫任务

    - **task_id**: 爬虫任务ID
    """
    try:
        # 查询爬虫任务
        task = db.query(CrawlerTask).filter(CrawlerTask.id == task_id).first()
        if not task:
            return error_response(
                message="爬虫任务不存在",
                code=status.HTTP_404_NOT_FOUND
            )

        # 记录操作日志
        log = OperationLog(
            admin_id=current_admin.id,
            module="爬虫管理",
            operation="删除爬虫任务",
            content=f"删除爬虫任务 {task.name}",
            ip="127.0.0.1",  # 实际应用中应获取真实IP
            user_agent="Unknown"  # 实际应用中应获取真实User-Agent
        )
        db.add(log)

        # 删除相关日志
        db.query(CrawlerLog).filter(CrawlerLog.task_id == task_id).delete()

        # 删除爬虫任务
        db.delete(task)
        db.commit()

        return success_response(message="删除爬虫任务成功")
    except Exception as e:
        db.rollback()
        logger.error(f"删除爬虫任务异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.post("/tasks/{task_id}/execute", response_model=Dict[str, Any])
async def execute_task(
    task_id: int = Path(..., ge=1, description="爬虫任务ID"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("crawler:task:execute"))
) -> Any:
    """
    执行爬虫任务

    - **task_id**: 爬虫任务ID
    """
    try:
        # 查询爬虫任务
        task = db.query(CrawlerTask).filter(CrawlerTask.id == task_id).first()
        if not task:
            return error_response(
                message="爬虫任务不存在",
                code=status.HTTP_404_NOT_FOUND
            )

        # 检查任务状态
        if task.status == "running":
            return error_response(
                message="爬虫任务正在运行中",
                code=status.HTTP_400_BAD_REQUEST
            )

        # 更新任务状态
        task.status = "running"
        task.last_run = datetime.now()
        db.commit()
        db.refresh(task)

        # 记录操作日志
        admin_log = OperationLog(
            admin_id=current_admin.id,
            module="爬虫管理",
            operation="执行爬虫任务",
            content=f"执行爬虫任务 {task.name}",
            ip="127.0.0.1",  # 实际应用中应获取真实IP
            user_agent="Unknown"  # 实际应用中应获取真实User-Agent
        )
        db.add(admin_log)
        db.commit()

        # 记录爬虫日志
        crawler_log = CrawlerLog(
            task_id=task.id,
            status="running",
            message="爬虫任务开始执行",
            details=None
        )
        db.add(crawler_log)
        db.commit()

        # 异步执行爬虫任务
        try:
            # 这里应该异步执行爬虫任务，实际实现可能需要使用Celery等任务队列
            # 为了简化示例，这里直接调用爬虫管理器
            config = json.loads(task.config) if task.config else {}
            result = crawler_manager.execute_crawler(task.crawler_type, config)

            # 更新任务状态
            task.status = "completed"
            db.commit()
            db.refresh(task)

            # 记录爬虫日志
            crawler_log = CrawlerLog(
                task_id=task.id,
                status="completed",
                message="爬虫任务执行完成",
                details=json.dumps(result) if result else None
            )
            db.add(crawler_log)
            db.commit()

            return success_response(
                message="爬虫任务执行成功",
                data={
                    "task_id": task.id,
                    "status": "completed",
                    "result": result
                }
            )
        except Exception as e:
            # 更新任务状态
            task.status = "failed"
            db.commit()
            db.refresh(task)

            # 记录爬虫日志
            crawler_log = CrawlerLog(
                task_id=task.id,
                status="failed",
                message=f"爬虫任务执行失败: {str(e)}",
                details=None
            )
            db.add(crawler_log)
            db.commit()

            return error_response(
                message=f"爬虫任务执行失败: {str(e)}",
                code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    except Exception as e:
        db.rollback()
        logger.error(f"执行爬虫任务异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.post("/tasks/{task_id}/stop", response_model=Dict[str, Any])
async def stop_task(
    task_id: int = Path(..., ge=1, description="爬虫任务ID"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("crawler:task:execute"))
) -> Any:
    """
    停止爬虫任务

    - **task_id**: 爬虫任务ID
    """
    try:
        # 查询爬虫任务
        task = db.query(CrawlerTask).filter(CrawlerTask.id == task_id).first()
        if not task:
            return error_response(
                message="爬虫任务不存在",
                code=status.HTTP_404_NOT_FOUND
            )

        # 检查任务状态
        if task.status != "running":
            return error_response(
                message="爬虫任务未在运行中",
                code=status.HTTP_400_BAD_REQUEST
            )

        # 更新任务状态
        task.status = "stopped"
        db.commit()
        db.refresh(task)

        # 记录操作日志
        admin_log = OperationLog(
            admin_id=current_admin.id,
            module="爬虫管理",
            operation="停止爬虫任务",
            content=f"停止爬虫任务 {task.name}",
            ip="127.0.0.1",  # 实际应用中应获取真实IP
            user_agent="Unknown"  # 实际应用中应获取真实User-Agent
        )
        db.add(admin_log)
        db.commit()

        # 记录爬虫日志
        crawler_log = CrawlerLog(
            task_id=task.id,
            status="stopped",
            message="爬虫任务被手动停止",
            details=None
        )
        db.add(crawler_log)
        db.commit()

        # 实际停止爬虫任务
        # 这里应该调用爬虫管理器的停止方法，实际实现可能需要使用Celery等任务队列
        # 为了简化示例，这里直接返回成功

        return success_response(
            message="爬虫任务已停止",
            data={
                "task_id": task.id,
                "status": "stopped"
            }
        )
    except Exception as e:
        db.rollback()
        logger.error(f"停止爬虫任务异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get("/tasks/{task_id}/logs", response_model=Dict[str, Any])
async def get_task_logs(
    task_id: int = Path(..., ge=1, description="爬虫任务ID"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("crawler:task:view"))
) -> Any:
    """
    获取爬虫任务日志

    - **task_id**: 爬虫任务ID
    - **page**: 页码
    - **page_size**: 每页数量
    """
    try:
        # 查询爬虫任务
        task = db.query(CrawlerTask).filter(CrawlerTask.id == task_id).first()
        if not task:
            return error_response(
                message="爬虫任务不存在",
                code=status.HTTP_404_NOT_FOUND
            )

        # 查询日志总数
        total = db.query(func.count(CrawlerLog.id)).filter(
            CrawlerLog.task_id == task_id
        ).scalar() or 0

        # 查询日志
        logs = db.query(CrawlerLog).filter(
            CrawlerLog.task_id == task_id
        ).order_by(desc(CrawlerLog.created_at)).offset(
            (page - 1) * page_size
        ).limit(page_size).all()

        # 构建结果
        log_list = []
        for log in logs:
            log_list.append({
                "id": log.id,
                "task_id": log.task_id,
                "status": log.status,
                "message": log.message,
                "details": json.loads(log.details) if log.details else None,
                "created_at": log.created_at
            })

        return success_response(data={
            "items": log_list,
            "total": total,
            "page": page,
            "page_size": page_size,
            "pages": (total + page_size - 1) // page_size,
            "has_next": page * page_size < total,
            "has_prev": page > 1
        })
    except Exception as e:
        logger.error(f"获取爬虫任务日志异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get("/crawler-types", response_model=Dict[str, Any])
async def get_crawler_types(
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("crawler:task:view"))
) -> Any:
    """
    获取可用的爬虫类型
    """
    try:
        # 获取可用的爬虫类型
        crawler_types = crawler_manager.get_available_crawlers()

        return success_response(data={
            "crawler_types": crawler_types
        })
    except Exception as e:
        logger.error(f"获取爬虫类型异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )