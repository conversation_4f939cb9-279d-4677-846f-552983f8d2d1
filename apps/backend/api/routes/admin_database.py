#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
管理员数据库优化管理API路由
"""

from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, Query, BackgroundTasks
from pydantic import BaseModel

from apps.backend.api.dependencies.auth import get_current_active_admin
from apps.backend.api.models.admin import Admin
from apps.backend.api.config.database_optimization import get_database_optimization_config
from apps.backend.api.utils.database_pool import get_connection_pool
from apps.backend.api.utils.query_optimizer import get_query_optimizer
from apps.backend.api.utils.index_manager import get_index_manager
from apps.backend.api.utils.response import success_response, error_response
from apps.backend.utils.logger import setup_logger

# 创建路由器
router = APIRouter(
    prefix="/database",
    tags=["admin-database"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        500: {"description": "Internal server error"},
    },
)

# 设置日志
logger = setup_logger("admin_database")


class IndexCreateRequest(BaseModel):
    """创建索引请求"""
    table_name: str
    index_name: str
    columns: List[str]
    index_type: str = "BTREE"
    unique: bool = False


class QueryOptimizationRequest(BaseModel):
    """查询优化请求"""
    query: str
    analyze_only: bool = True


@router.get("/config", response_model=Dict[str, Any])
async def get_database_config(
    section: Optional[str] = Query(None, description="配置节名称"),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取数据库优化配置

    获取数据库优化相关配置信息
    """
    try:
        if section:
            config = get_database_optimization_config(section)
            if not config:
                return error_response(message=f"配置节 {section} 不存在", code=404)

            return success_response(
                data={section: config},
                message=f"获取 {section} 配置成功"
            )
        else:
            # 返回所有配置（脱敏敏感信息）
            all_config = get_database_optimization_config()
            sanitized_config = _sanitize_database_config(all_config)
            return success_response(data=sanitized_config, message="获取数据库配置成功")
    except Exception as e:
        logger.error(f"Error getting database config: {e}")
        return error_response(message=str(e), code=500)


@router.get("/pool/status", response_model=Dict[str, Any])
async def get_connection_pool_status(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取连接池状态

    获取数据库连接池的当前状态
    """
    try:
        pool = get_connection_pool()
        if not pool:
            return error_response(message="连接池未初始化", code=404)

        pool_stats = pool.get_pool_stats()

        return success_response(data=pool_stats, message="获取连接池状态成功")
    except Exception as e:
        logger.error(f"Error getting connection pool status: {e}")
        return error_response(message=str(e), code=500)


@router.get("/indexes", response_model=Dict[str, Any])
async def get_database_indexes(
    table_name: Optional[str] = Query(None, description="表名"),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取数据库索引信息

    获取数据库中的索引信息
    """
    try:
        pool = get_connection_pool()
        if not pool:
            return error_response(message="连接池未初始化", code=404)

        index_manager = get_index_manager()

        with pool.get_connection() as connection:
            indexes = index_manager.get_existing_indexes(connection, table_name)
            index_stats = index_manager.get_index_statistics(connection)

        result = {
            "indexes": [index.to_dict() for index in indexes],
            "statistics": index_stats,
            "total_count": len(indexes)
        }

        return success_response(data=result, message="获取索引信息成功")
    except Exception as e:
        logger.error(f"Error getting database indexes: {e}")
        return error_response(message=str(e), code=500)


@router.post("/indexes", response_model=Dict[str, Any])
async def create_database_index(
    request: IndexCreateRequest,
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    创建数据库索引

    在指定表上创建索引
    """
    try:
        pool = get_connection_pool()
        if not pool:
            return error_response(message="连接池未初始化", code=404)

        index_manager = get_index_manager()

        with pool.get_connection() as connection:
            success = index_manager.create_index(
                connection=connection,
                table_name=request.table_name,
                index_name=request.index_name,
                columns=request.columns,
                index_type=request.index_type,
                unique=request.unique
            )

        if success:
            logger.info(f"Index created by admin {current_admin.username}: {request.index_name}")
            return success_response(message=f"索引 {request.index_name} 创建成功")
        else:
            return error_response(message="索引创建失败", code=500)
    except Exception as e:
        logger.error(f"Error creating database index: {e}")
        return error_response(message=str(e), code=500)


@router.delete("/indexes/{index_name}", response_model=Dict[str, Any])
async def drop_database_index(
    index_name: str,
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    删除数据库索引

    删除指定的数据库索引
    """
    try:
        pool = get_connection_pool()
        if not pool:
            return error_response(message="连接池未初始化", code=404)

        index_manager = get_index_manager()

        with pool.get_connection() as connection:
            success = index_manager.drop_index(connection, index_name)

        if success:
            logger.info(f"Index dropped by admin {current_admin.username}: {index_name}")
            return success_response(message=f"索引 {index_name} 删除成功")
        else:
            return error_response(message="索引删除失败", code=500)
    except Exception as e:
        logger.error(f"Error dropping database index: {e}")
        return error_response(message=str(e), code=500)


@router.get("/indexes/suggestions", response_model=Dict[str, Any])
async def get_index_suggestions(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取索引建议

    分析数据库并提供索引优化建议
    """
    try:
        pool = get_connection_pool()
        if not pool:
            return error_response(message="连接池未初始化", code=404)

        index_manager = get_index_manager()

        with pool.get_connection() as connection:
            missing_indexes = index_manager.analyze_missing_indexes(connection)
            unused_indexes = index_manager.get_unused_indexes(connection)

        result = {
            "missing_indexes": [suggestion.to_dict() for suggestion in missing_indexes],
            "unused_indexes": [index.to_dict() for index in unused_indexes],
            "missing_count": len(missing_indexes),
            "unused_count": len(unused_indexes)
        }

        return success_response(data=result, message="获取索引建议成功")
    except Exception as e:
        logger.error(f"Error getting index suggestions: {e}")
        return error_response(message=str(e), code=500)


@router.post("/indexes/create-recommended", response_model=Dict[str, Any])
async def create_recommended_indexes(
    background_tasks: BackgroundTasks,
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    创建推荐的索引

    自动创建系统推荐的索引
    """
    try:
        pool = get_connection_pool()
        if not pool:
            return error_response(message="连接池未初始化", code=404)

        # 在后台执行索引创建
        background_tasks.add_task(_create_recommended_indexes_task, current_admin.username)

        return success_response(message="推荐索引创建任务已启动")
    except Exception as e:
        logger.error(f"Error creating recommended indexes: {e}")
        return error_response(message=str(e), code=500)


@router.get("/queries/optimization", response_model=Dict[str, Any])
async def get_query_optimization_report(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取查询优化报告

    获取查询性能分析和优化建议
    """
    try:
        query_optimizer = get_query_optimizer()
        optimization_report = query_optimizer.get_optimization_report()

        return success_response(data=optimization_report, message="获取查询优化报告成功")
    except Exception as e:
        logger.error(f"Error getting query optimization report: {e}")
        return error_response(message=str(e), code=500)


@router.post("/queries/analyze", response_model=Dict[str, Any])
async def analyze_query(
    request: QueryOptimizationRequest,
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    分析查询

    分析指定查询的性能和优化建议
    """
    try:
        query_optimizer = get_query_optimizer()

        if request.analyze_only:
            # 只分析，不执行
            suggestions = query_optimizer.analyzer.analyze_query(request.query, 0)

            result = {
                "query": request.query,
                "suggestions": [
                    {
                        "issue_type": s.issue_type,
                        "severity": s.severity,
                        "description": s.description,
                        "suggestion": s.suggestion,
                        "optimized_query": s.optimized_query
                    }
                    for s in suggestions
                ],
                "suggestion_count": len(suggestions)
            }
        else:
            # 执行并分析
            pool = get_connection_pool()
            if not pool:
                return error_response(message="连接池未初始化", code=404)

            with pool.get_connection() as connection:
                import time
                start_time = time.time()
                result_data = query_optimizer.execute_query(connection, request.query)
                execution_time = time.time() - start_time

                suggestions = query_optimizer.analyzer.analyze_query(request.query, execution_time)

                result = {
                    "query": request.query,
                    "execution_time": execution_time,
                    "result_count": len(result_data) if isinstance(result_data, list) else result_data,
                    "suggestions": [
                        {
                            "issue_type": s.issue_type,
                            "severity": s.severity,
                            "description": s.description,
                            "suggestion": s.suggestion,
                            "optimized_query": s.optimized_query
                        }
                        for s in suggestions
                    ]
                }

        logger.info(f"Query analyzed by admin {current_admin.username}")

        return success_response(data=result, message="查询分析完成")
    except Exception as e:
        logger.error(f"Error analyzing query: {e}")
        return error_response(message=str(e), code=500)


@router.delete("/queries/cache", response_model=Dict[str, Any])
async def clear_query_cache(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    清空查询缓存

    清空所有查询缓存数据
    """
    try:
        query_optimizer = get_query_optimizer()
        query_optimizer.clear_cache()

        logger.info(f"Query cache cleared by admin {current_admin.username}")

        return success_response(message="查询缓存已清空")
    except Exception as e:
        logger.error(f"Error clearing query cache: {e}")
        return error_response(message=str(e), code=500)


@router.get("/performance", response_model=Dict[str, Any])
async def get_database_performance(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取数据库性能指标

    获取数据库的性能监控数据
    """
    try:
        # 获取连接池状态
        pool = get_connection_pool()
        pool_stats = pool.get_pool_stats() if pool else {}

        # 获取查询优化报告
        query_optimizer = get_query_optimizer()
        query_stats = query_optimizer.get_optimization_report()

        # 获取索引统计
        index_manager = get_index_manager()
        if pool:
            with pool.get_connection() as connection:
                index_stats = index_manager.get_index_statistics(connection)
        else:
            index_stats = {}

        performance_data = {
            "connection_pool": pool_stats,
            "query_performance": query_stats,
            "index_statistics": index_stats,
            "timestamp": "2025-05-23T17:30:00"
        }

        return success_response(data=performance_data, message="获取数据库性能指标成功")
    except Exception as e:
        logger.error(f"Error getting database performance: {e}")
        return error_response(message=str(e), code=500)


async def _create_recommended_indexes_task(admin_username: str):
    """创建推荐索引的后台任务"""
    try:
        logger.info(f"Starting recommended indexes creation task initiated by {admin_username}")

        pool = get_connection_pool()
        if not pool:
            logger.error("Connection pool not available for index creation task")
            return

        index_manager = get_index_manager()

        with pool.get_connection() as connection:
            result = index_manager.create_recommended_indexes(connection)
            logger.info(f"Recommended indexes creation completed: {result}")

    except Exception as e:
        logger.error(f"Recommended indexes creation task failed: {e}")


def _sanitize_database_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """脱敏数据库配置"""
    sanitized = {}
    sensitive_keys = ["password", "secret", "key", "token"]

    for key, value in config.items():
        if any(sensitive in key.lower() for sensitive in sensitive_keys):
            sanitized[key] = "***"
        elif isinstance(value, dict):
            sanitized[key] = _sanitize_database_config(value)
        else:
            sanitized[key] = value

    return sanitized
