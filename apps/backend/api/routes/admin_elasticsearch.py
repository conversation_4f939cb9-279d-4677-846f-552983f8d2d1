#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
管理员Elasticsearch管理API路由
"""

from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, Query, BackgroundTasks

from apps.backend.api.dependencies.auth import get_current_active_admin
from apps.backend.api.models.admin import Admin
from apps.backend.api.services.elasticsearch_search import search_service
from apps.backend.api.services.elasticsearch_sync import sync_service
from apps.backend.api.utils.response import success_response, error_response
from apps.backend.utils.logger import setup_logger

# 创建路由器
router = APIRouter(
    prefix="/elasticsearch",
    tags=["admin-elasticsearch"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        500: {"description": "Internal server error"},
    },
)

# 设置日志
logger = setup_logger("admin_elasticsearch")


@router.get("/status", response_model=Dict[str, Any])
async def get_elasticsearch_status(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取Elasticsearch状态
    
    获取Elasticsearch连接状态和索引信息
    """
    try:
        # 获取同步状态
        sync_status = sync_service.get_sync_status()
        
        # 获取搜索统计
        search_stats = search_service.get_search_stats()
        
        status = {
            "sync_status": sync_status,
            "search_stats": search_stats,
            "timestamp": "2025-05-23T16:30:00"
        }
        
        return success_response(data=status, message="获取Elasticsearch状态成功")
    except Exception as e:
        logger.error(f"Error getting Elasticsearch status: {e}")
        return error_response(message=str(e), code=500)


@router.post("/index/create", response_model=Dict[str, Any])
async def create_index(
    force_recreate: bool = Query(False, description="是否强制重新创建索引"),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    创建Elasticsearch索引
    
    创建岗位搜索索引
    """
    try:
        if force_recreate:
            # 先删除现有索引
            sync_service.delete_index()
        
        # 创建索引
        success = sync_service.create_index_if_not_exists()
        
        if success:
            message = "索引创建成功"
            logger.info(f"Index created by admin {current_admin.username}")
        else:
            message = "索引创建失败"
        
        return success_response(
            data={"success": success},
            message=message
        )
    except Exception as e:
        logger.error(f"Error creating index: {e}")
        return error_response(message=str(e), code=500)


@router.delete("/index", response_model=Dict[str, Any])
async def delete_index(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    删除Elasticsearch索引
    
    删除岗位搜索索引
    """
    try:
        success = sync_service.delete_index()
        
        if success:
            message = "索引删除成功"
            logger.info(f"Index deleted by admin {current_admin.username}")
        else:
            message = "索引删除失败或索引不存在"
        
        return success_response(
            data={"success": success},
            message=message
        )
    except Exception as e:
        logger.error(f"Error deleting index: {e}")
        return error_response(message=str(e), code=500)


@router.post("/sync/all", response_model=Dict[str, Any])
async def sync_all_jobs(
    background_tasks: BackgroundTasks,
    batch_size: int = Query(100, ge=1, le=1000, description="批处理大小"),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    同步所有岗位到Elasticsearch
    
    将数据库中的所有岗位同步到Elasticsearch索引
    """
    try:
        # 在后台执行同步任务
        background_tasks.add_task(
            _background_sync_all_jobs,
            batch_size,
            current_admin.username
        )
        
        message = f"已启动全量同步任务，批处理大小: {batch_size}"
        logger.info(f"Full sync initiated by admin {current_admin.username}")
        
        return success_response(
            data={"batch_size": batch_size},
            message=message
        )
    except Exception as e:
        logger.error(f"Error starting sync: {e}")
        return error_response(message=str(e), code=500)


@router.get("/search/test", response_model=Dict[str, Any])
async def test_search(
    query: str = Query(..., description="搜索关键词"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    测试Elasticsearch搜索
    
    测试搜索功能是否正常工作
    """
    try:
        # 执行搜索
        results = search_service.search_jobs(
            query=query,
            page=page,
            page_size=page_size
        )
        
        # 添加测试信息
        results["test_info"] = {
            "query": query,
            "admin": current_admin.username,
            "timestamp": "2025-05-23T16:30:00"
        }
        
        return success_response(data=results, message="搜索测试完成")
    except Exception as e:
        logger.error(f"Error testing search: {e}")
        return error_response(message=str(e), code=500)


@router.get("/search/suggest", response_model=Dict[str, Any])
async def test_search_suggestions(
    query: str = Query(..., description="查询词"),
    size: int = Query(5, ge=1, le=20, description="建议数量"),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    测试搜索建议
    
    测试搜索建议功能
    """
    try:
        suggestions = search_service.suggest_jobs(query, size)
        
        return success_response(
            data={
                "query": query,
                "suggestions": suggestions,
                "count": len(suggestions)
            },
            message="搜索建议测试完成"
        )
    except Exception as e:
        logger.error(f"Error testing suggestions: {e}")
        return error_response(message=str(e), code=500)


@router.get("/health", response_model=Dict[str, Any])
async def check_elasticsearch_health(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    检查Elasticsearch健康状态
    
    检查Elasticsearch连接和基本功能
    """
    try:
        from apps.backend.api.config.elasticsearch import get_elasticsearch
        
        es = get_elasticsearch()
        
        if not es:
            health_status = {
                "status": "unhealthy",
                "message": "Elasticsearch连接失败",
                "connected": False,
                "ping_test": False,
                "index_exists": False
            }
        else:
            # 测试连接
            ping_success = es.ping()
            
            # 检查索引是否存在
            index_exists = es.indices.exists(index=sync_service.jobs_index)
            
            health_status = {
                "status": "healthy" if ping_success and index_exists else "partial",
                "message": "Elasticsearch运行正常" if ping_success and index_exists else "部分功能异常",
                "connected": ping_success,
                "ping_test": ping_success,
                "index_exists": index_exists,
                "index_name": sync_service.jobs_index
            }
        
        return success_response(data=health_status, message="Elasticsearch健康检查完成")
    except Exception as e:
        logger.error(f"Error checking Elasticsearch health: {e}")
        health_status = {
            "status": "unhealthy",
            "message": str(e),
            "connected": False,
            "ping_test": False,
            "index_exists": False
        }
        return success_response(data=health_status, message="Elasticsearch健康检查完成")


async def _background_sync_all_jobs(batch_size: int, admin_username: str):
    """
    后台同步所有岗位任务
    
    Args:
        batch_size: 批处理大小
        admin_username: 管理员用户名
    """
    try:
        logger.info(f"Starting background sync task initiated by {admin_username}")
        
        # 确保索引存在
        sync_service.create_index_if_not_exists()
        
        # 执行同步
        result = sync_service.sync_all_jobs_from_db(batch_size)
        
        if result["success"]:
            logger.info(
                f"Background sync completed: {result['synced']}/{result['total']} jobs synced, "
                f"{result['errors']} errors"
            )
        else:
            logger.error(f"Background sync failed: {result.get('error')}")
            
    except Exception as e:
        logger.error(f"Background sync task failed: {e}")


@router.get("/sync/status", response_model=Dict[str, Any])
async def get_sync_status(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取同步状态
    
    获取数据同步状态信息
    """
    try:
        status = sync_service.get_sync_status()
        return success_response(data=status, message="获取同步状态成功")
    except Exception as e:
        logger.error(f"Error getting sync status: {e}")
        return error_response(message=str(e), code=500)
