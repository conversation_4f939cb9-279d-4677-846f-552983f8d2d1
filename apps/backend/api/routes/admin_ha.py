#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
管理员高可用部署管理API路由
"""

import asyncio
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, Query, BackgroundTasks
from pydantic import BaseModel

from apps.backend.api.dependencies.auth import get_current_active_admin
from apps.backend.api.models.admin import Admin
from apps.backend.api.config.high_availability import get_ha_config
from apps.backend.api.utils.service_discovery import get_service_discovery_manager
from apps.backend.api.utils.health_check import get_health_check_manager, check_health
from apps.backend.api.utils.load_balancer import get_load_balancer
from apps.backend.api.utils.response import success_response, error_response
from apps.backend.utils.logger import setup_logger

# 创建路由器
router = APIRouter(
    prefix="/ha",
    tags=["admin-ha"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        500: {"description": "Internal server error"},
    },
)

# 设置日志
logger = setup_logger("admin_ha")


class ServiceRegistrationRequest(BaseModel):
    """服务注册请求"""
    host: Optional[str] = None
    port: Optional[int] = None


@router.get("/config", response_model=Dict[str, Any])
async def get_ha_config_info(
    section: Optional[str] = Query(None, description="配置节名称"),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取高可用配置
    
    获取高可用部署相关配置信息
    """
    try:
        if section:
            config = get_ha_config(section)
            if not config:
                return error_response(message=f"配置节 {section} 不存在", code=404)
            
            return success_response(
                data={section: config},
                message=f"获取 {section} 配置成功"
            )
        else:
            # 返回所有配置（脱敏敏感信息）
            all_config = get_ha_config()
            sanitized_config = _sanitize_ha_config(all_config)
            return success_response(data=sanitized_config, message="获取高可用配置成功")
    except Exception as e:
        logger.error(f"Error getting HA config: {e}")
        return error_response(message=str(e), code=500)


@router.get("/status", response_model=Dict[str, Any])
async def get_ha_status(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取高可用状态
    
    获取整个高可用系统的状态信息
    """
    try:
        # 获取服务发现状态
        service_discovery = get_service_discovery_manager()
        discovery_status = service_discovery.get_discovery_status()
        
        # 获取负载均衡状态
        load_balancer = get_load_balancer()
        lb_stats = load_balancer.get_load_balancer_stats()
        
        # 获取健康检查状态
        health_manager = get_health_check_manager()
        health_summary = health_manager.get_health_summary()
        
        # 获取整体健康状态
        overall_health = await check_health()
        
        ha_status = {
            "service_discovery": discovery_status,
            "load_balancer": lb_stats,
            "health_check": health_summary,
            "overall_health": overall_health,
            "timestamp": "2025-05-23T18:00:00"
        }
        
        return success_response(data=ha_status, message="获取高可用状态成功")
    except Exception as e:
        logger.error(f"Error getting HA status: {e}")
        return error_response(message=str(e), code=500)


@router.post("/service/register", response_model=Dict[str, Any])
async def register_service(
    request: ServiceRegistrationRequest,
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    注册服务
    
    将当前服务注册到服务发现系统
    """
    try:
        service_discovery = get_service_discovery_manager()
        
        success = service_discovery.register_current_service(
            host=request.host,
            port=request.port
        )
        
        if success:
            logger.info(f"Service registered by admin {current_admin.username}")
            return success_response(message="服务注册成功")
        else:
            return error_response(message="服务注册失败", code=500)
    except Exception as e:
        logger.error(f"Error registering service: {e}")
        return error_response(message=str(e), code=500)


@router.delete("/service/register", response_model=Dict[str, Any])
async def deregister_service(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    注销服务
    
    从服务发现系统注销当前服务
    """
    try:
        service_discovery = get_service_discovery_manager()
        
        success = service_discovery.deregister_current_service()
        
        if success:
            logger.info(f"Service deregistered by admin {current_admin.username}")
            return success_response(message="服务注销成功")
        else:
            return error_response(message="服务注销失败", code=500)
    except Exception as e:
        logger.error(f"Error deregistering service: {e}")
        return error_response(message=str(e), code=500)


@router.get("/services", response_model=Dict[str, Any])
async def discover_services(
    service_name: Optional[str] = Query(None, description="服务名称"),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    发现服务
    
    从服务发现系统获取服务实例列表
    """
    try:
        service_discovery = get_service_discovery_manager()
        
        if service_name:
            # 发现指定服务
            instances = service_discovery.discover_service_instances(service_name)
            result = {
                "service_name": service_name,
                "instances": [instance.to_dict() for instance in instances],
                "instance_count": len(instances)
            }
        else:
            # 返回服务发现状态
            result = service_discovery.get_discovery_status()
        
        return success_response(data=result, message="服务发现成功")
    except Exception as e:
        logger.error(f"Error discovering services: {e}")
        return error_response(message=str(e), code=500)


@router.get("/health", response_model=Dict[str, Any])
async def get_health_status(
    component: Optional[str] = Query(None, description="组件名称"),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取健康状态
    
    获取系统或指定组件的健康状态
    """
    try:
        health_manager = get_health_check_manager()
        
        if component:
            # 检查指定组件
            result = await health_manager.check_component_health(component)
            if result:
                return success_response(data=result.to_dict(), message=f"获取 {component} 健康状态成功")
            else:
                return error_response(message=f"组件 {component} 不存在或检查失败", code=404)
        else:
            # 检查所有组件
            overall_health = await check_health()
            return success_response(data=overall_health, message="获取系统健康状态成功")
    except Exception as e:
        logger.error(f"Error getting health status: {e}")
        return error_response(message=str(e), code=500)


@router.post("/health/check", response_model=Dict[str, Any])
async def trigger_health_check(
    background_tasks: BackgroundTasks,
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    触发健康检查
    
    手动触发系统健康检查
    """
    try:
        # 在后台执行健康检查
        background_tasks.add_task(_perform_health_check_task, current_admin.username)
        
        return success_response(message="健康检查任务已启动")
    except Exception as e:
        logger.error(f"Error triggering health check: {e}")
        return error_response(message=str(e), code=500)


@router.get("/load-balancer", response_model=Dict[str, Any])
async def get_load_balancer_status(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取负载均衡器状态
    
    获取负载均衡器的状态和统计信息
    """
    try:
        load_balancer = get_load_balancer()
        lb_stats = load_balancer.get_load_balancer_stats()
        
        return success_response(data=lb_stats, message="获取负载均衡器状态成功")
    except Exception as e:
        logger.error(f"Error getting load balancer status: {e}")
        return error_response(message=str(e), code=500)


@router.delete("/load-balancer/cache", response_model=Dict[str, Any])
async def clear_load_balancer_cache(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    清空负载均衡器缓存
    
    清空负载均衡器的服务实例缓存
    """
    try:
        load_balancer = get_load_balancer()
        load_balancer.clear_cache()
        
        logger.info(f"Load balancer cache cleared by admin {current_admin.username}")
        
        return success_response(message="负载均衡器缓存已清空")
    except Exception as e:
        logger.error(f"Error clearing load balancer cache: {e}")
        return error_response(message=str(e), code=500)


@router.get("/deployment", response_model=Dict[str, Any])
async def get_deployment_info(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取部署信息
    
    获取当前部署的详细信息
    """
    try:
        deployment_config = get_ha_config("deployment")
        containerization_config = get_ha_config("containerization")
        
        deployment_info = {
            "deployment_mode": deployment_config.get("mode"),
            "environment": deployment_config.get("environment"),
            "region": deployment_config.get("region"),
            "availability_zones": deployment_config.get("availability_zones"),
            "instances": {
                "min": deployment_config.get("min_instances"),
                "max": deployment_config.get("max_instances"),
                "desired": deployment_config.get("desired_instances")
            },
            "auto_scaling": deployment_config.get("auto_scaling_enabled"),
            "containerization": {
                "enabled": containerization_config.get("enabled"),
                "platform": containerization_config.get("platform"),
                "image": f"{containerization_config.get('image_name')}:{containerization_config.get('image_tag')}"
            }
        }
        
        return success_response(data=deployment_info, message="获取部署信息成功")
    except Exception as e:
        logger.error(f"Error getting deployment info: {e}")
        return error_response(message=str(e), code=500)


@router.get("/monitoring", response_model=Dict[str, Any])
async def get_monitoring_status(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取监控状态
    
    获取高可用监控系统的状态
    """
    try:
        monitoring_config = get_ha_config("monitoring")
        
        monitoring_status = {
            "enabled": monitoring_config.get("enabled"),
            "prometheus_enabled": monitoring_config.get("prometheus_enabled"),
            "grafana_enabled": monitoring_config.get("grafana_enabled"),
            "alertmanager_enabled": monitoring_config.get("alertmanager_enabled"),
            "log_aggregation": monitoring_config.get("log_aggregation"),
            "distributed_tracing": monitoring_config.get("distributed_tracing"),
            "metrics_retention_days": monitoring_config.get("metrics_retention_days"),
            "alerts_count": len(monitoring_config.get("alerts", []))
        }
        
        return success_response(data=monitoring_status, message="获取监控状态成功")
    except Exception as e:
        logger.error(f"Error getting monitoring status: {e}")
        return error_response(message=str(e), code=500)


@router.post("/monitoring/start", response_model=Dict[str, Any])
async def start_monitoring(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    启动监控
    
    启动健康检查监控
    """
    try:
        health_manager = get_health_check_manager()
        health_manager.start_monitoring()
        
        logger.info(f"Monitoring started by admin {current_admin.username}")
        
        return success_response(message="监控已启动")
    except Exception as e:
        logger.error(f"Error starting monitoring: {e}")
        return error_response(message=str(e), code=500)


@router.post("/monitoring/stop", response_model=Dict[str, Any])
async def stop_monitoring(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    停止监控
    
    停止健康检查监控
    """
    try:
        health_manager = get_health_check_manager()
        health_manager.stop_monitoring()
        
        logger.info(f"Monitoring stopped by admin {current_admin.username}")
        
        return success_response(message="监控已停止")
    except Exception as e:
        logger.error(f"Error stopping monitoring: {e}")
        return error_response(message=str(e), code=500)


async def _perform_health_check_task(admin_username: str):
    """执行健康检查的后台任务"""
    try:
        logger.info(f"Starting health check task initiated by {admin_username}")
        
        health_manager = get_health_check_manager()
        results = await health_manager.check_all_health()
        
        logger.info(f"Health check completed: {len(results)} components checked")
        
    except Exception as e:
        logger.error(f"Health check task failed: {e}")


def _sanitize_ha_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """脱敏高可用配置"""
    sanitized = {}
    sensitive_keys = ["password", "secret", "key", "token"]
    
    for key, value in config.items():
        if any(sensitive in key.lower() for sensitive in sensitive_keys):
            sanitized[key] = "***"
        elif isinstance(value, dict):
            sanitized[key] = _sanitize_ha_config(value)
        else:
            sanitized[key] = value
    
    return sanitized
