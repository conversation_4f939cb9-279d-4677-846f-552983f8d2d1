#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
岗位管理API路由
"""

from typing import Any, Dict, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.orm import Session
from sqlalchemy import desc, asc
from datetime import datetime

from apps.backend.api.config.database import get_db
from apps.backend.api.dependencies.admin_auth import get_current_active_admin, check_admin_permission
from apps.backend.api.models.admin import Admin, OperationLog
from apps.backend.api.models.job import Job, JobDetail
from apps.backend.api.schemas.job import (
    JobCreate, JobUpdate, Job as JobSchema,
    JobDetail as JobDetailSchema
)
from apps.backend.api.utils.response import success_response, error_response
from apps.backend.utils.logger import setup_logger

# 创建路由
router = APIRouter(tags=["admin_job"])

# 设置日志
logger = setup_logger("api_admin_job")


@router.get("/jobs", response_model=Dict[str, Any])
async def get_jobs(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    keywords: Optional[str] = Query(None, description="关键词"),
    location: Optional[str] = Query(None, description="工作地点"),
    education: Optional[str] = Query(None, description="学历要求"),
    experience: Optional[str] = Query(None, description="经验要求"),
    job_type: Optional[str] = Query(None, description="工作类型"),
    status: Optional[str] = Query(None, description="状态"),
    sort: Optional[str] = Query("publish_date", description="排序字段"),
    order: Optional[str] = Query("desc", description="排序方向"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("job:view"))
) -> Any:
    """
    获取岗位列表

    - **page**: 页码
    - **page_size**: 每页数量
    - **keywords**: 关键词（可选）
    - **location**: 工作地点（可选）
    - **education**: 学历要求（可选）
    - **experience**: 经验要求（可选）
    - **job_type**: 工作类型（可选）
    - **status**: 状态（可选）
    - **sort**: 排序字段（可选）
    - **order**: 排序方向（可选）
    """
    try:
        # 构建查询
        query = db.query(Job)

        # 应用过滤条件
        if keywords:
            query = query.filter(
                Job.title.like(f"%{keywords}%") |
                Job.company_name.like(f"%{keywords}%") |
                Job.job_description.like(f"%{keywords}%")
            )

        if location:
            query = query.filter(Job.work_location.like(f"%{location}%"))

        if education:
            query = query.filter(Job.education_requirement.like(f"%{education}%"))

        if experience:
            query = query.filter(Job.experience_requirement.like(f"%{experience}%"))

        if job_type:
            query = query.filter(Job.job_type == job_type)

        if status:
            query = query.filter(Job.status == status)

        # 计算总数
        total = query.count()

        # 应用排序
        if sort and hasattr(Job, sort):
            sort_column = getattr(Job, sort)
            if order and order.lower() == "desc":
                query = query.order_by(desc(sort_column))
            else:
                query = query.order_by(asc(sort_column))
        else:
            # 默认按发布日期降序
            query = query.order_by(desc(Job.publish_date), desc(Job.id))

        # 分页
        query = query.offset((page - 1) * page_size).limit(page_size)

        # 执行查询
        jobs = query.all()

        # 构建结果
        job_list = []
        for job in jobs:
            job_list.append({
                "id": job.id,
                "job_id": job.job_id,
                "title": job.title,
                "company_name": job.company_name,
                "work_location": job.work_location,
                "salary_range": job.salary_range,
                "education_requirement": job.education_requirement,
                "experience_requirement": job.experience_requirement,
                "major_requirement": job.major_requirement,
                "publish_date": job.publish_date,
                "source_url": job.source_url,
                "is_graduate_friendly": job.is_graduate_friendly,
                "job_type": job.job_type,
                "data_source": job.data_source,
                "created_at": job.created_at,
                "updated_at": job.updated_at
            })

        return success_response(data={
            "items": job_list,
            "total": total,
            "page": page,
            "page_size": page_size,
            "pages": (total + page_size - 1) // page_size,
            "has_next": page * page_size < total,
            "has_prev": page > 1
        })
    except Exception as e:
        logger.error(f"获取岗位列表异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get("/jobs/{job_id}", response_model=Dict[str, Any])
async def get_job(
    job_id: str = Path(..., description="岗位ID"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("job:view"))
) -> Any:
    """
    获取岗位详情

    - **job_id**: 岗位ID
    """
    try:
        # 查询岗位
        job = db.query(Job).filter(Job.job_id == job_id).first()
        if not job:
            return error_response(
                message="岗位不存在",
                code=status.HTTP_404_NOT_FOUND
            )

        # 查询岗位详情
        job_detail = db.query(JobDetail).filter(JobDetail.job_id == job_id).first()

        # 构建结果
        result = {
            "id": job.id,
            "job_id": job.job_id,
            "title": job.title,
            "company_name": job.company_name,
            "work_location": job.work_location,
            "salary_range": job.salary_range,
            "job_description": job.job_description,
            "education_requirement": job.education_requirement,
            "experience_requirement": job.experience_requirement,
            "major_requirement": job.major_requirement,
            "publish_date": job.publish_date,
            "source_url": job.source_url,
            "is_graduate_friendly": job.is_graduate_friendly,
            "job_type": job.job_type,
            "data_source": job.data_source,
            "created_at": job.created_at,
            "updated_at": job.updated_at
        }

        # 添加岗位详情
        if job_detail:
            result["details"] = {
                "id": job_detail.id,
                "major_standardized": job_detail.major_standardized,
                "similar_majors": job_detail.similar_majors,
                "interview_questions": job_detail.interview_questions,
                "professional_questions": job_detail.professional_questions,
                "interview_preparation_tips": job_detail.interview_preparation_tips,
                "policy_summary": job_detail.policy_summary,
                "policy_key_points": job_detail.policy_key_points,
                "policy_special_notes": job_detail.policy_special_notes,
                "created_at": job_detail.created_at,
                "updated_at": job_detail.updated_at
            }

        return success_response(data=result)
    except Exception as e:
        logger.error(f"获取岗位详情异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.put("/jobs/{job_id}", response_model=Dict[str, Any])
async def update_job(
    job_id: str = Path(..., description="岗位ID"),
    job_data: JobUpdate = Depends(),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("job:edit"))
) -> Any:
    """
    更新岗位信息

    - **job_id**: 岗位ID
    """
    try:
        # 查询岗位
        job = db.query(Job).filter(Job.job_id == job_id).first()
        if not job:
            return error_response(
                message="岗位不存在",
                code=status.HTTP_404_NOT_FOUND
            )

        # 更新岗位信息
        if job_data.title is not None:
            setattr(job, "title", job_data.title)
        if job_data.company_name is not None:
            setattr(job, "company_name", job_data.company_name)
        if job_data.work_location is not None:
            setattr(job, "work_location", job_data.work_location)
        if job_data.salary_range is not None:
            setattr(job, "salary_range", job_data.salary_range)
        if job_data.job_description is not None:
            setattr(job, "job_description", job_data.job_description)
        if job_data.education_requirement is not None:
            setattr(job, "education_requirement", job_data.education_requirement)
        if job_data.experience_requirement is not None:
            setattr(job, "experience_requirement", job_data.experience_requirement)
        if job_data.major_requirement is not None:
            setattr(job, "major_requirement", job_data.major_requirement)
        if job_data.publish_date is not None:
            setattr(job, "publish_date", job_data.publish_date)
        if job_data.source_url is not None:
            setattr(job, "source_url", job_data.source_url)
        if job_data.is_graduate_friendly is not None:
            setattr(job, "is_graduate_friendly", job_data.is_graduate_friendly)
        if job_data.job_type is not None:
            setattr(job, "job_type", job_data.job_type)
        if job_data.data_source is not None:
            setattr(job, "data_source", job_data.data_source)

        db.commit()
        db.refresh(job)

        # 记录操作日志
        log = OperationLog(
            admin_id=current_admin.id,
            module="岗位管理",
            operation="更新岗位",
            content=f"更新岗位 {job.title}",
            ip="127.0.0.1",  # 实际应用中应获取真实IP
            user_agent="Unknown"  # 实际应用中应获取真实User-Agent
        )
        db.add(log)
        db.commit()

        return success_response(
            message="更新岗位成功",
            data={
                "id": job.id,
                "job_id": job.job_id,
                "title": job.title,
                "company_name": job.company_name,
                "work_location": job.work_location,
                "salary_range": job.salary_range,
                "education_requirement": job.education_requirement,
                "experience_requirement": job.experience_requirement,
                "major_requirement": job.major_requirement,
                "publish_date": job.publish_date,
                "source_url": job.source_url,
                "is_graduate_friendly": job.is_graduate_friendly,
                "job_type": job.job_type,
                "data_source": job.data_source,
                "created_at": job.created_at,
                "updated_at": job.updated_at
            }
        )
    except Exception as e:
        db.rollback()
        logger.error(f"更新岗位异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.delete("/jobs/{job_id}", response_model=Dict[str, Any])
async def delete_job(
    job_id: str = Path(..., description="岗位ID"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission("job:delete"))
) -> Any:
    """
    删除岗位

    - **job_id**: 岗位ID
    """
    try:
        # 查询岗位
        job = db.query(Job).filter(Job.job_id == job_id).first()
        if not job:
            return error_response(
                message="岗位不存在",
                code=status.HTTP_404_NOT_FOUND
            )

        # 记录操作日志
        log = OperationLog(
            admin_id=current_admin.id,
            module="岗位管理",
            operation="删除岗位",
            content=f"删除岗位 {job.title}",
            ip="127.0.0.1",  # 实际应用中应获取真实IP
            user_agent="Unknown"  # 实际应用中应获取真实User-Agent
        )
        db.add(log)

        # 删除岗位
        db.delete(job)
        db.commit()

        return success_response(message="删除岗位成功")
    except Exception as e:
        db.rollback()
        logger.error(f"删除岗位异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )