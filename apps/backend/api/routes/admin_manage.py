#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
管理员管理API路由
"""

from typing import Any, Dict, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.orm import Session
from sqlalchemy import desc

from apps.backend.api.config.database import get_db
from apps.backend.api.config.settings import settings
from apps.backend.api.dependencies.admin_auth import (
    get_current_active_admin, require_admin_manage, require_user_manage, require_user_view
)
from apps.backend.api.models.admin import Admin, AdminRole, AdminPermission, OperationLog
from apps.backend.api.schemas.admin import (
    AdminCreate, AdminUpdate, PermissionCreate, PermissionUpdate,
    RoleCreate, RoleUpdate
)
from apps.backend.api.schemas.base import PageParams
from apps.backend.api.utils.security import get_password_hash
from apps.backend.api.utils.response import success_response, error_response
from apps.backend.utils.logger import setup_logger

# 创建路由
router = APIRouter(tags=["admin_manage"])

# 设置日志
logger = setup_logger("api_admin_manage")


@router.get("/admins", response_model=Dict[str, Any])
async def get_admins(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    username: Optional[str] = Query(None, description="用户名"),
    role_id: Optional[int] = Query(None, description="角色ID"),
    status: Optional[bool] = Query(None, description="状态"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(require_admin_manage())
) -> Any:
    """
    获取管理员列表

    - **page**: 页码
    - **page_size**: 每页数量
    - **username**: 用户名（可选）
    - **role_id**: 角色ID（可选）
    - **status**: 状态（可选）
    """
    try:
        # 构建查询
        query = db.query(Admin)

        # 应用过滤条件
        if username:
            query = query.filter(Admin.username.like(f"%{username}%"))
        if role_id:
            query = query.filter(Admin.admin_role_id == role_id)
        if status is not None:
            query = query.filter(Admin.status == status)

        # 计算总数
        total = query.count()

        # 分页
        query = query.order_by(desc(Admin.id)).offset((page - 1) * page_size).limit(page_size)

        # 执行查询
        admins = query.all()

        # 构建结果
        admin_list = []
        for admin in admins:
            role = db.query(AdminRole).filter(AdminRole.id == admin.admin_role_id).first()
            admin_list.append({
                "id": admin.id,
                "username": admin.username,
                "real_name": admin.real_name,
                "email": admin.email,
                "phone": admin.phone,
                "role": {
                    "id": role.id,
                    "name": role.name,
                    "description": role.description
                } if role else None,
                "last_login": admin.last_login,
                "status": admin.status,
                "created_at": admin.created_at,
                "updated_at": admin.updated_at
            })

        return success_response(data={
            "items": admin_list,
            "total": total,
            "page": page,
            "page_size": page_size,
            "pages": (total + page_size - 1) // page_size,
            "has_next": page * page_size < total,
            "has_prev": page > 1
        })
    except Exception as e:
        logger.error(f"获取管理员列表异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.post("/admins", response_model=Dict[str, Any])
async def create_admin(
    admin_data: AdminCreate,
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(require_admin_manage())
) -> Any:
    """
    创建管理员
    """
    try:
        # 检查用户名是否已存在
        existing_admin = db.query(Admin).filter(Admin.username == admin_data.username).first()
        if existing_admin:
            return error_response(
                message="用户名已存在",
                code=status.HTTP_400_BAD_REQUEST
            )

        # 检查角色是否存在
        role = db.query(AdminRole).filter(AdminRole.id == admin_data.admin_role_id).first()
        if not role:
            return error_response(
                message="角色不存在",
                code=status.HTTP_400_BAD_REQUEST
            )

        # 创建管理员
        new_admin = Admin(
            username=admin_data.username,
            password_hash=get_password_hash(admin_data.password),
            real_name=admin_data.real_name,
            email=admin_data.email,
            phone=admin_data.phone,
            admin_role_id=admin_data.admin_role_id,
            status=admin_data.status
        )
        db.add(new_admin)
        db.commit()
        db.refresh(new_admin)

        # 记录操作日志
        log = OperationLog(
            admin_id=current_admin.id,
            module="系统管理",
            operation="创建管理员",
            content=f"创建管理员 {new_admin.username}",
            ip="127.0.0.1",  # 实际应用中应获取真实IP
            user_agent="Unknown"  # 实际应用中应获取真实User-Agent
        )
        db.add(log)
        db.commit()

        return success_response(
            message="创建管理员成功",
            data={
                "id": new_admin.id,
                "username": new_admin.username,
                "real_name": new_admin.real_name,
                "email": new_admin.email,
                "phone": new_admin.phone,
                "admin_role_id": new_admin.admin_role_id,
                "status": new_admin.status,
                "created_at": new_admin.created_at
            }
        )
    except Exception as e:
        db.rollback()
        logger.error(f"创建管理员异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get("/admins/{admin_id}", response_model=Dict[str, Any])
async def get_admin(
    admin_id: int = Path(..., ge=1, description="管理员ID"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(require_admin_manage())
) -> Any:
    """
    获取管理员详情

    - **admin_id**: 管理员ID
    """
    try:
        # 查询管理员
        admin = db.query(Admin).filter(Admin.id == admin_id).first()
        if not admin:
            return error_response(
                message="管理员不存在",
                code=status.HTTP_404_NOT_FOUND
            )

        # 查询角色
        role = db.query(AdminRole).filter(AdminRole.id == admin.admin_role_id).first()

        return success_response(data={
            "id": admin.id,
            "username": admin.username,
            "real_name": admin.real_name,
            "email": admin.email,
            "phone": admin.phone,
            "role": {
                "id": role.id,
                "name": role.name,
                "description": role.description
            } if role else None,
            "last_login": admin.last_login,
            "status": admin.status,
            "created_at": admin.created_at,
            "updated_at": admin.updated_at
        })
    except Exception as e:
        logger.error(f"获取管理员详情异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.put("/admins/{admin_id}", response_model=Dict[str, Any])
async def update_admin(
    admin_id: int = Path(..., ge=1, description="管理员ID"),
    admin_data: AdminUpdate = Depends(),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(require_admin_manage())
) -> Any:
    """
    更新管理员

    - **admin_id**: 管理员ID
    """
    try:
        # 查询管理员
        admin = db.query(Admin).filter(Admin.id == admin_id).first()
        if not admin:
            return error_response(
                message="管理员不存在",
                code=status.HTTP_404_NOT_FOUND
            )

        # 不允许修改超级管理员
        if admin_id == 1 and current_admin.id != 1:
            return error_response(
                message="不允许修改超级管理员",
                code=status.HTTP_403_FORBIDDEN
            )

        # 更新管理员信息
        if admin_data.real_name is not None:
            setattr(admin, "real_name", admin_data.real_name)
        if admin_data.email is not None:
            setattr(admin, "email", admin_data.email)
        if admin_data.phone is not None:
            setattr(admin, "phone", admin_data.phone)
        if admin_data.admin_role_id is not None:
            # 检查角色是否存在
            role = db.query(AdminRole).filter(AdminRole.id == admin_data.admin_role_id).first()
            if not role:
                return error_response(
                    message="角色不存在",
                    code=status.HTTP_400_BAD_REQUEST
                )
            setattr(admin, "admin_role_id", admin_data.admin_role_id)
        if admin_data.status is not None:
            setattr(admin, "status", admin_data.status)
        if admin_data.password:
            setattr(admin, "password_hash", get_password_hash(admin_data.password))

        db.commit()
        db.refresh(admin)

        # 记录操作日志
        log = OperationLog(
            admin_id=current_admin.id,
            module="系统管理",
            operation="更新管理员",
            content=f"更新管理员 {admin.username}",
            ip="127.0.0.1",  # 实际应用中应获取真实IP
            user_agent="Unknown"  # 实际应用中应获取真实User-Agent
        )
        db.add(log)
        db.commit()

        return success_response(
            message="更新管理员成功",
            data={
                "id": admin.id,
                "username": admin.username,
                "real_name": admin.real_name,
                "email": admin.email,
                "phone": admin.phone,
                "admin_role_id": admin.admin_role_id,
                "status": admin.status,
                "updated_at": admin.updated_at
            }
        )
    except Exception as e:
        db.rollback()
        logger.error(f"更新管理员异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.delete("/admins/{admin_id}", response_model=Dict[str, Any])
async def delete_admin(
    admin_id: int = Path(..., ge=1, description="管理员ID"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(require_admin_manage())
) -> Any:
    """
    删除管理员

    - **admin_id**: 管理员ID
    """
    try:
        # 查询管理员
        admin = db.query(Admin).filter(Admin.id == admin_id).first()
        if not admin:
            return error_response(
                message="管理员不存在",
                code=status.HTTP_404_NOT_FOUND
            )

        # 不允许删除超级管理员
        if admin_id == 1:
            return error_response(
                message="不允许删除超级管理员",
                code=status.HTTP_403_FORBIDDEN
            )

        # 不允许删除自己
        if admin_id == current_admin.id:
            return error_response(
                message="不允许删除自己",
                code=status.HTTP_403_FORBIDDEN
            )

        # 记录操作日志
        log = OperationLog(
            admin_id=current_admin.id,
            module="系统管理",
            operation="删除管理员",
            content=f"删除管理员 {admin.username}",
            ip="127.0.0.1",  # 实际应用中应获取真实IP
            user_agent="Unknown"  # 实际应用中应获取真实User-Agent
        )
        db.add(log)

        # 删除管理员
        db.delete(admin)
        db.commit()

        return success_response(message="删除管理员成功")
    except Exception as e:
        db.rollback()
        logger.error(f"删除管理员异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )