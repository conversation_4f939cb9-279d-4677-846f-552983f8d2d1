#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
管理员监控管理API路由
"""

import time
import psutil
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, Query, BackgroundTasks
from pydantic import BaseModel

from apps.backend.api.dependencies.auth import get_current_active_admin
from apps.backend.api.models.admin import Admin
from apps.backend.api.config.monitoring import get_monitoring_config, update_monitoring_config
from apps.backend.api.utils.metrics import metrics_collector, business_metrics
from apps.backend.api.utils.response import success_response, error_response
from apps.backend.utils.logger import setup_logger

# 创建路由器
router = APIRouter(
    prefix="/monitoring",
    tags=["admin-monitoring"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        500: {"description": "Internal server error"},
    },
)

# 设置日志
logger = setup_logger("admin_monitoring")


class MonitoringConfigRequest(BaseModel):
    """监控配置请求"""
    section: str
    config: Dict[str, Any]


@router.get("/config", response_model=Dict[str, Any])
async def get_monitoring_config_api(
    section: Optional[str] = Query(None, description="配置节名称"),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取监控配置
    
    获取系统监控配置信息
    """
    try:
        if section:
            config = get_monitoring_config(section)
            if not config:
                return error_response(message=f"配置节 {section} 不存在", code=404)
            
            return success_response(
                data={section: config},
                message=f"获取 {section} 配置成功"
            )
        else:
            # 返回所有配置（脱敏敏感信息）
            all_config = get_monitoring_config()
            sanitized_config = _sanitize_monitoring_config(all_config)
            return success_response(data=sanitized_config, message="获取监控配置成功")
    except Exception as e:
        logger.error(f"Error getting monitoring config: {e}")
        return error_response(message=str(e), code=500)


@router.put("/config", response_model=Dict[str, Any])
async def update_monitoring_config_api(
    request: MonitoringConfigRequest,
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    更新监控配置
    
    更新指定节的监控配置
    """
    try:
        success = update_monitoring_config(request.section, request.config)
        
        if success:
            logger.info(f"Monitoring config updated by admin {current_admin.username}: {request.section}")
            return success_response(message=f"配置节 {request.section} 更新成功")
        else:
            return error_response(message=f"配置节 {request.section} 不存在", code=404)
    except Exception as e:
        logger.error(f"Error updating monitoring config: {e}")
        return error_response(message=str(e), code=500)


@router.get("/status", response_model=Dict[str, Any])
async def get_monitoring_status(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取监控状态
    
    获取当前监控系统的运行状态
    """
    try:
        config = get_monitoring_config()
        
        status = {
            "enabled": config.get("enabled", False),
            "environment": config.get("environment", "unknown"),
            "service_name": config.get("service_name", "unknown"),
            "service_version": config.get("service_version", "unknown"),
            "components": {
                "prometheus": config.get("prometheus", {}).get("enabled", False),
                "metrics": config.get("metrics", {}).get("enabled", False),
                "system_metrics": config.get("system_metrics", {}).get("enabled", False),
                "application_metrics": config.get("application_metrics", {}).get("enabled", False),
                "alerting": config.get("alerting", {}).get("enabled", False),
                "health_checks": config.get("health_checks", {}).get("enabled", False),
                "performance": config.get("performance", {}).get("enabled", False)
            },
            "metrics_count": len(config.get("application_metrics", {}).get("metrics", {})),
            "alert_rules_count": len(config.get("alerting", {}).get("rules", {})),
            "timestamp": "2025-05-23T17:15:00"
        }
        
        return success_response(data=status, message="获取监控状态成功")
    except Exception as e:
        logger.error(f"Error getting monitoring status: {e}")
        return error_response(message=str(e), code=500)


@router.get("/metrics", response_model=Dict[str, Any])
async def get_current_metrics(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取当前指标
    
    获取系统当前的监控指标
    """
    try:
        # 获取Prometheus指标
        prometheus_metrics = metrics_collector.get_metrics()
        
        # 解析指标数据（简化版）
        metrics_summary = _parse_prometheus_metrics(prometheus_metrics)
        
        return success_response(data=metrics_summary, message="获取当前指标成功")
    except Exception as e:
        logger.error(f"Error getting current metrics: {e}")
        return error_response(message=str(e), code=500)


@router.get("/system", response_model=Dict[str, Any])
async def get_system_info(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取系统信息
    
    获取系统硬件和运行时信息
    """
    try:
        # CPU信息
        cpu_info = {
            "count": psutil.cpu_count(),
            "usage_percent": psutil.cpu_percent(interval=1),
            "load_average": psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None
        }
        
        # 内存信息
        memory = psutil.virtual_memory()
        memory_info = {
            "total": memory.total,
            "available": memory.available,
            "used": memory.used,
            "usage_percent": memory.percent
        }
        
        # 磁盘信息
        disk_info = []
        for partition in psutil.disk_partitions():
            try:
                disk_usage = psutil.disk_usage(partition.mountpoint)
                disk_info.append({
                    "device": partition.device,
                    "mountpoint": partition.mountpoint,
                    "fstype": partition.fstype,
                    "total": disk_usage.total,
                    "used": disk_usage.used,
                    "free": disk_usage.free,
                    "usage_percent": (disk_usage.used / disk_usage.total) * 100
                })
            except (PermissionError, FileNotFoundError):
                continue
        
        # 网络信息
        network_io = psutil.net_io_counters()
        network_info = {
            "bytes_sent": network_io.bytes_sent,
            "bytes_recv": network_io.bytes_recv,
            "packets_sent": network_io.packets_sent,
            "packets_recv": network_io.packets_recv
        }
        
        # 进程信息
        process_info = {
            "count": len(psutil.pids()),
            "current_process": {
                "pid": psutil.Process().pid,
                "memory_percent": psutil.Process().memory_percent(),
                "cpu_percent": psutil.Process().cpu_percent()
            }
        }
        
        system_info = {
            "cpu": cpu_info,
            "memory": memory_info,
            "disk": disk_info,
            "network": network_info,
            "process": process_info,
            "timestamp": time.time()
        }
        
        return success_response(data=system_info, message="获取系统信息成功")
    except Exception as e:
        logger.error(f"Error getting system info: {e}")
        return error_response(message=str(e), code=500)


@router.get("/health", response_model=Dict[str, Any])
async def get_health_status(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取健康状态
    
    获取系统各组件的健康状态
    """
    try:
        from apps.backend.api.middlewares.monitoring import HealthCheckMiddleware
        
        # 创建健康检查中间件实例
        health_middleware = HealthCheckMiddleware(None)
        
        # 执行健康检查
        health_status = await health_middleware._perform_health_checks()
        
        return success_response(data=health_status, message="获取健康状态成功")
    except Exception as e:
        logger.error(f"Error getting health status: {e}")
        return error_response(message=str(e), code=500)


@router.post("/metrics/update", response_model=Dict[str, Any])
async def update_business_metrics(
    background_tasks: BackgroundTasks,
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    更新业务指标
    
    手动触发业务指标更新
    """
    try:
        # 在后台更新业务指标
        background_tasks.add_task(_update_business_metrics_task, current_admin.username)
        
        return success_response(message="业务指标更新任务已启动")
    except Exception as e:
        logger.error(f"Error updating business metrics: {e}")
        return error_response(message=str(e), code=500)


@router.get("/alerts", response_model=Dict[str, Any])
async def get_alert_rules(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取告警规则
    
    获取配置的告警规则
    """
    try:
        config = get_monitoring_config("alerting")
        rules = config.get("rules", {})
        
        alert_summary = {
            "enabled": config.get("enabled", False),
            "total_rules": len(rules),
            "rules": []
        }
        
        for rule_name, rule_config in rules.items():
            alert_summary["rules"].append({
                "name": rule_name,
                "condition": rule_config.get("condition", ""),
                "duration": rule_config.get("duration", ""),
                "severity": rule_config.get("severity", ""),
                "message": rule_config.get("message", "")
            })
        
        return success_response(data=alert_summary, message="获取告警规则成功")
    except Exception as e:
        logger.error(f"Error getting alert rules: {e}")
        return error_response(message=str(e), code=500)


@router.post("/test/metric", response_model=Dict[str, Any])
async def test_metric(
    metric_name: str = Query(..., description="指标名称"),
    metric_type: str = Query("counter", description="指标类型"),
    value: float = Query(1.0, description="指标值"),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    测试指标
    
    测试指标收集功能
    """
    try:
        if metric_type == "counter":
            business_metrics.metrics.increment_counter(metric_name, value=value)
        elif metric_type == "gauge":
            business_metrics.metrics.set_gauge(metric_name, value)
        elif metric_type == "histogram":
            business_metrics.metrics.observe_histogram(metric_name, value)
        else:
            return error_response(message=f"不支持的指标类型: {metric_type}", code=400)
        
        logger.info(f"Test metric recorded by admin {current_admin.username}: {metric_name}={value}")
        
        return success_response(
            data={
                "metric_name": metric_name,
                "metric_type": metric_type,
                "value": value
            },
            message="测试指标记录成功"
        )
    except Exception as e:
        logger.error(f"Error testing metric: {e}")
        return error_response(message=str(e), code=500)


@router.get("/prometheus/config", response_model=Dict[str, Any])
async def get_prometheus_config(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取Prometheus配置
    
    生成Prometheus配置文件内容
    """
    try:
        config = get_monitoring_config("prometheus")
        metrics_config = get_monitoring_config("metrics")
        
        prometheus_config = {
            "global": {
                "scrape_interval": config.get("scrape_interval", "15s"),
                "evaluation_interval": config.get("evaluation_interval", "15s")
            },
            "scrape_configs": [
                {
                    "job_name": "recruitment-api",
                    "static_configs": [
                        {
                            "targets": [f"localhost:{metrics_config.get('port', 8001)}"]
                        }
                    ],
                    "metrics_path": metrics_config.get("path", "/metrics"),
                    "scrape_interval": "15s"
                }
            ]
        }
        
        return success_response(data=prometheus_config, message="获取Prometheus配置成功")
    except Exception as e:
        logger.error(f"Error getting Prometheus config: {e}")
        return error_response(message=str(e), code=500)


async def _update_business_metrics_task(admin_username: str):
    """更新业务指标后台任务"""
    try:
        logger.info(f"Starting business metrics update task initiated by {admin_username}")
        
        # 更新业务计数器
        business_metrics.update_business_counters()
        
        logger.info("Business metrics update task completed")
    except Exception as e:
        logger.error(f"Business metrics update task failed: {e}")


def _sanitize_monitoring_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """脱敏监控配置"""
    sanitized = {}
    sensitive_keys = ["password", "token", "key", "secret", "webhook_url"]
    
    for key, value in config.items():
        if any(sensitive in key.lower() for sensitive in sensitive_keys):
            sanitized[key] = "***"
        elif isinstance(value, dict):
            sanitized[key] = _sanitize_monitoring_config(value)
        else:
            sanitized[key] = value
    
    return sanitized


def _parse_prometheus_metrics(metrics_text: str) -> Dict[str, Any]:
    """解析Prometheus指标文本"""
    try:
        lines = metrics_text.strip().split('\n')
        metrics_summary = {
            "total_metrics": 0,
            "sample_metrics": [],
            "metric_types": {}
        }
        
        for line in lines:
            if line.startswith('#'):
                continue
            
            if ' ' in line:
                metric_name = line.split(' ')[0]
                metric_value = line.split(' ')[1]
                
                metrics_summary["total_metrics"] += 1
                
                # 添加样本指标（最多10个）
                if len(metrics_summary["sample_metrics"]) < 10:
                    metrics_summary["sample_metrics"].append({
                        "name": metric_name,
                        "value": metric_value
                    })
                
                # 统计指标类型
                metric_type = "unknown"
                if "_total" in metric_name:
                    metric_type = "counter"
                elif "_bucket" in metric_name:
                    metric_type = "histogram"
                elif "_sum" in metric_name or "_count" in metric_name:
                    metric_type = "summary"
                else:
                    metric_type = "gauge"
                
                metrics_summary["metric_types"][metric_type] = metrics_summary["metric_types"].get(metric_type, 0) + 1
        
        return metrics_summary
    except Exception as e:
        logger.error(f"Error parsing Prometheus metrics: {e}")
        return {"error": str(e)}
