#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
管理员性能测试管理API路由
"""

import os
import json
import time
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, Query, BackgroundTasks
from pydantic import BaseModel

from apps.backend.api.dependencies.auth import get_current_active_admin
from apps.backend.api.models.admin import Admin
from apps.backend.api.config.performance_testing import get_performance_config
from apps.backend.api.utils.performance_monitor import get_performance_monitor
from apps.backend.api.utils.response import success_response, error_response
from apps.backend.utils.logger import setup_logger

# 创建路由器
router = APIRouter(
    prefix="/performance",
    tags=["admin-performance"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        500: {"description": "Internal server error"},
    },
)

# 设置日志
logger = setup_logger("admin_performance")


class PerformanceTestRequest(BaseModel):
    """性能测试请求"""
    test_type: str = "load_test"
    concurrent_users: int = 10
    total_requests: int = 100
    test_duration: int = 60


@router.get("/config", response_model=Dict[str, Any])
async def get_performance_config_info(
    section: Optional[str] = Query(None, description="配置节名称"),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取性能测试配置
    
    获取性能测试相关配置信息
    """
    try:
        if section:
            config = get_performance_config(section)
            if not config:
                return error_response(message=f"配置节 {section} 不存在", code=404)
            
            return success_response(
                data={section: config},
                message=f"获取 {section} 配置成功"
            )
        else:
            # 返回所有配置
            all_config = get_performance_config()
            return success_response(data=all_config, message="获取性能测试配置成功")
    except Exception as e:
        logger.error(f"Error getting performance config: {e}")
        return error_response(message=str(e), code=500)


@router.get("/monitor/status", response_model=Dict[str, Any])
async def get_monitor_status(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取性能监控状态
    
    获取当前性能监控的状态信息
    """
    try:
        monitor = get_performance_monitor()
        current_metrics = monitor.get_current_metrics()
        
        return success_response(data=current_metrics, message="获取性能监控状态成功")
    except Exception as e:
        logger.error(f"Error getting monitor status: {e}")
        return error_response(message=str(e), code=500)


@router.get("/monitor/metrics", response_model=Dict[str, Any])
async def get_performance_metrics(
    duration: int = Query(300, description="时间范围（秒）"),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取性能指标
    
    获取指定时间范围内的性能指标数据
    """
    try:
        monitor = get_performance_monitor()
        metrics_summary = monitor.get_metrics_summary(duration)
        
        return success_response(data=metrics_summary, message="获取性能指标成功")
    except Exception as e:
        logger.error(f"Error getting performance metrics: {e}")
        return error_response(message=str(e), code=500)


@router.post("/monitor/start", response_model=Dict[str, Any])
async def start_performance_monitoring(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    启动性能监控
    
    启动系统性能监控
    """
    try:
        monitor = get_performance_monitor()
        monitor.start_monitoring()
        
        logger.info(f"Performance monitoring started by admin {current_admin.username}")
        
        return success_response(message="性能监控已启动")
    except Exception as e:
        logger.error(f"Error starting performance monitoring: {e}")
        return error_response(message=str(e), code=500)


@router.post("/monitor/stop", response_model=Dict[str, Any])
async def stop_performance_monitoring(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    停止性能监控
    
    停止系统性能监控
    """
    try:
        monitor = get_performance_monitor()
        monitor.stop_monitoring()
        
        logger.info(f"Performance monitoring stopped by admin {current_admin.username}")
        
        return success_response(message="性能监控已停止")
    except Exception as e:
        logger.error(f"Error stopping performance monitoring: {e}")
        return error_response(message=str(e), code=500)


@router.post("/monitor/reset", response_model=Dict[str, Any])
async def reset_performance_metrics(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    重置性能指标
    
    清空当前收集的性能指标数据
    """
    try:
        monitor = get_performance_monitor()
        monitor.reset_metrics()
        
        logger.info(f"Performance metrics reset by admin {current_admin.username}")
        
        return success_response(message="性能指标已重置")
    except Exception as e:
        logger.error(f"Error resetting performance metrics: {e}")
        return error_response(message=str(e), code=500)


@router.post("/test/run", response_model=Dict[str, Any])
async def run_performance_test(
    request: PerformanceTestRequest,
    background_tasks: BackgroundTasks,
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    运行性能测试
    
    启动性能测试任务
    """
    try:
        # 在后台执行性能测试
        background_tasks.add_task(
            _run_performance_test_task,
            request.dict(),
            current_admin.username
        )
        
        return success_response(message="性能测试任务已启动")
    except Exception as e:
        logger.error(f"Error running performance test: {e}")
        return error_response(message=str(e), code=500)


@router.get("/test/reports", response_model=Dict[str, Any])
async def get_performance_reports(
    limit: int = Query(10, description="返回报告数量"),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取性能测试报告
    
    获取历史性能测试报告列表
    """
    try:
        reports_dir = "performance_reports"
        reports = []
        
        if os.path.exists(reports_dir):
            # 获取所有报告文件
            report_files = [f for f in os.listdir(reports_dir) if f.endswith('.json')]
            report_files.sort(reverse=True)  # 按时间倒序
            
            for report_file in report_files[:limit]:
                try:
                    file_path = os.path.join(reports_dir, report_file)
                    with open(file_path, 'r', encoding='utf-8') as f:
                        report_data = json.load(f)
                    
                    # 提取报告摘要
                    report_summary = {
                        "filename": report_file,
                        "timestamp": report_data.get("timestamp"),
                        "test_duration": report_data.get("test_info", {}).get("duration"),
                        "performance_score": report_data.get("performance_analysis", {}).get("performance_score"),
                        "overall_status": report_data.get("performance_analysis", {}).get("overall_status"),
                        "issues_count": len(report_data.get("performance_analysis", {}).get("issues", []))
                    }
                    reports.append(report_summary)
                    
                except Exception as e:
                    logger.error(f"Error reading report file {report_file}: {e}")
        
        return success_response(
            data={"reports": reports, "total": len(reports)},
            message="获取性能测试报告成功"
        )
    except Exception as e:
        logger.error(f"Error getting performance reports: {e}")
        return error_response(message=str(e), code=500)


@router.get("/test/reports/{filename}", response_model=Dict[str, Any])
async def get_performance_report_detail(
    filename: str,
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取性能测试报告详情
    
    获取指定性能测试报告的详细内容
    """
    try:
        reports_dir = "performance_reports"
        file_path = os.path.join(reports_dir, filename)
        
        if not os.path.exists(file_path):
            return error_response(message="报告文件不存在", code=404)
        
        with open(file_path, 'r', encoding='utf-8') as f:
            report_data = json.load(f)
        
        return success_response(data=report_data, message="获取报告详情成功")
    except Exception as e:
        logger.error(f"Error getting performance report detail: {e}")
        return error_response(message=str(e), code=500)


@router.get("/system/resources", response_model=Dict[str, Any])
async def get_system_resources(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取系统资源使用情况
    
    获取当前系统的资源使用情况
    """
    try:
        import psutil
        
        # CPU信息
        cpu_info = {
            "usage_percent": psutil.cpu_percent(interval=1),
            "count": psutil.cpu_count(),
            "freq": psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None
        }
        
        # 内存信息
        memory = psutil.virtual_memory()
        memory_info = {
            "total": memory.total,
            "available": memory.available,
            "used": memory.used,
            "percent": memory.percent
        }
        
        # 磁盘信息
        disk = psutil.disk_usage('/')
        disk_info = {
            "total": disk.total,
            "used": disk.used,
            "free": disk.free,
            "percent": (disk.used / disk.total) * 100
        }
        
        # 网络信息
        network = psutil.net_io_counters()
        network_info = {
            "bytes_sent": network.bytes_sent,
            "bytes_recv": network.bytes_recv,
            "packets_sent": network.packets_sent,
            "packets_recv": network.packets_recv
        }
        
        system_resources = {
            "cpu": cpu_info,
            "memory": memory_info,
            "disk": disk_info,
            "network": network_info,
            "timestamp": time.time()
        }
        
        return success_response(data=system_resources, message="获取系统资源信息成功")
    except Exception as e:
        logger.error(f"Error getting system resources: {e}")
        return error_response(message=str(e), code=500)


async def _run_performance_test_task(test_config: Dict[str, Any], admin_username: str):
    """执行性能测试的后台任务"""
    try:
        logger.info(f"Starting performance test task initiated by {admin_username}")
        logger.info(f"Test config: {test_config}")
        
        # 这里可以集成实际的性能测试执行逻辑
        # 暂时记录日志
        logger.info("Performance test task completed")
        
    except Exception as e:
        logger.error(f"Performance test task failed: {e}")
