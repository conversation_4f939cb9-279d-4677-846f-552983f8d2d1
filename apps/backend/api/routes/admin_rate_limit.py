#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
管理员限流管理API路由
"""

from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, Query, Body
from pydantic import BaseModel

from apps.backend.api.dependencies.auth import get_current_active_admin
from apps.backend.api.models.admin import Admin
from apps.backend.api.config.rate_limit import (
    get_rate_limit_config, update_rate_limit_config, RateLimitMatcher
)
from apps.backend.api.utils.rate_limiter import rate_limiter_factory
from apps.backend.api.utils.response import success_response, error_response
from apps.backend.utils.logger import setup_logger

# 创建路由器
router = APIRouter(
    prefix="/rate-limit",
    tags=["admin-rate-limit"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        500: {"description": "Internal server error"},
    },
)

# 设置日志
logger = setup_logger("admin_rate_limit")


class RateLimitConfigRequest(BaseModel):
    """限流配置请求"""
    section: str
    config: Dict[str, Any]


class RateLimitTestRequest(BaseModel):
    """限流测试请求"""
    key: str
    strategy: str = "sliding_window"
    limit: int = 10
    window: int = 60
    requests: int = 15


@router.get("/config", response_model=Dict[str, Any])
async def get_rate_limit_config_api(
    section: Optional[str] = Query(None, description="配置节名称"),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取限流配置
    
    获取系统限流配置信息
    """
    try:
        if section:
            config = get_rate_limit_config(section)
            if not config:
                return error_response(message=f"配置节 {section} 不存在", code=404)
            
            return success_response(
                data={section: config},
                message=f"获取 {section} 配置成功"
            )
        else:
            # 返回所有配置
            all_config = get_rate_limit_config()
            return success_response(data=all_config, message="获取限流配置成功")
    except Exception as e:
        logger.error(f"Error getting rate limit config: {e}")
        return error_response(message=str(e), code=500)


@router.put("/config", response_model=Dict[str, Any])
async def update_rate_limit_config_api(
    request: RateLimitConfigRequest,
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    更新限流配置
    
    更新指定节的限流配置
    """
    try:
        success = update_rate_limit_config(request.section, request.config)
        
        if success:
            logger.info(f"Rate limit config updated by admin {current_admin.username}: {request.section}")
            return success_response(message=f"配置节 {request.section} 更新成功")
        else:
            return error_response(message=f"配置节 {request.section} 不存在", code=404)
    except Exception as e:
        logger.error(f"Error updating rate limit config: {e}")
        return error_response(message=str(e), code=500)


@router.get("/status", response_model=Dict[str, Any])
async def get_rate_limit_status(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取限流状态
    
    获取当前限流系统的运行状态
    """
    try:
        config = get_rate_limit_config()
        
        status = {
            "enabled": config.get("enabled", False),
            "strategy": config.get("strategy", "sliding_window"),
            "storage": config.get("storage", "redis"),
            "levels": {
                "ip_limits": config.get("ip_limits", {}).get("enabled", False),
                "user_limits": config.get("user_limits", {}).get("enabled", False),
                "endpoint_limits": len(config.get("endpoint_limits", {})),
                "user_group_limits": len(config.get("user_group_limits", {}))
            },
            "monitoring": config.get("monitoring", {}).get("enabled", False),
            "timestamp": "2025-05-23T17:00:00"
        }
        
        return success_response(data=status, message="获取限流状态成功")
    except Exception as e:
        logger.error(f"Error getting rate limit status: {e}")
        return error_response(message=str(e), code=500)


@router.post("/test", response_model=Dict[str, Any])
async def test_rate_limiter(
    request: RateLimitTestRequest,
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    测试限流器
    
    测试指定配置的限流器性能
    """
    try:
        # 创建限流器
        limiter = rate_limiter_factory.create_limiter(
            strategy=request.strategy,
            key=f"test:{request.key}",
            limit=request.limit,
            window=request.window
        )
        
        # 执行测试请求
        results = []
        allowed_count = 0
        blocked_count = 0
        
        for i in range(request.requests):
            allowed, info = limiter.is_allowed()
            
            if allowed:
                allowed_count += 1
            else:
                blocked_count += 1
            
            results.append({
                "request": i + 1,
                "allowed": allowed,
                "remaining": info.get("remaining", 0),
                "current": info.get("current", 0)
            })
        
        # 清理测试数据
        limiter.reset()
        
        test_result = {
            "strategy": request.strategy,
            "limit": request.limit,
            "window": request.window,
            "total_requests": request.requests,
            "allowed_requests": allowed_count,
            "blocked_requests": blocked_count,
            "block_rate": blocked_count / request.requests,
            "results": results[:10]  # 只返回前10个结果
        }
        
        logger.info(f"Rate limiter test completed by admin {current_admin.username}")
        
        return success_response(data=test_result, message="限流器测试完成")
    except Exception as e:
        logger.error(f"Error testing rate limiter: {e}")
        return error_response(message=str(e), code=500)


@router.get("/metrics", response_model=Dict[str, Any])
async def get_rate_limit_metrics(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取限流指标
    
    获取限流系统的运行指标
    """
    try:
        # 这里应该从实际的中间件获取指标
        # 为了演示，返回模拟数据
        
        metrics = {
            "total_requests": 12500,
            "blocked_requests": 125,
            "block_rate": 0.01,
            "rate_limit_hits": {
                "ip": 45,
                "user": 30,
                "endpoint": 25,
                "global": 25
            },
            "top_blocked_ips": [
                {"ip": "*************", "count": 15},
                {"ip": "*************", "count": 12},
                {"ip": "*************", "count": 8}
            ],
            "top_blocked_endpoints": [
                {"endpoint": "/api/v1/auth/login", "count": 20},
                {"endpoint": "/api/v1/jobs/search", "count": 15},
                {"endpoint": "/api/v1/upload", "count": 10}
            ],
            "hourly_stats": [
                {"hour": "16:00", "requests": 1200, "blocked": 12},
                {"hour": "17:00", "requests": 1350, "blocked": 15},
                {"hour": "18:00", "requests": 1100, "blocked": 8}
            ],
            "timestamp": "2025-05-23T17:00:00"
        }
        
        return success_response(data=metrics, message="获取限流指标成功")
    except Exception as e:
        logger.error(f"Error getting rate limit metrics: {e}")
        return error_response(message=str(e), code=500)


@router.delete("/reset/{key}", response_model=Dict[str, Any])
async def reset_rate_limiter(
    key: str,
    strategy: str = Query("sliding_window", description="限流策略"),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    重置限流器
    
    重置指定键的限流计数
    """
    try:
        # 创建限流器并重置
        limiter = rate_limiter_factory.create_limiter(
            strategy=strategy,
            key=key,
            limit=100,  # 临时值
            window=60   # 临时值
        )
        
        success = limiter.reset()
        
        if success:
            logger.info(f"Rate limiter reset by admin {current_admin.username}: {key}")
            return success_response(message=f"限流器 {key} 重置成功")
        else:
            return error_response(message="重置失败", code=500)
    except Exception as e:
        logger.error(f"Error resetting rate limiter: {e}")
        return error_response(message=str(e), code=500)


@router.get("/rules", response_model=Dict[str, Any])
async def get_rate_limit_rules(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取限流规则
    
    获取所有配置的限流规则
    """
    try:
        config = get_rate_limit_config()
        
        rules = {
            "default_limits": config.get("default_limits", {}),
            "ip_limits": config.get("ip_limits", {}),
            "user_limits": config.get("user_limits", {}),
            "endpoint_limits": config.get("endpoint_limits", {}),
            "user_group_limits": config.get("user_group_limits", {}),
            "total_endpoints": len(config.get("endpoint_limits", {})),
            "total_user_groups": len(config.get("user_group_limits", {})),
            "ip_whitelist_count": len(config.get("ip_limits", {}).get("whitelist", [])),
            "ip_blacklist_count": len(config.get("ip_limits", {}).get("blacklist", []))
        }
        
        return success_response(data=rules, message="获取限流规则成功")
    except Exception as e:
        logger.error(f"Error getting rate limit rules: {e}")
        return error_response(message=str(e), code=500)


@router.post("/rules/endpoint", response_model=Dict[str, Any])
async def add_endpoint_rule(
    endpoint: str = Body(..., description="端点路径"),
    limits: Dict[str, int] = Body(..., description="限制配置"),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    添加端点限流规则
    
    为指定端点添加限流规则
    """
    try:
        config = get_rate_limit_config("endpoint_limits")
        config[endpoint] = limits
        
        success = update_rate_limit_config("endpoint_limits", config)
        
        if success:
            logger.info(f"Endpoint rule added by admin {current_admin.username}: {endpoint}")
            return success_response(message=f"端点 {endpoint} 限流规则添加成功")
        else:
            return error_response(message="添加规则失败", code=500)
    except Exception as e:
        logger.error(f"Error adding endpoint rule: {e}")
        return error_response(message=str(e), code=500)


@router.delete("/rules/endpoint/{endpoint:path}", response_model=Dict[str, Any])
async def remove_endpoint_rule(
    endpoint: str,
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    删除端点限流规则
    
    删除指定端点的限流规则
    """
    try:
        config = get_rate_limit_config("endpoint_limits")
        
        if endpoint in config:
            del config[endpoint]
            success = update_rate_limit_config("endpoint_limits", config)
            
            if success:
                logger.info(f"Endpoint rule removed by admin {current_admin.username}: {endpoint}")
                return success_response(message=f"端点 {endpoint} 限流规则删除成功")
            else:
                return error_response(message="删除规则失败", code=500)
        else:
            return error_response(message=f"端点 {endpoint} 规则不存在", code=404)
    except Exception as e:
        logger.error(f"Error removing endpoint rule: {e}")
        return error_response(message=str(e), code=500)


@router.get("/health", response_model=Dict[str, Any])
async def check_rate_limit_health(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    检查限流系统健康状态
    
    检查限流系统各组件的健康状态
    """
    try:
        from apps.backend.api.config.redis import get_redis
        
        # 检查Redis连接
        redis_client = get_redis()
        redis_healthy = False
        try:
            redis_client.ping()
            redis_healthy = True
        except Exception:
            pass
        
        # 检查配置
        config = get_rate_limit_config()
        config_healthy = bool(config and config.get("enabled") is not None)
        
        # 测试限流器创建
        limiter_healthy = False
        try:
            test_limiter = rate_limiter_factory.create_limiter(
                strategy="sliding_window",
                key="health_check",
                limit=1,
                window=60
            )
            test_limiter.reset()
            limiter_healthy = True
        except Exception:
            pass
        
        health_status = {
            "overall_status": "healthy" if all([redis_healthy, config_healthy, limiter_healthy]) else "unhealthy",
            "components": {
                "redis": {
                    "status": "healthy" if redis_healthy else "unhealthy",
                    "message": "Redis连接正常" if redis_healthy else "Redis连接失败"
                },
                "config": {
                    "status": "healthy" if config_healthy else "unhealthy",
                    "message": "配置加载正常" if config_healthy else "配置加载失败"
                },
                "limiter": {
                    "status": "healthy" if limiter_healthy else "unhealthy",
                    "message": "限流器创建正常" if limiter_healthy else "限流器创建失败"
                }
            },
            "enabled": config.get("enabled", False),
            "timestamp": "2025-05-23T17:00:00"
        }
        
        return success_response(data=health_status, message="限流系统健康检查完成")
    except Exception as e:
        logger.error(f"Error checking rate limit health: {e}")
        return error_response(message=str(e), code=500)
