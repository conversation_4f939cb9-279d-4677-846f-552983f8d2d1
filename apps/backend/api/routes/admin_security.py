#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
管理员安全管理API路由
"""

from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, Query, Body
from pydantic import BaseModel

from apps.backend.api.dependencies.auth import get_current_active_admin
from apps.backend.api.models.admin import Admin
from apps.backend.api.utils.validators import input_validator, ValidationError
from apps.backend.api.utils.password_security import password_security, token_security
from apps.backend.api.config.security import SECURITY_CONFIG, get_security_config
from apps.backend.api.utils.response import success_response, error_response
from apps.backend.utils.logger import setup_logger

# 创建路由器
router = APIRouter(
    prefix="/security",
    tags=["admin-security"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        500: {"description": "Internal server error"},
    },
)

# 设置日志
logger = setup_logger("admin_security")


class PasswordStrengthRequest(BaseModel):
    """密码强度检查请求"""
    password: str


class GeneratePasswordRequest(BaseModel):
    """生成密码请求"""
    length: int = 12


class SecurityConfigRequest(BaseModel):
    """安全配置请求"""
    section: str
    config: Dict[str, Any]


@router.get("/config", response_model=Dict[str, Any])
async def get_security_config_api(
    section: Optional[str] = Query(None, description="配置节名称"),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取安全配置
    
    获取系统安全配置信息
    """
    try:
        if section:
            config = get_security_config(section)
            if not config:
                return error_response(message=f"配置节 {section} 不存在", code=404)
            
            # 脱敏敏感配置
            sanitized_config = _sanitize_config(config)
            
            return success_response(
                data={section: sanitized_config},
                message=f"获取 {section} 配置成功"
            )
        else:
            # 返回所有配置（脱敏后）
            all_config = {}
            for key, value in SECURITY_CONFIG.items():
                all_config[key] = _sanitize_config(value)
            
            return success_response(data=all_config, message="获取安全配置成功")
    except Exception as e:
        logger.error(f"Error getting security config: {e}")
        return error_response(message=str(e), code=500)


@router.post("/password/check-strength", response_model=Dict[str, Any])
async def check_password_strength(
    request: PasswordStrengthRequest,
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    检查密码强度
    
    检查给定密码的强度和安全性
    """
    try:
        # 验证输入
        password = input_validator.validate_string(request.password, "密码", allow_empty=False)
        
        # 检查密码强度
        strength_result = password_security.check_password_strength(password)
        
        return success_response(data=strength_result, message="密码强度检查完成")
    except ValidationError as e:
        return error_response(message=str(e), code=400)
    except Exception as e:
        logger.error(f"Error checking password strength: {e}")
        return error_response(message=str(e), code=500)


@router.post("/password/generate", response_model=Dict[str, Any])
async def generate_secure_password(
    request: GeneratePasswordRequest,
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    生成安全密码
    
    生成符合安全要求的随机密码
    """
    try:
        # 验证长度
        length = input_validator.validate_integer(request.length, "密码长度", min_value=8, max_value=128)
        
        # 生成密码
        password = password_security.generate_secure_password(length)
        
        # 检查生成的密码强度
        strength_result = password_security.check_password_strength(password)
        
        return success_response(
            data={
                "password": password,
                "strength": strength_result
            },
            message="安全密码生成成功"
        )
    except ValidationError as e:
        return error_response(message=str(e), code=400)
    except Exception as e:
        logger.error(f"Error generating password: {e}")
        return error_response(message=str(e), code=500)


@router.get("/tokens/generate", response_model=Dict[str, Any])
async def generate_tokens(
    token_type: str = Query("secure", description="令牌类型: secure, api_key"),
    length: int = Query(32, ge=16, le=128, description="令牌长度"),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    生成安全令牌
    
    生成各种类型的安全令牌
    """
    try:
        if token_type == "secure":
            token = token_security.generate_secure_token(length)
        elif token_type == "api_key":
            token = token_security.generate_api_key()
        else:
            return error_response(message="不支持的令牌类型", code=400)
        
        # 生成令牌哈希（用于存储）
        token_hash = token_security.hash_token(token)
        
        return success_response(
            data={
                "token": token,
                "token_hash": token_hash,
                "type": token_type,
                "length": len(token)
            },
            message=f"{token_type} 令牌生成成功"
        )
    except Exception as e:
        logger.error(f"Error generating token: {e}")
        return error_response(message=str(e), code=500)


@router.get("/failed-attempts", response_model=Dict[str, Any])
async def get_failed_attempts(
    identifier: Optional[str] = Query(None, description="标识符"),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取失败尝试记录
    
    获取登录失败尝试的记录
    """
    try:
        if identifier:
            # 检查特定标识符的锁定状态
            is_locked, unlock_time = password_security.is_locked(identifier)
            
            return success_response(
                data={
                    "identifier": identifier,
                    "is_locked": is_locked,
                    "unlock_time": unlock_time.isoformat() if unlock_time else None,
                    "failed_attempts": len(password_security.failed_attempts.get(identifier, []))
                },
                message="获取失败尝试记录成功"
            )
        else:
            # 返回所有失败尝试统计
            stats = {}
            for ident, attempts in password_security.failed_attempts.items():
                is_locked, unlock_time = password_security.is_locked(ident)
                stats[ident] = {
                    "failed_attempts": len(attempts),
                    "is_locked": is_locked,
                    "unlock_time": unlock_time.isoformat() if unlock_time else None
                }
            
            return success_response(data=stats, message="获取失败尝试统计成功")
    except Exception as e:
        logger.error(f"Error getting failed attempts: {e}")
        return error_response(message=str(e), code=500)


@router.delete("/failed-attempts/{identifier}", response_model=Dict[str, Any])
async def clear_failed_attempts(
    identifier: str,
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    清除失败尝试记录
    
    清除指定标识符的失败尝试记录，解除锁定
    """
    try:
        # 验证标识符
        identifier = input_validator.validate_string(identifier, "标识符", allow_empty=False)
        
        # 清除失败尝试记录
        password_security.clear_failed_attempts(identifier)
        
        logger.info(f"Failed attempts cleared for {identifier} by admin {current_admin.username}")
        
        return success_response(message=f"已清除 {identifier} 的失败尝试记录")
    except ValidationError as e:
        return error_response(message=str(e), code=400)
    except Exception as e:
        logger.error(f"Error clearing failed attempts: {e}")
        return error_response(message=str(e), code=500)


@router.get("/audit/events", response_model=Dict[str, Any])
async def get_security_events(
    event_type: Optional[str] = Query(None, description="事件类型"),
    start_date: Optional[str] = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="结束日期 (YYYY-MM-DD)"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取安全事件日志
    
    获取系统安全事件的审计日志
    """
    try:
        # 这里应该从日志文件或数据库中读取安全事件
        # 为了演示，返回模拟数据
        
        events = [
            {
                "id": 1,
                "event_type": "user_login",
                "timestamp": "2025-05-23T16:45:00",
                "details": {"user": "test_user", "ip": "*************", "success": True},
                "severity": "info"
            },
            {
                "id": 2,
                "event_type": "security_violation",
                "timestamp": "2025-05-23T16:40:00",
                "details": {"type": "sql_injection", "ip": "*************", "blocked": True},
                "severity": "warning"
            }
        ]
        
        # 过滤事件
        if event_type:
            events = [e for e in events if e["event_type"] == event_type]
        
        # 分页
        total = len(events)
        start = (page - 1) * page_size
        end = start + page_size
        page_events = events[start:end]
        
        result = {
            "total": total,
            "page": page,
            "page_size": page_size,
            "pages": (total + page_size - 1) // page_size,
            "has_next": page * page_size < total,
            "has_prev": page > 1,
            "items": page_events
        }
        
        return success_response(data=result, message="获取安全事件成功")
    except Exception as e:
        logger.error(f"Error getting security events: {e}")
        return error_response(message=str(e), code=500)


@router.get("/health", response_model=Dict[str, Any])
async def check_security_health(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    检查安全系统健康状态
    
    检查各种安全功能的状态
    """
    try:
        health_status = {
            "overall_status": "healthy",
            "components": {
                "password_security": {
                    "status": "healthy",
                    "active_lockouts": len([
                        ident for ident in password_security.failed_attempts.keys()
                        if password_security.is_locked(ident)[0]
                    ])
                },
                "input_validation": {
                    "status": "healthy",
                    "enabled": True
                },
                "rate_limiting": {
                    "status": "healthy",
                    "enabled": get_security_config("rate_limiting").get("enabled", True)
                },
                "csrf_protection": {
                    "status": "healthy",
                    "enabled": get_security_config("csrf_protection").get("enabled", True)
                },
                "xss_protection": {
                    "status": "healthy",
                    "enabled": get_security_config("xss_protection").get("enabled", True)
                }
            },
            "timestamp": "2025-05-23T16:45:00"
        }
        
        return success_response(data=health_status, message="安全系统健康检查完成")
    except Exception as e:
        logger.error(f"Error checking security health: {e}")
        return error_response(message=str(e), code=500)


def _sanitize_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    脱敏配置信息
    
    Args:
        config: 原始配置
        
    Returns:
        Dict[str, Any]: 脱敏后的配置
    """
    sanitized = {}
    sensitive_keys = ["secret_key", "password", "key", "token"]
    
    for key, value in config.items():
        if any(sensitive in key.lower() for sensitive in sensitive_keys):
            sanitized[key] = "***"
        elif isinstance(value, dict):
            sanitized[key] = _sanitize_config(value)
        else:
            sanitized[key] = value
    
    return sanitized
