#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
管理员安全审计管理API路由
"""

import os
import json
import time
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, Query, BackgroundTasks
from pydantic import BaseModel

from apps.backend.api.dependencies.auth import get_current_active_admin
from apps.backend.api.models.admin import Admin
from apps.backend.api.config.security_audit import get_security_audit_config
from apps.backend.api.utils.response import success_response, error_response
from apps.backend.utils.logger import setup_logger

# 创建路由器
router = APIRouter(
    prefix="/security-audit",
    tags=["admin-security-audit"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        500: {"description": "Internal server error"},
    },
)

# 设置日志
logger = setup_logger("admin_security_audit")


class SecurityAuditRequest(BaseModel):
    """安全审计请求"""
    audit_types: List[str] = ["authentication", "authorization", "input_validation", "api_security"]
    target_url: Optional[str] = None
    severity_threshold: str = "low"


@router.get("/config", response_model=Dict[str, Any])
async def get_security_audit_config_info(
    section: Optional[str] = Query(None, description="配置节名称"),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取安全审计配置
    
    获取安全审计相关配置信息
    """
    try:
        if section:
            config = get_security_audit_config(section)
            if not config:
                return error_response(message=f"配置节 {section} 不存在", code=404)
            
            return success_response(
                data={section: config},
                message=f"获取 {section} 配置成功"
            )
        else:
            # 返回主要配置节
            main_sections = [
                "audit_environment",
                "authentication_audit", 
                "authorization_audit",
                "input_validation_audit",
                "api_security_audit",
                "file_security_audit",
                "network_security_audit",
                "configuration_audit"
            ]
            
            config_summary = {}
            for section in main_sections:
                section_config = get_security_audit_config(section)
                if section_config:
                    config_summary[section] = {
                        "enabled": section_config.get("enabled", False),
                        "test_cases_count": len(section_config.get("test_cases", []))
                    }
            
            return success_response(data=config_summary, message="获取安全审计配置成功")
    except Exception as e:
        logger.error(f"Error getting security audit config: {e}")
        return error_response(message=str(e), code=500)


@router.post("/audit/run", response_model=Dict[str, Any])
async def run_security_audit(
    request: SecurityAuditRequest,
    background_tasks: BackgroundTasks,
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    运行安全审计
    
    启动安全审计任务
    """
    try:
        # 在后台执行安全审计
        background_tasks.add_task(
            _run_security_audit_task,
            request.dict(),
            current_admin.username
        )
        
        return success_response(message="安全审计任务已启动")
    except Exception as e:
        logger.error(f"Error running security audit: {e}")
        return error_response(message=str(e), code=500)


@router.get("/audit/reports", response_model=Dict[str, Any])
async def get_security_audit_reports(
    limit: int = Query(10, description="返回报告数量"),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取安全审计报告
    
    获取历史安全审计报告列表
    """
    try:
        reports_dir = "security_reports"
        reports = []
        
        if os.path.exists(reports_dir):
            # 获取所有报告文件
            report_files = [f for f in os.listdir(reports_dir) if f.endswith('.json')]
            report_files.sort(reverse=True)  # 按时间倒序
            
            for report_file in report_files[:limit]:
                try:
                    file_path = os.path.join(reports_dir, report_file)
                    with open(file_path, 'r', encoding='utf-8') as f:
                        report_data = json.load(f)
                    
                    # 提取报告摘要
                    audit_info = report_data.get("audit_info", {})
                    summary = report_data.get("summary", {})
                    
                    report_summary = {
                        "filename": report_file,
                        "timestamp": audit_info.get("timestamp"),
                        "duration": audit_info.get("duration"),
                        "target_url": audit_info.get("target_url"),
                        "total_tests": summary.get("total_tests"),
                        "total_findings": summary.get("total_findings"),
                        "security_score": summary.get("security_score"),
                        "risk_level": summary.get("risk_level")
                    }
                    reports.append(report_summary)
                    
                except Exception as e:
                    logger.error(f"Error reading report file {report_file}: {e}")
        
        return success_response(
            data={"reports": reports, "total": len(reports)},
            message="获取安全审计报告成功"
        )
    except Exception as e:
        logger.error(f"Error getting security audit reports: {e}")
        return error_response(message=str(e), code=500)


@router.get("/audit/reports/{filename}", response_model=Dict[str, Any])
async def get_security_audit_report_detail(
    filename: str,
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取安全审计报告详情
    
    获取指定安全审计报告的详细内容
    """
    try:
        reports_dir = "security_reports"
        file_path = os.path.join(reports_dir, filename)
        
        if not os.path.exists(file_path):
            return error_response(message="报告文件不存在", code=404)
        
        with open(file_path, 'r', encoding='utf-8') as f:
            report_data = json.load(f)
        
        return success_response(data=report_data, message="获取报告详情成功")
    except Exception as e:
        logger.error(f"Error getting security audit report detail: {e}")
        return error_response(message=str(e), code=500)


@router.get("/vulnerabilities/summary", response_model=Dict[str, Any])
async def get_vulnerabilities_summary(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取漏洞摘要
    
    获取最新安全审计中发现的漏洞摘要
    """
    try:
        reports_dir = "security_reports"
        
        if not os.path.exists(reports_dir):
            return success_response(
                data={"message": "暂无安全审计报告"},
                message="获取漏洞摘要成功"
            )
        
        # 获取最新的报告
        report_files = [f for f in os.listdir(reports_dir) if f.endswith('.json')]
        if not report_files:
            return success_response(
                data={"message": "暂无安全审计报告"},
                message="获取漏洞摘要成功"
            )
        
        latest_report = max(report_files)
        file_path = os.path.join(reports_dir, latest_report)
        
        with open(file_path, 'r', encoding='utf-8') as f:
            report_data = json.load(f)
        
        # 提取漏洞摘要
        summary = report_data.get("summary", {})
        findings_by_severity = report_data.get("findings_by_severity", {})
        
        vulnerability_summary = {
            "report_timestamp": report_data.get("audit_info", {}).get("timestamp"),
            "security_score": summary.get("security_score", 0),
            "risk_level": summary.get("risk_level", "unknown"),
            "total_findings": summary.get("total_findings", 0),
            "findings_by_severity": {
                severity: len(findings) 
                for severity, findings in findings_by_severity.items()
            },
            "top_vulnerabilities": []
        }
        
        # 获取前5个最严重的漏洞
        all_findings = []
        for severity in ["critical", "high", "medium", "low"]:
            findings = findings_by_severity.get(severity, [])
            for finding in findings:
                finding["severity"] = severity
                all_findings.append(finding)
        
        vulnerability_summary["top_vulnerabilities"] = all_findings[:5]
        
        return success_response(data=vulnerability_summary, message="获取漏洞摘要成功")
    except Exception as e:
        logger.error(f"Error getting vulnerabilities summary: {e}")
        return error_response(message=str(e), code=500)


@router.get("/compliance/status", response_model=Dict[str, Any])
async def get_compliance_status(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取合规性状态
    
    获取系统的安全合规性状态
    """
    try:
        reports_dir = "security_reports"
        
        if not os.path.exists(reports_dir):
            return success_response(
                data={"message": "暂无合规性数据"},
                message="获取合规性状态成功"
            )
        
        # 获取最新的报告
        report_files = [f for f in os.listdir(reports_dir) if f.endswith('.json')]
        if not report_files:
            return success_response(
                data={"message": "暂无合规性数据"},
                message="获取合规性状态成功"
            )
        
        latest_report = max(report_files)
        file_path = os.path.join(reports_dir, latest_report)
        
        with open(file_path, 'r', encoding='utf-8') as f:
            report_data = json.load(f)
        
        compliance_data = report_data.get("compliance", {})
        
        return success_response(data=compliance_data, message="获取合规性状态成功")
    except Exception as e:
        logger.error(f"Error getting compliance status: {e}")
        return error_response(message=str(e), code=500)


@router.get("/recommendations", response_model=Dict[str, Any])
async def get_security_recommendations(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取安全建议
    
    获取基于最新审计结果的安全改进建议
    """
    try:
        reports_dir = "security_reports"
        
        if not os.path.exists(reports_dir):
            return success_response(
                data={"recommendations": ["建议进行安全审计以获取具体建议"]},
                message="获取安全建议成功"
            )
        
        # 获取最新的报告
        report_files = [f for f in os.listdir(reports_dir) if f.endswith('.json')]
        if not report_files:
            return success_response(
                data={"recommendations": ["建议进行安全审计以获取具体建议"]},
                message="获取安全建议成功"
            )
        
        latest_report = max(report_files)
        file_path = os.path.join(reports_dir, latest_report)
        
        with open(file_path, 'r', encoding='utf-8') as f:
            report_data = json.load(f)
        
        recommendations = report_data.get("recommendations", [])
        
        return success_response(
            data={"recommendations": recommendations},
            message="获取安全建议成功"
        )
    except Exception as e:
        logger.error(f"Error getting security recommendations: {e}")
        return error_response(message=str(e), code=500)


async def _run_security_audit_task(audit_config: Dict[str, Any], admin_username: str):
    """执行安全审计的后台任务"""
    try:
        logger.info(f"Starting security audit task initiated by {admin_username}")
        logger.info(f"Audit config: {audit_config}")
        
        # 这里可以集成实际的安全审计执行逻辑
        from apps.backend.api.utils.security_auditor import SecurityAuditor
        
        auditor = SecurityAuditor()
        
        # 如果指定了目标URL，更新配置
        if audit_config.get("target_url"):
            auditor.base_url = audit_config["target_url"]
        
        # 运行安全审计
        report = auditor.run_comprehensive_audit()
        
        logger.info("Security audit task completed")
        logger.info(f"Security score: {report.get('summary', {}).get('security_score', 'N/A')}")
        
    except Exception as e:
        logger.error(f"Security audit task failed: {e}")
