#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
系统信息管理API路由
"""

import os
import psutil
import platform
from typing import Any, Dict, List, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from sqlalchemy import desc, func

from apps.backend.api.dependencies.database import get_db
from apps.backend.api.dependencies.admin_auth import get_current_admin_user
from apps.backend.api.models.admin import Admin, OperationLog
from apps.backend.api.models.user import User
from apps.backend.api.models.job import Job
from apps.backend.api.schemas.common import SuccessResponse, PaginatedResponse
from apps.backend.storage.db_manager import DatabaseManager
from apps.backend.utils.logger import setup_logger

# 创建路由
router = APIRouter(tags=["admin_system"])

# 设置日志
logger = setup_logger("api_admin_system")


@router.get("/system/info", response_model=Dict[str, Any])
async def get_system_info(
    current_admin: Admin = Depends(get_current_admin_user)
) -> Any:
    """
    获取系统信息
    """
    try:
        # 获取系统基本信息
        system_info = {
            "system": {
                "platform": platform.platform(),
                "system": platform.system(),
                "release": platform.release(),
                "version": platform.version(),
                "machine": platform.machine(),
                "processor": platform.processor(),
                "python_version": platform.python_version(),
                "hostname": platform.node()
            },
            "cpu": {
                "count": psutil.cpu_count(),
                "usage": psutil.cpu_percent(interval=1),
                "load_avg": os.getloadavg() if hasattr(os, 'getloadavg') else None
            },
            "memory": {
                "total": psutil.virtual_memory().total,
                "available": psutil.virtual_memory().available,
                "used": psutil.virtual_memory().used,
                "percent": psutil.virtual_memory().percent
            },
            "disk": {
                "total": psutil.disk_usage('/').total,
                "used": psutil.disk_usage('/').used,
                "free": psutil.disk_usage('/').free,
                "percent": psutil.disk_usage('/').percent
            },
            "network": {
                "bytes_sent": psutil.net_io_counters().bytes_sent,
                "bytes_recv": psutil.net_io_counters().bytes_recv,
                "packets_sent": psutil.net_io_counters().packets_sent,
                "packets_recv": psutil.net_io_counters().packets_recv
            },
            "uptime": datetime.now() - datetime.fromtimestamp(psutil.boot_time()),
            "timestamp": datetime.now()
        }

        return SuccessResponse(
            message="获取系统信息成功",
            data=system_info
        ).dict()

    except Exception as e:
        logger.error(f"获取系统信息异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="服务器内部错误"
        )


@router.get("/system/stats", response_model=Dict[str, Any])
async def get_system_stats(
    db: DatabaseManager = Depends(get_db),
    current_admin: Admin = Depends(get_current_admin_user)
) -> Any:
    """
    获取系统统计信息
    """
    try:
        # 获取统计数据 - 使用原生SQL查询
        total_users_result = db.execute_query("SELECT COUNT(*) as count FROM users")
        total_users = total_users_result[0]['count'] if total_users_result else 0

        total_jobs_result = db.execute_query("SELECT COUNT(*) as count FROM jobs")
        total_jobs = total_jobs_result[0]['count'] if total_jobs_result else 0

        total_admins_result = db.execute_query("SELECT COUNT(*) as count FROM admins")
        total_admins = total_admins_result[0]['count'] if total_admins_result else 0

        # 获取最近7天的数据
        seven_days_ago = datetime.now() - timedelta(days=7)
        new_users_7d_result = db.execute_query("SELECT COUNT(*) as count FROM users WHERE created_at >= %s", (seven_days_ago,))
        new_users_7d = new_users_7d_result[0]['count'] if new_users_7d_result else 0

        new_jobs_7d_result = db.execute_query("SELECT COUNT(*) as count FROM jobs WHERE created_at >= %s", (seven_days_ago,))
        new_jobs_7d = new_jobs_7d_result[0]['count'] if new_jobs_7d_result else 0

        # 获取最近30天的数据
        thirty_days_ago = datetime.now() - timedelta(days=30)
        new_users_30d_result = db.execute_query("SELECT COUNT(*) as count FROM users WHERE created_at >= %s", (thirty_days_ago,))
        new_users_30d = new_users_30d_result[0]['count'] if new_users_30d_result else 0

        new_jobs_30d_result = db.execute_query("SELECT COUNT(*) as count FROM jobs WHERE created_at >= %s", (thirty_days_ago,))
        new_jobs_30d = new_jobs_30d_result[0]['count'] if new_jobs_30d_result else 0

        stats = {
            "overview": {
                "total_users": total_users,
                "total_jobs": total_jobs,
                "total_admins": total_admins,
                "new_users_7d": new_users_7d,
                "new_jobs_7d": new_jobs_7d,
                "new_users_30d": new_users_30d,
                "new_jobs_30d": new_jobs_30d
            },
            "growth": {
                "user_growth_7d": round((new_users_7d / max(total_users - new_users_7d, 1)) * 100, 2),
                "job_growth_7d": round((new_jobs_7d / max(total_jobs - new_jobs_7d, 1)) * 100, 2),
                "user_growth_30d": round((new_users_30d / max(total_users - new_users_30d, 1)) * 100, 2),
                "job_growth_30d": round((new_jobs_30d / max(total_jobs - new_jobs_30d, 1)) * 100, 2)
            },
            "timestamp": datetime.now()
        }

        return SuccessResponse(
            message="获取系统统计成功",
            data=stats
        ).dict()

    except Exception as e:
        logger.error(f"获取系统统计异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="服务器内部错误"
        )


@router.get("/operation-logs", response_model=Dict[str, Any])
async def get_operation_logs(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    module: Optional[str] = Query(None, description="模块"),
    operation: Optional[str] = Query(None, description="操作"),
    admin_id: Optional[int] = Query(None, description="管理员ID"),
    start_date: Optional[str] = Query(None, description="开始日期"),
    end_date: Optional[str] = Query(None, description="结束日期"),
    db: DatabaseManager = Depends(get_db),
    current_admin: Admin = Depends(get_current_admin_user)
) -> Any:
    """
    获取操作日志列表
    """
    try:
        # 构建SQL查询
        where_conditions = []
        params = []

        # 应用过滤条件
        if module:
            where_conditions.append("ol.module LIKE %s")
            params.append(f"%{module}%")

        if operation:
            where_conditions.append("ol.operation LIKE %s")
            params.append(f"%{operation}%")

        if admin_id:
            where_conditions.append("ol.admin_id = %s")
            params.append(admin_id)

        if start_date:
            try:
                start_dt = datetime.strptime(start_date, "%Y-%m-%d")
                where_conditions.append("ol.created_at >= %s")
                params.append(start_dt)
            except ValueError:
                pass

        if end_date:
            try:
                end_dt = datetime.strptime(end_date, "%Y-%m-%d") + timedelta(days=1)
                where_conditions.append("ol.created_at < %s")
                params.append(end_dt)
            except ValueError:
                pass

        # 构建WHERE子句
        where_clause = ""
        if where_conditions:
            where_clause = "WHERE " + " AND ".join(where_conditions)

        # 计算总数
        count_query = f"""
        SELECT COUNT(*) as total
        FROM operation_logs ol
        LEFT JOIN admins a ON ol.admin_id = a.id
        {where_clause}
        """
        total_result = db.execute_query(count_query, tuple(params))
        total = total_result[0]['total'] if total_result else 0

        # 分页查询
        offset = (page - 1) * page_size
        data_query = f"""
        SELECT ol.id, ol.admin_id, ol.module, ol.operation, ol.content,
               ol.ip, ol.user_agent, ol.created_at, a.username as admin_username
        FROM operation_logs ol
        LEFT JOIN admins a ON ol.admin_id = a.id
        {where_clause}
        ORDER BY ol.created_at DESC
        LIMIT %s OFFSET %s
        """

        # 添加分页参数
        query_params = list(params) + [page_size, offset]
        logs_result = db.execute_query(data_query, tuple(query_params))

        # 构建结果
        log_list = []
        for log in logs_result:
            log_list.append({
                "id": log['id'],
                "admin_id": log['admin_id'],
                "admin_username": log['admin_username'],
                "module": log['module'],
                "operation": log['operation'],
                "content": log['content'],
                "ip": log['ip'],
                "user_agent": log['user_agent'],
                "created_at": log['created_at']
            })

        return SuccessResponse(
            message="获取操作日志成功",
            data={
                "items": log_list,
                "total": total,
                "page": page,
                "page_size": page_size,
                "pages": (total + page_size - 1) // page_size,
                "has_next": page * page_size < total,
                "has_prev": page > 1
            }
        ).dict()

    except Exception as e:
        logger.error(f"获取操作日志异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="服务器内部错误"
        )


@router.get("/users/stats")
async def get_user_stats(
    db: DatabaseManager = Depends(get_db),
    current_admin = Depends(get_current_admin_user)
):
    """获取用户统计数据"""
    try:
        # 总用户数
        total_users_result = db.execute_query("SELECT COUNT(*) as count FROM users")
        total_users = total_users_result[0]['count'] if total_users_result else 0

        # 活跃用户数
        active_users_result = db.execute_query("SELECT COUNT(*) as count FROM users WHERE status = 1")
        active_users = active_users_result[0]['count'] if active_users_result else 0

        # 今日新增用户
        today_users_result = db.execute_query(
            "SELECT COUNT(*) as count FROM users WHERE DATE(created_at) = CURDATE()"
        )
        today_users = today_users_result[0]['count'] if today_users_result else 0

        # 本月新增用户
        month_users_result = db.execute_query(
            "SELECT COUNT(*) as count FROM users WHERE YEAR(created_at) = YEAR(CURDATE()) AND MONTH(created_at) = MONTH(CURDATE())"
        )
        month_users = month_users_result[0]['count'] if month_users_result else 0

        return SuccessResponse(
            message="获取用户统计成功",
            data={
                "total_users": total_users,
                "active_users": active_users,
                "today_users": today_users,
                "month_users": month_users,
                "inactive_users": total_users - active_users
            }
        )

    except Exception as e:
        logger.error(f"获取用户统计异常: {e}")
        raise HTTPException(status_code=500, detail="服务器内部错误")


@router.get("/jobs/stats")
async def get_job_stats(
    db: DatabaseManager = Depends(get_db),
    current_admin = Depends(get_current_admin_user)
):
    """获取岗位统计数据"""
    try:
        # 总岗位数
        total_jobs_result = db.execute_query("SELECT COUNT(*) as count FROM jobs")
        total_jobs = total_jobs_result[0]['count'] if total_jobs_result else 0

        # 活跃岗位数
        active_jobs_result = db.execute_query("SELECT COUNT(*) as count FROM jobs WHERE status = 1")
        active_jobs = active_jobs_result[0]['count'] if active_jobs_result else 0

        # 今日新增岗位
        today_jobs_result = db.execute_query(
            "SELECT COUNT(*) as count FROM jobs WHERE DATE(created_at) = CURDATE()"
        )
        today_jobs = today_jobs_result[0]['count'] if today_jobs_result else 0

        # 本月新增岗位
        month_jobs_result = db.execute_query(
            "SELECT COUNT(*) as count FROM jobs WHERE YEAR(created_at) = YEAR(CURDATE()) AND MONTH(created_at) = MONTH(CURDATE())"
        )
        month_jobs = month_jobs_result[0]['count'] if month_jobs_result else 0

        return SuccessResponse(
            message="获取岗位统计成功",
            data={
                "total_jobs": total_jobs,
                "active_jobs": active_jobs,
                "today_jobs": today_jobs,
                "month_jobs": month_jobs,
                "inactive_jobs": total_jobs - active_jobs
            }
        )

    except Exception as e:
        logger.error(f"获取岗位统计异常: {e}")
        raise HTTPException(status_code=500, detail="服务器内部错误")


@router.get("/system/configs")
async def get_system_configs(
    db: DatabaseManager = Depends(get_db),
    current_admin = Depends(get_current_admin_user)
):
    """获取系统配置列表"""
    try:
        # 查询系统配置
        configs_result = db.execute_query("""
            SELECT config_key, config_value, description, created_at, updated_at
            FROM system_configs
            ORDER BY config_key
        """)

        configs = []
        for i, config in enumerate(configs_result):
            configs.append({
                "id": i + 1,  # 添加id字段，Table组件需要
                "config_key": config['config_key'],
                "config_value": config['config_value'],
                "description": config['description'],
                "created_at": config['created_at'],
                "updated_at": config['updated_at']
            })

        return SuccessResponse(
            message="获取系统配置成功",
            data=configs
        )

    except Exception as e:
        logger.error(f"获取系统配置异常: {e}")
        # 如果表不存在，返回默认配置
        default_configs = [
            {
                "id": 1,
                "config_key": "site_name",
                "config_value": "事业编制招聘信息查询系统",
                "description": "网站名称",
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            },
            {
                "id": 2,
                "config_key": "site_description",
                "config_value": "优智帮工作室开发的事业编制招聘信息查询系统",
                "description": "网站描述",
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            },
            {
                "id": 3,
                "config_key": "max_upload_size",
                "config_value": "10485760",
                "description": "最大上传文件大小（字节）",
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            }
        ]

        return SuccessResponse(
            message="获取系统配置成功（默认配置）",
            data=default_configs
        )


@router.put("/system/configs/{config_key}")
async def update_system_config(
    config_key: str,
    config_data: dict,
    db: DatabaseManager = Depends(get_db),
    current_admin = Depends(get_current_admin_user)
):
    """更新系统配置"""
    try:
        config_value = config_data.get("config_value", "")
        description = config_data.get("description", "")

        # 尝试更新配置
        update_query = """
            UPDATE system_configs
            SET config_value = %s, description = %s, updated_at = %s
            WHERE config_key = %s
        """

        with db.get_cursor() as cursor:
            cursor.execute(update_query, (config_value, description, datetime.now(), config_key))

            if cursor.rowcount == 0:
                # 如果没有更新任何行，则插入新配置
                insert_query = """
                    INSERT INTO system_configs (config_key, config_value, description, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s)
                """
                cursor.execute(insert_query, (config_key, config_value, description, datetime.now(), datetime.now()))

        return SuccessResponse(
            message="更新系统配置成功",
            data={
                "config_key": config_key,
                "config_value": config_value,
                "description": description,
                "updated_at": datetime.now()
            }
        )

    except Exception as e:
        logger.error(f"更新系统配置异常: {e}")
        raise HTTPException(status_code=500, detail="服务器内部错误")
