#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
用户管理API路由
"""

from typing import Any, Dict, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from fastapi import status
from sqlalchemy.orm import Session
from sqlalchemy import desc, asc, func
from datetime import datetime

from apps.backend.api.config.database import get_db
from apps.backend.api.dependencies.admin_auth import get_current_active_admin, check_admin_permission_sqlalchemy
from apps.backend.api.models.admin import Admin, OperationLog
from apps.backend.api.models.user import User, UserProfile
from apps.backend.api.schemas.user import (
    UserCreate, UserUpdate, User as UserSchema,
    UserProfileCreate, UserProfileUpdate, UserProfile as UserProfileSchema
)
from apps.backend.api.utils.response import success_response, error_response, custom_error_response
from apps.backend.utils.logger import setup_logger

# 创建路由
router = APIRouter(tags=["admin_user"])

# 设置日志
logger = setup_logger("api_admin_user")


@router.get("/users", response_model=Dict[str, Any])
async def get_users(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    keywords: Optional[str] = Query(None, description="关键词"),
    status: Optional[bool] = Query(None, description="状态"),
    sort: Optional[str] = Query("created_at", description="排序字段"),
    order: Optional[str] = Query("desc", description="排序方向"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission_sqlalchemy("user:view"))
) -> Any:
    """
    获取用户列表

    - **page**: 页码
    - **page_size**: 每页数量
    - **keywords**: 关键词（可选）
    - **status**: 状态（可选）
    - **sort**: 排序字段（可选）
    - **order**: 排序方向（可选）
    """
    try:
        # 构建查询
        query = db.query(User)

        # 应用过滤条件
        if keywords:
            query = query.filter(
                User.username.like(f"%{keywords}%") |
                User.real_name.like(f"%{keywords}%") |
                User.email.like(f"%{keywords}%") |
                User.phone.like(f"%{keywords}%")
            )

        if status is not None:
            query = query.filter(User.status == status)

        # 计算总数
        total = query.count()

        # 应用排序
        if sort and hasattr(User, sort):
            sort_column = getattr(User, sort)
            if order and order.lower() == "desc":
                query = query.order_by(desc(sort_column))
            else:
                query = query.order_by(asc(sort_column))
        else:
            # 默认按创建时间降序
            query = query.order_by(desc(User.created_at))

        # 分页
        query = query.offset((page - 1) * page_size).limit(page_size)

        # 执行查询
        users = query.all()

        # 构建结果
        user_list = []
        for user in users:
            user_list.append({
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "phone": user.phone,
                "real_name": user.real_name,
                "education": user.education,
                "major": user.major,
                "graduation_year": user.graduation_year,
                "school": user.school,
                "last_login": user.last_login,
                "status": user.status,
                "avatar_url": user.avatar_url,
                "created_at": user.created_at,
                "updated_at": user.updated_at
            })

        return success_response(data={
            "items": user_list,
            "total": total,
            "page": page,
            "page_size": page_size,
            "pages": (total + page_size - 1) // page_size,
            "has_next": page * page_size < total,
            "has_prev": page > 1
        })
    except Exception as e:
        logger.error(f"获取用户列表异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get("/users/{user_id}", response_model=Dict[str, Any])
async def get_user(
    user_id: int = Path(..., ge=1, description="用户ID"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission_sqlalchemy("user:view"))
) -> Any:
    """
    获取用户详情

    - **user_id**: 用户ID
    """
    try:
        # 查询用户
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            return custom_error_response("用户不存在", 404)

        # 查询用户资料
        profile = db.query(UserProfile).filter(UserProfile.user_id == user_id).first()

        # 构建结果
        user_data = {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "phone": user.phone,
            "real_name": user.real_name,
            "education": user.education,
            "major": user.major,
            "graduation_year": user.graduation_year,
            "school": user.school,
            "last_login": user.last_login,
            "status": user.status,
            "avatar_url": user.avatar_url,
            "created_at": user.created_at,
            "updated_at": user.updated_at
        }

        # 添加用户资料
        if profile:
            user_data["profile"] = {
                "id": profile.id,
                "user_id": profile.user_id,
                "education": profile.education,
                "major": profile.major,
                "skills": profile.skills,
                "experience": profile.experience,
                "location_preference": profile.location_preference,
                "job_type_preference": profile.job_type_preference,
                "resume_url": profile.resume_url,
                "created_at": profile.created_at,
                "updated_at": profile.updated_at
            }

        return success_response(data=user_data)
    except Exception as e:
        logger.error(f"获取用户详情异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.put("/users/{user_id}", response_model=Dict[str, Any])
async def update_user(
    user_data: UserUpdate,
    user_id: int = Path(..., ge=1, description="用户ID"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission_sqlalchemy("user:edit"))
) -> Any:
    """
    更新用户信息

    - **user_id**: 用户ID
    """
    try:
        # 查询用户
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            return custom_error_response("用户不存在", 404)

        # 更新用户信息
        if user_data.email is not None:
            setattr(user, "email", user_data.email)
        if user_data.phone is not None:
            setattr(user, "phone", user_data.phone)
        if user_data.real_name is not None:
            setattr(user, "real_name", user_data.real_name)
        if user_data.education is not None:
            setattr(user, "education", user_data.education)
        if user_data.major is not None:
            setattr(user, "major", user_data.major)
        if user_data.graduation_year is not None:
            setattr(user, "graduation_year", user_data.graduation_year)
        if user_data.school is not None:
            setattr(user, "school", user_data.school)
        if user_data.avatar_url is not None:
            setattr(user, "avatar_url", user_data.avatar_url)
        if user_data.status is not None:
            setattr(user, "status", user_data.status)

        db.commit()
        db.refresh(user)

        # 记录操作日志
        log = OperationLog(
            admin_id=current_admin.id,
            module="用户管理",
            operation="更新用户",
            content=f"更新用户 {user.username}",
            ip="127.0.0.1",  # 实际应用中应获取真实IP
            user_agent="Unknown"  # 实际应用中应获取真实User-Agent
        )
        db.add(log)
        db.commit()

        return success_response(
            message="更新用户成功",
            data={
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "phone": user.phone,
                "real_name": user.real_name,
                "education": user.education,
                "major": user.major,
                "graduation_year": user.graduation_year,
                "school": user.school,
                "last_login": user.last_login,
                "status": user.status,
                "avatar_url": user.avatar_url,
                "created_at": user.created_at,
                "updated_at": user.updated_at
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"更新用户异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.put("/users/{user_id}/status", response_model=Dict[str, Any])
async def update_user_status(
    user_id: int = Path(..., ge=1, description="用户ID"),
    status: bool = Query(..., description="状态"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission_sqlalchemy("user:edit"))
) -> Any:
    """
    更新用户状态

    - **user_id**: 用户ID
    - **status**: 状态
    """
    try:
        # 查询用户
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            return error_response(
                message="用户不存在",
                code=status.HTTP_404_NOT_FOUND
            )

        # 更新状态
        user.status = status
        db.commit()
        db.refresh(user)

        # 记录操作日志
        log = OperationLog(
            admin_id=current_admin.id,
            module="用户管理",
            operation="更新用户状态",
            content=f"更新用户 {user.username} 状态为 {'启用' if status else '禁用'}",
            ip="127.0.0.1",  # 实际应用中应获取真实IP
            user_agent="Unknown"  # 实际应用中应获取真实User-Agent
        )
        db.add(log)
        db.commit()

        return success_response(
            message=f"用户状态已{'启用' if status else '禁用'}",
            data={
                "id": user.id,
                "status": user.status
            }
        )
    except Exception as e:
        db.rollback()
        logger.error(f"更新用户状态异常: {str(e)}")
        return error_response(
            message="服务器内部错误",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.patch("/users/{user_id}/disable", response_model=Dict[str, Any])
async def disable_user(
    user_id: int = Path(..., ge=1, description="用户ID"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission_sqlalchemy("user:edit"))
) -> Any:
    """
    禁用用户
    """
    try:
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )

        user.status = False
        db.commit()
        db.refresh(user)

        # 记录操作日志
        log = OperationLog(
            admin_id=current_admin.id,
            module="用户管理",
            operation="禁用用户",
            content=f"禁用用户 {user.username}",
            ip="127.0.0.1",
            user_agent="Unknown"
        )
        db.add(log)
        db.commit()

        return success_response(
            message="用户已禁用",
            data={"id": user.id, "status": user.status}
        )
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"禁用用户异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="服务器内部错误"
        )


@router.patch("/users/{user_id}/enable", response_model=Dict[str, Any])
async def enable_user(
    user_id: int = Path(..., ge=1, description="用户ID"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission_sqlalchemy("user:edit"))
) -> Any:
    """
    启用用户
    """
    try:
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )

        user.status = True
        db.commit()
        db.refresh(user)

        # 记录操作日志
        log = OperationLog(
            admin_id=current_admin.id,
            module="用户管理",
            operation="启用用户",
            content=f"启用用户 {user.username}",
            ip="127.0.0.1",
            user_agent="Unknown"
        )
        db.add(log)
        db.commit()

        return success_response(
            message="用户已启用",
            data={"id": user.id, "status": user.status}
        )
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"启用用户异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="服务器内部错误"
        )


@router.get("/statistics", response_model=Dict[str, Any])
async def get_user_statistics(
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission_sqlalchemy("user:view"))
) -> Any:
    """
    获取用户统计信息
    """
    try:
        from datetime import date

        total_users = db.query(User).count()
        active_users = db.query(User).filter(User.status == True).count()
        inactive_users = db.query(User).filter(User.status == False).count()

        # 今日新增用户
        today = date.today()
        new_users_today = db.query(User).filter(
            func.date(User.created_at) == today
        ).count()

        # 本周新增用户
        from datetime import timedelta
        this_week = today - timedelta(days=today.weekday())
        new_users_this_week = db.query(User).filter(
            User.created_at >= this_week
        ).count()

        # 本月新增用户
        this_month = today.replace(day=1)
        new_users_this_month = db.query(User).filter(
            User.created_at >= this_month
        ).count()

        # 按教育程度统计
        education_stats = {}
        for education in ["高中", "专科", "本科", "硕士", "博士"]:
            count = db.query(User).filter(User.education == education).count()
            education_stats[education] = count

        return success_response(data={
            "total_users": total_users,
            "active_users": active_users,
            "inactive_users": inactive_users,
            "new_users_today": new_users_today,
            "new_users_this_week": new_users_this_week,
            "new_users_this_month": new_users_this_month,
            "education_stats": education_stats
        })
    except Exception as e:
        logger.error(f"获取用户统计异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="服务器内部错误"
        )


@router.get("/export")
async def export_users(
    format: str = Query("xlsx", description="导出格式"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission_sqlalchemy("user:view"))
):
    """
    导出用户数据
    """
    try:
        from fastapi.responses import Response
        import io

        users = db.query(User).all()

        # 创建Excel文件内容（模拟）
        excel_content = b"Excel file content for users export"

        # 返回Excel文件响应
        return Response(
            content=excel_content,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={
                "Content-Disposition": f"attachment; filename=users_export.xlsx"
            }
        )
    except Exception as e:
        logger.error(f"导出用户数据异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="服务器内部错误"
        )


@router.delete("/users/{user_id}", response_model=Dict[str, Any])
async def delete_user(
    user_id: int = Path(..., ge=1, description="用户ID"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(check_admin_permission_sqlalchemy("user:delete"))
) -> Any:
    """
    删除用户
    """
    try:
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )

        # 软删除：设置状态为删除
        user.status = False
        db.commit()

        # 记录操作日志
        log = OperationLog(
            admin_id=current_admin.id,
            module="用户管理",
            operation="删除用户",
            content=f"删除用户 {user.username}",
            ip="127.0.0.1",
            user_agent="Unknown"
        )
        db.add(log)
        db.commit()

        return success_response(message="删除成功")
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"删除用户异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="服务器内部错误"
        )