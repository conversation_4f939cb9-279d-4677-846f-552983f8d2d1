#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI功能API路由
"""

from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.orm import Session

from apps.backend.api.config.database import get_db
from apps.backend.api.dependencies.auth import get_current_active_user
from apps.backend.api.models.user import User
from apps.backend.api.schemas.ai import (
    MajorMatchRequest, MajorMatchResponse,
    SkillMatchRequest, SkillMatchResponse,
    JobRecommendRequest, JobRecommendResponse,
    ResumeParseRequest, ResumeParseResponse,
    InterviewPrepRequest, InterviewPrepResponse,
    PolicyInterpretRequest, PolicyInterpretResponse
)
from apps.backend.api.services.ai import AIService
from apps.backend.api.utils.response import success_response, error_response
from apps.backend.utils.logger import setup_logger

# 创建路由
router = APIRouter()

# 设置日志
logger = setup_logger("api_ai")


@router.post("/major-match", response_model=Dict[str, Any])
async def analyze_major_match(
    request: MajorMatchRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    分析专业匹配度
    
    分析用户专业与岗位专业要求的匹配程度
    """
    try:
        # 调用AI服务
        result = AIService.analyze_major_match(request)
        return success_response(data=result)
    except Exception as e:
        logger.error(f"Error analyzing major match: {e}")
        return error_response(message=str(e), code=500)


@router.post("/skill-match", response_model=Dict[str, Any])
async def analyze_skill_match(
    request: SkillMatchRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    分析技能匹配度
    
    分析用户技能与岗位技能要求的匹配程度
    """
    try:
        # 调用AI服务
        result = AIService.analyze_skill_match(request)
        return success_response(data=result)
    except Exception as e:
        logger.error(f"Error analyzing skill match: {e}")
        return error_response(message=str(e), code=500)


@router.post("/job-recommend", response_model=Dict[str, Any])
async def recommend_jobs(
    request: JobRecommendRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    推荐岗位
    
    根据用户专业和技能推荐匹配的岗位
    """
    try:
        # 调用AI服务
        result = AIService.recommend_jobs(request, db)
        return success_response(data=result)
    except Exception as e:
        logger.error(f"Error recommending jobs: {e}")
        return error_response(message=str(e), code=500)


@router.post("/resume-parse", response_model=Dict[str, Any])
async def parse_resume(
    request: ResumeParseRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    解析简历
    
    解析简历文本，提取关键信息
    """
    try:
        # 调用AI服务
        result = AIService.parse_resume(request)
        return success_response(data=result)
    except Exception as e:
        logger.error(f"Error parsing resume: {e}")
        return error_response(message=str(e), code=500)


@router.post("/interview-prep", response_model=Dict[str, Any])
async def prepare_interview(
    request: InterviewPrepRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    准备面试
    
    根据岗位描述生成面试问题和准备建议
    """
    try:
        # 调用AI服务
        result = AIService.prepare_interview(request)
        return success_response(data=result)
    except Exception as e:
        logger.error(f"Error preparing interview: {e}")
        return error_response(message=str(e), code=500)


@router.post("/policy-interpret", response_model=Dict[str, Any])
async def interpret_policy(
    request: PolicyInterpretRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    解读政策
    
    解读就业政策文本，提供通俗易懂的解释
    """
    try:
        # 调用AI服务
        result = AIService.interpret_policy(request)
        return success_response(data=result)
    except Exception as e:
        logger.error(f"Error interpreting policy: {e}")
        return error_response(message=str(e), code=500)


@router.get("/llm-status", response_model=Dict[str, Any])
async def get_llm_status(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取LLM状态
    
    获取LLM客户端的状态信息
    """
    try:
        # 获取LLM客户端
        llm_client = AIService.get_llm_client()
        
        # 获取状态
        status = llm_client.get_client_status()
        
        return success_response(data=status)
    except Exception as e:
        logger.error(f"Error getting LLM status: {e}")
        return error_response(message=str(e), code=500)
