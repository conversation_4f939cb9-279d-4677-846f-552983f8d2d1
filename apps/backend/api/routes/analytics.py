"""
数据分析相关路由
"""
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_

from apps.backend.api.dependencies.database import get_db
from apps.backend.api.dependencies.admin_auth import get_current_admin_user as get_current_admin
from apps.backend.api.schemas.analytics import (
    UserBehaviorAnalytics,
    JobAnalytics,
    CommunityAnalytics,
    AnalyticsDateRange
)
from apps.backend.api.models.user import User
from apps.backend.api.models.job import Job
from apps.backend.api.models.community import CommunityPost as Post, Comment
from apps.backend.api.services.analytics import AnalyticsService

router = APIRouter(prefix="/analytics", tags=["数据分析"])


@router.get("/user-behavior", response_model=UserBehaviorAnalytics)
async def get_user_behavior_analytics(
    start_date: Optional[str] = Query(None, description="开始日期 YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期 YYYY-MM-DD"),
    granularity: str = Query("day", description="时间粒度: day/week/month"),
    current_admin: User = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """获取用户行为分析数据"""
    
    # 设置默认日期范围（最近30天）
    if not end_date:
        end_date = datetime.now().strftime("%Y-%m-%d")
    if not start_date:
        start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
    
    date_range = AnalyticsDateRange(
        start_date=start_date,
        end_date=end_date,
        granularity=granularity
    )
    
    analytics_service = AnalyticsService(db)
    return await analytics_service.get_user_behavior_analytics(date_range)


@router.get("/job-analytics", response_model=JobAnalytics)
async def get_job_analytics(
    start_date: Optional[str] = Query(None, description="开始日期 YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期 YYYY-MM-DD"),
    category: Optional[str] = Query(None, description="岗位类别"),
    current_admin: User = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """获取岗位热度分析数据"""
    
    # 设置默认日期范围（最近30天）
    if not end_date:
        end_date = datetime.now().strftime("%Y-%m-%d")
    if not start_date:
        start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
    
    date_range = AnalyticsDateRange(
        start_date=start_date,
        end_date=end_date
    )
    
    analytics_service = AnalyticsService(db)
    return await analytics_service.get_job_analytics(date_range, category)


@router.get("/community-analytics", response_model=CommunityAnalytics)
async def get_community_analytics(
    start_date: Optional[str] = Query(None, description="开始日期 YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期 YYYY-MM-DD"),
    category: Optional[str] = Query(None, description="内容类别"),
    current_admin: User = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """获取社区活跃度分析数据"""
    
    # 设置默认日期范围（最近30天）
    if not end_date:
        end_date = datetime.now().strftime("%Y-%m-%d")
    if not start_date:
        start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
    
    date_range = AnalyticsDateRange(
        start_date=start_date,
        end_date=end_date
    )
    
    analytics_service = AnalyticsService(db)
    return await analytics_service.get_community_analytics(date_range, category)


@router.get("/dashboard-stats")
async def get_dashboard_stats(
    current_admin: User = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """获取仪表板统计数据"""
    
    analytics_service = AnalyticsService(db)
    return await analytics_service.get_dashboard_stats()


@router.post("/export")
async def export_analytics_report(
    report_type: str,
    start_date: str,
    end_date: str,
    current_admin: User = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """导出分析报告"""
    
    date_range = AnalyticsDateRange(
        start_date=start_date,
        end_date=end_date
    )
    
    analytics_service = AnalyticsService(db)
    
    if report_type == "user-behavior":
        data = await analytics_service.get_user_behavior_analytics(date_range)
    elif report_type == "job-analytics":
        data = await analytics_service.get_job_analytics(date_range)
    elif report_type == "community-analytics":
        data = await analytics_service.get_community_analytics(date_range)
    else:
        raise ValueError("不支持的报告类型")
    
    # 这里可以实现具体的导出逻辑（Excel、PDF等）
    return {
        "message": "报告导出成功",
        "download_url": f"/downloads/analytics_{report_type}_{start_date}_{end_date}.xlsx"
    }
