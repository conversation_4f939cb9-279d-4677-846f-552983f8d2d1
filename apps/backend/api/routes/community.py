#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
社区功能API路由
"""

from typing import Dict, List, Optional, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.orm import Session

from apps.backend.api.config.database import get_db
from apps.backend.api.dependencies.auth import get_current_active_user
from apps.backend.api.models.user import User
from apps.backend.api.models.community import CommunityPost as Post, Comment, Like, Tag
from apps.backend.api.schemas.community import (
    PostCreate, PostUpdate, CommentCreate, CommentUpdate,
    PostQuery, CommentQuery
)
from apps.backend.api.services.community import CommunityService
from apps.backend.api.utils.response import success_response, error_response
from apps.backend.utils.logger import setup_logger

# 创建路由
router = APIRouter()

# 设置日志
logger = setup_logger("api_community")

@router.get("/posts", response_model=Dict[str, Any])
async def get_posts(
    query: PostQuery = Depends(),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取帖子列表

    根据多种条件搜索帖子，支持分页和排序
    """
    try:
        # 调用社区服务
        result = CommunityService.get_posts(db, query)

        # 转换帖子对象为字典
        posts_data = []
        for post in result["items"]:
            post_dict = {
                "id": post.id,
                "post_id": post.post_id,
                "title": post.title,
                "content": post.content,
                "category": post.category,
                "author_id": post.author_id,
                "author_name": post.author.username if post.author else None,
                "view_count": post.view_count,
                "like_count": post.like_count,
                "comment_count": post.comment_count,
                "status": post.status,
                "is_pinned": post.is_pinned,
                "is_essence": post.is_essence,
                "created_at": post.created_at.isoformat() if post.created_at else None,
                "updated_at": post.updated_at.isoformat() if post.updated_at else None
            }
            posts_data.append(post_dict)

        # 社区API使用直接的分页格式，不包装在success_response中
        return {
            "total": result["total"],
            "results": posts_data,
            "page": result["page"],
            "page_size": result["page_size"],
            "pages": result["pages"],
            "has_next": result["has_next"],
            "has_prev": result["has_prev"]
        }
    except Exception as e:
        logger.error(f"Error getting posts: {e}")
        return error_response(message=str(e), code=500)


@router.get("/posts/{post_id}", response_model=Dict[str, Any])
async def get_post_detail(
    post_id: int = Path(..., description="帖子ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取帖子详情

    根据帖子ID获取帖子详细信息
    """
    try:
        # 调用社区服务
        post = CommunityService.get_post(db, post_id)
        if not post:
            return error_response(message="Post not found", code=404)

        # 检查点赞状态
        is_liked = CommunityService.check_like_status(db, post_id, current_user.id)

        # 社区API返回直接的帖子数据，包含is_liked字段
        post_data = {
            "id": post.id,
            "post_id": post.post_id,
            "title": post.title,
            "content": post.content,
            "category": post.category,
            "author_id": post.author_id,
            "author_name": post.author.username if post.author else None,
            "view_count": post.view_count,
            "like_count": post.like_count,
            "comment_count": post.comment_count,
            "status": post.status,
            "is_pinned": post.is_pinned,
            "is_essence": post.is_essence,
            "is_liked": is_liked,
            "created_at": post.created_at.isoformat() if post.created_at else None,
            "updated_at": post.updated_at.isoformat() if post.updated_at else None
        }

        return post_data
    except Exception as e:
        logger.error(f"Error getting post detail: {e}")
        return error_response(message=str(e), code=500)


@router.post("/posts", response_model=Dict[str, Any])
async def create_post(
    post: PostCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    创建帖子

    创建新帖子并返回帖子信息
    """
    try:
        # 调用社区服务
        db_post = CommunityService.create_post(db, post, current_user.id)
        # 社区API返回直接的帖子数据，转换为字典
        post_dict = {
            "id": db_post.id,
            "post_id": db_post.post_id,
            "title": db_post.title,
            "content": db_post.content,
            "category": db_post.category,
            "tags": post.tags if isinstance(post.tags, list) else [],
            "author_id": db_post.author_id,
            "author_name": current_user.username,
            "view_count": db_post.view_count,
            "like_count": db_post.like_count,
            "comment_count": db_post.comment_count,
            "status": db_post.status,
            "is_pinned": db_post.is_pinned,
            "is_essence": db_post.is_essence,
            "created_at": db_post.created_at.isoformat() if db_post.created_at else None,
            "updated_at": db_post.updated_at.isoformat() if db_post.updated_at else None
        }
        return post_dict
    except Exception as e:
        logger.error(f"Error creating post: {e}")
        return error_response(message=str(e), code=500)


@router.put("/posts/{post_id}", response_model=Dict[str, Any])
async def update_post(
    post_id: int = Path(..., description="帖子ID"),
    post: PostUpdate = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    更新帖子

    更新帖子信息并返回更新后的帖子
    """
    try:
        # 调用社区服务
        db_post = CommunityService.update_post(db, post_id, post, current_user.id)
        if not db_post:
            return error_response(message="Post not found", code=404)

        return success_response(data=db_post)
    except ValueError as e:
        return error_response(message=str(e), code=403)
    except Exception as e:
        logger.error(f"Error updating post: {e}")
        return error_response(message=str(e), code=500)


@router.delete("/posts/{post_id}", response_model=Dict[str, Any])
async def delete_post(
    post_id: int = Path(..., description="帖子ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    删除帖子

    删除指定帖子
    """
    try:
        # 调用社区服务
        success = CommunityService.delete_post(db, post_id, current_user.id)
        if not success:
            return error_response(message="Post not found", code=404)

        return success_response(message="Post deleted successfully")
    except ValueError as e:
        return error_response(message=str(e), code=403)
    except Exception as e:
        logger.error(f"Error deleting post: {e}")
        return error_response(message=str(e), code=500)





@router.get("/posts/{post_id}/comments", response_model=Dict[str, Any])
async def get_comments(
    post_id: int = Path(..., description="帖子ID"),
    query: CommentQuery = Depends(),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取评论列表

    获取指定帖子的评论列表
    """
    try:
        # 调用社区服务
        result = CommunityService.get_comments(db, post_id, query)

        # 转换评论对象为字典
        comments_data = []
        for comment in result["items"]:
            comment_dict = {
                "id": comment.id,
                "comment_id": comment.comment_id,
                "post_id": comment.post_id,
                "content": comment.content,
                "author_id": comment.author_id,
                "author_name": comment.author.username if comment.author else None,
                "parent_id": comment.parent_id,
                "like_count": comment.like_count,
                "created_at": comment.created_at.isoformat() if comment.created_at else None,
                "updated_at": comment.updated_at.isoformat() if comment.updated_at else None
            }
            comments_data.append(comment_dict)

        # 社区API使用直接的分页格式，不包装在success_response中
        return {
            "total": result["total"],
            "results": comments_data,
            "page": result["page"],
            "page_size": result["page_size"],
            "pages": result["pages"],
            "has_next": result["has_next"],
            "has_prev": result["has_prev"]
        }
    except Exception as e:
        logger.error(f"Error getting comments: {e}")
        return error_response(message=str(e), code=500)


@router.post("/posts/{post_id}/comments", response_model=Dict[str, Any])
async def create_comment(
    comment: CommentCreate,
    post_id: int = Path(..., description="帖子ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    发表评论

    在指定帖子下发表评论
    """
    try:
        # 调用社区服务
        db_comment = CommunityService.create_comment(db, post_id, comment, current_user.id)
        # 社区API返回直接的评论数据，转换为字典
        comment_dict = {
            "id": db_comment.id,
            "comment_id": db_comment.comment_id,
            "post_id": post_id,  # 返回数字ID而不是字符串post_id
            "content": db_comment.content,
            "author_id": db_comment.author_id,
            "author_name": current_user.username,
            "parent_id": db_comment.parent_id,
            "like_count": db_comment.like_count,
            "created_at": db_comment.created_at.isoformat() if db_comment.created_at else None,
            "updated_at": db_comment.updated_at.isoformat() if db_comment.updated_at else None
        }
        return comment_dict
    except ValueError as e:
        return error_response(message=str(e), code=400)
    except Exception as e:
        logger.error(f"Error creating comment: {e}")
        return error_response(message=str(e), code=500)


@router.post("/posts/{post_id}/like", response_model=Dict[str, Any])
async def like_post(
    post_id: int = Path(..., description="帖子ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    点赞帖子

    对指定帖子进行点赞
    """
    try:
        # 调用社区服务
        like = CommunityService.like_post(db, post_id, current_user.id)
        # 转换Like对象为字典
        like_data = {
            "id": like.id,
            "post_id": post_id,  # 返回数字ID
            "user_id": like.user_id,
            "created_at": like.created_at.isoformat() if like.created_at else None
        }
        return success_response(data=like_data, message="点赞成功")
    except ValueError as e:
        return error_response(message=str(e), code=400)
    except Exception as e:
        logger.error(f"Error liking post: {e}")
        return error_response(message=str(e), code=500)


@router.delete("/posts/{post_id}/like", response_model=Dict[str, Any])
async def unlike_post(
    post_id: int = Path(..., description="帖子ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    取消点赞

    取消对指定帖子的点赞
    """
    try:
        # 调用社区服务
        success = CommunityService.unlike_post(db, post_id, current_user.id)
        if not success:
            return error_response(message="未找到点赞记录", code=404)

        return success_response(message="取消点赞成功")
    except Exception as e:
        logger.error(f"Error unliking post: {e}")
        return error_response(message=str(e), code=500)


@router.get("/tags", response_model=Dict[str, Any])
async def get_tags(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取标签列表

    获取所有可用的标签列表
    """
    try:
        # 调用社区服务
        tags = CommunityService.get_tags(db)
        return success_response(data=tags)
    except Exception as e:
        logger.error(f"Error getting tags: {e}")
        return error_response(message=str(e), code=500)


@router.put("/posts/{post_id}/review", response_model=Dict[str, Any])
async def review_post(
    post_id: int = Path(..., description="帖子ID"),
    status: int = Query(..., description="状态（0: 待审核, 1: 已通过, 2: 已拒绝）"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    审核帖子

    审核指定帖子，设置其状态
    """
    try:
        # 调用社区服务
        post = CommunityService.review_post(db, post_id, status, current_user.id)
        if not post:
            return error_response(message="Post not found", code=404)

        return success_response(data=post)
    except Exception as e:
        logger.error(f"Error reviewing post: {e}")
        return error_response(message=str(e), code=500)
