#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
爬虫管理API路由
"""

from typing import Dict, List, Optional, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, BackgroundTasks, status
from sqlalchemy.orm import Session

from apps.backend.api.config.database import get_db
from apps.backend.api.dependencies.auth import get_current_active_user, get_current_active_admin
from apps.backend.api.models.user import User
from apps.backend.api.models.admin import Admin
from apps.backend.api.schemas.crawler import (
    CrawlerCreate, CrawlerUpdate, CrawlerQuery, CrawlerTaskCreate,
    CrawlerTaskUpdate, CrawlerTaskQuery, CrawlerLogQuery
)
from apps.backend.api.services.crawler import CrawlerService
from apps.backend.api.utils.response import success_response, error_response
from apps.backend.utils.logger import setup_logger

# 创建路由
router = APIRouter()

# 设置日志
logger = setup_logger("api_crawler")


@router.get("/crawlers", response_model=Dict[str, Any])
async def get_crawlers(
    query: CrawlerQuery = Depends(),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取爬虫列表

    获取系统中所有可用的爬虫列表
    """
    try:
        # 调用爬虫服务
        result = CrawlerService.get_crawlers(db, query)
        return success_response(data=result)
    except Exception as e:
        logger.error(f"Error getting crawlers: {e}")
        return error_response(message=str(e), code=500)


@router.post("/crawlers", response_model=Dict[str, Any])
async def create_crawler(
    crawler: CrawlerCreate,
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    创建爬虫

    创建新的爬虫配置
    """
    try:
        # 调用爬虫服务
        result = CrawlerService.create_crawler(db, crawler, current_admin.id)
        return success_response(data=result, message="爬虫创建成功")
    except Exception as e:
        logger.error(f"Error creating crawler: {e}")
        return error_response(message=str(e), code=500)


@router.get("/crawlers/{crawler_id}", response_model=Dict[str, Any])
async def get_crawler(
    crawler_id: int = Path(..., description="爬虫ID"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取爬虫详情

    获取指定爬虫的详细信息
    """
    try:
        # 调用爬虫服务
        result = CrawlerService.get_crawler(db, crawler_id)
        if not result:
            return error_response(message="爬虫不存在", code=404)
        return success_response(data=result)
    except Exception as e:
        logger.error(f"Error getting crawler: {e}")
        return error_response(message=str(e), code=500)


@router.put("/crawlers/{crawler_id}", response_model=Dict[str, Any])
async def update_crawler(
    crawler_id: int = Path(..., description="爬虫ID"),
    crawler: CrawlerUpdate = None,
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    更新爬虫

    更新指定爬虫的配置信息
    """
    try:
        # 调用爬虫服务
        result = CrawlerService.update_crawler(db, crawler_id, crawler)
        if not result:
            return error_response(message="爬虫不存在", code=404)
        return success_response(data=result, message="爬虫更新成功")
    except Exception as e:
        logger.error(f"Error updating crawler: {e}")
        return error_response(message=str(e), code=500)


@router.delete("/crawlers/{crawler_id}", response_model=Dict[str, Any])
async def delete_crawler(
    crawler_id: int = Path(..., description="爬虫ID"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    删除爬虫

    删除指定的爬虫配置
    """
    try:
        # 调用爬虫服务
        result = CrawlerService.delete_crawler(db, crawler_id)
        if not result:
            return error_response(message="爬虫不存在", code=404)
        return success_response(message="爬虫删除成功")
    except Exception as e:
        logger.error(f"Error deleting crawler: {e}")
        return error_response(message=str(e), code=500)


@router.post("/tasks", response_model=Dict[str, Any])
async def create_task(
    task: CrawlerTaskCreate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    创建爬虫任务

    创建新的爬虫任务
    """
    try:
        # 调用爬虫服务
        result = CrawlerService.create_task(db, task, current_admin.id)

        # 如果需要立即执行，添加到后台任务
        if task.run_immediately:
            background_tasks.add_task(CrawlerService.run_task, db, result.id)
            return success_response(data=result, message="爬虫任务已创建并开始执行")

        return success_response(data=result, message="爬虫任务创建成功")
    except Exception as e:
        logger.error(f"Error creating crawler task: {e}")
        return error_response(message=str(e), code=500)


@router.get("/tasks", response_model=Dict[str, Any])
async def get_tasks(
    query: CrawlerTaskQuery = Depends(),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取爬虫任务列表

    获取系统中的爬虫任务列表
    """
    try:
        # 调用爬虫服务
        result = CrawlerService.get_tasks(db, query)
        return success_response(data=result)
    except Exception as e:
        logger.error(f"Error getting crawler tasks: {e}")
        return error_response(message=str(e), code=500)


@router.get("/tasks/{task_id}", response_model=Dict[str, Any])
async def get_task(
    task_id: int = Path(..., description="任务ID"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取爬虫任务详情

    获取指定爬虫任务的详细信息
    """
    try:
        # 调用爬虫服务
        result = CrawlerService.get_task(db, task_id)
        if not result:
            return error_response(message="爬虫任务不存在", code=404)
        return success_response(data=result)
    except Exception as e:
        logger.error(f"Error getting crawler task: {e}")
        return error_response(message=str(e), code=500)


@router.post("/tasks/{task_id}/run", response_model=Dict[str, Any])
async def run_task(
    task_id: int = Path(..., description="任务ID"),
    background_tasks: BackgroundTasks = None,
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    运行爬虫任务

    运行指定的爬虫任务
    """
    try:
        # 检查任务是否存在
        task = CrawlerService.get_task(db, task_id)
        if not task:
            return error_response(message="爬虫任务不存在", code=404)

        # 添加到后台任务
        background_tasks.add_task(CrawlerService.run_task, db, task_id)

        return success_response(message="爬虫任务已开始执行")
    except Exception as e:
        logger.error(f"Error running crawler task: {e}")
        return error_response(message=str(e), code=500)


@router.post("/tasks/{task_id}/stop", response_model=Dict[str, Any])
async def stop_task(
    task_id: int = Path(..., description="任务ID"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    停止爬虫任务

    停止指定的爬虫任务
    """
    try:
        # 调用爬虫服务
        result = CrawlerService.stop_task(db, task_id)
        if not result:
            return error_response(message="爬虫任务不存在或无法停止", code=404)
        return success_response(message="爬虫任务已停止")
    except Exception as e:
        logger.error(f"Error stopping crawler task: {e}")
        return error_response(message=str(e), code=500)


@router.get("/logs", response_model=Dict[str, Any])
async def get_logs(
    query: CrawlerLogQuery = Depends(),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取爬虫日志列表

    获取系统中的爬虫日志列表
    """
    try:
        # 调用爬虫服务
        result = CrawlerService.get_logs(db, query)
        return success_response(data=result)
    except Exception as e:
        logger.error(f"Error getting crawler logs: {e}")
        return error_response(message=str(e), code=500)


@router.get("/logs/{log_id}", response_model=Dict[str, Any])
async def get_log(
    log_id: int = Path(..., description="日志ID"),
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取爬虫日志详情

    获取指定爬虫日志的详细信息
    """
    try:
        # 调用爬虫服务
        result = CrawlerService.get_log(db, log_id)
        if not result:
            return error_response(message="爬虫日志不存在", code=404)
        return success_response(data=result)
    except Exception as e:
        logger.error(f"Error getting crawler log: {e}")
        return error_response(message=str(e), code=500)


@router.get("/stats", response_model=Dict[str, Any])
async def get_stats(
    db: Session = Depends(get_db),
    current_admin: Admin = Depends(get_current_active_admin)
):
    """
    获取爬虫统计信息

    获取系统中的爬虫统计信息
    """
    try:
        # 调用爬虫服务
        result = CrawlerService.get_stats(db)
        return success_response(data=result)
    except Exception as e:
        logger.error(f"Error getting crawler stats: {e}")
        return error_response(message=str(e), code=500)
