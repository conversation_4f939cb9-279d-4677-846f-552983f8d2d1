#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
岗位相关API路由
"""

import logging
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.orm import Session

from apps.backend.api.config.database import get_db
from apps.backend.api.dependencies.auth import get_current_active_user
from apps.backend.api.models.user import User
from apps.backend.api.models.job import Job
from apps.backend.api.models.interaction import Favorite as JobFavorite
from apps.backend.api.schemas.job import (
    JobDetail, JobQuery, NaturalLanguageSearchRequest,
    JobMatchRequest, JobMatchResponse
)
from apps.backend.api.services.job import JobService
from apps.backend.api.utils.response import success_response, error_response
from apps.backend.api.utils.logger import setup_logger
from apps.backend.api.utils.cache_decorators import (
    cache_response, cache_invalidate,
    job_list_key_generator, job_detail_key_generator, job_search_key_generator,
    cache_successful_response, cache_non_empty_result
)

# 创建路由器
router = APIRouter(
    tags=["jobs"],
    responses={
        404: {"description": "Not found"},
        500: {"description": "Internal server error"},
    },
)

# 设置日志
logger = setup_logger("api_jobs")


@router.get(
    "/",
    response_model=Dict[str, Any],
    summary="搜索岗位",
    description="根据多种条件搜索岗位，支持分页和排序",
    response_description="返回岗位列表和分页信息",
    responses={
        200: {
            "description": "成功获取岗位列表",
            "content": {
                "application/json": {
                    "example": {
                        "code": 200,
                        "message": "success",
                        "data": {
                            "total": 100,
                            "page": 1,
                            "page_size": 10,
                            "pages": 10,
                            "has_next": True,
                            "has_prev": False,
                            "items": [
                                {
                                    "id": "job123",
                                    "title": "软件工程师",
                                    "company": "示例科技有限公司",
                                    "location": "北京",
                                    "salary": "15k-25k",
                                    "education": "本科",
                                    "experience": "3-5年",
                                    "publish_date": "2023-12-01"
                                }
                            ]
                        }
                    }
                }
            }
        }
    }
)
@cache_response(
    cache_type="job_list",
    ttl=300,  # 5分钟缓存
    key_generator=job_list_key_generator,
    condition=cache_successful_response
)
async def search_jobs(
    query: JobQuery = Depends(),
    db: Session = Depends(get_db)
):
    """
    搜索岗位

    根据多种条件搜索岗位，支持分页和排序

    - **keywords**: 关键词搜索
    - **location**: 工作地点
    - **education**: 学历要求
    - **experience**: 经验要求
    - **job_type**: 工作类型
    - **salary_min**: 最低薪资
    - **salary_max**: 最高薪资
    - **company_type**: 单位性质
    - **industry**: 行业
    - **major**: 专业要求
    - **is_graduate_friendly**: 是否应届生友好
    - **page**: 页码
    - **page_size**: 每页数量
    - **sort**: 排序字段
    - **order**: 排序方向
    """
    try:
        # 调用岗位服务
        result = JobService.get_jobs(db, query)
        return success_response(data=result)
    except Exception as e:
        logger.error(f"Error searching jobs: {e}")
        return error_response(message=str(e), code=500)


@router.post("/nl-search", response_model=Dict[str, Any])
async def natural_language_search(
    request: NaturalLanguageSearchRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    自然语言搜索

    使用自然语言描述搜索岗位
    """
    try:
        # 这里应该调用AI服务解析自然语言查询
        # 暂时使用简单的关键词提取
        keywords = request.query.split()

        # 构建查询参数
        query = JobQuery(
            keywords=" ".join(keywords),
            location=None,
            education=None,
            experience=None,
            job_type=None,
            salary=None,
            major=None,
            is_graduate_friendly=None,
            sort="publish_date",
            order="desc",
            page=1,
            page_size=10
        )

        # 调用岗位服务
        result = JobService.get_jobs(db, query)
        return success_response(data=result)
    except Exception as e:
        logger.error(f"Error performing natural language search: {e}")
        return error_response(message=str(e), code=500)


@router.get("/recommend", response_model=Dict[str, Any])
async def recommend_jobs(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    推荐岗位

    根据用户资料推荐匹配的岗位
    """
    try:
        # 获取用户资料
        # 这里应该从数据库获取用户资料
        # 暂时使用模拟数据
        user_profile = {
            "education": current_user.education if hasattr(current_user, 'education') else "",
            "major": current_user.major if hasattr(current_user, 'major') else "",
            "experience": "",
            "location_preference": ""
        }

        # 构建查询参数
        query = JobQuery(
            keywords=None,
            location=None,
            education=user_profile["education"],
            experience=None,
            job_type=None,
            salary=None,
            major=user_profile["major"],
            is_graduate_friendly=True,
            sort="publish_date",
            order="desc",
            page=1,
            page_size=10
        )

        # 调用岗位服务
        result = JobService.get_jobs(db, query)
        return success_response(data=result)
    except Exception as e:
        logger.error(f"Error recommending jobs: {e}")
        return error_response(message=str(e), code=500)


@router.get("/favorites", response_model=Dict[str, Any])
async def get_favorite_jobs(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量")
):
    """
    获取收藏岗位列表

    获取用户收藏的岗位列表
    """
    try:
        # 查询收藏记录
        query = db.query(Job).join(
            JobFavorite, JobFavorite.job_id == Job.job_id
        ).filter(
            JobFavorite.user_id == current_user.id
        ).order_by(JobFavorite.created_at.desc())

        # 分页
        total = query.count()
        items = query.offset((page - 1) * page_size).limit(page_size).all()

        # 将SQLAlchemy模型转换为字典
        job_items = []
        for job in items:
            job_dict = {
                "job_id": job.job_id,
                "title": job.title,
                "company_name": job.company_name,
                "work_location": job.work_location,
                "salary_range": job.salary_range,
                "education_requirement": job.education_requirement,
                "experience_requirement": job.experience_requirement,
                "job_type": job.job_type,
                "publish_date": job.publish_date.isoformat() if hasattr(job, 'publish_date') and job.publish_date is not None else None,
                "job_description": job.job_description,
                "major_requirement": job.major_requirement,
                "is_graduate_friendly": job.is_graduate_friendly,
                "source_url": job.source_url,
                "data_source": job.data_source,
                "created_at": job.created_at.isoformat() if hasattr(job, 'created_at') and job.created_at is not None else None,
                "updated_at": job.updated_at.isoformat() if hasattr(job, 'updated_at') and job.updated_at is not None else None,
            }
            job_items.append(job_dict)

        # 构建结果
        result = {
            "total": total,
            "page": page,
            "page_size": page_size,
            "pages": (total + page_size - 1) // page_size,
            "has_next": page * page_size < total,
            "has_prev": page > 1,
            "items": job_items
        }

        return success_response(data=result)
    except Exception as e:
        logger.error(f"Error getting favorite jobs: {e}")
        return error_response(message=str(e), code=500)


@router.get("/{job_id}", response_model=Dict[str, Any])
@cache_response(
    cache_type="job_detail",
    ttl=1800,  # 30分钟缓存
    key_generator=job_detail_key_generator,
    condition=cache_successful_response
)
async def get_job_detail(
    job_id: str = Path(..., description="岗位ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取岗位详情

    根据岗位ID获取岗位详细信息
    """
    try:
        # 调用岗位服务
        job = JobService.get_job(db, job_id)
        if not job:
            from fastapi import HTTPException
            raise HTTPException(status_code=404, detail="Job not found")

        # 更新浏览历史（这里应该调用浏览历史服务）
        # 暂时省略

        return success_response(data=job)
    except HTTPException:
        # 重新抛出HTTPException，不要捕获它
        raise
    except Exception as e:
        logger.error(f"Error getting job detail: {e}")
        return error_response(message=str(e), code=500)


@router.get("/{job_id}/interview-prep", response_model=Dict[str, Any])
async def prepare_interview(
    job_id: str = Path(..., description="岗位ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    准备面试

    获取岗位的面试准备材料
    """
    try:
        # 调用岗位服务
        job = JobService.get_job(db, job_id)
        if not job:
            return error_response(message="Job not found", code=404)

        # 这里应该调用AI服务生成面试准备材料
        # 暂时使用模拟数据
        interview_prep = {
            "general_questions": [
                "请介绍一下你自己",
                "你为什么选择应聘这个岗位",
                "你的优势和劣势是什么"
            ],
            "professional_questions": [
                f"你对{job['title']}岗位有什么了解",
                "你有哪些相关的工作经验",
                "你如何处理工作中的挑战"
            ],
            "preparation_tips": [
                "提前了解公司业务和文化",
                "准备具体的项目经验和成果",
                "思考你能为公司带来什么价值"
            ]
        }

        return success_response(data=interview_prep)
    except Exception as e:
        logger.error(f"Error preparing interview: {e}")
        return error_response(message=str(e), code=500)


@router.post("/{job_id}/match", response_model=Dict[str, Any])
async def analyze_job_match(
    job_id: str = Path(..., description="岗位ID"),
    request: Optional[JobMatchRequest] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    分析岗位匹配度

    分析用户与岗位的匹配程度
    """
    try:
        # 如果没有提供用户资料，使用当前用户的资料
        if not request:
            # 这里应该从数据库获取用户资料
            # 暂时使用模拟数据
            user_profile = {
                "education": current_user.education if hasattr(current_user, 'education') else "",
                "major": current_user.major if hasattr(current_user, 'major') else "",
                "experience": "",
                "location_preference": ""
            }
            request = JobMatchRequest(user_profile=user_profile)

        # 调用岗位服务
        match_result = JobService.analyze_job_match(db, job_id, request)
        return success_response(data=match_result)
    except Exception as e:
        logger.error(f"Error analyzing job match: {e}")
        return error_response(message=str(e), code=500)

@router.post("/{job_id}/favorite", response_model=Dict[str, Any])
@cache_invalidate(cache_types=["job_list", "hot_jobs"])
async def favorite_job(
    job_id: str = Path(..., description="岗位ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    收藏岗位

    将岗位添加到收藏列表
    """
    try:
        # 检查岗位是否存在
        job = JobService.get_job(db, job_id)
        if not job:
            return error_response(message="Job not found", code=404)

        # 检查是否已收藏
        favorite = db.query(JobFavorite).filter(
            JobFavorite.job_id == job["job_id"],
            JobFavorite.user_id == current_user.id
        ).first()

        if favorite:
            return success_response(data={"message": "Already favorited"})

        # 创建收藏
        favorite = JobFavorite(
            job_id=job["job_id"],
            user_id=current_user.id
        )
        db.add(favorite)
        db.commit()
        db.refresh(favorite)

        # 将SQLAlchemy模型转换为字典
        favorite_dict = {
            "id": favorite.id,
            "job_id": favorite.job_id,
            "user_id": favorite.user_id,
            "created_at": favorite.created_at.isoformat() if hasattr(favorite, 'created_at') else None,
        }
        return success_response(data=favorite_dict)
    except Exception as e:
        logger.error(f"Error favoriting job: {e}")
        return error_response(message=str(e), code=500)


@router.delete("/{job_id}/favorite", response_model=Dict[str, Any])
@cache_invalidate(cache_types=["job_list", "hot_jobs"])
async def unfavorite_job(
    job_id: str = Path(..., description="岗位ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    取消收藏岗位

    从收藏列表中移除岗位
    """
    try:
        # 检查岗位是否存在
        job = JobService.get_job(db, job_id)
        if not job:
            return error_response(message="Job not found", code=404)

        # 查询收藏记录
        favorite = db.query(JobFavorite).filter(
            JobFavorite.job_id == job["job_id"],
            JobFavorite.user_id == current_user.id
        ).first()

        if not favorite:
            return error_response(message="Favorite not found", code=404)

        # 删除收藏
        db.delete(favorite)
        db.commit()

        return success_response(message="Unfavorite success")
    except Exception as e:
        logger.error(f"Error unfavoriting job: {e}")
        return error_response(message=str(e), code=500)

