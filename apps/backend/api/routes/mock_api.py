#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Mock API路由 - 用于开发环境测试
提供模拟的认证和数据接口
"""

from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, HTTPException, status, Query, Depends
from fastapi.security import OAuth2PasswordRequestForm
from pydantic import BaseModel

from apps.backend.api.config.settings import settings
from apps.backend.api.utils.security import create_access_token, create_refresh_token
from apps.backend.utils.logger import setup_logger

logger = setup_logger(__name__)

router = APIRouter()

# Mock用户数据
MOCK_USERS = {
    "admin": {
        "id": "1",
        "username": "admin",
        "email": "<EMAIL>",
        "password": "admin123",  # 明文密码，仅用于Mock
        "role": "admin",
        "permissions": ["user:read", "user:write", "job:read", "job:write", "admin:all"],
        "profile": {
            "realName": "系统管理员",
            "avatar": "",
        },
        "registeredAt": "2024-01-01T00:00:00Z",
        "status": "active",
    },
    "user": {
        "id": "2",
        "username": "user",
        "email": "<EMAIL>",
        "password": "user123",
        "role": "user",
        "permissions": ["job:read", "user:read"],
        "profile": {
            "realName": "测试用户",
        },
        "registeredAt": "2024-01-01T00:00:00Z",
        "trialExpiresAt": (datetime.now() + timedelta(days=30)).isoformat(),
        "status": "active",
    }
}

# Mock Token存储
MOCK_TOKENS = {
    "mock-admin-token": "1",
    "mock-user-token": "2",
    "mock-admin-refresh-token": "1",
    "mock-user-refresh-token": "2",
}


class LoginRequest(BaseModel):
    username: str
    password: str
    userType: Optional[str] = "admin"


class RefreshTokenRequest(BaseModel):
    refresh_token: str


@router.post("/auth/login")
async def mock_login(request: LoginRequest):
    """Mock登录接口"""
    logger.info(f"Mock login attempt for user: {request.username}")
    
    # 验证用户
    if request.username not in MOCK_USERS:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误"
        )
    
    user_data = MOCK_USERS[request.username]
    if user_data["password"] != request.password:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误"
        )
    
    # 生成Token
    access_token = f"mock-{request.username}-token"
    refresh_token = f"mock-{request.username}-refresh-token"
    
    # 更新Token存储
    MOCK_TOKENS[access_token] = user_data["id"]
    MOCK_TOKENS[refresh_token] = user_data["id"]
    
    # 返回登录响应
    response_data = {
        "token": access_token,
        "refreshToken": refresh_token,
        "user": {
            "id": user_data["id"],
            "username": user_data["username"],
            "email": user_data["email"],
            "role": user_data["role"],
            "permissions": user_data["permissions"],
            "profile": user_data["profile"],
            "registeredAt": user_data["registeredAt"],
            "lastLoginAt": datetime.now().isoformat(),
            "status": user_data["status"],
        },
        "expiresIn": 7200,
    }
    
    if "trialExpiresAt" in user_data:
        response_data["user"]["trialExpiresAt"] = user_data["trialExpiresAt"]
    
    logger.info(f"Mock login successful for user: {request.username}")
    return response_data


@router.post("/auth/refresh-token")
async def mock_refresh_token(refresh_token: str = Query(...)):
    """Mock Token刷新接口"""
    logger.info(f"Mock token refresh attempt with token: {refresh_token}")
    
    # 验证刷新Token
    if refresh_token not in MOCK_TOKENS:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )
    
    user_id = MOCK_TOKENS[refresh_token]
    
    # 查找用户
    user_data = None
    for username, data in MOCK_USERS.items():
        if data["id"] == user_id:
            user_data = data
            break
    
    if not user_data:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )
    
    # 生成新Token
    new_access_token = f"new-mock-{user_data['username']}-token-{int(datetime.now().timestamp())}"
    new_refresh_token = f"new-mock-{user_data['username']}-refresh-token-{int(datetime.now().timestamp())}"
    
    # 更新Token存储
    MOCK_TOKENS[new_access_token] = user_id
    MOCK_TOKENS[new_refresh_token] = user_id
    
    logger.info(f"Mock token refresh successful for user: {user_data['username']}")
    
    return {
        "access_token": new_access_token,
        "refresh_token": new_refresh_token,
        "token_type": "bearer",
        "expires_in": 7200,
    }


@router.get("/admin/statistics")
async def mock_admin_statistics():
    """Mock管理后台统计数据"""
    logger.info("Returning mock admin statistics")
    
    return {
        "totalUsers": 1248,
        "newUsersToday": 23,
        "activeUsers": 856,
        "totalJobs": 342,
        "newJobsToday": 8,
        "totalApplications": 2156,
        "systemStatus": "normal",
        "trends": {
            "userGrowth": 12.5,
            "jobGrowth": 8.9,
            "applicationGrowth": 15.2,
            "engagementRate": 68.5
        }
    }


@router.get("/admin/analytics/user-activity")
async def mock_user_activity(
    time_range: str = Query("7d"),
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None)
):
    """Mock用户活动数据"""
    logger.info(f"Returning mock user activity data for range: {time_range}")
    
    # 生成模拟数据
    mock_data = []
    days = 30 if time_range == "30d" else 7
    
    for i in range(days):
        date = (datetime.now() - timedelta(days=days-i-1)).strftime("%Y-%m-%d")
        mock_data.append({
            "date": date,
            "users": 1200 + (i % 7) * 100,
            "newUsers": 50 + (i % 5) * 10,
            "activeUsers": 800 + (i % 6) * 50,
        })
    
    return mock_data


@router.get("/admin/analytics/real-time/metrics")
async def mock_realtime_metrics():
    """Mock实时指标数据"""
    logger.info("Returning mock real-time metrics")
    
    return {
        "totalUsers": 1248,
        "activeUsers": 856,
        "totalJobs": 342,
        "totalApplications": 2156,
        "systemUptime": 99.8,
        "memoryUsage": 68.5,
        "cpuUsage": 45.2,
        "onlineUsers": 156,
        "currentSearches": 23,
        "responseTime": 120,
        "errorRate": 0.2
    }


@router.get("/health")
async def mock_health():
    """Mock健康检查"""
    return {"status": "ok", "mode": "mock", "timestamp": datetime.now().isoformat()}
