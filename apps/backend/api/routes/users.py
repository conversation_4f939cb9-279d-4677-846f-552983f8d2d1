#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
用户相关API路由
"""

import logging
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, Depends, HTTPException, status, Path, Query
from sqlalchemy.orm import Session

from apps.backend.api.config.database import get_db
from apps.backend.api.dependencies.auth import get_current_active_user
from apps.backend.api.models.user import User
from apps.backend.api.schemas.user import (
    UserCreate, UserUpdate, UserProfileCreate, UserProfileUpdate,
    User as UserSchema, UserProfile as UserProfileSchema
)
from apps.backend.api.schemas.interaction import (
    Favorite as FavoriteSchema, BrowseHistory as BrowseHistorySchema
)
from apps.backend.api.services.user import UserService
from apps.backend.api.utils.response import success_response, error_response
from apps.backend.utils.logger import setup_logger

# 创建路由器
router = APIRouter(
    tags=["users"],
    responses={
        404: {"description": "Not found"},
        400: {"description": "Bad request"},
        500: {"description": "Internal server error"},
    },
)

# 设置日志
logger = setup_logger("api_users")


@router.post("/", response_model=Dict[str, Any])
async def create_user(
    user: UserCreate,
    db: Session = Depends(get_db)
):
    """
    创建用户

    创建新用户并返回用户信息
    """
    try:
        # 检查用户名是否已存在
        existing_user = UserService.get_user_by_username(db, user.username)
        if existing_user:
            return error_response(message="Username already registered", code=400)

        # 创建用户
        db_user = UserService.create_user(db, user)
        return success_response(data=db_user)
    except Exception as e:
        logger.error(f"Error creating user: {e}")
        return error_response(message=str(e), code=500)


@router.get("/me")
async def read_users_me(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取当前用户信息

    返回当前登录用户的详细信息
    """
    try:
        # 直接返回用户信息，符合测试期望的格式
        return {
            "id": current_user.id,
            "username": current_user.username,
            "email": current_user.email,
            "real_name": current_user.real_name,
            "education": current_user.education,
            "major": current_user.major,
            "graduation_year": current_user.graduation_year,
            "school": current_user.school,
            "phone": current_user.phone,
            "avatar_url": current_user.avatar_url,
            "status": current_user.status,
            "last_login": current_user.last_login,
            "created_at": current_user.created_at,
            "updated_at": current_user.updated_at
        }
    except Exception as e:
        logger.error(f"Error getting current user: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.put("/me", response_model=Dict[str, Any])
async def update_user_me(
    user: UserUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    更新当前用户信息

    更新当前登录用户的基本信息
    """
    try:
        # 更新用户信息
        updated_user = UserService.update_user(db, current_user.id, user)
        if not updated_user:
            return error_response(message="User not found", code=404)

        return success_response(data=updated_user)
    except Exception as e:
        logger.error(f"Error updating user: {e}")
        return error_response(message=str(e), code=500)


@router.get(
    "/me/profile",
    response_model=Dict[str, Any],
    summary="获取用户资料",
    description="获取当前登录用户的详细资料",
    response_description="返回用户资料信息",
    responses={
        200: {
            "description": "成功获取用户资料",
            "content": {
                "application/json": {
                    "example": {
                        "code": 200,
                        "message": "success",
                        "data": {
                            "id": 1,
                            "user_id": 1,
                            "education": "本科",
                            "major": "计算机科学与技术",
                            "skills": "Python, JavaScript, SQL",
                            "experience": "3年软件开发经验",
                            "location_preference": "北京",
                            "job_type_preference": "全职",
                            "resume_url": "https://example.com/resume.pdf"
                        }
                    }
                }
            }
        },
        404: {
            "description": "用户资料不存在"
        }
    }
)
async def get_user_profile(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取用户资料

    获取当前登录用户的详细资料，包括教育背景、专业、技能、经验等信息
    """
    try:
        # 获取用户资料
        user_profile = UserService.get_user_profile(db, current_user.id)
        if not user_profile:
            return error_response(message="User profile not found", code=404)

        return success_response(data=user_profile)
    except Exception as e:
        logger.error(f"Error getting user profile: {e}")
        return error_response(message=str(e), code=500)


@router.post("/me/profile", response_model=Dict[str, Any])
async def create_user_profile(
    profile: UserProfileCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    创建用户资料

    为当前登录用户创建详细资料
    """
    try:
        # 创建用户资料
        user_profile = UserService.create_user_profile(db, current_user.id, profile)
        return success_response(data=user_profile)
    except ValueError as e:
        return error_response(message=str(e), code=400)
    except Exception as e:
        logger.error(f"Error creating user profile: {e}")
        return error_response(message=str(e), code=500)


@router.put("/me/profile", response_model=Dict[str, Any])
async def update_user_profile(
    profile: UserProfileUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    更新用户资料

    更新当前登录用户的详细资料
    """
    try:
        # 更新用户资料
        user_profile = UserService.update_user_profile(db, current_user.id, profile)
        return success_response(data=user_profile)
    except Exception as e:
        logger.error(f"Error updating user profile: {e}")
        return error_response(message=str(e), code=500)


@router.get("/me/favorites", response_model=Dict[str, Any])
async def get_user_favorites(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取收藏的岗位

    获取当前登录用户收藏的岗位列表
    """
    try:
        # 获取收藏列表
        favorites = UserService.get_user_favorites(db, current_user.id, skip, limit)
        return success_response(data={
            "total": len(favorites),
            "items": favorites
        })
    except Exception as e:
        logger.error(f"Error getting user favorites: {e}")
        return error_response(message=str(e), code=500)


@router.post("/me/favorites/{job_id}", response_model=Dict[str, Any])
async def add_favorite(
    job_id: str = Path(..., description="岗位ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    添加收藏

    将指定岗位添加到收藏列表
    """
    try:
        # 添加收藏
        favorite = UserService.add_favorite(db, current_user.id, job_id)
        return success_response(data=favorite)
    except Exception as e:
        logger.error(f"Error adding favorite: {e}")
        return error_response(message=str(e), code=500)


@router.delete("/me/favorites/{job_id}", response_model=Dict[str, Any])
async def remove_favorite(
    job_id: str = Path(..., description="岗位ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    取消收藏

    将指定岗位从收藏列表中移除
    """
    try:
        # 取消收藏
        success = UserService.remove_favorite(db, current_user.id, job_id)
        if not success:
            return error_response(message="Favorite not found", code=404)

        return success_response(message="Favorite removed successfully")
    except Exception as e:
        logger.error(f"Error removing favorite: {e}")
        return error_response(message=str(e), code=500)


@router.get("/me/views", response_model=Dict[str, Any])
async def get_user_views(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取浏览历史

    获取当前登录用户的岗位浏览历史
    """
    try:
        # 获取浏览历史
        views = UserService.get_user_views(db, current_user.id, skip, limit)
        return success_response(data={
            "total": len(views),
            "items": views
        })
    except Exception as e:
        logger.error(f"Error getting user views: {e}")
        return error_response(message=str(e), code=500)


@router.post("/me/views/{job_id}", response_model=Dict[str, Any])
async def add_view(
    job_id: str = Path(..., description="岗位ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    添加浏览记录

    记录用户浏览指定岗位
    """
    try:
        # 添加浏览记录
        view = UserService.add_view(db, current_user.id, job_id)
        return success_response(data=view)
    except Exception as e:
        logger.error(f"Error adding view: {e}")
        return error_response(message=str(e), code=500)
