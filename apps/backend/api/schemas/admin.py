from datetime import datetime
from typing import Optional, List

from pydantic import BaseModel, Field, EmailStr

from apps.backend.api.schemas.base import BaseSchema, PageParams


class PermissionBase(BaseSchema):
    """权限基础模式"""

    name: str = Field(..., description="权限名称")
    code: str = Field(..., description="权限代码")
    description: Optional[str] = Field(None, description="描述")


class PermissionCreate(PermissionBase):
    """权限创建模式"""

    pass


class PermissionUpdate(BaseSchema):
    """权限更新模式"""

    name: Optional[str] = Field(None, description="权限名称")
    description: Optional[str] = Field(None, description="描述")


class Permission(PermissionBase):
    """权限模式"""

    id: int
    created_at: datetime
    updated_at: datetime


class RoleBase(BaseSchema):
    """角色基础模式"""

    name: str = Field(..., description="角色名称")
    description: Optional[str] = Field(None, description="描述")


class RoleCreate(RoleBase):
    """角色创建模式"""

    permission_ids: List[int] = Field([], description="权限ID列表")


class RoleUpdate(BaseSchema):
    """角色更新模式"""

    name: Optional[str] = Field(None, description="角色名称")
    description: Optional[str] = Field(None, description="描述")
    permission_ids: Optional[List[int]] = Field(None, description="权限ID列表")


class Role(RoleBase):
    """角色模式"""

    id: int
    created_at: datetime
    updated_at: datetime
    permissions: Optional[List[Permission]] = None


class AdminBase(BaseSchema):
    """管理员基础模式"""

    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    real_name: Optional[str] = Field(None, description="真实姓名")
    email: Optional[EmailStr] = Field(None, description="电子邮箱")
    phone: Optional[str] = Field(None, description="手机号")
    admin_role_id: int = Field(..., description="管理员角色ID")
    status: bool = Field(True, description="状态")


class AdminCreate(AdminBase):
    """管理员创建模式"""

    password: str = Field(..., min_length=6, max_length=50, description="密码")


class AdminUpdate(BaseSchema):
    """管理员更新模式"""

    real_name: Optional[str] = Field(None, description="真实姓名")
    email: Optional[EmailStr] = Field(None, description="电子邮箱")
    phone: Optional[str] = Field(None, description="手机号")
    admin_role_id: Optional[int] = Field(None, description="管理员角色ID")
    status: Optional[bool] = Field(None, description="状态")
    password: Optional[str] = Field(None, min_length=6, max_length=50, description="密码")


class Admin(AdminBase):
    """管理员模式"""

    id: int
    last_login: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    role: Optional[Role] = None


class AdminInDB(Admin):
    """数据库中的管理员模式"""

    password_hash: str


class AdminLogin(BaseSchema):
    """管理员登录模式"""

    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")


class Token(BaseSchema):
    """令牌模式"""

    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field(..., description="令牌类型")
    expires_in: int = Field(..., description="过期时间（秒）")
    refresh_token: str = Field(..., description="刷新令牌")


class TokenData(BaseSchema):
    """令牌数据模式"""

    user_id: int = Field(..., description="用户ID")
    exp: Optional[int] = Field(None, description="过期时间戳")


class OperationLogBase(BaseSchema):
    """操作日志基础模式"""

    admin_id: int = Field(..., description="管理员ID")
    module: str = Field(..., description="模块")
    operation: str = Field(..., description="操作")
    content: Optional[str] = Field(None, description="内容")
    ip: Optional[str] = Field(None, description="IP地址")
    user_agent: Optional[str] = Field(None, description="用户代理")


class OperationLogCreate(OperationLogBase):
    """操作日志创建模式"""

    pass


class OperationLog(OperationLogBase):
    """操作日志模式"""

    id: int
    created_at: datetime
    admin: Optional[Admin] = None


class OperationLogQuery(PageParams):
    """操作日志查询模式"""

    admin_id: Optional[int] = Field(None, description="管理员ID")
    module: Optional[str] = Field(None, description="模块")
    operation: Optional[str] = Field(None, description="操作")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
