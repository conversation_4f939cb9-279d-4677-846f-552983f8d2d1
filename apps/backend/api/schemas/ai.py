#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI相关模式
"""

from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field

from apps.backend.api.schemas.base import PageParams


class MajorMatchRequest(BaseModel):
    """专业匹配请求"""

    job_major: str = Field(..., description="岗位专业要求")
    user_major: str = Field(..., description="用户专业")


class MajorMatchResponse(BaseModel):
    """专业匹配响应"""

    match_score: int = Field(..., description="匹配分数")
    match_reason: str = Field(..., description="匹配理由")
    standardized_job_major: str = Field(..., description="标准化后的岗位专业")
    standardized_user_major: str = Field(..., description="标准化后的用户专业")
    similar_job_majors: List[str] = Field(default=[], description="相似岗位专业")
    similar_user_majors: List[str] = Field(default=[], description="相似用户专业")


class SkillMatchRequest(BaseModel):
    """技能匹配请求"""

    job_skills: str = Field(..., description="岗位技能要求")
    user_skills: str = Field(..., description="用户技能")


class SkillMatchResponse(BaseModel):
    """技能匹配响应"""

    match_score: int = Field(..., description="匹配分数")
    match_analysis: str = Field(..., description="匹配分析")
    skill_advantages: str = Field(..., description="技能优势")
    skill_gaps: str = Field(..., description="技能差距")
    improvement_suggestions: str = Field(..., description="提升建议")


class JobRecommendRequest(BaseModel):
    """岗位推荐请求"""

    user_major: Optional[str] = Field(None, description="用户专业")
    user_skills: Optional[str] = Field(None, description="用户技能")
    keywords: Optional[str] = Field(None, description="关键词")
    location: Optional[str] = Field(None, description="地点")
    job_type: Optional[str] = Field(None, description="工作类型")
    education: Optional[str] = Field(None, description="学历要求")
    experience: Optional[str] = Field(None, description="经验要求")
    salary_min: Optional[int] = Field(None, description="最低薪资")
    salary_max: Optional[int] = Field(None, description="最高薪资")
    company_type: Optional[str] = Field(None, description="单位性质")
    industry: Optional[str] = Field(None, description="行业")
    match_threshold: Optional[int] = Field(60, description="匹配阈值")
    max_count: Optional[int] = Field(10, description="最大返回数量")


class JobRecommendResponse(BaseModel):
    """岗位推荐响应"""

    recommended_jobs: List[Dict[str, Any]] = Field(..., description="推荐岗位列表")
    total: int = Field(..., description="推荐岗位总数")
    user_major: Optional[str] = Field(None, description="用户专业")
    error_message: Optional[str] = Field(None, description="错误信息")

    # 新增个性化推荐字段
    recommendation_scores: Optional[List[float]] = Field(None, description="推荐分数列表")
    match_reasons: Optional[List[List[str]]] = Field(None, description="匹配原因列表")
    recommendation_type: Optional[str] = Field(None, description="推荐类型")
    user_profile_summary: Optional[Dict[str, Any]] = Field(None, description="用户画像摘要")
    recommendation_insights: Optional[List[str]] = Field(None, description="推荐洞察")


class ResumeParseRequest(BaseModel):
    """简历解析请求"""

    resume_text: str = Field(..., description="简历文本")
    resume_type: Optional[str] = Field("text", description="简历类型")


class ResumeParseResponse(BaseModel):
    """简历解析响应"""

    basic_info: Dict[str, Any] = Field(..., description="基本信息")
    education: List[Dict[str, Any]] = Field(..., description="教育背景")
    work_experience: List[Dict[str, Any]] = Field(..., description="工作经历")
    projects: List[Dict[str, Any]] = Field(..., description="项目经历")
    skills: List[str] = Field(..., description="技能列表")
    self_evaluation: str = Field(..., description="自我评价")


class InterviewPrepRequest(BaseModel):
    """面试准备请求"""

    job_description: str = Field(..., description="岗位描述")
    user_background: Optional[str] = Field("", description="用户背景")


class InterviewPrepResponse(BaseModel):
    """面试准备响应"""

    general_questions: List[str] = Field(..., description="通用问题")
    professional_questions: List[str] = Field(..., description="专业问题")
    preparation_tips: List[str] = Field(..., description="准备建议")
    job_type: str = Field(..., description="岗位类型")
    major_type: str = Field(..., description="专业类型")


class PolicyInterpretRequest(BaseModel):
    """政策解读请求"""

    policy_text: str = Field(..., description="政策文本")
    user_question: Optional[str] = Field(None, description="用户问题")


class PolicyInterpretResponse(BaseModel):
    """政策解读响应"""

    interpretation: str = Field(..., description="解读内容")
    key_points: List[str] = Field(..., description="关键点")
    applicable_groups: List[str] = Field(..., description="适用群体")
    application_process: str = Field(..., description="申请流程")
    related_policies: List[str] = Field(..., description="相关政策")
