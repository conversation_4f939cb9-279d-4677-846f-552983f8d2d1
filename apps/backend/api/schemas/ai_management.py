#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI管理相关的Schema定义
"""

from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum


class ModelStatus(str, Enum):
    """AI模型状态"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    TRAINING = "training"
    ERROR = "error"


class ModelType(str, Enum):
    """AI模型类型"""
    MAJOR_MATCHING = "major_matching"
    SKILL_MATCHING = "skill_matching"
    JOB_RECOMMENDATION = "job_recommendation"
    INTERVIEW_PREP = "interview_prep"
    RESUME_PARSE = "resume_parse"


class AIModelPerformance(BaseModel):
    """AI模型性能指标"""
    accuracy: float = Field(..., description="准确率")
    response_time: float = Field(..., description="响应时间(秒)")
    call_count: int = Field(..., description="调用次数")
    error_rate: float = Field(..., description="错误率")


class AIModelBase(BaseModel):
    """AI模型基础信息"""
    name: str = Field(..., description="模型名称")
    type: ModelType = Field(..., description="模型类型")
    version: str = Field(..., description="版本号")
    description: Optional[str] = Field(None, description="模型描述")
    api_endpoint: str = Field(..., description="API端点")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="模型参数")


class AIModelCreate(AIModelBase):
    """创建AI模型"""
    pass


class AIModelUpdate(BaseModel):
    """更新AI模型"""
    name: Optional[str] = Field(None, description="模型名称")
    description: Optional[str] = Field(None, description="模型描述")
    parameters: Optional[Dict[str, Any]] = Field(None, description="模型参数")
    status: Optional[ModelStatus] = Field(None, description="模型状态")


class AIModel(AIModelBase):
    """AI模型完整信息"""
    id: str = Field(..., description="模型ID")
    status: ModelStatus = Field(..., description="模型状态")
    performance: AIModelPerformance = Field(..., description="性能指标")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class RecommendationAlgorithmWeights(BaseModel):
    """推荐算法权重配置"""
    content_based: float = Field(..., ge=0, le=100, description="基于内容推荐权重")
    collaborative_filtering: float = Field(..., ge=0, le=100, description="协同过滤权重")
    behavioral: float = Field(..., ge=0, le=100, description="行为推荐权重")
    trending: float = Field(..., ge=0, le=100, description="热门推荐权重")


class RecommendationFeatureWeights(BaseModel):
    """推荐特征权重配置"""
    major_match: float = Field(..., ge=0, le=100, description="专业匹配权重")
    skill_match: float = Field(..., ge=0, le=100, description="技能匹配权重")
    location_match: float = Field(..., ge=0, le=100, description="地点匹配权重")
    salary_match: float = Field(..., ge=0, le=100, description="薪资匹配权重")
    experience_match: float = Field(..., ge=0, le=100, description="经验匹配权重")
    education_match: float = Field(..., ge=0, le=100, description="学历匹配权重")


class RecommendationParams(BaseModel):
    """推荐参数配置"""
    max_recommendations: int = Field(..., ge=1, le=100, description="最大推荐数量")
    min_match_score: float = Field(..., ge=0, le=100, description="最小匹配分数")
    diversity_factor: float = Field(..., ge=0, le=1, description="多样性因子")
    freshness_weight: float = Field(..., ge=0, le=1, description="新鲜度权重")
    popularity_weight: float = Field(..., ge=0, le=1, description="热门度权重")


class CollaborativeParams(BaseModel):
    """协同过滤参数配置"""
    user_similarity_threshold: float = Field(..., ge=0, le=1, description="用户相似度阈值")
    item_similarity_threshold: float = Field(..., ge=0, le=1, description="物品相似度阈值")
    min_interactions: int = Field(..., ge=1, description="最小交互次数")
    max_neighbors: int = Field(..., ge=10, le=200, description="最大邻居数")


class ExperimentConfig(BaseModel):
    """实验配置"""
    ab_test_enabled: bool = Field(..., description="是否启用A/B测试")
    test_group_ratio: float = Field(..., ge=0.01, le=0.5, description="测试组比例")
    experiment_duration: int = Field(..., ge=1, le=30, description="实验持续时间(天)")


class RecommendationConfigBase(BaseModel):
    """推荐配置基础信息"""
    algorithm_weights: RecommendationAlgorithmWeights = Field(..., description="算法权重")
    feature_weights: RecommendationFeatureWeights = Field(..., description="特征权重")
    recommendation_params: RecommendationParams = Field(..., description="推荐参数")
    collaborative_params: CollaborativeParams = Field(..., description="协同过滤参数")
    experiment_config: ExperimentConfig = Field(..., description="实验配置")


class RecommendationConfig(RecommendationConfigBase):
    """推荐配置完整信息"""
    id: str = Field(..., description="配置ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class MatchRuleType(str, Enum):
    """匹配规则类型"""
    MAJOR = "major"
    SKILL = "skill"
    EDUCATION = "education"
    EXPERIENCE = "experience"
    LOCATION = "location"
    SALARY = "salary"


class RuleStatus(str, Enum):
    """规则状态"""
    ACTIVE = "active"
    INACTIVE = "inactive"


class MatchCondition(BaseModel):
    """匹配条件"""
    field: str = Field(..., description="字段名")
    operator: str = Field(..., description="操作符")
    value: Union[str, int, float] = Field(..., description="值")
    weight: float = Field(..., ge=0, le=1, description="权重")


class MatchRuleUsage(BaseModel):
    """匹配规则使用统计"""
    match_count: int = Field(..., description="匹配次数")
    success_rate: float = Field(..., description="成功率")
    avg_score: float = Field(..., description="平均分数")


class MatchRuleBase(BaseModel):
    """匹配规则基础信息"""
    name: str = Field(..., description="规则名称")
    type: MatchRuleType = Field(..., description="规则类型")
    category: str = Field(..., description="规则分类")
    conditions: List[MatchCondition] = Field(..., description="匹配条件")
    threshold: float = Field(..., ge=0, le=100, description="匹配阈值")
    priority: int = Field(..., ge=1, le=10, description="优先级")
    description: Optional[str] = Field(None, description="规则描述")


class MatchRuleCreate(MatchRuleBase):
    """创建匹配规则"""
    pass


class MatchRuleUpdate(BaseModel):
    """更新匹配规则"""
    name: Optional[str] = Field(None, description="规则名称")
    conditions: Optional[List[MatchCondition]] = Field(None, description="匹配条件")
    threshold: Optional[float] = Field(None, ge=0, le=100, description="匹配阈值")
    priority: Optional[int] = Field(None, ge=1, le=10, description="优先级")
    description: Optional[str] = Field(None, description="规则描述")
    status: Optional[RuleStatus] = Field(None, description="规则状态")


class MatchRule(MatchRuleBase):
    """匹配规则完整信息"""
    id: str = Field(..., description="规则ID")
    status: RuleStatus = Field(..., description="规则状态")
    usage: MatchRuleUsage = Field(..., description="使用统计")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class TagType(str, Enum):
    """标签类型"""
    JOB = "job"
    USER = "user"
    SKILL = "skill"
    INDUSTRY = "industry"


class TagStatus(str, Enum):
    """标签状态"""
    ACTIVE = "active"
    INACTIVE = "inactive"


class SmartTagRules(BaseModel):
    """智能标签规则"""
    keywords: List[str] = Field(..., description="关键词")
    conditions: List[str] = Field(..., description="条件")
    weight: float = Field(..., ge=0, le=1, description="权重")


class SmartTagBase(BaseModel):
    """智能标签基础信息"""
    name: str = Field(..., description="标签名称")
    type: TagType = Field(..., description="标签类型")
    category: str = Field(..., description="标签分类")
    description: Optional[str] = Field(None, description="标签描述")
    color: str = Field(..., description="标签颜色")
    rules: SmartTagRules = Field(..., description="标签规则")


class SmartTagCreate(SmartTagBase):
    """创建智能标签"""
    pass


class SmartTagUpdate(BaseModel):
    """更新智能标签"""
    name: Optional[str] = Field(None, description="标签名称")
    description: Optional[str] = Field(None, description="标签描述")
    color: Optional[str] = Field(None, description="标签颜色")
    rules: Optional[SmartTagRules] = Field(None, description="标签规则")
    status: Optional[TagStatus] = Field(None, description="标签状态")


class SmartTag(SmartTagBase):
    """智能标签完整信息"""
    id: str = Field(..., description="标签ID")
    is_ai_generated: bool = Field(..., description="是否AI生成")
    confidence: float = Field(..., description="置信度")
    usage_count: int = Field(..., description="使用次数")
    status: TagStatus = Field(..., description="标签状态")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class ServiceMetrics(BaseModel):
    """服务指标"""
    timestamp: datetime = Field(..., description="时间戳")
    api_calls: int = Field(..., description="API调用次数")
    success_rate: float = Field(..., description="成功率")
    avg_response_time: float = Field(..., description="平均响应时间")
    error_rate: float = Field(..., description="错误率")
    active_users: int = Field(..., description="活跃用户数")


class APIEndpointStatus(BaseModel):
    """API端点状态"""
    endpoint: str = Field(..., description="端点路径")
    method: str = Field(..., description="HTTP方法")
    calls: int = Field(..., description="调用次数")
    success_rate: float = Field(..., description="成功率")
    avg_response_time: float = Field(..., description="平均响应时间")
    status: str = Field(..., description="状态")


class ErrorLog(BaseModel):
    """错误日志"""
    id: str = Field(..., description="日志ID")
    timestamp: datetime = Field(..., description="时间戳")
    endpoint: str = Field(..., description="端点")
    error_type: str = Field(..., description="错误类型")
    message: str = Field(..., description="错误消息")
    severity: str = Field(..., description="严重程度")


class GenerateTagsRequest(BaseModel):
    """生成标签请求"""
    type: Optional[TagType] = Field(None, description="标签类型")
    count: int = Field(default=10, ge=1, le=50, description="生成数量")
    source_data: Optional[Dict[str, Any]] = Field(None, description="源数据")


class TagStatistics(BaseModel):
    """标签统计"""
    total_tags: int = Field(..., description="总标签数")
    ai_generated_tags: int = Field(..., description="AI生成标签数")
    manual_tags: int = Field(..., description="手动标签数")
    average_confidence: float = Field(..., description="平均置信度")
    top_used_tags: List[SmartTag] = Field(..., description="热门标签")
