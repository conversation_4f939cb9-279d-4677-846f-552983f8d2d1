#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据分析相关的Schema定义
"""

from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum


class TimeRange(str, Enum):
    """时间范围"""
    HOUR_1 = "1h"
    HOUR_24 = "24h"
    DAY_7 = "7d"
    DAY_30 = "30d"
    DAY_90 = "90d"
    YEAR_1 = "1y"


class ReportType(str, Enum):
    """报表类型"""
    TABLE = "table"
    CHART = "chart"
    DASHBOARD = "dashboard"


class ChartType(str, Enum):
    """图表类型"""
    LINE = "line"
    BAR = "bar"
    PIE = "pie"
    AREA = "area"


class ReportStatus(str, Enum):
    """报表状态"""
    ACTIVE = "active"
    INACTIVE = "inactive"


class Frequency(str, Enum):
    """频率"""
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"


# 用户行为分析相关Schema

class UserActivityData(BaseModel):
    """用户活动数据"""
    date: str = Field(..., description="日期")
    visits: int = Field(..., description="访问量")
    searches: int = Field(..., description="搜索量")
    applications: int = Field(..., description="申请量")
    favorites: int = Field(..., description="收藏量")


class UserSourceData(BaseModel):
    """用户来源数据"""
    name: str = Field(..., description="来源名称")
    value: int = Field(..., description="数量")
    color: str = Field(..., description="颜色")


class PopularKeyword(BaseModel):
    """热门关键词"""
    keyword: str = Field(..., description="关键词")
    count: int = Field(..., description="搜索次数")
    trend: str = Field(..., description="趋势")


class HourlyActivityData(BaseModel):
    """小时活动数据"""
    hour: str = Field(..., description="小时")
    users: int = Field(..., description="用户数")


class FunnelData(BaseModel):
    """漏斗数据"""
    name: str = Field(..., description="步骤名称")
    value: int = Field(..., description="数量")
    fill: str = Field(..., description="颜色")


# 业务报表相关Schema

class JobTrendData(BaseModel):
    """岗位趋势数据"""
    date: str = Field(..., description="日期")
    published: int = Field(..., description="发布岗位数")
    applications: int = Field(..., description="申请数")
    matches: int = Field(..., description="匹配数")


class IndustryData(BaseModel):
    """行业数据"""
    name: str = Field(..., description="行业名称")
    jobs: int = Field(..., description="岗位数")
    applications: int = Field(..., description="申请数")
    color: str = Field(..., description="颜色")


class SalaryData(BaseModel):
    """薪资数据"""
    range: str = Field(..., description="薪资范围")
    count: int = Field(..., description="数量")
    percentage: float = Field(..., description="百分比")


class RegionData(BaseModel):
    """地区数据"""
    region: str = Field(..., description="地区")
    jobs: int = Field(..., description="岗位数")
    growth: float = Field(..., description="增长率")


class PopularJob(BaseModel):
    """热门岗位"""
    position: str = Field(..., description="岗位名称")
    count: int = Field(..., description="岗位数量")
    avg_salary: int = Field(..., description="平均薪资")
    trend: str = Field(..., description="趋势")


class CompanySizeData(BaseModel):
    """企业规模数据"""
    size: str = Field(..., description="规模")
    count: int = Field(..., description="数量")
    percentage: float = Field(..., description="百分比")


# 实时监控相关Schema

class RealTimeMetrics(BaseModel):
    """实时指标"""
    online_users: int = Field(..., description="在线用户数")
    page_views: int = Field(..., description="页面浏览量")
    searches: int = Field(..., description="搜索次数")
    applications: int = Field(..., description="申请次数")
    system_load: float = Field(..., description="系统负载")
    response_time: float = Field(..., description="响应时间")
    error_rate: float = Field(..., description="错误率")
    success_rate: float = Field(..., description="成功率")


class RecentActivity(BaseModel):
    """最近活动"""
    id: int = Field(..., description="活动ID")
    user: str = Field(..., description="用户")
    action: str = Field(..., description="动作")
    time: str = Field(..., description="时间")
    type: str = Field(..., description="类型")


class SystemStatus(BaseModel):
    """系统状态"""
    service: str = Field(..., description="服务名称")
    status: str = Field(..., description="状态")
    response_time: float = Field(..., description="响应时间")
    uptime: float = Field(..., description="可用性")


class HotSearch(BaseModel):
    """热门搜索"""
    keyword: str = Field(..., description="关键词")
    count: int = Field(..., description="搜索次数")
    trend: str = Field(..., description="趋势")


# 自定义报表相关Schema

class ReportCondition(BaseModel):
    """报表条件"""
    field: str = Field(..., description="字段")
    operator: str = Field(..., description="操作符")
    value: Union[str, int, float] = Field(..., description="值")


class ReportFilters(BaseModel):
    """报表过滤器"""
    date_range: Optional[List[str]] = Field(None, description="日期范围")
    conditions: Optional[List[ReportCondition]] = Field(None, description="条件")


class ReportSchedule(BaseModel):
    """报表调度"""
    enabled: bool = Field(..., description="是否启用")
    frequency: Frequency = Field(..., description="频率")
    time: str = Field(..., description="时间")
    recipients: List[str] = Field(..., description="接收者")


class CustomReportBase(BaseModel):
    """自定义报表基础信息"""
    name: str = Field(..., description="报表名称")
    description: Optional[str] = Field(None, description="报表描述")
    type: ReportType = Field(..., description="报表类型")
    chart_type: Optional[ChartType] = Field(None, description="图表类型")
    data_source: str = Field(..., description="数据源")
    filters: ReportFilters = Field(..., description="过滤器")
    columns: List[str] = Field(..., description="列")
    schedule: Optional[ReportSchedule] = Field(None, description="调度")


class CustomReportCreate(CustomReportBase):
    """创建自定义报表"""
    pass


class CustomReportUpdate(BaseModel):
    """更新自定义报表"""
    name: Optional[str] = Field(None, description="报表名称")
    description: Optional[str] = Field(None, description="报表描述")
    type: Optional[ReportType] = Field(None, description="报表类型")
    chart_type: Optional[ChartType] = Field(None, description="图表类型")
    data_source: Optional[str] = Field(None, description="数据源")
    filters: Optional[ReportFilters] = Field(None, description="过滤器")
    columns: Optional[List[str]] = Field(None, description="列")
    schedule: Optional[ReportSchedule] = Field(None, description="调度")
    status: Optional[ReportStatus] = Field(None, description="状态")


class CustomReport(CustomReportBase):
    """自定义报表完整信息"""
    id: str = Field(..., description="报表ID")
    status: ReportStatus = Field(..., description="状态")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    last_run: Optional[datetime] = Field(None, description="最后运行时间")

    class Config:
        from_attributes = True


class DataSource(BaseModel):
    """数据源"""
    value: str = Field(..., description="值")
    label: str = Field(..., description="标签")


class DataSourceField(BaseModel):
    """数据源字段"""
    name: str = Field(..., description="字段名")
    type: str = Field(..., description="字段类型")
    description: str = Field(..., description="字段描述")


class ReportPreviewConfig(BaseModel):
    """报表预览配置"""
    data_source: str = Field(..., description="数据源")
    filters: Optional[ReportFilters] = Field(None, description="过滤器")
    columns: Optional[List[str]] = Field(None, description="列")
    limit: int = Field(default=100, description="限制数量")


class ReportRunRequest(BaseModel):
    """报表运行请求"""
    format: str = Field(default="json", description="格式")


class ExportRequest(BaseModel):
    """导出请求"""
    format: str = Field(..., description="格式")
    time_range: Optional[str] = Field(None, description="时间范围")


# 通用分析Schema

class OverviewData(BaseModel):
    """概览数据"""
    total_users: int = Field(..., description="总用户数")
    active_users: int = Field(..., description="活跃用户数")
    total_jobs: int = Field(..., description="总岗位数")
    new_jobs: int = Field(..., description="新增岗位数")
    total_applications: int = Field(..., description="总申请数")
    success_matches: int = Field(..., description="成功匹配数")


class KeyMetric(BaseModel):
    """关键指标"""
    name: str = Field(..., description="指标名称")
    value: Union[int, float] = Field(..., description="指标值")
    unit: str = Field(..., description="单位")
    trend: str = Field(..., description="趋势")
    change: float = Field(..., description="变化")


class TrendPoint(BaseModel):
    """趋势点"""
    timestamp: datetime = Field(..., description="时间戳")
    value: Union[int, float] = Field(..., description="值")


class TrendAnalysis(BaseModel):
    """趋势分析"""
    metric: str = Field(..., description="指标")
    data: List[TrendPoint] = Field(..., description="数据点")
    summary: Dict[str, Any] = Field(..., description="摘要")


class ComparisonAnalysis(BaseModel):
    """对比分析"""
    metrics: List[str] = Field(..., description="指标")
    current_period: Dict[str, Any] = Field(..., description="当前周期")
    previous_period: Dict[str, Any] = Field(..., description="上一周期")
    comparison: Dict[str, Any] = Field(..., description="对比结果")


# 缺失的Schema类
class AnalyticsDateRange(BaseModel):
    """分析日期范围"""
    start_date: str = Field(..., description="开始日期")
    end_date: str = Field(..., description="结束日期")
    granularity: str = Field(default="day", description="时间粒度")


class UserBehaviorAnalytics(BaseModel):
    """用户行为分析"""
    activity_data: List[UserActivityData] = Field(..., description="活动数据")
    source_data: List[UserSourceData] = Field(..., description="来源数据")
    popular_keywords: List[PopularKeyword] = Field(..., description="热门关键词")
    hourly_activity: List[HourlyActivityData] = Field(..., description="小时活动数据")
    funnel_data: List[FunnelData] = Field(..., description="漏斗数据")


class JobAnalytics(BaseModel):
    """岗位分析"""
    trend_data: List[JobTrendData] = Field(..., description="趋势数据")
    industry_data: List[IndustryData] = Field(..., description="行业数据")
    salary_data: List[SalaryData] = Field(..., description="薪资数据")
    region_data: List[RegionData] = Field(..., description="地区数据")
    popular_jobs: List[PopularJob] = Field(..., description="热门岗位")


class CommunityAnalytics(BaseModel):
    """社区分析"""
    activity_data: List[UserActivityData] = Field(..., description="活动数据")
    popular_topics: List[PopularKeyword] = Field(..., description="热门话题")
    user_engagement: Dict[str, Any] = Field(..., description="用户参与度")
    content_stats: Dict[str, Any] = Field(..., description="内容统计")
