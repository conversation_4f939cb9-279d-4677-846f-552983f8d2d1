from datetime import datetime
from typing import Optional, Any, List, Dict

from pydantic import BaseModel, Field


class BaseSchema(BaseModel):
    """基础模式"""

    model_config = {
        "from_attributes": True,
        "json_encoders": {
            datetime: lambda dt: dt.isoformat()
        }
    }


class PageParams(BaseSchema):
    """分页参数"""
    
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(10, ge=1, le=100, description="每页数量")


class PageResponse(BaseSchema):
    """分页响应"""
    
    items: List[Any]
    total: int
    page: int
    page_size: int
    pages: int


class ResponseBase(BaseSchema):
    """基础响应"""
    
    code: int = Field(200, description="状态码")
    message: str = Field("success", description="消息")
    data: Optional[Any] = Field(None, description="数据")


class ErrorResponse(ResponseBase):
    """错误响应"""
    
    code: int = Field(..., description="错误码")
    message: str = Field(..., description="错误消息")
    data: Optional[Dict[str, Any]] = Field(None, description="错误详情")
