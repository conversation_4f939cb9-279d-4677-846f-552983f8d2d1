#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
通用Schema定义
"""

from typing import Generic, TypeVar, List, Optional, Any, Dict
from pydantic import BaseModel, Field

T = TypeVar('T')


class PaginatedResponse(BaseModel, Generic[T]):
    """分页响应"""
    data: List[T] = Field(..., description="数据列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页大小")
    total_pages: Optional[int] = Field(None, description="总页数")
    
    def __init__(self, **data):
        super().__init__(**data)
        if self.total_pages is None and self.page_size > 0:
            self.total_pages = (self.total + self.page_size - 1) // self.page_size


class SuccessResponse(BaseModel):
    """成功响应"""
    success: bool = Field(default=True, description="是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[Any] = Field(None, description="响应数据")


class ErrorResponse(BaseModel):
    """错误响应"""
    success: bool = Field(default=False, description="是否成功")
    message: str = Field(..., description="错误消息")
    error_code: Optional[str] = Field(None, description="错误代码")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")


class StatusResponse(BaseModel):
    """状态响应"""
    status: str = Field(..., description="状态")
    message: Optional[str] = Field(None, description="状态消息")
    timestamp: Optional[str] = Field(None, description="时间戳")


class ListResponse(BaseModel, Generic[T]):
    """列表响应"""
    items: List[T] = Field(..., description="项目列表")
    count: int = Field(..., description="项目数量")


class BaseFilter(BaseModel):
    """基础过滤器"""
    page: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=10, ge=1, le=100, description="每页大小")
    sort_by: Optional[str] = Field(None, description="排序字段")
    sort_order: Optional[str] = Field(default="desc", description="排序顺序")


class SearchFilter(BaseFilter):
    """搜索过滤器"""
    keywords: Optional[str] = Field(None, description="关键词")
    category: Optional[str] = Field(None, description="分类")
    status: Optional[str] = Field(None, description="状态")


class DateRangeFilter(BaseFilter):
    """日期范围过滤器"""
    start_date: Optional[str] = Field(None, description="开始日期")
    end_date: Optional[str] = Field(None, description="结束日期")


class BulkOperationRequest(BaseModel):
    """批量操作请求"""
    ids: List[str] = Field(..., description="ID列表")
    action: str = Field(..., description="操作类型")
    params: Optional[Dict[str, Any]] = Field(None, description="操作参数")


class BulkOperationResponse(BaseModel):
    """批量操作响应"""
    success_count: int = Field(..., description="成功数量")
    failed_count: int = Field(..., description="失败数量")
    failed_items: Optional[List[Dict[str, Any]]] = Field(None, description="失败项目")
    message: str = Field(..., description="操作结果消息")
