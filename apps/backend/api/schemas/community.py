from datetime import datetime
from typing import Optional, List, Union

from pydantic import BaseModel, Field, validator

from apps.backend.api.schemas.base import BaseSchema, PageParams
from apps.backend.api.schemas.user import User


class CommentBase(BaseSchema):
    """评论基础模式"""

    content: str = Field(..., description="内容")
    parent_id: Optional[str] = Field(None, description="父评论ID")


class CommentCreate(CommentBase):
    """评论创建模式"""

    pass


class CommentUpdate(BaseSchema):
    """评论更新模式"""

    content: Optional[str] = Field(None, description="内容")


class Comment(CommentBase):
    """评论模式"""

    id: int
    comment_id: str
    post_id: str
    author_id: int
    like_count: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    author: Optional[User] = None


class PostBase(BaseSchema):
    """帖子基础模式"""

    title: str = Field(..., min_length=1, max_length=200, description="标题")
    content: str = Field(..., description="内容")
    category: Optional[str] = Field(None, description="分类")
    tags: Optional[Union[str, List[str]]] = Field(None, description="标签")
    status: Optional[int] = Field(1, description="状态：1-正常，0-禁用，2-审核中")

    @validator('tags', pre=True)
    def validate_tags(cls, v):
        """验证并转换tags"""
        if v is None:
            return None
        if isinstance(v, list):
            return v  # 保持列表格式用于处理
        if isinstance(v, str):
            return [tag.strip() for tag in v.split(',') if tag.strip()]
        return v


class PostCreate(PostBase):
    """帖子创建模式"""

    pass


class PostUpdate(BaseSchema):
    """帖子更新模式"""

    title: Optional[str] = Field(None, min_length=1, max_length=200, description="标题")
    content: Optional[str] = Field(None, description="内容")
    category: Optional[str] = Field(None, description="分类")
    tags: Optional[Union[str, List[str]]] = Field(None, description="标签")

    @validator('tags', pre=True)
    def validate_tags(cls, v):
        """验证并转换tags"""
        if v is None:
            return None
        if isinstance(v, list):
            return v  # 保持列表格式用于处理
        if isinstance(v, str):
            return [tag.strip() for tag in v.split(',') if tag.strip()]
        return v


class Post(PostBase):
    """帖子模式"""

    id: int
    post_id: str
    author_id: int
    view_count: int
    like_count: int
    comment_count: int
    is_pinned: bool
    is_essence: bool
    created_at: datetime
    updated_at: Optional[datetime] = None
    author: Optional[User] = None
    comments: Optional[List[Comment]] = None


class PostQuery(PageParams):
    """帖子查询模式"""

    category: Optional[str] = Field(None, description="分类")
    tag: Optional[str] = Field(None, description="标签")
    author_id: Optional[Union[int, str]] = Field(None, description="作者ID")
    status: Optional[int] = Field(None, description="状态：1-正常，0-禁用，2-审核中")
    keywords: Optional[str] = Field(None, description="关键词")
    sort: Optional[str] = Field("created_at", description="排序字段")
    order: Optional[str] = Field("desc", description="排序方向")

    @validator('author_id', pre=True)
    def validate_author_id(cls, v):
        """验证并转换author_id"""
        if v is None:
            return None
        if isinstance(v, str):
            # 如果是字符串，尝试转换为整数
            try:
                return int(v)
            except ValueError:
                # 如果转换失败，返回None（忽略无效的author_id）
                return None
        return v


class CommentQuery(PageParams):
    """评论查询模式"""

    post_id: Optional[str] = Field(None, description="帖子ID")
    author_id: Optional[int] = Field(None, description="作者ID")
    parent_id: Optional[str] = Field(None, description="父评论ID")
    keywords: Optional[str] = Field(None, description="关键词")
    sort: Optional[str] = Field("created_at", description="排序字段")
    order: Optional[str] = Field("desc", description="排序方向")
