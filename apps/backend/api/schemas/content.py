from datetime import datetime
from typing import Optional, List

from pydantic import BaseModel, Field, validator

from apps.backend.api.schemas.base import BaseSchema, PageParams


class PolicyBase(BaseSchema):
    """政策基础模式"""

    policy_id: str = Field(..., description="政策ID")
    title: str = Field(..., description="标题")
    content: str = Field(..., description="内容")
    summary: Optional[str] = Field(None, description="摘要")
    key_points: Optional[str] = Field(None, description="要点")
    special_notes: Optional[str] = Field(None, description="特别说明")
    publish_date: Optional[datetime] = Field(None, description="发布日期")
    source_url: Optional[str] = Field(None, description="来源URL")


class PolicyCreate(PolicyBase):
    """政策创建模式"""

    pass


class PolicyUpdate(BaseSchema):
    """政策更新模式"""

    title: Optional[str] = Field(None, description="标题")
    content: Optional[str] = Field(None, description="内容")
    summary: Optional[str] = Field(None, description="摘要")
    key_points: Optional[str] = Field(None, description="要点")
    special_notes: Optional[str] = Field(None, description="特别说明")
    publish_date: Optional[datetime] = Field(None, description="发布日期")
    source_url: Optional[str] = Field(None, description="来源URL")


class Policy(PolicyBase):
    """政策模式"""

    id: int
    created_at: datetime
    updated_at: datetime


class PolicyQuery(PageParams):
    """政策查询模式"""

    keywords: Optional[str] = Field(None, description="关键词")
    sort: Optional[str] = Field("publish_date", description="排序字段")
    order: Optional[str] = Field("desc", description="排序方向")


class BannerBase(BaseSchema):
    """轮播图基础模式"""

    title: str = Field(..., min_length=1, max_length=200, description="标题")
    description: Optional[str] = Field(None, max_length=500, description="描述")
    image_url: str = Field(..., min_length=1, description="图片URL")
    link_url: Optional[str] = Field(None, description="链接URL")
    sort_order: int = Field(0, ge=0, description="排序")
    status: bool = Field(True, description="状态")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")

    @validator('title')
    def validate_title(cls, v):
        if not v or not v.strip():
            raise ValueError('标题不能为空')
        return v.strip()

    @validator('image_url')
    def validate_image_url(cls, v):
        if not v or not v.strip():
            raise ValueError('图片URL不能为空')
        # 简单的URL格式验证
        if not (v.startswith('http://') or v.startswith('https://') or v.startswith('/')):
            raise ValueError('图片URL格式无效')
        return v.strip()

    @validator('sort_order')
    def validate_sort_order(cls, v):
        if v < 0:
            raise ValueError('排序值不能为负数')
        return v


class BannerCreate(BannerBase):
    """轮播图创建模式"""

    pass


class BannerUpdate(BaseSchema):
    """轮播图更新模式"""

    title: Optional[str] = Field(None, description="标题")
    description: Optional[str] = Field(None, description="描述")
    image_url: Optional[str] = Field(None, description="图片URL")
    link_url: Optional[str] = Field(None, description="链接URL")
    sort_order: Optional[int] = Field(None, description="排序")
    status: Optional[bool] = Field(None, description="状态")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")


class Banner(BannerBase):
    """轮播图模式"""

    id: int
    created_at: datetime
    updated_at: datetime


class SystemConfigBase(BaseSchema):
    """系统配置基础模式"""

    config_key: str = Field(..., description="配置键")
    config_value: str = Field(..., description="配置值")
    description: Optional[str] = Field(None, description="描述")


class SystemConfigCreate(SystemConfigBase):
    """系统配置创建模式"""

    pass


class SystemConfigUpdate(BaseSchema):
    """系统配置更新模式"""

    config_value: Optional[str] = Field(None, description="配置值")
    description: Optional[str] = Field(None, description="描述")


class SystemConfig(SystemConfigBase):
    """系统配置模式"""

    id: int
    created_at: datetime
    updated_at: datetime
