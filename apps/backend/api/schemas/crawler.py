from datetime import datetime
from typing import Optional, List, Dict, Any

from pydantic import BaseModel, Field

from apps.backend.api.schemas.base import BaseSchema, PageParams


class CrawlerBase(BaseSchema):
    """爬虫基础模式"""

    name: str = Field(..., description="爬虫名称")
    crawler_type: str = Field(..., description="爬虫类型")
    description: Optional[str] = Field(None, description="爬虫描述")
    config: Optional[Dict[str, Any]] = Field(None, description="爬虫配置")
    is_active: bool = Field(True, description="是否激活")


class CrawlerCreate(CrawlerBase):
    """爬虫创建模式"""

    pass


class CrawlerUpdate(BaseSchema):
    """爬虫更新模式"""

    name: Optional[str] = Field(None, description="爬虫名称")
    crawler_type: Optional[str] = Field(None, description="爬虫类型")
    description: Optional[str] = Field(None, description="爬虫描述")
    config: Optional[Dict[str, Any]] = Field(None, description="爬虫配置")
    is_active: Optional[bool] = Field(None, description="是否激活")


class Crawler(CrawlerBase):
    """爬虫模式"""

    id: int
    created_by: int
    created_at: datetime
    updated_at: datetime


class CrawlerQuery(PageParams):
    """爬虫查询模式"""

    name: Optional[str] = Field(None, description="爬虫名称")
    crawler_type: Optional[str] = Field(None, description="爬虫类型")
    is_active: Optional[bool] = Field(None, description="是否激活")
    created_by: Optional[int] = Field(None, description="创建人")


class CrawlerTaskBase(BaseSchema):
    """爬虫任务基础模式"""

    task_name: str = Field(..., description="任务名称")
    task_type: str = Field(..., description="任务类型")
    target_url: Optional[str] = Field(None, description="目标URL")
    parameters: Optional[Dict[str, Any]] = Field(None, description="参数")
    schedule: Optional[str] = Field(None, description="调度表达式")
    status: str = Field("pending", description="状态")
    created_by: int = Field(..., description="创建人")
    crawler_id: Optional[int] = Field(None, description="爬虫ID")


class CrawlerTaskCreate(CrawlerTaskBase):
    """爬虫任务创建模式"""

    pass


class CrawlerTaskUpdate(BaseSchema):
    """爬虫任务更新模式"""

    task_name: Optional[str] = Field(None, description="任务名称")
    task_type: Optional[str] = Field(None, description="任务类型")
    target_url: Optional[str] = Field(None, description="目标URL")
    parameters: Optional[Dict[str, Any]] = Field(None, description="参数")
    schedule: Optional[str] = Field(None, description="调度表达式")
    status: Optional[str] = Field(None, description="状态")


class CrawlerTask(CrawlerTaskBase):
    """爬虫任务模式"""

    id: int
    last_run_time: Optional[datetime] = None
    next_run_time: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime


class CrawlerTaskLogBase(BaseSchema):
    """爬虫任务日志基础模式"""

    task_id: int = Field(..., description="任务ID")
    start_time: datetime = Field(..., description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    status: str = Field(..., description="状态")
    result: Optional[str] = Field(None, description="结果")
    error_message: Optional[str] = Field(None, description="错误信息")


class CrawlerTaskLogCreate(CrawlerTaskLogBase):
    """爬虫任务日志创建模式"""

    pass


class CrawlerTaskLogUpdate(BaseSchema):
    """爬虫任务日志更新模式"""

    end_time: Optional[datetime] = Field(None, description="结束时间")
    status: Optional[str] = Field(None, description="状态")
    result: Optional[str] = Field(None, description="结果")
    error_message: Optional[str] = Field(None, description="错误信息")


class CrawlerTaskLog(CrawlerTaskLogBase):
    """爬虫任务日志模式"""

    id: int
    created_at: datetime


class CrawlerTaskQuery(PageParams):
    """爬虫任务查询模式"""

    task_name: Optional[str] = Field(None, description="任务名称")
    task_type: Optional[str] = Field(None, description="任务类型")
    status: Optional[str] = Field(None, description="状态")
    created_by: Optional[int] = Field(None, description="创建人")


class CrawlerTaskLogQuery(PageParams):
    """爬虫任务日志查询模式"""

    task_id: Optional[int] = Field(None, description="任务ID")
    status: Optional[str] = Field(None, description="状态")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")


class CrawlerLogQuery(PageParams):
    """爬虫日志查询模式"""

    crawler_id: Optional[int] = Field(None, description="爬虫ID")
    log_type: Optional[str] = Field(None, description="日志类型")
    status: Optional[str] = Field(None, description="状态")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
