from datetime import datetime
from typing import Optional, List

from pydantic import BaseModel, Field

from apps.backend.api.schemas.base import BaseSchema, PageParams
from apps.backend.api.schemas.job import Job


class FavoriteBase(BaseSchema):
    """收藏基础模式"""
    
    job_id: str = Field(..., description="岗位ID")


class FavoriteCreate(FavoriteBase):
    """收藏创建模式"""
    
    pass


class Favorite(FavoriteBase):
    """收藏模式"""
    
    id: int
    user_id: int
    created_at: datetime
    job: Optional[Job] = None


class BrowseHistoryBase(BaseSchema):
    """浏览历史基础模式"""
    
    job_id: str = Field(..., description="岗位ID")
    view_count: int = Field(1, description="浏览次数")
    last_view_time: datetime = Field(..., description="最后浏览时间")


class BrowseHistoryCreate(BrowseHistoryBase):
    """浏览历史创建模式"""
    
    pass


class BrowseHistory(BrowseHistoryBase):
    """浏览历史模式"""
    
    id: int
    user_id: int
    created_at: datetime
    job: Optional[Job] = None


class SubscriptionBase(BaseSchema):
    """订阅基础模式"""
    
    job_id: str = Field(..., description="岗位ID")
    subscription_type: str = Field(..., description="订阅类型")
    notification_time: Optional[datetime] = Field(None, description="通知时间")


class SubscriptionCreate(SubscriptionBase):
    """订阅创建模式"""
    
    pass


class SubscriptionUpdate(BaseSchema):
    """订阅更新模式"""
    
    notification_time: Optional[datetime] = Field(None, description="通知时间")
    status: Optional[str] = Field(None, description="状态")


class Subscription(SubscriptionBase):
    """订阅模式"""
    
    id: int
    user_id: int
    status: str
    created_at: datetime
    updated_at: datetime
    job: Optional[Job] = None
