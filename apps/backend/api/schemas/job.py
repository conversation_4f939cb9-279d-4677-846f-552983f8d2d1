from datetime import datetime
from typing import Optional, List, Dict, Any

from pydantic import BaseModel, Field

from apps.backend.api.schemas.base import BaseSchema, PageParams


class JobDetailBase(BaseSchema):
    """岗位详情基础模式"""
    
    major_standardized: Optional[str] = Field(None, description="标准化专业")
    similar_majors: Optional[str] = Field(None, description="相似专业")
    interview_questions: Optional[str] = Field(None, description="面试问题")
    professional_questions: Optional[str] = Field(None, description="专业问题")
    interview_preparation_tips: Optional[str] = Field(None, description="面试准备建议")
    policy_summary: Optional[str] = Field(None, description="政策摘要")
    policy_key_points: Optional[str] = Field(None, description="政策要点")
    policy_special_notes: Optional[str] = Field(None, description="政策特别说明")


class JobDetail(JobDetailBase):
    """岗位详情模式"""
    
    id: int
    job_id: str
    created_at: datetime
    updated_at: datetime


class JobBase(BaseSchema):
    """岗位基础模式"""
    
    job_id: str = Field(..., description="岗位ID")
    title: str = Field(..., description="岗位标题")
    company_name: str = Field(..., description="公司名称")
    work_location: Optional[str] = Field(None, description="工作地点")
    salary_range: Optional[str] = Field(None, description="薪资范围")
    job_description: Optional[str] = Field(None, description="岗位描述")
    education_requirement: Optional[str] = Field(None, description="学历要求")
    experience_requirement: Optional[str] = Field(None, description="经验要求")
    major_requirement: Optional[str] = Field(None, description="专业要求")
    publish_date: Optional[datetime] = Field(None, description="发布日期")
    source_url: Optional[str] = Field(None, description="来源URL")
    is_graduate_friendly: Optional[bool] = Field(False, description="是否适合应届生")
    job_type: Optional[str] = Field(None, description="工作类型")
    data_source: Optional[str] = Field(None, description="数据来源")


class JobCreate(JobBase):
    """岗位创建模式"""
    
    pass


class JobUpdate(BaseSchema):
    """岗位更新模式"""
    
    title: Optional[str] = Field(None, description="岗位标题")
    company_name: Optional[str] = Field(None, description="公司名称")
    work_location: Optional[str] = Field(None, description="工作地点")
    salary_range: Optional[str] = Field(None, description="薪资范围")
    job_description: Optional[str] = Field(None, description="岗位描述")
    education_requirement: Optional[str] = Field(None, description="学历要求")
    experience_requirement: Optional[str] = Field(None, description="经验要求")
    major_requirement: Optional[str] = Field(None, description="专业要求")
    publish_date: Optional[datetime] = Field(None, description="发布日期")
    source_url: Optional[str] = Field(None, description="来源URL")
    is_graduate_friendly: Optional[bool] = Field(None, description="是否适合应届生")
    job_type: Optional[str] = Field(None, description="工作类型")
    data_source: Optional[str] = Field(None, description="数据来源")


class Job(JobBase):
    """岗位模式"""
    
    id: int
    created_at: datetime
    updated_at: datetime
    details: Optional[JobDetail] = None


class JobQuery(PageParams):
    """岗位查询模式"""
    
    keywords: Optional[str] = Field(None, description="关键词")
    location: Optional[str] = Field(None, description="地点")
    education: Optional[str] = Field(None, description="学历")
    experience: Optional[str] = Field(None, description="经验")
    job_type: Optional[str] = Field(None, description="工作类型")
    salary: Optional[str] = Field(None, description="薪资")
    major: Optional[str] = Field(None, description="专业")
    is_graduate_friendly: Optional[bool] = Field(None, description="是否适合应届生")
    sort: Optional[str] = Field("publish_date", description="排序字段")
    order: Optional[str] = Field("desc", description="排序方向")


class NaturalLanguageSearchRequest(BaseSchema):
    """自然语言搜索请求模式"""
    
    query: str = Field(..., description="自然语言查询")


class JobMatchRequest(BaseSchema):
    """岗位匹配请求模式"""
    
    user_profile: Dict[str, Any] = Field(..., description="用户资料")


class JobMatchResponse(BaseSchema):
    """岗位匹配响应模式"""
    
    overall_match: float = Field(..., description="总体匹配度")
    education_match: float = Field(..., description="学历匹配度")
    major_match: float = Field(..., description="专业匹配度")
    experience_match: float = Field(..., description="经验匹配度")
    location_match: float = Field(..., description="地点匹配度")
    analysis: str = Field(..., description="匹配分析")
