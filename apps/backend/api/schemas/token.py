#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
令牌相关模式
"""

from typing import Optional
from pydantic import BaseModel, Field

from apps.backend.api.schemas.base import BaseSchema


class Token(BaseSchema):
    """令牌模式"""
    
    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field(..., description="令牌类型")
    expires_in: int = Field(..., description="过期时间（秒）")
    refresh_token: str = Field(..., description="刷新令牌")


class RefreshToken(BaseSchema):
    """刷新令牌模式"""
    
    refresh_token: str = Field(..., description="刷新令牌")


class TokenData(BaseSchema):
    """令牌数据模式"""
    
    user_id: Optional[int] = Field(None, description="用户ID")
    admin_id: Optional[int] = Field(None, description="管理员ID")
    exp: Optional[int] = Field(None, description="过期时间")
