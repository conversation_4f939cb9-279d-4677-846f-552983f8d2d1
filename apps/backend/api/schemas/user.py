from datetime import datetime
from typing import Optional, List

from pydantic import BaseModel, Field, EmailStr

from apps.backend.api.schemas.base import BaseSchema


class UserBase(BaseSchema):
    """用户基础模式"""

    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    email: Optional[EmailStr] = Field(None, description="电子邮箱")
    phone: Optional[str] = Field(None, description="手机号")
    real_name: Optional[str] = Field(None, description="真实姓名")
    education: Optional[str] = Field(None, description="学历")
    major: Optional[str] = Field(None, description="专业")
    graduation_year: Optional[int] = Field(None, description="毕业年份")
    school: Optional[str] = Field(None, description="学校")
    avatar_url: Optional[str] = Field(None, description="头像URL")


class UserCreate(UserBase):
    """用户创建模式"""

    password: str = Field(..., min_length=6, max_length=50, description="密码")


class UserUpdate(BaseSchema):
    """用户更新模式"""

    email: Optional[EmailStr] = Field(None, description="电子邮箱")
    phone: Optional[str] = Field(None, description="手机号")
    real_name: Optional[str] = Field(None, description="真实姓名")
    education: Optional[str] = Field(None, description="学历")
    major: Optional[str] = Field(None, description="专业")
    graduation_year: Optional[int] = Field(None, description="毕业年份")
    school: Optional[str] = Field(None, description="学校")
    avatar_url: Optional[str] = Field(None, description="头像URL")
    status: Optional[bool] = Field(None, description="用户状态")


class UserProfileBase(BaseSchema):
    """用户资料基础模式"""

    education: Optional[str] = Field(None, description="学历")
    major: Optional[str] = Field(None, description="专业")
    skills: Optional[str] = Field(None, description="技能")
    experience: Optional[str] = Field(None, description="经验")
    location_preference: Optional[str] = Field(None, description="地点偏好")
    job_type_preference: Optional[str] = Field(None, description="工作类型偏好")
    resume_url: Optional[str] = Field(None, description="简历URL")


class UserProfileCreate(UserProfileBase):
    """用户资料创建模式"""

    user_id: int = Field(..., description="用户ID")


class UserProfileUpdate(UserProfileBase):
    """用户资料更新模式"""

    pass


class UserProfile(UserProfileBase):
    """用户资料模式"""

    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime


class User(UserBase):
    """用户模式"""

    id: int
    status: bool
    last_login: Optional[datetime] = None
    created_at: datetime
    profile: Optional[UserProfile] = None


class UserResponse(BaseSchema):
    """用户响应模式"""

    id: int
    username: str
    email: Optional[str] = None
    is_active: bool = True
    created_at: datetime


class UserInDB(User):
    """数据库中的用户模式"""

    password_hash: str


class Token(BaseSchema):
    """令牌模式"""

    access_token: str
    token_type: str = "bearer"
    expires_in: int
    refresh_token: Optional[str] = None


class TokenData(BaseSchema):
    """令牌数据模式"""

    user_id: Optional[int] = None
    username: Optional[str] = None
    exp: Optional[datetime] = None





# 角色和权限相关Schema
class PermissionBase(BaseSchema):
    """权限基础模式"""

    name: str = Field(..., description="权限名称")
    display_name: Optional[str] = Field(None, description="权限显示名称")
    description: Optional[str] = Field(None, description="权限描述")
    resource: Optional[str] = Field(None, description="资源类型")
    action: Optional[str] = Field(None, description="操作类型")


class PermissionCreate(PermissionBase):
    """权限创建模式"""
    pass


class PermissionUpdate(BaseSchema):
    """权限更新模式"""

    display_name: Optional[str] = Field(None, description="权限显示名称")
    description: Optional[str] = Field(None, description="权限描述")
    is_active: Optional[bool] = Field(None, description="是否激活")


class Permission(PermissionBase):
    """权限模式"""

    id: int
    is_active: bool
    created_at: datetime
    updated_at: datetime


class RoleBase(BaseSchema):
    """角色基础模式"""

    name: str = Field(..., description="角色名称")
    display_name: str = Field(..., description="角色显示名称")
    description: Optional[str] = Field(None, description="角色描述")


class RoleCreate(RoleBase):
    """角色创建模式"""

    permission_ids: Optional[List[int]] = Field(default=[], description="权限ID列表")


class RoleUpdate(BaseSchema):
    """角色更新模式"""

    display_name: Optional[str] = Field(None, description="角色显示名称")
    description: Optional[str] = Field(None, description="角色描述")
    is_active: Optional[bool] = Field(None, description="是否激活")
    permission_ids: Optional[List[int]] = Field(None, description="权限ID列表")


class Role(RoleBase):
    """角色模式"""

    id: int
    is_active: bool
    created_at: datetime
    updated_at: datetime
    permissions: List[Permission] = []


class UserRoleUpdate(BaseSchema):
    """用户角色更新模式"""

    role_ids: List[int] = Field(..., description="角色ID列表")


class UserLoginLogBase(BaseSchema):
    """用户登录日志基础模式"""

    ip_address: Optional[str] = Field(None, description="IP地址")
    user_agent: Optional[str] = Field(None, description="用户代理")
    login_type: str = Field(default='web', description="登录类型")


class UserLoginLog(UserLoginLogBase):
    """用户登录日志模式"""

    id: int
    user_id: int
    login_time: datetime
    is_success: bool
    failure_reason: Optional[str] = None


class UserWithRoles(User):
    """带角色的用户模式"""

    roles: List[Role] = []


class UserListResponse(BaseSchema):
    """用户列表响应模式"""

    users: List[UserWithRoles]
    total: int
    page: int
    page_size: int
    pages: int


class UserStatsResponse(BaseSchema):
    """用户统计响应模式"""

    total_users: int
    active_users: int
    inactive_users: int
    new_users_today: int
    new_users_this_week: int
    new_users_this_month: int
    login_stats: dict
