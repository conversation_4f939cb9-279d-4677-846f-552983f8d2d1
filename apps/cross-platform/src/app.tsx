import React, { PropsWithChildren } from 'react'
import { useLaunch } from '@tarojs/taro'
import './app.scss'

function App({ children }: PropsWithChildren) {
  useLaunch(() => {
    console.log('🚀 App launched - 应用启动成功')
    console.log('🔍 Children:', children)
    console.log('🌐 Current URL:', window.location.href)
    console.log('🔗 Hash:', window.location.hash)
    console.log('📍 Pathname:', window.location.pathname)
  })

  console.log('🎯 App render called - 应用渲染中')
  console.log('🌍 Current location:', window.location.href)

  // children 是将要会渲染的页面
  return children
}

export default App