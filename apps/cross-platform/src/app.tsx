import React, { PropsWithChildren, useEffect } from 'react'
import { useLaunch } from '@tarojs/taro'
import './app.scss'

function App({ children }: PropsWithChildren) {
  useLaunch(() => {
    console.log('🚀 App launched - 应用启动成功')
    console.log('🔍 Children:', children)
    console.log('🌐 Current URL:', window.location.href)
    console.log('🔗 Hash:', window.location.hash)
    console.log('📍 Pathname:', window.location.pathname)

    // 手动测试动态导入
    console.log('🧪 Testing manual dynamic import...')
    import('./pages/test/index').then(module => {
      console.log('✅ Manual import successful:', module)
      console.log('✅ Module default export:', module.default)
    }).catch(error => {
      console.error('❌ Manual import failed:', error)
    })
  })

  useEffect(() => {
    console.log('🔄 App useEffect - children changed:', children)
    console.log('🔍 Children type:', typeof children)
    console.log('🔍 Children props:', children?.props)

    // 检查DOM结构
    setTimeout(() => {
      const appElement = document.getElementById('app')
      console.log('🏗️ App DOM element:', appElement)
      console.log('🏗️ App innerHTML:', appElement?.innerHTML)
      console.log('🏗️ App children count:', appElement?.children.length)
    }, 100)
  }, [children])

  console.log('🎯 App render called - 应用渲染中')
  console.log('🌍 Current location:', window.location.href)
  console.log('🔍 Rendering children:', children)

  // 如果没有children，显示一个fallback
  if (!children) {
    console.log('❌ No children provided - 没有子组件')
    return React.createElement('div', {
      style: {
        width: '100vw',
        height: '100vh',
        backgroundColor: '#ff0000',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '24px',
        color: 'white',
        fontWeight: 'bold'
      }
    }, '❌ 没有页面内容 - No Page Content')
  }

  // 添加一个包装器来确保内容可见
  return React.createElement('div', {
    style: {
      width: '100vw',
      height: '100vh',
      backgroundColor: '#f0f0f0',
      position: 'relative'
    }
  }, [
    React.createElement('div', {
      key: 'debug-info',
      style: {
        position: 'absolute',
        top: '10px',
        left: '10px',
        backgroundColor: 'rgba(0,0,0,0.8)',
        color: 'white',
        padding: '10px',
        fontSize: '12px',
        zIndex: 9999
      }
    }, `Debug: Children type: ${typeof children}, URL: ${window.location.hash}`),
    children
  ])
}

export default App