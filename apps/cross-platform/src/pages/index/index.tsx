import React, { Component } from 'react'
import { View, Text, Button } from '@tarojs/components'
import Taro from '@tarojs/taro'
import './index.scss'

export default class Index extends Component {
  componentDidMount() {
    console.log('Index page mounted')
    console.log('Component rendered successfully')
    // 设置页面标题
    Taro.setNavigationBarTitle({
      title: '首页'
    })
  }

  handleNavigateToJobs = () => {
    Taro.switchTab({
      url: '/pages/jobs/index'
    })
  }

  handleNavigateToMatch = () => {
    Taro.switchTab({
      url: '/pages/match/index'
    })
  }

  render() {
    console.log('Index render called')
    return (
      <View className="index-simple">
        <View className="header-simple">
          <Text className="title-simple">事业编制招聘平台</Text>
          <Text className="subtitle-simple">欢迎使用招聘信息查询系统</Text>
        </View>

        <View className="content-simple">
          <View className="card-simple">
            <Text className="card-title">快速导航</Text>
            <View className="button-group">
              <Button
                onClick={this.handleNavigateToJobs}
                type='primary'
                className="nav-button"
              >
                查看职位
              </Button>
              <Button
                onClick={this.handleNavigateToMatch}
                type='default'
                className="nav-button"
              >
                智能匹配
              </Button>
            </View>
          </View>

          <View className="info-card">
            <Text className="info-title">系统状态</Text>
            <Text className="info-text">✅ 前端应用运行正常</Text>
            <Text className="info-text">✅ 导航组件显示正常</Text>
            <Text className="info-text">✅ 页面内容正在显示</Text>
            <Text className="info-text">🔄 正在测试样式渲染</Text>
          </View>
        </View>
      </View>
    )
  }
}