import React, { Component } from 'react'
import { View, Text, Button } from '@tarojs/components'
import Taro from '@tarojs/taro'

class Index extends Component {
  constructor(props) {
    super(props)
    console.log('🚀 Index constructor called')
  }

  componentDidMount() {
    console.log('🎯 Index componentDidMount called')
    console.log('📱 Component mounted successfully')
  }

  componentWillUnmount() {
    console.log('🔚 Index componentWillUnmount called')
  }

  handleNavigateToJobs = () => {
    console.log('🔄 Navigate to jobs clicked')
    Taro.navigateTo({
      url: '/pages/jobs/index'
    })
  }

  render() {
    console.log('🎨 Index render called - 测试渲染开始')
    console.log('⏰ Render time:', new Date().toLocaleString())

    return (
      <View style={{
        padding: '20px',
        backgroundColor: '#f0f0f0',
        minHeight: '100vh',
        fontSize: '16px'
      }}>
        <Text style={{
          display: 'block',
          fontSize: '20px',
          fontWeight: 'bold',
          marginBottom: '20px',
          color: '#333'
        }}>
          🔥 测试页面 - 如果您能看到这行文字，说明页面渲染正常！
        </Text>

        <Text style={{
          display: 'block',
          marginBottom: '20px',
          color: '#666'
        }}>
          当前时间：{new Date().toLocaleString()}
        </Text>

        <View style={{
          backgroundColor: 'red',
          height: '100px',
          width: '100%',
          marginTop: '20px',
          marginBottom: '20px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <Text style={{
            color: 'white',
            fontSize: '18px',
            fontWeight: 'bold'
          }}>
            红色测试区域
          </Text>
        </View>

        <Button
          onClick={this.handleNavigateToJobs}
          style={{
            backgroundColor: '#1890ff',
            color: 'white',
            border: 'none',
            padding: '10px 20px',
            borderRadius: '4px',
            fontSize: '16px'
          }}
        >
          测试按钮
        </Button>

        <View style={{
          marginTop: '20px',
          padding: '10px',
          backgroundColor: 'yellow',
          border: '2px solid orange'
        }}>
          <Text style={{ color: 'black', fontWeight: 'bold' }}>
            黄色调试区域 - 如果看到这个说明样式正常
          </Text>
        </View>
      </View>
    )
  }
}

export default Index