import React, { Component } from 'react'
import { View, Text, Button } from '@tarojs/components'
import Taro from '@tarojs/taro'
import './index.scss'

class Index extends Component {
  componentDidMount() {
    console.log('Index page mounted')
    console.log('Component rendered successfully')
    // 设置页面标题
    Taro.setNavigationBarTitle({
      title: '首页'
    })
  }

  handleNavigateToJobs = () => {
    Taro.switchTab({
      url: '/pages/jobs/index'
    })
  }

  handleNavigateToMatch = () => {
    Taro.switchTab({
      url: '/pages/match/index'
    })
  }

  render() {
    console.log('Index render called')
    return (
      <View style={{
        backgroundColor: '#f8f9fa',
        minHeight: '100vh',
        padding: '20px',
        position: 'relative',
        zIndex: 1
      }}>
        <View style={{
          backgroundColor: '#667eea',
          padding: '40px 20px',
          color: 'white',
          textAlign: 'center',
          marginBottom: '20px',
          borderRadius: '12px'
        }}>
          <Text style={{
            fontSize: '28px',
            fontWeight: 'bold',
            display: 'block',
            marginBottom: '10px'
          }}>事业编制招聘平台</Text>
          <Text style={{
            fontSize: '16px',
            display: 'block'
          }}>欢迎使用招聘信息查询系统</Text>
        </View>

        <View style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          padding: '24px',
          marginBottom: '20px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
        }}>
          <Text style={{
            fontSize: '18px',
            fontWeight: '600',
            color: '#333',
            marginBottom: '16px',
            display: 'block'
          }}>快速导航</Text>
          <View style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            <Button
              onClick={this.handleNavigateToJobs}
              type='primary'
              style={{
                padding: '12px 24px',
                borderRadius: '8px',
                fontSize: '16px'
              }}
            >
              查看职位
            </Button>
            <Button
              onClick={this.handleNavigateToMatch}
              type='default'
              style={{
                padding: '12px 24px',
                borderRadius: '8px',
                fontSize: '16px'
              }}
            >
              智能匹配
            </Button>
          </View>
        </View>

        <View style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          padding: '24px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
        }}>
          <Text style={{
            fontSize: '18px',
            fontWeight: '600',
            color: '#333',
            marginBottom: '16px',
            display: 'block'
          }}>系统状态</Text>
          <Text style={{
            fontSize: '14px',
            color: '#666',
            marginBottom: '8px',
            display: 'block'
          }}>✅ 前端应用运行正常</Text>
          <Text style={{
            fontSize: '14px',
            color: '#666',
            marginBottom: '8px',
            display: 'block'
          }}>✅ 导航组件显示正常</Text>
          <Text style={{
            fontSize: '14px',
            color: '#666',
            marginBottom: '8px',
            display: 'block'
          }}>✅ 页面内容正在显示</Text>
          <Text style={{
            fontSize: '14px',
            color: '#666',
            display: 'block'
          }}>🔄 正在测试内联样式渲染</Text>
        </View>
      </View>
    )
  }
}

export default Index