.job-detail {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 16px;
}

.loading-section, .error-section, .empty-section {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  margin: 20px 0;

  .loading-text, .error-text, .empty-text {
    font-size: 16px;
    color: #666;
    margin-bottom: 16px;
  }

  .error-text {
    color: #e53e3e;
  }

  .retry-btn {
    margin-top: 12px;
  }
}

.job-header {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .job-title {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 12px;
    display: block;
    line-height: 1.4;
  }

  .job-salary {
    font-size: 18px;
    color: #e74c3c;
    font-weight: 600;
    margin-bottom: 8px;
    display: block;
  }

  .job-company {
    font-size: 16px;
    color: #666;
    margin-bottom: 4px;
    display: block;
  }

  .job-location {
    font-size: 14px;
    color: #999;
    display: block;
  }
}

.job-info {
  .info-section {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .section-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin-bottom: 16px;
      display: block;
    }

    .info-grid {
      display: grid;
      gap: 12px;

      .info-item {
        display: flex;
        align-items: center;

        .info-label {
          font-size: 14px;
          color: #666;
          width: 80px;
          flex-shrink: 0;
        }

        .info-value {
          font-size: 14px;
          color: #333;
          flex: 1;

          &.graduate-friendly {
            color: #27ae60;
            font-weight: 500;
          }
        }
      }
    }

    .job-description {
      font-size: 14px;
      color: #666;
      line-height: 1.6;
      white-space: pre-line;
    }
  }
}

.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 16px;
  display: flex;
  gap: 12px;
  border-top: 1px solid #e8e8e8;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);

  .favorite-btn {
    flex: 1;
    height: 44px;
    background: #f5f5f5;
    color: #333;
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    font-size: 14px;
  }

  .apply-btn {
    flex: 2;
    height: 44px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
  }
}