import React, { Component } from 'react'
import { View, Text, Button } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { jobsAPI } from '../../services/api'
import './detail.scss'

interface JobDetail {
  job_id: string
  title: string
  company_name: string
  work_location: string
  salary_range: string
  publish_date: string
  education_requirement?: string
  experience_requirement?: string
  job_description?: string
  major_requirement?: string
  is_graduate_friendly?: boolean
  job_type?: string
  source_url?: string
}

interface JobDetailState {
  jobDetail: JobDetail | null
  loading: boolean
  error: string | null
  jobId: string
}

class JobDetail extends Component<{}, JobDetailState> {
  constructor(props: {}) {
    super(props)
    this.state = {
      jobDetail: null,
      loading: false,
      error: null,
      jobId: ''
    }
  }

  componentDidMount() {
    Taro.setNavigationBarTitle({ title: '职位详情' })

    const router = Taro.getCurrentInstance().router
    if (router && router.params && router.params.id) {
      const jobId = router.params.id
      this.setState({ jobId })
      this.loadJobDetail(jobId)
    } else {
      this.setState({ error: '职位ID不存在' })
    }
  }

  // 加载职位详情
  loadJobDetail = async (jobId: string) => {
    this.setState({ loading: true, error: null })

    try {
      const response = await jobsAPI.getJobDetail(jobId)
      console.log('职位详情:', response)

      this.setState({
        jobDetail: response,
        loading: false
      })
    } catch (error: any) {
      console.error('加载职位详情失败:', error)
      this.setState({
        error: error.message || '加载职位详情失败',
        loading: false
      })
    }
  }

  // 格式化日期
  formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    } catch {
      return dateString
    }
  }

  // 收藏职位
  handleFavorite = async () => {
    const { jobId } = this.state

    try {
      await jobsAPI.favoriteJob(jobId)
      Taro.showToast({
        title: '收藏成功',
        icon: 'success',
        duration: 1500
      })
    } catch (error: any) {
      console.error('收藏失败:', error)
      Taro.showToast({
        title: '收藏失败',
        icon: 'none',
        duration: 1500
      })
    }
  }

  render() {
    const { jobDetail, loading, error } = this.state

    if (loading) {
      return (
        <View className='job-detail'>
          <View className='loading-section'>
            <Text className='loading-text'>🔄 加载中...</Text>
          </View>
        </View>
      )
    }

    if (error) {
      return (
        <View className='job-detail'>
          <View className='error-section'>
            <Text className='error-text'>❌ {error}</Text>
            <Button
              className='retry-btn'
              size='mini'
              onClick={() => this.loadJobDetail(this.state.jobId)}
            >
              重试
            </Button>
          </View>
        </View>
      )
    }

    if (!jobDetail) {
      return (
        <View className='job-detail'>
          <View className='empty-section'>
            <Text className='empty-text'>职位信息不存在</Text>
          </View>
        </View>
      )
    }

    return (
      <View className='job-detail'>
        <View className='job-header'>
          <Text className='job-title'>{jobDetail.title}</Text>
          <Text className='job-salary'>{jobDetail.salary_range}</Text>
          <Text className='job-company'>{jobDetail.company_name}</Text>
          <Text className='job-location'>{jobDetail.work_location}</Text>
        </View>

        <View className='job-info'>
          <View className='info-section'>
            <Text className='section-title'>职位信息</Text>

            <View className='info-grid'>
              {jobDetail.education_requirement && (
                <View className='info-item'>
                  <Text className='info-label'>学历要求</Text>
                  <Text className='info-value'>{jobDetail.education_requirement}</Text>
                </View>
              )}

              {jobDetail.experience_requirement && (
                <View className='info-item'>
                  <Text className='info-label'>工作经验</Text>
                  <Text className='info-value'>{jobDetail.experience_requirement}</Text>
                </View>
              )}

              {jobDetail.major_requirement && (
                <View className='info-item'>
                  <Text className='info-label'>专业要求</Text>
                  <Text className='info-value'>{jobDetail.major_requirement}</Text>
                </View>
              )}

              {jobDetail.job_type && (
                <View className='info-item'>
                  <Text className='info-label'>工作性质</Text>
                  <Text className='info-value'>{jobDetail.job_type}</Text>
                </View>
              )}

              <View className='info-item'>
                <Text className='info-label'>发布时间</Text>
                <Text className='info-value'>{this.formatDate(jobDetail.publish_date)}</Text>
              </View>

              {jobDetail.is_graduate_friendly && (
                <View className='info-item'>
                  <Text className='info-label'>应届生</Text>
                  <Text className='info-value graduate-friendly'>✅ 友好</Text>
                </View>
              )}
            </View>
          </View>

          {jobDetail.job_description && (
            <View className='info-section'>
              <Text className='section-title'>职位描述</Text>
              <Text className='job-description'>{jobDetail.job_description}</Text>
            </View>
          )}
        </View>

        <View className='action-buttons'>
          <Button
            className='favorite-btn'
            onClick={this.handleFavorite}
          >
            收藏职位
          </Button>

          {jobDetail.source_url && (
            <Button
              className='apply-btn'
              type='primary'
              onClick={() => {
                Taro.showModal({
                  title: '跳转提示',
                  content: '即将跳转到原始招聘页面',
                  success: (res) => {
                    if (res.confirm && jobDetail.source_url) {
                      // 在小程序中可能需要特殊处理外部链接
                      console.log('跳转到:', jobDetail.source_url)
                    }
                  }
                })
              }}
            >
              立即申请
            </Button>
          )}
        </View>
      </View>
    )
  }
}

export default JobDetail