.jobs-simple {
  background-color: #f8f9fa;
  min-height: 100vh;
  padding: 0;
}

.search-section {
  background: white;
  padding: 16px;
  display: flex;
  gap: 12px;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  .search-input {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    font-size: 14px;
  }
  
  .search-btn {
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
  }
}

.jobs-list {
  padding: 16px;
  
  .list-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 16px;
    display: block;
  }
  
  .job-card {
    background: white;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s;
    
    &:active {
      transform: scale(0.98);
    }
    
    .job-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 12px;
      
      .job-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        flex: 1;
        margin-right: 12px;
      }
      
      .job-salary {
        font-size: 14px;
        color: #e74c3c;
        font-weight: 500;
      }
    }
    
    .job-info {
      display: flex;
      justify-content: space-between;
      margin-bottom: 12px;
      
      .job-company {
        font-size: 14px;
        color: #666;
      }
      
      .job-location {
        font-size: 14px;
        color: #999;
      }
    }
    
    .job-requirements {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-bottom: 12px;

      .job-requirement {
        font-size: 12px;
        color: #666;
        background: #f5f5f5;
        padding: 4px 8px;
        border-radius: 4px;
      }

      .job-tag {
        font-size: 12px;
        color: #3498db;
        background: #e3f2fd;
        padding: 4px 8px;
        border-radius: 4px;
      }
    }

    .job-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .job-time {
        font-size: 12px;
        color: #999;
      }

      .job-status {
        font-size: 12px;
        color: #27ae60;
        background: #e8f5e8;
        padding: 4px 8px;
        border-radius: 4px;
      }
    }
  }
}

.error-section {
  background: #fff5f5;
  border: 1px solid #fed7d7;
  margin: 16px;
  padding: 16px;
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .error-text {
    font-size: 14px;
    color: #e53e3e;
    flex: 1;
  }

  .retry-btn {
    margin-left: 12px;
  }
}

.empty-state {
  text-align: center;
  padding: 40px 20px;

  .empty-text {
    font-size: 16px;
    color: #999;
    margin-bottom: 16px;
    display: block;
  }

  .refresh-btn {
    margin: 0 auto;
  }
}

.loading-section {
  text-align: center;
  padding: 20px;

  .loading-text {
    font-size: 14px;
    color: #666;
  }
}

.status-info {
  background: white;
  margin: 16px;
  padding: 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .status-text {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    display: block;
    line-height: 1.5;
  }
}