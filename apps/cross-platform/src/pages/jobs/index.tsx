import React, { Component } from 'react'
import { View, Text, Button, Input } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { jobsAPI } from '../../services/api'
import './index.scss'

interface Job {
  job_id: string
  title: string
  company_name: string
  work_location: string
  salary_range: string
  publish_date: string
  education_requirement?: string
  experience_requirement?: string
  job_description?: string
  major_requirement?: string
  is_graduate_friendly?: boolean
}

interface State {
  searchKeyword: string
  jobs: Job[]
  loading: boolean
  error: string | null
  total: number
  page: number
  pageSize: number
  hasMore: boolean
}

export default class Jobs extends Component<{}, State> {
  constructor(props: {}) {
    super(props)
    this.state = {
      searchKeyword: '',
      jobs: [],
      loading: false,
      error: null,
      total: 0,
      page: 1,
      pageSize: 10,
      hasMore: true
    }
  }

  componentDidMount() {
    console.log('Jobs page mounted')
    Taro.setNavigationBarTitle({ title: '职位列表' })
    this.loadJobs()
  }

  // 加载职位数据
  loadJobs = async (isRefresh: boolean = false) => {
    const { page, pageSize, jobs, loading } = this.state

    if (loading) return

    this.setState({ loading: true, error: null })

    try {
      const currentPage = isRefresh ? 1 : page
      const response = await jobsAPI.searchJobs({
        page: currentPage,
        page_size: pageSize
      })

      console.log('API Response:', response)

      const newJobs = response.items || []

      this.setState({
        jobs: isRefresh ? newJobs : [...jobs, ...newJobs],
        total: response.total || 0,
        page: currentPage + 1,
        hasMore: newJobs.length === pageSize,
        loading: false
      })
    } catch (error: any) {
      console.error('加载职位失败:', error)
      this.setState({
        error: error.message || '加载职位失败',
        loading: false
      })

      // 显示错误提示
      Taro.showToast({
        title: '加载失败，请重试',
        icon: 'none',
        duration: 2000
      })
    }
  }

  // 搜索职位
  searchJobs = async () => {
    const { searchKeyword } = this.state

    if (!searchKeyword.trim()) {
      this.loadJobs(true)
      return
    }

    this.setState({ loading: true, error: null })

    try {
      const response = await jobsAPI.searchJobs({
        keywords: searchKeyword.trim(),
        page: 1,
        page_size: this.state.pageSize
      })

      console.log('Search Response:', response)

      this.setState({
        jobs: response.items || [],
        total: response.total || 0,
        page: 2,
        hasMore: (response.items || []).length === this.state.pageSize,
        loading: false
      })
    } catch (error: any) {
      console.error('搜索职位失败:', error)
      this.setState({
        error: error.message || '搜索失败',
        loading: false
      })

      Taro.showToast({
        title: '搜索失败，请重试',
        icon: 'none',
        duration: 2000
      })
    }
  }

  handleSearchChange = (e: any) => {
    this.setState({
      searchKeyword: e.detail.value
    })
  }

  handleJobDetail = (jobId: string) => {
    Taro.navigateTo({
      url: `/pages/jobs/detail?id=${jobId}`
    })
  }

  // 格式化日期
  formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    } catch {
      return dateString
    }
  }

  // 下拉刷新
  onPullDownRefresh = () => {
    this.loadJobs(true).finally(() => {
      Taro.stopPullDownRefresh()
    })
  }

  // 上拉加载更多
  onReachBottom = () => {
    const { hasMore, loading } = this.state
    if (hasMore && !loading) {
      this.loadJobs()
    }
  }

  render() {
    const { searchKeyword, jobs, loading, error, total } = this.state

    return (
      <View className='jobs-simple'>
        <View className='search-section'>
          <Input
            className='search-input'
            placeholder='搜索职位、公司、地区...'
            value={searchKeyword}
            onInput={this.handleSearchChange}
          />
          <Button
            className='search-btn'
            type='primary'
            loading={loading}
            onClick={this.searchJobs}
          >
            搜索
          </Button>
        </View>

        {error && (
          <View className='error-section'>
            <Text className='error-text'>❌ {error}</Text>
            <Button
              className='retry-btn'
              size='mini'
              onClick={() => this.loadJobs(true)}
            >
              重试
            </Button>
          </View>
        )}

        <View className='jobs-list'>
          <Text className='list-title'>
            最新职位 ({total > 0 ? total : jobs.length})
          </Text>

          {jobs.length === 0 && !loading && !error && (
            <View className='empty-state'>
              <Text className='empty-text'>暂无职位数据</Text>
              <Button
                className='refresh-btn'
                size='mini'
                onClick={() => this.loadJobs(true)}
              >
                刷新
              </Button>
            </View>
          )}

          {jobs.map(job => (
            <View
              key={job.job_id}
              className='job-card'
              onClick={() => this.handleJobDetail(job.job_id)}
            >
              <View className='job-header'>
                <Text className='job-title'>{job.title}</Text>
                <Text className='job-salary'>{job.salary_range}</Text>
              </View>

              <View className='job-info'>
                <Text className='job-company'>{job.company_name}</Text>
                <Text className='job-location'>{job.work_location}</Text>
              </View>

              {(job.education_requirement || job.experience_requirement) && (
                <View className='job-requirements'>
                  {job.education_requirement && (
                    <Text className='job-requirement'>学历: {job.education_requirement}</Text>
                  )}
                  {job.experience_requirement && (
                    <Text className='job-requirement'>经验: {job.experience_requirement}</Text>
                  )}
                  {job.is_graduate_friendly && (
                    <Text className='job-tag'>应届生友好</Text>
                  )}
                </View>
              )}

              <View className='job-footer'>
                <Text className='job-time'>
                  发布时间: {this.formatDate(job.publish_date)}
                </Text>
                <Text className='job-status'>招聘中</Text>
              </View>
            </View>
          ))}

          {loading && (
            <View className='loading-section'>
              <Text className='loading-text'>🔄 加载中...</Text>
            </View>
          )}
        </View>

        <View className='status-info'>
          <Text className='status-text'>✅ 职位页面运行正常</Text>
          <Text className='status-text'>🔗 已连接后端API</Text>
          <Text className='status-text'>📊 显示真实职位数据</Text>
        </View>
      </View>
    )
  }
}