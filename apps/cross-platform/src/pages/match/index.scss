.match-simple {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px;

  .user-profile {
    margin-bottom: 30px;

    .section-title {
      font-size: 18px;
      font-weight: bold;
      color: #333;
      margin-bottom: 15px;
      display: block;
    }

    .profile-card {
      background: white;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .profile-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        
        &:last-child {
          margin-bottom: 0;
        }

        .label {
          font-size: 14px;
          color: #666;
          width: 80px;
          flex-shrink: 0;
        }

        .value {
          font-size: 14px;
          color: #333;
          flex: 1;
        }
      }
    }

    .button-group {
      display: flex;
      gap: 12px;
      align-items: center;
    }

    .match-button {
      flex: 1;
      height: 44px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      border-radius: 22px;
      color: white;
      font-size: 16px;
      font-weight: bold;
    }
  }

  .match-results {
    margin-bottom: 30px;

    .section-title {
      font-size: 18px;
      font-weight: bold;
      color: #333;
      margin-bottom: 15px;
      display: block;
    }

    .result-card {
      background: white;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 15px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      cursor: pointer;
      transition: transform 0.2s ease;

      &:hover {
        transform: translateY(-2px);
      }

      .result-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;

        .job-title {
          font-size: 16px;
          font-weight: bold;
          color: #333;
          flex: 1;
        }

        .match-score {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          padding: 4px 12px;
          border-radius: 12px;
          font-size: 12px;

          .score-text {
            font-weight: bold;
          }
        }
      }

      .job-info {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
        margin-bottom: 12px;

        .company-name {
          font-size: 14px;
          color: #666;
        }

        .job-location {
          font-size: 14px;
          color: #999;
        }

        .job-salary {
          font-size: 14px;
          color: #e74c3c;
          font-weight: 500;
        }
      }

      .job-requirements {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-bottom: 12px;

        .job-requirement {
          font-size: 12px;
          color: #666;
          background: #f5f5f5;
          padding: 4px 8px;
          border-radius: 4px;
        }

        .job-tag {
          font-size: 12px;
          color: #3498db;
          background: #e3f2fd;
          padding: 4px 8px;
          border-radius: 4px;
        }
      }

      .company-name {
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
        display: block;
      }

      .match-reason {
        font-size: 13px;
        color: #888;
        margin-bottom: 15px;
        display: block;
        line-height: 1.4;
      }

      .result-footer {
        display: flex;
        align-items: center;
        gap: 10px;

        .match-label {
          font-size: 12px;
          color: #666;
          width: 40px;
          flex-shrink: 0;
        }

        .progress-bar {
          flex: 1;
          height: 6px;
          background-color: #f0f0f0;
          border-radius: 3px;
          overflow: hidden;

          .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 3px;
            transition: width 0.3s ease;
          }
        }
      }
    }
  }

  .error-section {
    background: #fff5f5;
    border: 1px solid #fed7d7;
    margin-bottom: 20px;
    padding: 16px;
    border-radius: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .error-text {
      font-size: 14px;
      color: #e53e3e;
      flex: 1;
    }

    .retry-btn {
      margin-left: 12px;
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px 20px;
    background: white;
    border-radius: 8px;
    margin-bottom: 20px;

    .empty-text {
      font-size: 16px;
      color: #666;
      margin-bottom: 8px;
      display: block;
    }

    .empty-desc {
      font-size: 14px;
      color: #999;
      line-height: 1.5;
    }
  }

  .status-info {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .status-text {
      display: block;
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
      line-height: 1.5;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}