import React, { Component } from 'react'
import { View, Text, Button, Input, Image } from '@tarojs/components'
import Taro from '@tarojs/taro'
import './index.scss'

interface State {
  userInfo: {
    name: string
    email: string
    phone: string
    education: string
    major: string
    experience: string
    location: string
    bio: string
    avatar: string
  }
  isEditing: boolean
}

export default class Profile extends Component<{}, State> {
  constructor(props: {}) {
    super(props)
    this.state = {
      userInfo: {
        name: '张三',
        email: '<EMAIL>',
        phone: '138****8888',
        education: '本科',
        major: '计算机科学与技术',
        experience: '2年',
        location: '北京市',
        bio: '热爱编程，具有良好的团队合作精神和学习能力。',
        avatar: ''
      },
      isEditing: false
    }
  }

  componentDidMount() {
    console.log('Profile page mounted')
    Taro.setNavigationBarTitle({ title: '个人中心' })
  }

  handleEdit = () => {
    this.setState({ isEditing: true })
  }

  handleSave = () => {
    this.setState({ isEditing: false })
    Taro.showToast({
      title: '保存成功',
      icon: 'success'
    })
  }

  handleCancel = () => {
    this.setState({ isEditing: false })
  }

  handleInputChange = (field: string, value: string) => {
    this.setState({
      userInfo: {
        ...this.state.userInfo,
        [field]: value
      }
    })
  }

  handleLogout = () => {
    Taro.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          Taro.showToast({
            title: '已退出登录',
            icon: 'success'
          })
          // 这里可以添加实际的退出登录逻辑
        }
      }
    })
  }

  handleAvatarChange = () => {
    Taro.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0]
        this.setState({
          userInfo: {
            ...this.state.userInfo,
            avatar: tempFilePath
          }
        })
        Taro.showToast({
          title: '头像已更新',
          icon: 'success'
        })
      }
    })
  }

  render() {
    const { userInfo, isEditing } = this.state

    return (
      <View className='profile-simple'>
        <View className='profile-header'>
          <View className='avatar-section'>
            <View className='avatar-container' onClick={this.handleAvatarChange}>
              {userInfo.avatar ? (
                <Image className='avatar' src={userInfo.avatar} mode='aspectFill' />
              ) : (
                <View className='avatar-placeholder'>
                  <Text className='avatar-text'>{userInfo.name.charAt(0)}</Text>
                </View>
              )}
              <View className='avatar-edit-hint'>点击更换</View>
            </View>
          </View>
          
          <View className='user-basic-info'>
            <Text className='user-name'>{userInfo.name}</Text>
            <Text className='user-title'>{userInfo.major} · {userInfo.experience}</Text>
          </View>
        </View>

        <View className='profile-content'>
          <View className='section'>
            <View className='section-header'>
              <Text className='section-title'>基本信息</Text>
              {!isEditing && (
                <Button className='edit-btn' size='mini' onClick={this.handleEdit}>
                  编辑
                </Button>
              )}
            </View>

            <View className='info-list'>
              <View className='info-item'>
                <Text className='label'>姓名：</Text>
                {isEditing ? (
                  <Input
                    className='edit-input'
                    value={userInfo.name}
                    onInput={(e) => this.handleInputChange('name', e.detail.value)}
                  />
                ) : (
                  <Text className='value'>{userInfo.name}</Text>
                )}
              </View>

              <View className='info-item'>
                <Text className='label'>邮箱：</Text>
                {isEditing ? (
                  <Input
                    className='edit-input'
                    value={userInfo.email}
                    onInput={(e) => this.handleInputChange('email', e.detail.value)}
                  />
                ) : (
                  <Text className='value'>{userInfo.email}</Text>
                )}
              </View>

              <View className='info-item'>
                <Text className='label'>手机：</Text>
                {isEditing ? (
                  <Input
                    className='edit-input'
                    value={userInfo.phone}
                    onInput={(e) => this.handleInputChange('phone', e.detail.value)}
                  />
                ) : (
                  <Text className='value'>{userInfo.phone}</Text>
                )}
              </View>

              <View className='info-item'>
                <Text className='label'>学历：</Text>
                {isEditing ? (
                  <Input
                    className='edit-input'
                    value={userInfo.education}
                    onInput={(e) => this.handleInputChange('education', e.detail.value)}
                  />
                ) : (
                  <Text className='value'>{userInfo.education}</Text>
                )}
              </View>

              <View className='info-item'>
                <Text className='label'>专业：</Text>
                {isEditing ? (
                  <Input
                    className='edit-input'
                    value={userInfo.major}
                    onInput={(e) => this.handleInputChange('major', e.detail.value)}
                  />
                ) : (
                  <Text className='value'>{userInfo.major}</Text>
                )}
              </View>

              <View className='info-item'>
                <Text className='label'>经验：</Text>
                {isEditing ? (
                  <Input
                    className='edit-input'
                    value={userInfo.experience}
                    onInput={(e) => this.handleInputChange('experience', e.detail.value)}
                  />
                ) : (
                  <Text className='value'>{userInfo.experience}</Text>
                )}
              </View>

              <View className='info-item'>
                <Text className='label'>地区：</Text>
                {isEditing ? (
                  <Input
                    className='edit-input'
                    value={userInfo.location}
                    onInput={(e) => this.handleInputChange('location', e.detail.value)}
                  />
                ) : (
                  <Text className='value'>{userInfo.location}</Text>
                )}
              </View>

              <View className='info-item bio-item'>
                <Text className='label'>简介：</Text>
                {isEditing ? (
                  <Input
                    className='edit-input bio-input'
                    value={userInfo.bio}
                    onInput={(e) => this.handleInputChange('bio', e.detail.value)}
                  />
                ) : (
                  <Text className='value bio-value'>{userInfo.bio}</Text>
                )}
              </View>
            </View>

            {isEditing && (
              <View className='edit-actions'>
                <Button className='cancel-btn' onClick={this.handleCancel}>
                  取消
                </Button>
                <Button className='save-btn' type='primary' onClick={this.handleSave}>
                  保存
                </Button>
              </View>
            )}
          </View>

          <View className='section'>
            <Text className='section-title'>功能菜单</Text>
            
            <View className='menu-list'>
              <View className='menu-item'>
                <Text className='menu-text'>我的简历</Text>
                <Text className='menu-arrow'>{'>'}</Text>
              </View>

              <View className='menu-item'>
                <Text className='menu-text'>投递记录</Text>
                <Text className='menu-arrow'>{'>'}</Text>
              </View>

              <View className='menu-item'>
                <Text className='menu-text'>收藏职位</Text>
                <Text className='menu-arrow'>{'>'}</Text>
              </View>

              <View className='menu-item'>
                <Text className='menu-text'>设置</Text>
                <Text className='menu-arrow'>{'>'}</Text>
              </View>
            </View>
          </View>

          <View className='logout-section'>
            <Button className='logout-btn' onClick={this.handleLogout}>
              退出登录
            </Button>
          </View>
        </View>
        
        <View className='status-info'>
          <Text className='status-text'>✅ 个人中心页面运行正常</Text>
          <Text className='status-text'>👤 显示模拟用户信息</Text>
          <Text className='status-text'>🔧 等待用户系统接入</Text>
        </View>
      </View>
    )
  }
}