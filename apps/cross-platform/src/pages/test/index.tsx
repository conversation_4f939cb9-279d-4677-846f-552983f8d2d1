import React, { useEffect } from 'react'
import { View, Text } from '@tarojs/components'

export default function Test() {
  console.log('🧪 Test function component called')

  useEffect(() => {
    console.log('🧪 Test useEffect called')
  }, [])

  return (
    <View style={{
      padding: '20px',
      backgroundColor: 'red',
      minHeight: '100vh',
      fontSize: '18px',
      color: 'white'
    }}>
      <Text style={{
        fontSize: '24px',
        color: 'white',
        fontWeight: 'bold',
        display: 'block',
        marginBottom: '20px'
      }}>
        🧪 测试页面 - 函数组件
      </Text>
      <Text style={{
        display: 'block',
        marginTop: '10px',
        color: 'yellow'
      }}>
        如果您能看到这个红色页面，说明 Taro 路由工作正常！
      </Text>
      <Text style={{
        display: 'block',
        marginTop: '10px',
        color: 'lightgreen',
        fontSize: '16px'
      }}>
        当前时间：{new Date().toLocaleString()}
      </Text>
    </View>
  )
}