import { View } from '@tarojs/components'

console.log('🧪 Test page module loaded')

export default function Test() {
  console.log('🧪 Test function component called')

  return (
    <View style={{
      width: '100vw',
      height: '100vh',
      backgroundColor: '#ff0000',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontSize: '24px',
      color: 'white',
      fontWeight: 'bold'
    }}>
      🧪 测试页面成功！
    </View>
  )
}

Test.config = {
  navigationBarTitleText: '测试页面'
}