import React, { Component } from 'react'
import { View, Text } from '@tarojs/components'

console.log('🧪 Test page module loaded - 模块已加载')
console.log('🧪 Test page module React version:', React.version)
console.log('🧪 Test page module View component:', View)

class Test extends Component {
  constructor(props) {
    super(props)
    console.log('🧪 Test constructor called - 构造函数被调用')
    console.log('🧪 Test constructor props:', props)
  }

  componentDidMount() {
    console.log('🧪 Test componentDidMount called - 组件已挂载')

    // 检查当前组件是否在DOM中
    setTimeout(() => {
      const testElements = document.querySelectorAll('[style*="background-color: rgb(255, 0, 0)"]')
      console.log('🧪 Test elements in DOM:', testElements)

      const viewElements = document.querySelectorAll('taro-view-core')
      console.log('🧪 Taro View elements:', viewElements)

      const allElements = document.querySelectorAll('*')
      console.log('🧪 All DOM elements count:', allElements.length)

      // 查找包含测试文本的元素
      const textElements = Array.from(allElements).filter(el =>
        el.textContent && el.textContent.includes('测试页面成功')
      )
      console.log('🧪 Elements with test text:', textElements)
    }, 100)
  }

  componentWillUnmount() {
    console.log('🧪 Test componentWillUnmount called - 组件即将卸载')
  }

  render() {
    console.log('🧪 Test render called - 渲染函数被调用')
    console.log('🧪 Test render props:', this.props)
    console.log('🧪 Test render time:', new Date().toLocaleString())

    const testView = (
      <View style={{
        width: '100vw',
        height: '100vh',
        backgroundColor: '#ff0000',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '24px',
        color: 'white',
        fontWeight: 'bold',
        flexDirection: 'column'
      }}>
        <Text style={{
          fontSize: '24px',
          color: 'white',
          fontWeight: 'bold',
          marginBottom: '20px'
        }}>
          🧪 测试页面成功！Test Page Success!
        </Text>
        <Text style={{
          fontSize: '16px',
          color: 'white'
        }}>
          类组件版本 - Class Component Version
        </Text>
        <Text style={{
          fontSize: '14px',
          color: 'white',
          marginTop: '10px'
        }}>
          {new Date().toLocaleString()}
        </Text>
      </View>
    )

    console.log('🧪 Test view element:', testView)
    console.log('🧪 Test view element type:', typeof testView)
    console.log('🧪 Test view element $$typeof:', testView.$$typeof)

    return testView
  }
}

Test.config = {
  navigationBarTitleText: '测试页面'
}

console.log('🧪 Test component defined - 组件已定义')
console.log('🧪 Test component:', Test)
console.log('🧪 Test component config:', Test.config)

export default Test