import React from 'react'
import { View } from '@tarojs/components'

console.log('🧪 Test page module loaded - 模块已加载')

function Test() {
  console.log('🧪 Test function component called - 函数组件被调用')

  return (
    <View style={{
      width: '100vw',
      height: '100vh',
      backgroundColor: '#ff0000',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontSize: '24px',
      color: 'white',
      fontWeight: 'bold'
    }}>
      🧪 测试页面成功！Test Page Success!
    </View>
  )
}

Test.config = {
  navigationBarTitleText: '测试页面'
}

export default Test