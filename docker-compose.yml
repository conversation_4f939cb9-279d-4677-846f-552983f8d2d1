services:
  backend:
    build:
      context: .
      dockerfile: docker/Dockerfile.backend
    depends_on:
    - db
    - redis
    environment:
    - FLASK_ENV=production
    - DATABASE_URL=mysql://user:password@db:3306/recruitment
    ports:
    - 5000:5000
    volumes:
    - ./logs:/app/logs
  db:
    environment:
    - MYSQL_ROOT_PASSWORD=rootpassword
    - MYSQL_DATABASE=recruitment
    - MYSQL_USER=user
    - MYSQL_PASSWORD=password
    image: mysql:8.0
    ports:
    - 3306:3306
    volumes:
    - mysql_data:/var/lib/mysql
    - ./docker/mysql-init:/docker-entrypoint-initdb.d
  nginx:
    depends_on:
    - backend
    image: nginx:alpine
    ports:
    - 443:443
    volumes:
    - ./docker/nginx.conf:/etc/nginx/nginx.conf
    - ./docker/ssl:/etc/nginx/ssl
  redis:
    image: redis:7-alpine
    ports:
    - 6379:6379
    volumes:
    - redis_data:/data
version: '3.8'
volumes:
  mysql_data: {}
  redis_data: {}
