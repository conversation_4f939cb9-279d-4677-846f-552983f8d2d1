# 招聘信息系统完整API接口文档

## 目录

1. [通用说明](#通用说明)
2. [用户端API](#用户端api)
3. [管理端API](#管理端api)
4. [AI功能API](#ai功能api)
5. [微信小程序API](#微信小程序api)
6. [系统监控API](#系统监控api)

## 通用说明

### 基础URL

```
http://localhost:8000/api/v1
```

### 请求格式

所有请求均使用JSON格式，Content-Type为application/json。

### 响应格式

所有响应均使用统一的JSON格式：

```json
{
  "code": 200,           // 状态码，200表示成功，其他表示失败
  "message": "success",  // 消息，成功或错误信息
  "data": {}             // 数据，具体接口返回的数据
}
```

### 认证方式

除了登录接口外，所有接口均需要在请求头中携带Token：

```
Authorization: Bearer {token}
```

### 错误码

| 错误码 | 说明 |
| ----- | ---- |
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未认证或认证失败 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 422 | 数据验证失败 |
| 500 | 服务器内部错误 |

## 用户端API

### 认证相关

#### 用户注册
- **URL**: `/auth/register`
- **方法**: `POST`
- **描述**: 用户注册
- **请求参数**:
```json
{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### 用户登录
- **URL**: `/auth/login`
- **方法**: `POST`
- **描述**: 用户登录
- **请求参数**:
```json
{
  "username": "testuser",
  "password": "password123"
}
```

#### 微信小程序登录
- **URL**: `/wechat/login`
- **方法**: `POST`
- **描述**: 微信小程序登录
- **请求参数**:
```json
{
  "code": "wx_login_code",
  "user_info": {
    "nickname": "用户昵称",
    "avatar_url": "头像URL"
  }
}
```

### 岗位相关

#### 搜索岗位
- **URL**: `/jobs/`
- **方法**: `GET`
- **描述**: 搜索岗位列表
- **请求参数**:
  - `keywords`: 关键词
  - `location`: 工作地点
  - `education`: 学历要求
  - `experience`: 经验要求
  - `salary_min`: 最低薪资
  - `salary_max`: 最高薪资
  - `page`: 页码，默认1
  - `page_size`: 每页数量，默认10

#### 获取岗位详情
- **URL**: `/jobs/{job_id}`
- **方法**: `GET`
- **描述**: 获取岗位详细信息
- **请求参数**:
  - `job_id`: 岗位ID（路径参数）

#### 自然语言搜索
- **URL**: `/jobs/nl-search`
- **方法**: `POST`
- **描述**: 使用自然语言搜索岗位
- **请求参数**:
```json
{
  "query": "我想找北京的软件工程师工作，薪资15k以上"
}
```

### 用户管理

#### 获取当前用户信息
- **URL**: `/users/me`
- **方法**: `GET`
- **描述**: 获取当前登录用户信息

#### 更新用户资料
- **URL**: `/users/me/profile`
- **方法**: `PUT`
- **描述**: 更新用户个人资料
- **请求参数**:
```json
{
  "real_name": "张三",
  "phone": "13812345678",
  "education": "本科",
  "major": "计算机科学与技术",
  "graduation_year": 2023,
  "work_experience": "1-3年"
}
```

#### 收藏岗位
- **URL**: `/users/me/favorites/{job_id}`
- **方法**: `POST`
- **描述**: 收藏指定岗位

#### 取消收藏
- **URL**: `/users/me/favorites/{job_id}`
- **方法**: `DELETE`
- **描述**: 取消收藏指定岗位

#### 获取收藏列表
- **URL**: `/users/me/favorites`
- **方法**: `GET`
- **描述**: 获取用户收藏的岗位列表

#### 获取浏览历史
- **URL**: `/users/me/views`
- **方法**: `GET`
- **描述**: 获取用户浏览历史

### 社区功能

#### 获取帖子列表
- **URL**: `/community/posts`
- **方法**: `GET`
- **描述**: 获取社区帖子列表
- **请求参数**:
  - `keywords`: 关键词搜索
  - `category`: 分类筛选
  - `sort`: 排序方式（latest/hot/top）
  - `page`: 页码
  - `page_size`: 每页数量

#### 获取帖子详情
- **URL**: `/community/posts/{post_id}`
- **方法**: `GET`
- **描述**: 获取帖子详细信息

#### 创建帖子
- **URL**: `/community/posts`
- **方法**: `POST`
- **描述**: 创建新帖子
- **请求参数**:
```json
{
  "title": "帖子标题",
  "content": "帖子内容",
  "category": "求职经验",
  "tags": ["面试", "经验分享"]
}
```

#### 获取评论列表
- **URL**: `/community/posts/{post_id}/comments`
- **方法**: `GET`
- **描述**: 获取帖子评论列表

#### 创建评论
- **URL**: `/community/posts/{post_id}/comments`
- **方法**: `POST`
- **描述**: 创建评论
- **请求参数**:
```json
{
  "content": "评论内容",
  "parent_id": null
}
```

## AI功能API

### 专业匹配分析
- **URL**: `/ai/major-match`
- **方法**: `POST`
- **描述**: 分析用户专业与岗位要求的匹配度
- **请求参数**:
```json
{
  "job_major": "计算机相关专业",
  "user_major": "计算机科学与技术"
}
```

### 技能匹配分析
- **URL**: `/ai/skill-match`
- **方法**: `POST`
- **描述**: 分析用户技能与岗位要求的匹配度
- **请求参数**:
```json
{
  "job_skills": ["Java", "Spring", "MySQL"],
  "user_skills": ["Java", "Python", "MySQL", "Redis"]
}
```

### 岗位推荐
- **URL**: `/ai/job-recommend`
- **方法**: `POST`
- **描述**: 基于用户画像推荐岗位
- **请求参数**:
```json
{
  "user_profile": {
    "education": "本科",
    "major": "计算机科学与技术",
    "skills": ["Java", "Python"],
    "experience": "1-3年",
    "location_preference": "北京"
  },
  "limit": 10
}
```

### 简历解析
- **URL**: `/ai/resume-parse`
- **方法**: `POST`
- **描述**: 解析简历内容提取关键信息
- **请求参数**:
```json
{
  "resume_text": "简历文本内容...",
  "format": "text"
}
```

### 面试准备
- **URL**: `/ai/interview-prep`
- **方法**: `POST`
- **描述**: 生成面试问题和准备建议
- **请求参数**:
```json
{
  "job_description": "岗位描述内容",
  "user_background": "用户背景信息"
}
```

### 政策解读
- **URL**: `/ai/policy-interpret`
- **方法**: `POST`
- **描述**: 解读就业政策内容
- **请求参数**:
```json
{
  "policy_text": "政策文本内容",
  "user_situation": "用户情况描述"
}
```

## 微信小程序API

### 认证相关

#### 获取访问令牌
- **URL**: `/auth/token`
- **方法**: `POST`
- **描述**: 通过微信登录获取访问令牌
- **请求参数**:
```json
{
  "code": "string",  // 微信登录返回的code
  "user_info": {     // 微信用户信息（可选）
    "nickName": "string",
    "avatarUrl": "string",
    "gender": "number"
  }
}
```

#### 刷新访问令牌
- **URL**: `/auth/refresh`
- **方法**: `POST`
- **描述**: 使用刷新令牌获取新的访问令牌
- **请求参数**:
```json
{
  "refresh_token": "string"  // 刷新令牌
}
```

### 岗位匹配分析

#### 分析岗位匹配度
- **URL**: `/jobs/{job_id}/match-analysis`
- **方法**: `POST`
- **描述**: 分析用户与岗位的匹配度
- **请求参数**:
```json
{
  "education": {
    "degree": "string",
    "school": "string",
    "major": "string",
    "graduation_year": "number"
  },
  "user_id": "string"  // 可选，如果不提供则使用当前登录用户
}
```

### 申请时间管理

#### 获取时间节点
- **URL**: `/jobs/{job_id}/timeline`
- **方法**: `GET`
- **描述**: 获取岗位的关键时间节点

#### 设置提醒
- **URL**: `/reminders`
- **方法**: `POST`
- **描述**: 设置时间节点提醒
- **请求参数**:
```json
{
  "job_id": "string",
  "event_type": "string",  // application_start, application_deadline, exam, interview, result
  "remind_before": 86400,  // 提前多少秒提醒，默认86400（1天）
  "notification_type": "string"  // wechat, in_app
}
```

#### 取消提醒
- **URL**: `/reminders/{reminder_id}`
- **方法**: `DELETE`
- **描述**: 取消时间节点提醒

### Token验证
- **URL**: `/wechat/check-token`
- **方法**: `GET`
- **描述**: 验证Token有效性

## 系统监控API

### 健康检查
- **URL**: `/health`
- **方法**: `GET`
- **描述**: 系统健康状态检查

### 系统信息
- **URL**: `/system/info`
- **方法**: `GET`
- **描述**: 获取系统基本信息

### 性能指标
- **URL**: `/system/metrics`
- **方法**: `GET`
- **描述**: 获取系统性能指标

---

*注: 本文档将根据项目进展持续更新*
