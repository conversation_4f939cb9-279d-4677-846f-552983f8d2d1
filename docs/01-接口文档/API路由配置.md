# API路径映射指南

**优智帮工作室 (Youzhbang Studio)**  
**文档版本**: 1.0.0  
**创建时间**: 2025-05-30 16:35  
**状态**: 🟢 已完成

## 概述

本文档详细说明了前后端API路径映射规则，确保前端请求能正确访问后端API端点，避免404错误和路径配置问题。

## 前后端API配置

### 前端配置 (apps/admin-frontend/src/services/api.ts)

```typescript
// 基础API配置
const api = axios.create({
  baseURL: 'http://localhost:8000/01-api/v1',  // 后端API基础地址
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})
```

### 后端API结构

**基础路径**: `http://localhost:8000/01-api/v1`

## API路径映射表

### 认证相关API

| 功能 | 前端调用 | 实际请求路径 | HTTP方法 | 状态 |
|------|----------|--------------|----------|------|
| 管理员登录 | `/auth/login` | `http://localhost:8000/01-api/v1/auth/login` | POST | ✅ 正常 |
| 刷新令牌 | `/auth/refresh-token` | `http://localhost:8000/01-api/v1/auth/refresh-token` | POST | ✅ 正常 |
| 获取用户信息 | `/users/me` | `http://localhost:8000/01-api/v1/users/me` | GET | ✅ 正常 |

### 用户管理API

| 功能 | 前端调用 | 实际请求路径 | HTTP方法 | 状态 |
|------|----------|--------------|----------|------|
| 获取用户列表 | `/admin/users` | `http://localhost:8000/01-api/v1/admin/users` | GET | ✅ 正常 |
| 创建用户 | `/admin/users` | `http://localhost:8000/01-api/v1/admin/users` | POST | ✅ 正常 |
| 更新用户 | `/admin/users/{id}` | `http://localhost:8000/01-api/v1/admin/users/{id}` | PUT | ✅ 正常 |
| 删除用户 | `/admin/users/{id}` | `http://localhost:8000/01-api/v1/admin/users/{id}` | DELETE | ✅ 正常 |

### 职位管理API

| 功能 | 前端调用 | 实际请求路径 | HTTP方法 | 状态 |
|------|----------|--------------|----------|------|
| 获取职位列表 | `/admin/jobs` | `http://localhost:8000/01-api/v1/admin/jobs` | GET | ✅ 正常 |
| 创建职位 | `/admin/jobs` | `http://localhost:8000/01-api/v1/admin/jobs` | POST | ✅ 正常 |
| 更新职位 | `/admin/jobs/{id}` | `http://localhost:8000/01-api/v1/admin/jobs/{id}` | PUT | ✅ 正常 |
| 删除职位 | `/admin/jobs/{id}` | `http://localhost:8000/01-api/v1/admin/jobs/{id}` | DELETE | ✅ 正常 |

### 系统管理API

| 功能 | 前端调用 | 实际请求路径 | HTTP方法 | 状态 |
|------|----------|--------------|----------|------|
| 系统配置 | `/admin/system/config` | `http://localhost:8000/01-api/v1/admin/system/config` | GET | ✅ 正常 |
| 更新配置 | `/admin/system/config` | `http://localhost:8000/01-api/v1/admin/system/config` | PUT | ✅ 正常 |
| 系统统计 | `/admin/system/stats` | `http://localhost:8000/01-api/v1/admin/system/stats` | GET | ✅ 正常 |

## 修复历史

### 问题1: 用户信息API路径错误
**发现时间**: 2025-05-30 16:15  
**问题描述**: 前端请求 `/admin/me` 返回404错误  
**错误路径**: `http://localhost:3001/01-api/admin/me`  
**正确路径**: `http://localhost:8000/01-api/v1/users/me`  

**修复内容**:
1. 修改 `apps/admin-frontend/src/services/api.ts` 中的 baseURL
2. 修改 `apps/admin-frontend/src/services/auth.ts` 中的用户信息API路径

```typescript
// 修复前
baseURL: '/api'
return api.get('/admin/me')

// 修复后
baseURL: 'http://localhost:8000/api/v1'
return api.get('/users/me')
```

### 问题2: 刷新令牌API路径错误
**修复内容**:
```typescript
// 修复前
return api.post('/admin/auth/refresh', {...})

// 修复后
return api.post('/auth/refresh-token', {...})
```

## 配置最佳实践

### 1. 环境变量配置
建议使用环境变量管理API地址：

```typescript
// .env.development
VITE_API_BASE_URL=http://localhost:8000/api/v1

// .env.production
VITE_API_BASE_URL=https://api.yourdomain.com/api/v1

// api.ts
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  // ...
})
```

### 2. API路径规范
- **认证相关**: `/auth/*`
- **用户信息**: `/users/*`  
- **管理员功能**: `/admin/*`
- **公共接口**: `/public/*`

### 3. 错误处理
```typescript
// 统一错误处理
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 404) {
      console.error('API路径不存在:', error.config.url)
    }
    return Promise.reject(error)
  }
)
```

## 验证方法

### 1. 手动测试
```bash
# 测试登录API
curl -X POST "http://localhost:8000/01-api/v1/auth/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=admin&password=admin123"

# 测试用户信息API
curl "http://localhost:8000/01-api/v1/users/me"
```

### 2. 前端测试
1. 打开浏览器开发者工具
2. 访问 http://localhost:3001/login
3. 输入用户名: admin, 密码: admin123
4. 检查网络面板中的API请求路径
5. 确认请求返回200状态码

### 3. 自动化测试
运行测试脚本验证API连通性：
```bash
python3 tests/frontend_login_test.py
```

## 故障排除

### 常见问题

1. **404 Not Found**
   - 检查API路径是否正确
   - 确认后端服务是否启动
   - 验证路由配置是否正确

2. **CORS错误**
   - 检查后端CORS配置
   - 确认前端请求域名是否在允许列表中

3. **超时错误**
   - 检查网络连接
   - 增加请求超时时间
   - 确认后端服务响应正常

### 调试技巧

1. **使用浏览器开发者工具**
   - Network面板查看实际请求
   - Console面板查看错误信息

2. **使用curl命令测试**
   - 直接测试后端API
   - 排除前端配置问题

3. **检查后端日志**
   - 查看uvicorn访问日志
   - 确认请求是否到达后端

## 🟢 问题解决记录 (2024-12-19 15:05)

### ✅ 已解决的403错误问题

**问题描述**: 前端调用 `GET http://localhost:8000/01-api/v1/admin/statistics` 返回403 (Forbidden)错误

**根本原因**:
1. 新旧权限验证系统冲突
2. `admin_analytics.py` 使用了旧的 `get_current_admin_user` 依赖项
3. 前端登录使用新的管理员系统，但统计API使用旧的权限验证

**解决方案**:
1. **统一权限验证系统**: 将 `admin_analytics.py` 中的权限验证改为新系统
   ```python
   # 修复前
   from apps.backend.api.dependencies import get_current_admin_user

   # 修复后
   from apps.backend.api.dependencies.admin_auth import get_current_active_admin
   ```

2. **API路径确认**: `/admin/statistics` 正常工作，返回完整统计数据
3. **前端API调用**: 建议继续使用 `/admin/statistics` 获取统计数据

**测试结果**:
- ✅ 管理员登录: 正常 (admin/admin123)
- ✅ 管理员统计API: 正常 (200状态码)
- ✅ 管理员信息API: 正常
- ✅ 权限验证: 正常
- ❌ 用户统计API: 仍需修复 (可选，前端可使用管理员统计API)

**API响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "overview": {
      "total_users": 12580,
      "total_jobs": 3420,
      "total_applications": 8960,
      "total_companies": 1280
    },
    "today": {
      "new_users": 156,
      "new_jobs": 23,
      "new_applications": 89,
      "page_views": 15680
    },
    "trends": {
      "user_growth": 12.5,
      "job_growth": 8.9,
      "application_growth": 15.2,
      "engagement_rate": 68.5
    },
    "real_time": {
      "online_users": 1256,
      "active_sessions": 890,
      "current_searches": 45,
      "system_load": 65.2
    }
  }
}
```

## 更新记录

| 日期 | 版本 | 更新内容 | 更新人 |
|------|------|----------|--------|
| 2025-05-30 | 1.0.0 | 创建API路径映射指南 | Augment Agent |
| 2024-12-19 | 1.1.0 | 🟢 修复403错误问题，统一权限验证系统 | Augment Agent |

---

**注意**: 本文档会随着API变更持续更新，请及时同步最新版本。
