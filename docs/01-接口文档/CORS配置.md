# CORS跨域配置指南

**优智帮工作室 (Youzhbang Studio)**  
**文档版本**: 1.0.0  
**创建时间**: 2025-05-30 16:55  
**状态**: 🟢 已完成

## 概述

本文档详细说明了前后端CORS跨域配置的完整解决方案，包括问题诊断、配置修复、测试验证和最佳实践。

## 问题背景

### 原始问题
- **CORS错误**: `Access to XMLHttpRequest at 'http://localhost:8000/api/v1/auth/login' from origin 'http://localhost:3001' has been blocked by CORS policy`
- **网络状态**: `POST http://localhost:8000/api/v1/auth/login net::ERR_FAILED 200 (OK)`
- **根本原因**: 测试API缺少CORS中间件配置

### 问题分析
1. **后端API有两套**：
   - 原始API (`apps/backend/01-api/main.py`) - 有CORS配置
   - 测试API (`apps/backend/test_main.py`) - 缺少CORS配置
2. **启动脚本优先使用测试API**
3. **前端请求被CORS策略阻止**

## 解决方案

### 方案1: 直接CORS配置（推荐）

#### 后端配置

**文件**: `apps/backend/test_main.py`

```python
from fastapi import FastAPI, Form
from fastapi.middleware.cors import CORSMiddleware

# 创建FastAPI应用
app = FastAPI(
    title="事业编制招聘信息查询系统 - 测试API",
    description="优智帮工作室 (Youzhbang Studio) - 启动脚本验证用测试API",
    version="1.0.0"
)

# 添加CORS中间件 - 解决跨域问题
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3001",  # 前端开发服务器
        "http://127.0.0.1:3001",  # 前端开发服务器（备用）
        "http://localhost:3000",  # 备用前端端口
        "http://127.0.0.1:3000",  # 备用前端端口（备用）
        "http://localhost:8080",  # 其他可能的前端端口
        "http://127.0.0.1:8080",  # 其他可能的前端端口（备用）
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    allow_headers=["*"],
)

# 添加OPTIONS预检请求处理
@app.options("/01-api/v1/auth/login")
async def login_options():
    """处理登录API的OPTIONS预检请求"""
    return {"message": "CORS preflight for login"}

@app.options("/01-api/v1/users/me")
async def user_info_options():
    """处理用户信息API的OPTIONS预检请求"""
    return {"message": "CORS preflight for user info"}
```

#### 前端配置

**文件**: `apps/admin-frontend/src/services/api.ts`

```typescript
// 创建axios实例
const api = axios.create({
  baseURL: 'http://localhost:8000/01-api/v1', // 直接请求后端
  timeout: 10000,
  withCredentials: true, // 支持跨域携带凭据
  headers: {
    'Content-Type': 'application/json',
  },
})
```

### 方案2: Vite代理配置（备选）

#### Vite配置

**文件**: `apps/admin-frontend/vite.config.ts`

```typescript
export default defineConfig({
  server: {
    port: 3001,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false,
      },
    },
  },
})
```

#### 前端配置

**文件**: `apps/admin-frontend/src/services/api.ts`

```typescript
// 创建axios实例
const api = axios.create({
  baseURL: '/01-api/v1', // 使用Vite代理
  timeout: 10000,
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
  },
})
```

## 配置验证

### 1. CORS头验证

**OPTIONS预检请求**:
```bash
curl -X OPTIONS "http://localhost:8000/01-api/v1/auth/login" \
  -H "Origin: http://localhost:3001" \
  -H "Access-Control-Request-Method: POST" \
  -H "Access-Control-Request-Headers: Content-Type" -v
```

**期望响应头**:
```
access-control-allow-origin: http://localhost:3001
access-control-allow-methods: GET, POST, PUT, DELETE, OPTIONS, PATCH
access-control-allow-credentials: true
access-control-allow-headers: Content-Type
```

### 2. 实际请求验证

**POST登录请求**:
```bash
curl -X POST "http://localhost:8000/01-api/v1/auth/login" \
  -H "Origin: http://localhost:3001" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=admin&password=admin123" -v
```

**期望响应**:
- 状态码: 200
- CORS头: `access-control-allow-origin: http://localhost:3001`
- 响应体: 包含 `access_token`

### 3. 自动化测试

运行CORS测试脚本：
```bash
python3 tests/auth/cors_test.py
```

## 最佳实践

### 1. 安全配置

```python
# 生产环境CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "https://yourdomain.com",  # 只允许生产域名
        "https://www.yourdomain.com",
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],  # 限制方法
    allow_headers=["Content-Type", "Authorization"],  # 限制头部
)
```

### 2. 环境区分

```python
import os

# 根据环境配置CORS
if os.getenv("ENVIRONMENT") == "development":
    cors_origins = [
        "http://localhost:3001",
        "http://localhost:3000",
    ]
else:
    cors_origins = [
        "https://yourdomain.com",
        "https://www.yourdomain.com",
    ]

app.add_middleware(
    CORSMiddleware,
    allow_origins=cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

### 3. 前端配置

```typescript
// 环境配置
const API_BASE_URL = import.meta.env.PROD 
  ? 'https://api.yourdomain.com/01-api/v1'
  : 'http://localhost:8000/01-api/v1'

const api = axios.create({
  baseURL: API_BASE_URL,
  withCredentials: true,
  timeout: 10000,
})
```

## 故障排除

### 常见问题

1. **CORS错误仍然存在**
   - 检查后端CORS中间件是否正确配置
   - 确认前端Origin是否在允许列表中
   - 验证浏览器缓存是否清除

2. **OPTIONS请求失败**
   - 检查是否添加了OPTIONS路由处理
   - 确认CORS中间件在路由之前添加

3. **凭据无法携带**
   - 后端设置 `allow_credentials=True`
   - 前端设置 `withCredentials: true`

4. **代理不工作**
   - 检查Vite配置是否正确
   - 确认前端服务器是否重启
   - 验证代理目标地址是否正确

### 调试技巧

1. **浏览器开发者工具**
   - Network面板查看请求头和响应头
   - Console面板查看CORS错误详情

2. **curl命令测试**
   - 直接测试后端CORS配置
   - 验证OPTIONS预检请求

3. **日志分析**
   - 检查后端访问日志
   - 确认请求是否到达后端

## 测试清单

- [ ] OPTIONS预检请求返回正确CORS头
- [ ] POST登录请求成功且有CORS头
- [ ] GET用户信息请求成功
- [ ] 未授权Origin被正确拒绝
- [ ] 前端登录功能完全正常
- [ ] 浏览器控制台无CORS错误

## 更新记录

| 日期 | 版本 | 更新内容 | 更新人 |
|------|------|----------|--------|
| 2025-05-30 | 1.0.0 | 创建CORS配置指南 | Augment Agent |

---

**注意**: CORS配置涉及安全性，生产环境请严格限制允许的域名和方法。
