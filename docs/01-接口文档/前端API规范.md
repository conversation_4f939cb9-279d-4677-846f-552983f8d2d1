# 前端API接口清单和交互规范

## 文档信息

**文档版本**: v1.0  
**创建日期**: 2025年01月27日  
**作者**: 优智帮工作室  
**项目**: 事业编制招聘信息查询系统  
**文档状态**: 🟢 已完成  

## 📋 API接口清单概述

### 1.1 接口分类

基于用户角色分离的设计，API接口分为以下几类：

- **认证接口**: 用户登录、注册、权限验证
- **管理员接口**: 管理后台专用接口（/admin前缀）
- **用户接口**: 普通用户界面接口（/user前缀）
- **公共接口**: 无需认证的公开接口

### 1.2 接口规范

- **基础URL**: `http://localhost:8000/api/v1`
- **认证方式**: JWT Bearer Token
- **数据格式**: JSON
- **字符编码**: UTF-8

## 🔐 认证接口

### 1. 用户登录

**接口地址**: `POST /api/v1/auth/login`  
**用途**: 用户登录认证  
**权限**: 公开接口  

**请求参数**:
```typescript
interface LoginRequest {
  username: string;    // 用户名
  password: string;    // 密码
  userType?: 'admin' | 'user';  // 用户类型（可选）
}
```

**响应数据**:
```typescript
interface LoginResponse {
  code: number;
  message: string;
  data: {
    token: string;           // JWT访问令牌
    refreshToken: string;    // 刷新令牌
    user: {
      id: string;
      username: string;
      role: 'admin' | 'user';
      permissions: string[];
      profile: UserProfile;
    };
    expiresIn: number;       // 令牌过期时间（秒）
  };
}
```

### 2. 用户注册

**接口地址**: `POST /auth/register`  
**用途**: 普通用户注册  
**权限**: 公开接口  

**请求参数**:
```typescript
interface RegisterRequest {
  username: string;
  password: string;
  email: string;
  phone?: string;
  realName?: string;
}
```

### 3. 令牌刷新

**接口地址**: `POST /auth/refresh`  
**用途**: 刷新访问令牌  
**权限**: 需要刷新令牌  

**请求参数**:
```typescript
interface RefreshRequest {
  refreshToken: string;
}
```

### 4. 用户登出

**接口地址**: `POST /auth/logout`  
**用途**: 用户登出  
**权限**: 需要认证  

## 👨‍💼 管理员接口 (/admin)

### 1. 仪表盘数据

#### 1.1 系统概览统计

**接口地址**: `GET /admin/dashboard/overview`  
**用途**: 获取系统概览数据  
**权限**: 管理员  

**响应数据**:
```typescript
interface DashboardOverview {
  totalUsers: number;          // 总用户数
  newUsersToday: number;       // 今日新增用户
  activeUsers: number;         // 活跃用户数
  totalJobs: number;           // 总岗位数
  newJobsToday: number;        // 今日新增岗位
  totalApplications: number;   // 总申请数
  systemStatus: 'normal' | 'warning' | 'error';
}
```

#### 1.2 用户增长趋势

**接口地址**: `GET /admin/dashboard/user-growth`  
**用途**: 获取用户增长趋势数据  
**权限**: 管理员  

**查询参数**:
```typescript
interface UserGrowthQuery {
  period: '7d' | '30d' | '90d' | '1y';  // 时间周期
  granularity: 'day' | 'week' | 'month'; // 数据粒度
}
```

### 2. 用户管理

#### 2.1 用户列表

**接口地址**: `GET /admin/users`  
**用途**: 获取用户列表  
**权限**: 管理员  

**查询参数**:
```typescript
interface UserListQuery {
  page?: number;           // 页码，默认1
  pageSize?: number;       // 每页数量，默认20
  keyword?: string;        // 搜索关键词
  status?: 'active' | 'inactive' | 'banned';  // 用户状态
  role?: 'admin' | 'user'; // 用户角色
  sortBy?: 'createdAt' | 'lastLoginAt' | 'username';
  sortOrder?: 'asc' | 'desc';
}
```

**响应数据**:
```typescript
interface UserListResponse {
  code: number;
  message: string;
  data: {
    users: User[];
    pagination: {
      page: number;
      pageSize: number;
      total: number;
      totalPages: number;
    };
  };
}
```

#### 2.2 用户详情

**接口地址**: `GET /admin/users/{userId}`  
**用途**: 获取用户详细信息  
**权限**: 管理员  

#### 2.3 更新用户状态

**接口地址**: `PUT /admin/users/{userId}/status`  
**用途**: 更新用户状态  
**权限**: 管理员  

**请求参数**:
```typescript
interface UpdateUserStatusRequest {
  status: 'active' | 'inactive' | 'banned';
  reason?: string;  // 状态变更原因
}
```

### 3. 岗位管理

#### 3.1 岗位列表

**接口地址**: `GET /admin/jobs`  
**用途**: 获取岗位列表  
**权限**: 管理员  

**查询参数**:
```typescript
interface JobListQuery {
  page?: number;
  pageSize?: number;
  keyword?: string;
  status?: 'draft' | 'published' | 'closed';
  category?: string;
  location?: string;
  sortBy?: 'createdAt' | 'updatedAt' | 'deadline';
  sortOrder?: 'asc' | 'desc';
}
```

#### 3.2 创建岗位

**接口地址**: `POST /admin/jobs`  
**用途**: 创建新岗位  
**权限**: 管理员  

**请求参数**:
```typescript
interface CreateJobRequest {
  title: string;
  description: string;
  requirements: string[];
  location: string;
  category: string;
  salary?: {
    min: number;
    max: number;
    currency: string;
  };
  deadline: string;  // ISO日期字符串
  contactInfo: {
    email: string;
    phone?: string;
  };
}
```

#### 3.3 更新岗位

**接口地址**: `PUT /admin/jobs/{jobId}`  
**用途**: 更新岗位信息  
**权限**: 管理员  

#### 3.4 删除岗位

**接口地址**: `DELETE /admin/jobs/{jobId}`  
**用途**: 删除岗位  
**权限**: 管理员  

### 4. 数据分析

#### 4.1 用户行为分析

**接口地址**: `GET /admin/analytics/user-behavior`  
**用途**: 获取用户行为分析数据  
**权限**: 管理员  

**查询参数**:
```typescript
interface UserBehaviorQuery {
  startDate: string;  // 开始日期
  endDate: string;    // 结束日期
  metrics: ('pageViews' | 'searches' | 'applications')[];
}
```

#### 4.2 岗位统计分析

**接口地址**: `GET /admin/analytics/job-stats`  
**用途**: 获取岗位统计数据  
**权限**: 管理员  

### 5. 系统设置

#### 5.1 获取系统配置

**接口地址**: `GET /admin/settings`  
**用途**: 获取系统配置  
**权限**: 管理员  

#### 5.2 更新系统配置

**接口地址**: `PUT /admin/settings`  
**用途**: 更新系统配置  
**权限**: 管理员  

**请求参数**:
```typescript
interface SystemSettings {
  siteName: string;
  siteDescription: string;
  maintenanceMode: boolean;
  registrationEnabled: boolean;
  emailNotifications: boolean;
  maxFileUploadSize: number;
}
```

## 👤 用户接口 (/user)

### 1. 个人信息

#### 1.1 获取个人信息

**接口地址**: `GET /user/profile`  
**用途**: 获取当前用户个人信息  
**权限**: 需要认证  

**响应数据**:
```typescript
interface UserProfile {
  id: string;
  username: string;
  email: string;
  phone?: string;
  realName?: string;
  avatar?: string;
  registeredAt: string;
  lastLoginAt: string;
  trialExpiresAt: string;  // 试用期到期时间
  status: 'active' | 'inactive' | 'expired';
}
```

#### 1.2 更新个人信息

**接口地址**: `PUT /user/profile`  
**用途**: 更新个人信息  
**权限**: 需要认证  

### 2. 岗位搜索

#### 2.1 搜索岗位

**接口地址**: `GET /user/jobs/search`  
**用途**: 搜索岗位信息  
**权限**: 需要认证  

**查询参数**:
```typescript
interface JobSearchQuery {
  keyword?: string;        // 搜索关键词
  location?: string;       // 工作地点
  category?: string;       // 岗位类别
  salaryMin?: number;      // 最低薪资
  salaryMax?: number;      // 最高薪资
  page?: number;
  pageSize?: number;
  sortBy?: 'relevance' | 'salary' | 'deadline' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
}
```

#### 2.2 获取岗位详情

**接口地址**: `GET /user/jobs/{jobId}`  
**用途**: 获取岗位详细信息  
**权限**: 需要认证  

### 3. 专业匹配

#### 3.1 专业匹配分析

**接口地址**: `POST /user/match/analyze`  
**用途**: 基于专业信息进行岗位匹配  
**权限**: 需要认证  

**请求参数**:
```typescript
interface MatchAnalysisRequest {
  major: string;           // 专业名称
  degree: string;          // 学历
  experience?: number;     // 工作经验年数
  skills?: string[];       // 技能列表
  preferences?: {
    location?: string[];
    salary?: {
      min: number;
      max: number;
    };
    workType?: 'fulltime' | 'parttime' | 'contract';
  };
}
```

**响应数据**:
```typescript
interface MatchAnalysisResponse {
  code: number;
  message: string;
  data: {
    matchedJobs: {
      job: Job;
      matchScore: number;    // 匹配度分数 (0-100)
      matchReasons: string[]; // 匹配原因
    }[];
    recommendations: {
      skillGaps: string[];   // 技能差距
      suggestions: string[]; // 改进建议
    };
  };
}
```

### 4. 收藏和历史

#### 4.1 收藏岗位

**接口地址**: `POST /user/favorites`  
**用途**: 收藏岗位  
**权限**: 需要认证  

**请求参数**:
```typescript
interface AddFavoriteRequest {
  jobId: string;
}
```

#### 4.2 获取收藏列表

**接口地址**: `GET /user/favorites`  
**用途**: 获取收藏的岗位列表  
**权限**: 需要认证  

#### 4.3 取消收藏

**接口地址**: `DELETE /user/favorites/{jobId}`  
**用途**: 取消收藏岗位  
**权限**: 需要认证  

#### 4.4 搜索历史

**接口地址**: `GET /user/search-history`  
**用途**: 获取搜索历史记录  
**权限**: 需要认证  

## 🌐 公共接口

### 1. 系统信息

#### 1.1 系统状态

**接口地址**: `GET /public/health`  
**用途**: 获取系统健康状态  
**权限**: 公开接口  

#### 1.2 系统配置

**接口地址**: `GET /public/config`  
**用途**: 获取公开的系统配置  
**权限**: 公开接口  

### 2. 数据字典

#### 2.1 岗位类别

**接口地址**: `GET /public/categories`  
**用途**: 获取岗位类别列表  
**权限**: 公开接口  

#### 2.2 地区信息

**接口地址**: `GET /public/regions`  
**用途**: 获取地区信息  
**权限**: 公开接口  

## 🔄 前后端交互规范

### 1. 请求规范

#### 1.1 请求头设置

**必需请求头**:
```typescript
const headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
  'Authorization': `Bearer ${token}`,  // 需要认证的接口
  'X-Client-Version': '1.0.0',        // 客户端版本
  'X-Request-ID': uuid(),              // 请求唯一标识
};
```

**可选请求头**:
```typescript
const optionalHeaders = {
  'X-User-Agent': 'RecruitmentSystem/1.0.0',
  'X-Platform': 'web',
  'X-Language': 'zh-CN',
};
```

#### 1.2 请求拦截器

```typescript
// Axios请求拦截器示例
axios.interceptors.request.use(
  (config) => {
    // 添加认证Token
    const token = getAuthToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // 添加请求ID
    config.headers['X-Request-ID'] = generateRequestId();

    // 添加时间戳
    config.metadata = { startTime: Date.now() };

    return config;
  },
  (error) => Promise.reject(error)
);
```

### 2. 响应规范

#### 2.1 统一响应格式

**成功响应**:
```typescript
interface SuccessResponse<T> {
  code: 200;
  message: string;
  data: T;
  timestamp: number;
  requestId: string;
  pagination?: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}
```

**错误响应**:
```typescript
interface ErrorResponse {
  code: number;          // 错误码
  message: string;       // 错误消息
  error?: string;        // 错误类型
  details?: any;         // 错误详情
  timestamp: number;
  requestId: string;
  path: string;          // 请求路径
}
```

#### 2.2 响应拦截器

```typescript
// Axios响应拦截器示例
axios.interceptors.response.use(
  (response) => {
    // 记录响应时间
    const duration = Date.now() - response.config.metadata.startTime;
    console.log(`API ${response.config.url} took ${duration}ms`);

    // 处理业务错误
    if (response.data.code !== 200) {
      throw new ApiError(response.data);
    }

    return response.data;
  },
  (error) => {
    // 处理网络错误
    if (error.response?.status === 401) {
      // Token过期，跳转登录
      redirectToLogin();
    }

    return Promise.reject(error);
  }
);
```

### 3. 错误处理规范

#### 3.1 HTTP状态码

| 状态码 | 含义 | 处理方式 |
|--------|------|----------|
| 200 | 成功 | 正常处理响应数据 |
| 400 | 请求参数错误 | 显示错误提示，表单验证 |
| 401 | 未认证 | 跳转登录页面 |
| 403 | 权限不足 | 显示权限错误页面 |
| 404 | 资源不存在 | 显示404页面 |
| 429 | 请求过于频繁 | 显示限流提示 |
| 500 | 服务器错误 | 显示系统错误页面 |

#### 3.2 业务错误码

```typescript
enum BusinessErrorCode {
  // 认证相关 (1000-1099)
  INVALID_CREDENTIALS = 1001,
  TOKEN_EXPIRED = 1002,
  ACCOUNT_LOCKED = 1003,

  // 权限相关 (1100-1199)
  INSUFFICIENT_PERMISSIONS = 1101,
  ROLE_NOT_FOUND = 1102,

  // 用户相关 (1200-1299)
  USER_NOT_FOUND = 1201,
  USERNAME_EXISTS = 1202,
  EMAIL_EXISTS = 1203,

  // 岗位相关 (1300-1399)
  JOB_NOT_FOUND = 1301,
  JOB_EXPIRED = 1302,
  APPLICATION_LIMIT_EXCEEDED = 1303,

  // 系统相关 (9000-9999)
  SYSTEM_MAINTENANCE = 9001,
  RATE_LIMIT_EXCEEDED = 9002,
}
```

### 4. 数据缓存策略

#### 4.1 React Query配置

```typescript
// React Query全局配置
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000,      // 5分钟内数据视为新鲜
      cacheTime: 10 * 60 * 1000,     // 10分钟缓存时间
      retry: 3,                       // 失败重试3次
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
    mutations: {
      retry: 1,                       // 变更操作重试1次
    },
  },
});
```

#### 4.2 缓存策略

**长期缓存** (30分钟):
- 系统配置信息
- 数据字典（地区、类别）
- 用户权限信息

**中期缓存** (5分钟):
- 用户个人信息
- 岗位列表数据
- 统计数据

**短期缓存** (1分钟):
- 搜索结果
- 实时数据

**无缓存**:
- 敏感操作结果
- 一次性数据

### 5. 性能优化

#### 5.1 请求优化

**请求合并**:
```typescript
// 使用React Query的批量请求
const useUserData = (userId: string) => {
  return useQueries([
    {
      queryKey: ['user', userId],
      queryFn: () => fetchUser(userId),
    },
    {
      queryKey: ['userProfile', userId],
      queryFn: () => fetchUserProfile(userId),
    },
  ]);
};
```

**请求去重**:
```typescript
// React Query自动处理相同请求的去重
const { data: users } = useQuery(['users'], fetchUsers);
```

#### 5.2 分页优化

**无限滚动**:
```typescript
const useInfiniteJobs = (searchParams: JobSearchQuery) => {
  return useInfiniteQuery(
    ['jobs', searchParams],
    ({ pageParam = 1 }) => fetchJobs({ ...searchParams, page: pageParam }),
    {
      getNextPageParam: (lastPage) => {
        const { page, totalPages } = lastPage.pagination;
        return page < totalPages ? page + 1 : undefined;
      },
    }
  );
};
```

### 6. 安全规范

#### 6.1 Token管理

**Token存储**:
```typescript
// 使用HttpOnly Cookie存储Token（推荐）
const setAuthToken = (token: string) => {
  // 设置HttpOnly Cookie
  document.cookie = `auth_token=${token}; HttpOnly; Secure; SameSite=Strict`;
};

// 或使用安全的localStorage
const setAuthTokenLocal = (token: string) => {
  localStorage.setItem('auth_token', token);
};
```

**Token刷新**:
```typescript
// 自动Token刷新
const useTokenRefresh = () => {
  const refreshToken = useCallback(async () => {
    try {
      const response = await api.post('/auth/refresh', {
        refreshToken: getRefreshToken(),
      });
      setAuthToken(response.data.token);
      return response.data.token;
    } catch (error) {
      // 刷新失败，跳转登录
      redirectToLogin();
      throw error;
    }
  }, []);

  return { refreshToken };
};
```

#### 6.2 请求安全

**CSRF防护**:
```typescript
// 添加CSRF Token
const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
if (csrfToken) {
  headers['X-CSRF-Token'] = csrfToken;
}
```

**输入验证**:
```typescript
// 前端输入验证
const validateInput = (data: any) => {
  // XSS防护
  const sanitizedData = DOMPurify.sanitize(data);

  // 数据类型验证
  const schema = Joi.object({
    username: Joi.string().alphanum().min(3).max(30).required(),
    email: Joi.string().email().required(),
  });

  return schema.validate(sanitizedData);
};
```

### 7. 监控和日志

#### 7.1 API监控

```typescript
// API性能监控
const apiMonitor = {
  logRequest: (config: AxiosRequestConfig) => {
    console.log(`[API] ${config.method?.toUpperCase()} ${config.url}`, {
      timestamp: new Date().toISOString(),
      requestId: config.headers['X-Request-ID'],
      params: config.params,
    });
  },

  logResponse: (response: AxiosResponse, duration: number) => {
    console.log(`[API] Response ${response.status}`, {
      url: response.config.url,
      duration: `${duration}ms`,
      size: JSON.stringify(response.data).length,
    });
  },

  logError: (error: AxiosError) => {
    console.error(`[API] Error ${error.response?.status}`, {
      url: error.config?.url,
      message: error.message,
      response: error.response?.data,
    });
  },
};
```

#### 7.2 用户行为追踪

```typescript
// 用户行为埋点
const trackUserAction = (action: string, data?: any) => {
  // 发送埋点数据
  analytics.track(action, {
    userId: getCurrentUserId(),
    timestamp: Date.now(),
    page: window.location.pathname,
    ...data,
  });
};

// 使用示例
trackUserAction('job_search', { keyword: 'frontend', location: 'beijing' });
trackUserAction('job_apply', { jobId: '123', jobTitle: 'Frontend Developer' });
```

## 📱 移动端适配

### 1. 响应式API调用

```typescript
// 根据设备类型调整请求参数
const useResponsiveApi = () => {
  const isMobile = useMediaQuery('(max-width: 768px)');

  const getJobList = (params: JobSearchQuery) => {
    return fetchJobs({
      ...params,
      pageSize: isMobile ? 10 : 20,  // 移动端减少每页数量
    });
  };

  return { getJobList };
};
```

### 2. 离线支持

```typescript
// Service Worker缓存策略
const cacheStrategy = {
  // 缓存关键API响应
  cacheApiResponse: (url: string, response: any) => {
    if ('caches' in window) {
      caches.open('api-cache-v1').then(cache => {
        cache.put(url, new Response(JSON.stringify(response)));
      });
    }
  },

  // 离线时从缓存获取数据
  getCachedResponse: async (url: string) => {
    if ('caches' in window) {
      const cache = await caches.open('api-cache-v1');
      const response = await cache.match(url);
      return response ? response.json() : null;
    }
    return null;
  },
};
```

---

**优智帮工作室 (Youzhbang Studio)**
**文档创建时间**: 2025年01月27日
**最后更新时间**: 2025年01月27日
**状态**: 🟢 已完成
**关联文档**: [前端架构设计文档](../04-开发者指南/前端架构.md) | [API接口完整文档](../05-系统架构/API接口完整文档.md)
