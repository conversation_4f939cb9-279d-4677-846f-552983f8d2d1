{"name": "recruitment-system", "version": "1.0.0", "description": "招聘信息系统 - Monorepo架构", "private": true, "workspaces": ["apps/backend", "apps/cross-platform", "tools/testing"], "scripts": {"dev": "pnpm --parallel dev", "build": "pnpm --recursive build", "test": "pnpm --recursive test", "lint": "pnpm --recursive lint", "clean": "pnpm --recursive clean", "install:all": "pnpm install", "backend:dev": "cd apps/backend && python main.py", "cross-platform:dev": "cd apps/cross-platform && pnpm dev:h5", "cross-platform:build": "cd apps/cross-platform && pnpm build:h5", "docker:build": "docker-compose -f configs/deployment/docker-compose.yml build", "docker:up": "docker-compose -f configs/deployment/docker-compose.yml up -d", "docker:down": "docker-compose -f configs/deployment/docker-compose.yml down"}, "keywords": ["recruitment", "job-search", "monorepo", "<PERSON><PERSON><PERSON>", "react"], "author": "Recruitment Team", "license": "MIT", "devDependencies": {"@types/node": "^20.10.4", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "pnpm": "^8.0.0", "prettier": "^3.1.1", "puppeteer": "^24.9.0", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}}