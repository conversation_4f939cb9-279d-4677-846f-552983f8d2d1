#!/bin/bash

# 事业编制招聘信息查询系统 - 停止所有服务脚本
# 优智帮工作室 (Youzhbang Studio)
# 创建时间: 2025-05-30
# 版本: 1.0.0
# 使用方法: ./scripts/stop-all.sh

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_step() {
    echo -e "${CYAN}[STEP]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_system() {
    echo -e "${MAGENTA}[SYSTEM]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 显示停止横幅
show_banner() {
    echo -e "${MAGENTA}"
    echo "=================================================================="
    echo "  事业编制招聘信息查询系统 - 停止所有服务"
    echo "  优智帮工作室 (Youzhbang Studio)"
    echo "  版本: 1.0.0 | 创建时间: 2025-05-30"
    echo "=================================================================="
    echo -e "${NC}"
}

# 停止指定端口的进程
stop_port_process() {
    local port=$1
    local service_name=$2
    
    log_info "检查端口 $port 上的 $service_name 服务..."
    
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        local pids=$(lsof -Pi :$port -sTCP:LISTEN -t)
        log_info "发现 $service_name 进程: $pids"
        
        for pid in $pids; do
            log_info "停止进程 $pid ($service_name)"
            
            # 首先尝试优雅停止
            if kill -TERM $pid 2>/dev/null; then
                log_info "发送 SIGTERM 信号到进程 $pid"
                
                # 等待进程停止
                local count=0
                while kill -0 $pid 2>/dev/null && [ $count -lt 10 ]; do
                    sleep 1
                    count=$((count + 1))
                    echo -n "."
                done
                echo ""
                
                # 如果进程仍在运行，强制停止
                if kill -0 $pid 2>/dev/null; then
                    log_warning "进程 $pid 未响应 SIGTERM，使用 SIGKILL 强制停止"
                    kill -KILL $pid 2>/dev/null || true
                    sleep 1
                fi
                
                if ! kill -0 $pid 2>/dev/null; then
                    log_success "进程 $pid ($service_name) 已停止"
                else
                    log_error "无法停止进程 $pid ($service_name)"
                fi
            else
                log_warning "无法发送信号到进程 $pid，可能已经停止"
            fi
        done
    else
        log_info "$service_name 服务未在端口 $port 上运行"
    fi
}

# 停止所有相关进程
stop_all_processes() {
    log_info "查找所有相关进程..."
    
    # 查找uvicorn进程
    local uvicorn_pids=$(pgrep -f "uvicorn.*api.main:app" 2>/dev/null || true)
    if [ ! -z "$uvicorn_pids" ]; then
        log_info "发现uvicorn进程: $uvicorn_pids"
        for pid in $uvicorn_pids; do
            log_info "停止uvicorn进程 $pid"
            kill -TERM $pid 2>/dev/null || true
        done
    fi
    
    # 查找vite进程
    local vite_pids=$(pgrep -f "vite.*--port.*3001" 2>/dev/null || true)
    if [ ! -z "$vite_pids" ]; then
        log_info "发现vite进程: $vite_pids"
        for pid in $vite_pids; do
            log_info "停止vite进程 $pid"
            kill -TERM $pid 2>/dev/null || true
        done
    fi
    
    # 查找node进程（可能是前端开发服务器）
    local node_pids=$(pgrep -f "node.*vite" 2>/dev/null || true)
    if [ ! -z "$node_pids" ]; then
        log_info "发现node vite进程: $node_pids"
        for pid in $node_pids; do
            log_info "停止node进程 $pid"
            kill -TERM $pid 2>/dev/null || true
        done
    fi
    
    # 等待进程停止
    sleep 3
}

# 清理日志文件
cleanup_logs() {
    local project_root="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
    local logs_dir="$project_root/logs"
    
    if [ -d "$logs_dir" ]; then
        log_info "清理日志文件..."
        
        # 备份现有日志
        if [ -f "$logs_dir/backend.log" ]; then
            mv "$logs_dir/backend.log" "$logs_dir/backend.log.$(date +%Y%m%d_%H%M%S)" 2>/dev/null || true
        fi
        
        if [ -f "$logs_dir/cross-platform.log" ]; then
            mv "$logs_dir/cross-platform.log" "$logs_dir/cross-platform.log.$(date +%Y%m%d_%H%M%S)" 2>/dev/null || true
        fi
        
        log_success "日志文件已备份"
    fi
}

# 主函数
main() {
    show_banner
    
    log_step "1/4 停止端口服务"
    # 停止后端服务 (端口 8000)
    stop_port_process 8000 "后端API"
    
    # 停止前端服务 (端口 3001)
    stop_port_process 3001 "前端开发服务器"
    
    log_step "2/4 停止相关进程"
    stop_all_processes
    
    log_step "3/4 验证服务停止"
    # 验证服务是否已停止
    local backend_running=false
    local frontend_running=false
    
    if lsof -Pi :8000 -sTCP:LISTEN -t >/dev/null 2>&1; then
        backend_running=true
        log_warning "后端服务仍在运行"
    else
        log_success "后端服务已停止"
    fi
    
    if lsof -Pi :3001 -sTCP:LISTEN -t >/dev/null 2>&1; then
        frontend_running=true
        log_warning "前端服务仍在运行"
    else
        log_success "前端服务已停止"
    fi
    
    log_step "4/4 清理和总结"
    cleanup_logs
    
    echo ""
    echo -e "${GREEN}=================================================================="
    echo "  🛑 服务停止完成！"
    echo "=================================================================="
    echo -e "${NC}"
    
    if [ "$backend_running" = false ] && [ "$frontend_running" = false ]; then
        echo -e "${GREEN}✅ 所有服务已成功停止${NC}"
        echo ""
        echo -e "${CYAN}服务状态:${NC}"
        echo "  🔧 后端API服务:     已停止"
        echo "  🌐 前端开发服务器:   已停止"
    else
        echo -e "${YELLOW}⚠️  部分服务可能仍在运行${NC}"
        echo ""
        echo -e "${CYAN}服务状态:${NC}"
        if [ "$backend_running" = true ]; then
            echo "  🔧 后端API服务:     仍在运行"
        else
            echo "  🔧 后端API服务:     已停止"
        fi
        
        if [ "$frontend_running" = true ]; then
            echo "  🌐 前端开发服务器:   仍在运行"
        else
            echo "  🌐 前端开发服务器:   已停止"
        fi
        
        echo ""
        echo -e "${YELLOW}如果服务仍在运行，请手动检查并停止相关进程${NC}"
    fi
    
    echo ""
    echo -e "${CYAN}提示:${NC}"
    echo "  📄 日志文件已备份到 logs/ 目录"
    echo "  🚀 使用 ./scripts/start-all.sh 重新启动所有服务"
    echo ""
}

# 执行主函数
main "$@"
